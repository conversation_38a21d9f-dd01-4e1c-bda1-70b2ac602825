name: bff-device-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        required: true
        type: string

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: bff
      part_name: device-api
      app_name: bff-device-api
      sonar_project_key: ${{ vars.SONAR_BFF_DEVICE_API_PROJECT_KEY }}
      build_image: ubuntu-8core
      deployment_s3_bucket: false
      use_workspace_focus: true

  predeploy-bff-stacks:
    name: predeploy appsync stacks
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
    strategy:
      fail-fast: false
      matrix:
        component:
          - name: mp
            app_name: mp-api
            component_dir: apps/bff-api/mp/api
          - name: dbs
            app_name: dbs-api
            component_dir: apps/bff-api/dbs/api
          - name: crms
            app_name: crms-engine
            component_dir: apps/bff-api/crms/engine
          - name: sdk
            app_name: sdk-api
            component_dir: apps/payments/sdk/api
    environment: ${{ inputs.environment }}-${{ matrix.component.app_name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/restore-archive
        if: needs.pre-deploy.outputs.archived_name != '' && needs.pre-deploy.outputs.archived_file_name != ''
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - uses: ./.github/workflows/common/sls-library
        if: inputs.environment == 'st'
        with:
          app_name: bff-device-api
          environment: ${{ inputs.environment }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          run_test: false
      - uses: ./.github/workflows/common/aws-setup
        if: inputs.environment == 'st'
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.app_name] }}
          aws_region: ${{ inputs.region }}

      - name: Deploy mock appsync
        if: inputs.environment == 'st'
        run: |
          echo "Deploying appsync"
          cd ${{ matrix.component.component_dir}}
          yarn build:schemas
          yarn sls deploy --stage ${{ inputs.stage }} --region ${{ inputs.region }} --config serverlessAppsync.ts

      - id: deploy
        name: Deploy to mock eventbridge
        shell: bash
        if: inputs.environment == 'st' && (matrix.component.name == 'dbs' || matrix.component.name == 'mp')
        env:
          STAGE: ${{ inputs.stage }}
        run: |
          cd apps/payments/device/api/
          aws cloudformation deploy --template-file mockCqrs.yaml --stack-name "${{ inputs.stage }}-${{ matrix.component.name }}-device-cqrs-iac-eventBridge" \
          --parameter-overrides EventBusName="${{ inputs.stage }}-${{ matrix.component.name }}-device" \
          --tags STAGE="${{ inputs.stage }}" COMPONENT_NAME="${{ matrix.component.name }}" PART_NAME="cqrs"

          aws cloudformation deploy --template-file mockSqs.yaml --stack-name "${{ inputs.stage }}-${{ matrix.component.name }}-device-cqrs-iac-sqs" \
          --parameter-overrides SqsName="${{ inputs.stage }}-${{ matrix.component.name }}-device" \
          --tags STAGE="${{ inputs.stage }}" COMPONENT_NAME="${{ matrix.component.name }}" PART_NAME="cqrs"
          echo deploy to ${{ inputs.stage }}

  deploy-bff-device:
    name: deploy stacks
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - predeploy-bff-stacks
    strategy:
      fail-fast: false
      matrix:
        component:
          - name: mp
            app_name: mp-api
            sls_config: serverlessMp.ts
          - name: dbs
            app_name: dbs-api
            sls_config: serverlessDbs.yml
          - name: dbs
            app_name: dbs-api
            sls_config: serverlessIdentity.yml
          - name: crms
            app_name: crms-engine
            sls_config: serverlessCrms.ts
          - name: sdk
            app_name: sdk-api
            sls_config: serverlessSdk.ts
    environment: ${{ inputs.environment }}-${{ matrix.component.app_name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/restore-archive
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - uses: ./.github/workflows/common/library
        if: inputs.environment == 'st' && matrix.component.enabled
        with:
          app_name: bff-device-api
          environment: ${{ inputs.environment }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          run_test: false
      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.app_name] }}
          aws_region: ${{ inputs.region }}

      - uses: ./.github/workflows/common/pact-test
        with:
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          work_dir: apps/payments/device/contract
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.app_name] }}
          app_name: bff-device-contract-test
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          skip_checkout: true

      - name: Deploy app
        uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          pre_deploy_script: sh bin/loadenv.sh ${{ inputs.stage }} ${{ matrix.component.name }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: bff-device-api
          sls_config: ${{ matrix.component.sls_config }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.app_name] }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/payments/device/api/'
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  system-test:
    name: system test
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-bff-device
    environment: ${{ inputs.environment }}-${{ matrix.component.app_name }}
    if: inputs.environment != 'prod' && inputs.environment != 'staging'
    strategy:
      fail-fast: false
      matrix:
        component:
          - name: mp
            app_name: mp-api
          - name: dbs
            app_name: dbs-api
          - name: crms
            app_name: crms-engine
          - name: sdk
            app_name: sdk-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.app_name] }}
          aws_region: ${{ inputs.region }}

      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/restore-archive
        if: needs.pre-deploy.outputs.archived_name != '' && needs.pre-deploy.outputs.archived_file_name != ''
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - name: Run system tests
        env:
          AWS_REGION: ${{ inputs.region }}
          STAGE: ${{ inputs.stage }}
          SYDNEY_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          LONDON_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          region: ${{ inputs.region }}
        run: |
          yarn workspaces focus component-bff bff-device-system-test
          yarn nx build bff-device-system-test
          cd apps/payments/device/system-test
          yarn system:test src/${{ matrix.component.name }}/

  integration-test:
    name: integration test
    if: inputs.environment == 'dev' && inputs.region == vars.SYDNEY_REGION
    needs:
      - pre-deploy
      - system-test
    uses: ./.github/workflows/bff-device-integration-test.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['integration-tests'] }}

  tag:
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-bff-device
    environment: ${{ inputs.environment }}-bff-device-api
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-bff-device-api
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - pre-deploy
      - predeploy-bff-stacks
      - deploy-bff-device
      - system-test
      - tag
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: bff-device
          region: ${{ inputs.region }}
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
