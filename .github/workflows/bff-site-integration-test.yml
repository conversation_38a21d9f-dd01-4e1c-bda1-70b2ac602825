name: bff-site-integration-test

on:
  schedule:
    - cron: '0 14 * * *'

  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        required: true
        type: string

  workflow_dispatch:
    inputs:
      environment:
        default: dev
        type: string
        description: 'Environment to test against'
      region:
          default: ap-southeast-2
          type: string
          description: 'Region to test against'

jobs:
  integration-test:
    name: integration test
    runs-on: ubuntu-latest
    environment: dev-bff-integration-tests
    permissions:
      id-token: write
      contents: read
    if: github.event_name == 'schedule' || inputs.environment == 'dev'
    steps:
      - uses: actions/checkout@v4

      - name: set AWS_REGION
        run: echo "AWS_REGION=${{ github.event_name == 'schedule' && vars.SYDNEY_REGION || inputs.region }}" >> $GITHUB_ENV

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ inputs.role_to_assume }}
          aws_region: ${{ env.AWS_REGION }}

      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          JFROG_EMAIL: ${{ secrets.NPCO_JFROG_EMAIL }}
          JFROG_ACCESS_TOKEN: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          JFROG_REGISTRY: ${{ secrets.NPCO_JFROG_REGISTRY }}
          STAGE: dev
        with:
          project-name: dev-bff-integration-tests-site
          buildspec-override: |
            version: 0.2
            env:
              shell: bash
            phases:
              pre_build:
                commands:
                  - env
                  - cat .yarnrc.yml
                  - yarn config set npmPublishRegistry https://$JFROG_REGISTRY
                  - yarn config set npmScopes.npco.npmRegistryServer https://$JFROG_REGISTRY
                  - yarn config set npmScopes.npco.npmAuthToken "$JFROG_ACCESS_TOKEN"
                  - yarn config set npmScopes.npco.npmAlwaysAuth true
                  - corepack enable
                  - export AUTH0_DBS_CLIENT_ID=$(aws ssm get-parameter --with-decryption --name /dev-dbs-api/AUTH0_CLIENT_ID | jq -r '.Parameter.Value')
                  - export AUTH0_DBS_CLIENT_SECRET=$(aws ssm get-parameter --with-decryption --name /dev-dbs-api/AUTH0_CLIENT_SECRET | jq -r '.Parameter.Value')
                  - export AUTH0_MP_CLIENT_ID=$(aws ssm get-parameter --with-decryption --name /dev-mp-app/AUTH0_CLIENT_ID | jq -r '.Parameter.Value')
                  - export AUTH0_MP_CLIENT_SECRET=$(aws ssm get-parameter --with-decryption --name /dev-mp-app/AUTH0_CLIENT_SECRET | jq -r '.Parameter.Value')
                  - export AUTH0_MP_API_CLIENT_ID=$(aws ssm get-parameter --with-decryption --name /dev-mp-api/AUTH0_CLIENT_ID | jq -r '.Parameter.Value')
                  - export AUTH0_MP_API_CLIENT_SECRET=$(aws ssm get-parameter --with-decryption --name /dev-mp-api/AUTH0_CLIENT_SECRET | jq -r '.Parameter.Value')
                  - export AUTH0_AMS_CLIENT_ID=$(aws ssm get-parameter --with-decryption --name /dev-ams-engine/AUTH0_CLIENT_ID | jq -r '.Parameter.Value')
                  - export AUTH0_AMS_CLIENT_SECRET=$(aws ssm get-parameter --with-decryption --name /dev-ams-engine/AUTH0_CLIENT_SECRET | jq -r '.Parameter.Value')
                  - export ZELLER_APP_AUTH0_CLIENT_ID=$(aws ssm get-parameter --with-decryption --name /dev-mp-api/ZELLER_APP_AUTH0_CLIENT_ID | jq -r '.Parameter.Value')
                  - export ZELLER_APP_AUTH0_CLIENT_SECRET=$(aws ssm get-parameter --with-decryption --name /dev-mp-api/ZELLER_APP_AUTH0_CLIENT_SECRET | jq -r '.Parameter.Value')
                  - echo $AUTH0_DBS_CLIENT_ID $AUTH0_MP_CLIENT_ID $AUTH0_MP_API_CLIENT_ID $AUTH0_AMS_CLIENT_ID $ZELLER_APP_AUTH0_CLIENT_ID

              build:
                commands:
                  - set +e
                  - yarn workspaces focus component-bff bff-site-integration-test
                  - export STAGE=dev
                  - yarn nx run bff-site-integration-test:build
                  - yarn nx run bff-site-integration-test:integration:test
          env-vars-for-codebuild: |
            JFROG_EMAIL,
            JFROG_ACCESS_TOKEN,
            JFROG_REGISTRY,
            STAGE

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'  # Runs only when triggered by cron
    needs:
      - integration-test
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: bff-site
          environment: dev
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}