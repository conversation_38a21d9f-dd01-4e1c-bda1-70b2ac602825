name: cpi-tevalis-engine-destroy

on:
  pull_request:
    types: [closed]
    paths:
      - apps/payments/cpi/tevalis/**
      - .github/workflows/cpi-tevalis*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      stage:
        description: 'Stage to destroy'
        type: string
        required: true

jobs:
  destroy-cpi-tevalis-engine:
    runs-on: ubuntu-latest
    environment: st-cpi-tevalis-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/teardown
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          stage: ${{ inputs.stage }}
          component_name: 'cpi'
          part_name: 'tevalis-engine'
          region: ${{ vars.SYDNEY_REGION }}