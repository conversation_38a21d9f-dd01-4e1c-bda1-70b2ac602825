name: hlpos-destroy

on:
  pull_request:
    types: [closed]
    paths:
      - apps/payments/pay-at-table/hlpos/**
      - .github/workflows/hlpos*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      stage:
        description: "Stage to deploy to"
        type: string
        required: true

jobs:
  destroy-hlpos-engine:
    runs-on: ubuntu-latest
    environment: st-hlpos-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/teardown
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          stage: ${{ inputs.stage }}
          component_name: 'hlpos'
          part_name: 'engine'
          region: ${{ vars.SYDNEY_REGION }}
