name: oraclepos-destroy

on:
  pull_request:
    types: [closed]
    paths:
      - apps/payments/pay-at-table/oraclepos/**
      - .github/workflows/oraclepos*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      stage:
        description: "Stage to deploy to"
        type: string
        required: true

jobs:
  destroy-oraclepos-engine:
    runs-on: ubuntu-latest
    environment: st-oraclepos-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/teardown
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          stage: ${{ inputs.stage }}
          component_name: 'oraclepos'
          part_name: 'engine'
          region: ${{ vars.SYDNEY_REGION }}