name: posconnector-destroy

on:
  pull_request:
    types: [closed]
    paths:
      - apps/payments/pay-at-table/posconnector/broker/**
      - apps/payments/pay-at-table/posconnector/cqrs/cqrs/**
      - .github/workflows/posconnector*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      stage:
        description: 'Stage to deploy to (st)'
        type: string
        required: true

jobs:
  destroy-posconnector-api:
    runs-on: ubuntu-latest
    environment: st-posconnector-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/teardown
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          stage: ${{ inputs.stage }}
          component_name: 'posconnector'
          part_name: 'api'
          region: ${{ vars.SYDNEY_REGION }}
