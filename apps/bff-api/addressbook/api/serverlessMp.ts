import {
  ApiAppServerlessStack,
  vpcImport,
  MpApiAppEnvConfig,
  esbuildNestWithSharp,
  pluginsAppDefault,
  lambdaExtensions,
} from '@npco/component-bff-serverless';
import { policy } from './resources/common/policy';
import { eventBridgeRule } from './resources/mp/projection/eventBridgeRule';
import { s3 } from './resources/mp/s3';
import { lambdas as contact } from './resources/mp/contact/lambdas';
import { lambdas as image } from './resources/mp/image/lambdas';
import { lambdas as contactDcaTransaction } from './resources/mp/contactDcaTransaction/lambdas';
import { lambdas as paymentInstrument } from './resources/mp/paymentInstrument/lambdas';
import { lambdas as contactCommon } from './resources/common/contact/lambdas';
import { lambdas as contactEmail } from './resources/common/contactEmail/lambdas';
import { lambdas as contactPhone } from './resources/common/contactPhone/lambdas';
import { lambdas as contactMutation } from './resources/common/contactMutation/lambdas';
import { lambdas as label } from './resources/common/label/lambdas';
import { lambdas as projection } from './resources/common/projection/lambdas';
import { lambdas as tag } from './resources/common/tag/lambdas';
import { lambdas as subcategory } from './resources/common/subcategory/lambdas';

const envConfig = new MpApiAppEnvConfig('resources/mp/config/', true);

const sls = new ApiAppServerlessStack('addrbook', envConfig, {
  plugins: pluginsAppDefault,
  provider: {
    layers: [lambdaExtensions.parametersAndSecretsLambdaLayer],
  },
  custom: {
    ...envConfig.getDynamoDb(),
    ...envConfig.getAuth0(),
    ...envConfig.getAmsApi(),
    ...envConfig.getComponentCqrs(),
    vpcImport,
    esbuild: esbuildNestWithSharp,
    mpApiStackName: '${opt:stage}-mp-api',
    sessionCacheTableName: envConfig.sessionCacheTableName,
    auth0Tenant: envConfig.auth0Tenant,
    entityGsi: '${cf:${self:custom.dynamodbStackName}.entityGsi}',
    typeGsi: '${cf:${self:custom.dynamodbStackName}.typeGsi}',
    sortKeyGsi: '${cf:${self:custom.dynamodbStackName}.sortKeyGsi}',
    entityCacheGsi: '${cf:${self:custom.dynamodbStackName}.entityCacheGsi}',
    source: '${env:COMPONENT_NAME}',
    cqrsEventBusStackName: {
      dev: '${self:custom.cqrsStackName}-iac-eventBridge',
      staging: '${self:custom.cqrsStackName}-iac-eventBridge',
      prod: '${self:custom.cqrsStackName}-iac-eventBridge',
      st: '${self:custom.cqrsStackName}-iac-eventBridge-mock-adbk',
    },
    cqrsEventBus:
      '${cf:${self:custom.cqrsEventBusStackName.${opt:stage}, "${self:custom.cqrsEventBusStackName.st}"}.EventBusProjectionArn}',
    cbsEndpoint: 'http://${ssm:${env:STATIC_ENV_NAME}-banking-service-endpoint}',
    lambdaPath: 'src/lambdas/mp',
    contactImageBaseUrl: '${env:CONTACT_IMAGE_BASE_URL}',
    allowedOrigins: '${env:IMAGE_UPLOAD_BUCKET_ALLOWED_ORIGINS}',
    contactBucket: '${self:provider.stackName}-contact-${self:provider.region}-${self:custom.accountId}',
    s3DeletedTopic: '${ssm:s3-sns-notification-arn}',
    contactImageDownloadBucket: '${ssm:${env:STATIC_ENV_NAME}-s3-bucket-contact-image}',
    accessLogsBucketName: '${self:provider.stackName}-s3-logs-${self:provider.region}-${self:custom.accountId}',
    domicileLookupTableReadRolePolicyArn: envConfig.domicileLookupTableReadRolePolicyArn,
    permissionsTableReadRolePolicyArn: envConfig.permissionsTableReadRolePolicyArn,
    sessionCacheTableDBPolicyArn: {
      'Fn::ImportValue': '${self:custom.serviceName}-sessionCacheTableDBPolicyArn',
    },
    lambdaPararameterExtensionAccountId: envConfig.lambdaPararameterExtensionAccountId,
    ...envConfig.firebaseConfig,
  },
  environment: {
    ...envConfig.dotenvConfig,
    ENTITY_GSI: '${self:custom.entityGsi}',
    TYPE_GSI: '${self:custom.typeGsi}',
    ENTITY_CACHE_GSI: '${self:custom.entityCacheGsi}',
    SORT_KEY_GSI: '${self:custom.sortKeyGsi}',
    IS_RBAC_ENFORCED: '${env:IS_RBAC_ENFORCED}',
    IS_RBAC_ENFORCE_ROLE: '${env:IS_RBAC_ENFORCE_ROLE}',
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
    STAGE: '${opt:stage}',
    AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
    AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    AUTH0_TENANT: '${self:custom.auth0Tenant}',
  },
  functions: {
    ...contact,
    ...image,
    ...contactDcaTransaction,
    ...paymentInstrument,
    ...contactCommon,
    ...contactEmail,
    ...contactPhone,
    ...contactMutation,
    ...label,
    ...projection,
    ...tag,
    ...subcategory,
  },
  resources: {
    ...policy.Resources,
    ...eventBridgeRule.Resources,
    ...s3.Resources,
  },
});

module.exports = sls.build();
