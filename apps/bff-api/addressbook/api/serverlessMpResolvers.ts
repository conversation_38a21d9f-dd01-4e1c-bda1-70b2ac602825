import {
  ApiAppServerlessStack,
  vpcImport,
  pluginsAppDefault,
  MpApiAppEnvConfig,
  esbuildNestCommon,
} from '@npco/component-bff-serverless';
import { lambdas as cardholderLambdas } from './resources/mp/cardholder/lambdas';
import { resolvers as cardholderResolvers } from './resources/mp/resolvers/cardholderResolvers';
import { resolvers as emailResolvers } from './resources/mp/resolvers/emailResolvers';
import { resolvers as cashflowReportingResolvers } from './resources/mp/resolvers/cashflowReportingResolvers';
import { resolvers as dcaTransactionSenderResolvers } from './resources/mp/resolvers/dcaTransactionSenderResolvers';
import { resolvers as labelResolvers } from './resources/mp/resolvers/labelResolvers';
import { resolvers as paymentInstrumentResolvers } from './resources/mp/resolvers/paymentInstrumentResolvers';
import { resolvers as phoneResolvers } from './resources/mp/resolvers/phoneResolvers';
import {
  resolvers,
  getContactResolverDataSource,
  publishContactUpdateEventResolver,
  publishContactUpdateEventDataSource,
} from './resources/mp/resolvers/resolvers';
import { resolvers as subcategoryResolvers } from './resources/mp/resolvers/subcategoryResolvers';
import { resolvers as tagResolvers } from './resources/mp/resolvers/tagResolvers';

export const envConfig = new MpApiAppEnvConfig('resources/mp/config', true);

const bffStackName = `${envConfig.service}-addrbook`;

const sls = new ApiAppServerlessStack('addressbook', envConfig, {
  plugins: pluginsAppDefault,
  custom: {
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAuth0(),
    ...envConfig.getAppsync(),
    vpcImport,
    esbuild: esbuildNestCommon,
    entityTableName: envConfig.entityTableName,
    sessionCacheTableName: envConfig.sessionCacheTableName,
    entityGsi: '${env:ENTITY_GSI}',
    entityCacheGsi: '${env:ENTITY_CACHE_GSI}',
    typeGsi: '${env:TYPE_GSI}',
    cardholderGsi: '${env:CARDHOLDER_GSI}',
    mpCqrsStackName: '${opt:stage}-mp-cqrs',
    mpCqrsCommandHandler: '${self:custom.mpCqrsStackName}-commandHandlers-handler',
    lambdaPath: 'src/lambdas/mp',
    domicileLookupTableReadRolePolicyArn: envConfig.domicileLookupTableReadRolePolicyArn,
    permissionsTableReadRolePolicyArn: envConfig.permissionsTableReadRolePolicyArn,
    sessionCacheTableDBPolicyArn: {
      'Fn::ImportValue': '${self:custom.serviceName}-sessionCacheTableDBPolicyArn',
    },
    entityTableQueryRolePolicyArn: {
      'Fn::ImportValue': '${self:custom.serviceName}-entityTableQueryRolePolicyArn',
    },
    getContactsByBpayBillerCodeLambda: `${bffStackName}-getContactsByBpayBillerCode`,
    exportContactsLambda: `${bffStackName}-exportContacts`,
    getContactImageUploadFormLambda: `${bffStackName}-getContactImageUploadFormHandler`,
    removeImageLambda: `${bffStackName}-removeImageHandler`,
    getSubscriptionContactTagsLambda: `${bffStackName}-getSubscriptionContactTags`,
    untagContactLambda: `${bffStackName}-unTagContactHandler`,
    getSubscriptionLinkedContactsLambda: `${bffStackName}-getSubscriptionLinkedContactsHandler`,
    linkContactDcaTransactionSenderLambda: `${bffStackName}-linkContactDcaTransactionSender`,
    unlinkContactDcaTransactionSenderLambda: `${bffStackName}-unlinkContactDcaTransactionSender`,
    onContactUpdateLambda: `${bffStackName}-onContactUpdateHandler`,
    confirmPayeeLambda: `${bffStackName}-confirmPayeeHandler`,
    getContactLambda: `${bffStackName}-getContactHandler`,
    getContactsLambda: `${bffStackName}-getContactsHandler`,
    getLinkedContactsLambda: `${bffStackName}-getLinkedContactsHandler`,
    getContactTagsLambda: `${bffStackName}-getContactTags`,
    getContactEmailsLambda: `${bffStackName}-getContactEmails`,
    getContactPhonesLambda: `${bffStackName}-getContactPhones`,
    getContactResolverLambda: `${bffStackName}-getContactResolverHandler`,
    linkContactLambda: `${bffStackName}-linkContactHandler`,
    unlinkContactLambda: `${bffStackName}-unlinkContactHandler`,
    createContactLambda: `${bffStackName}-createContactHandler`,
    createContactsLambda: `${bffStackName}-createContactsHandler`,
    updateContactLambda: `${bffStackName}-updateContactHandler`,
    deleteContactLambda: `${bffStackName}-deleteContactHandler`,
    getLabelsLambda: `${bffStackName}-getLabels`,
    addLabelLambda: `${bffStackName}-addLabel`,
    updateLabelLambda: `${bffStackName}-updateLabel`,
    deleteLabelLambda: `${bffStackName}-deleteLabel`,
    tagContactLambda: `${bffStackName}-tagContactHandler`,
    linkContactCardholderLambda: `${bffStackName}-linkCardholderHandler`,
    unlinkContactCardholderLambda: `${bffStackName}-unlinkCardholderHandler`,
    linkContactSubcategoryLambda: `${bffStackName}-linkSubcategory`,
    unlinkContactSubcategoryLambda: `${bffStackName}-unlinkSubcategory`,
    getContactForDcaTransactionResolverLambda: `${bffStackName}-getContactForDcaTransactionResolver`,
    getPaymentInstrumentLambda: `${bffStackName}-getPaymentInstrument`,
    getPaymentInstrumentsLambda: `${bffStackName}-getPaymentInstruments`,
    getPaymentInstrumentsWithoutContactLambda: `${bffStackName}-getPaymentInstrumentsWithoutContact`,
    createPaymentInstrumentLambda: `${bffStackName}-createPaymentInstrument`,
    createAndLinkPaymentInstrumentLambda: `${bffStackName}-createAndLinkPaymentInstrument`,
    linkPaymentInstrumentWithContactLambda: `${bffStackName}-linkPIWithContactHandler`,
    unlinkPaymentInstrumentFromContactLambda: `${bffStackName}-unlinkPIFromContactHandler`,
    updatePaymentInstrumentLambda: `${bffStackName}-updatePaymentInstrument`,
  },
  functions: {
    ...cardholderLambdas,
  },
  environment: {
    ...envConfig.dotenvConfig,
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
    CQRS_COMMAND_HANDLER: '${self:custom.mpCqrsCommandHandler}',
  },
  resources: {
    ...resolvers,
    ...tagResolvers,
    ...emailResolvers,
    ...phoneResolvers,
    ...paymentInstrumentResolvers,
    ...labelResolvers,
    ...subcategoryResolvers,
    ...cardholderResolvers,
    ...cashflowReportingResolvers,
    ...dcaTransactionSenderResolvers,
    publishContactUpdateEventResolver,
    publishContactUpdateEventDataSource,
    getContactResolverDataSource,
  },
});

module.exports = sls.build();
