COMPONENT_NAME=crms
PART_NAME=engine

LAMBDA_TIMEOUT_IN_SECONDS=30
LAMBDA_MEMORY_DEFAULT=512
IDENTITY_AUTH0_AUDIENCE=https://devices.myzeller.com
IDENTITY_AUTH0_TENANT=https://zeller-dev.au.auth0.com
OPENID_ISSUER_URL=https://zeller-dev.au.auth0.com/

# hubspot
HUBSPOT_API_ENDPOINT=https://api.hubapi.com
API_VERSION=v1
HUBSPOT_SPA_URL=https://crms-crm.myzeller.dev
HUBSPOT_SPA_ORIGIN=*

# hubspot private api
HUBSPOT_PRIVATE_API_VERSION=v1

LOG_LEVEL=debug

STATIC_ENV_NAME=dev

NODE_JS_RUNTIME=nodejs18.x


# AMS
AMS_API_ENDPOINT_VERSION=v2

# CMS
CMS_API_ENDPOINT_VERSION=v1

#fee service
FS_API_ENDPOINT_VERSION=v1

#Keep warm
KEEP_WARM_SCHEDULER=cron(0/5 * ? * * *)
KEEP_WARM_SOURCE=keep.warm
LAMBDA_KEEP_WARM_STATE=ENABLED

# VPC
VPC_ENV_NAME=dev
SHOULD_CREATE_CUSTOM_DOMAIN=true
DOMAIN_NAME=*.crms.myzeller.dev
CRMS_API_ENDPOINT=api.crms.myzeller.dev
CERTIFICATE_NAME=*.crms.myzeller.dev
HOSTED_ZONE_ID=Z08299362VLOKXC5IN9E7
CERTIFICATE_ARN=arn:aws:acm:ap-southeast-2:264100014405:certificate/ccb79628-6081-459d-acdb-79f875eca505
CERTIFICATE_ARN_SINGAPORE=arn:aws:acm:ap-southeast-1:264100014405:certificate/21ef655f-1e4b-4076-aa29-804d9a1774be

# Frontend assets
SCREENSAVER_LOGO_BASE_URL=https://dashboard.myzeller.dev/screensaver/assets/

# Dynamodb
COMPONENT_TABLE=Entities
TRANSACTION_GSI=transactionGsi
ACCESS_TOKEN_GSI=accessTokenGsi
ENTITY_GSI=entityGsi
BILL_STATUS_GSI=billStatusGsi
SHORT_ID_GSI=shortIdGsi
TYPE_GSI=typeGsiV2
SITE_GSI=siteGsi
DEPOSIT_GSI=depositGsi
SORT_KEY_GSI=sortKeyGsi
DEVICE_GSI=deviceGsi
SITE_NAME_GSI=siteNameGsi
CUSTOMER_GSI=customerGsi
CARDHOLDER_GSI=cardholderGsi
HUBSPOT_TOKEN_GSI=hubspotTokenGsi
HUBSPOT_COMPANY_GSI=hubspotCompanyGsi
HUBSPOT_CONTACT_GSI=hubspotContactGsi
DEVICE_SERIALMODEL_GSI=deviceSerialModelGsi
TRANSACTION_DATE_GSI=transactionDateGsi
WAS_OFFLINE_APPROVED_GSI=wasOfflineApprovedGsi
METRIC_OUTSTANDING_AMOUNT_GSI=outstandingAmountGsi
METRIC_AMOUNT_GSI=amountGsi
METRIC_LIFETIME_OUTSTANDING_AMOUNT_GSI=lifetimeOutstandingAmountGsi
METRIC_LIFETIME_AMOUNT_GSI=lifetimeAmountGsi
METRIC_LIFETIME_GPV_GSI=lifetimeGpvGsi
METRIC_AVERAGE_VALUE_GSI=averageValueGsi
ORIGINAL_TRANSACTION_GSI=originalTransactionGsi
DEBIT_CARD_ID_GSI=debitCardIdGsi
SECONDARY_GSI_V1=secondaryGsiV1
CONTACT_UUID_GSI=contactUuidGsi
ENTITY_SORT_KEY_GSI=entitySortKeyGsi

MERCHANT_TABLE=Merchant
BANKING_PRODUCT_TABLE=BankingProduct

CRON_JOB_OAUTH_REFRESH="rate(5 minutes)"
BACKUP_SCHEDULER=cron(0 16 1 * ? *)
DEPOSITS_PENDING_GSI=depositsPendingGsi
DEPOSITS_PENDING_SCHEDULER=cron(0 15 ? * 1 *)
STAND_IN_METRIC_30_DAY_SCHEDULER=cron(0 0 * * ? *)

EXPORT_BUCKET_ALLOWED_ORIGINS=http://localhost:3000,https://dashboard.myzeller.dev,https://crms-crm.myzeller.dev
UPLOAD_BUCKET_ALLOWED_ORIGINS=http://localhost:3000,https://dashboard.myzeller.dev,https://crms-crm.myzeller.dev
GROWSURF_ENABLE=false

#Risk engine ticket creation - using pipeline My replaced pipeline
HUBSPOT_RISK_ENGINE_PIPELINE=4135723
HUBSPOT_RISK_ENGINE_PIPELINE_STAGE=********

#Entity Secure Document ticket creation - using pipeline My replaced pipeline
HUBSPOT_SECURE_TICKET_PIPELINE=9368734
HUBSPOT_SECURE_TICKET_PIPELINE_STAGE=********

#Fortiro envs for document scanning
FORTIRO_ENABLE=true

# global event bus name
GLOBAL_EVENT_BUS_NAME=dev-eventBus-global

# Billing
BILLING_API_ENDPOINT_VERSION=v1

# Multiple region
DEPLOY_MULTI_REGION=false

# Banking Wrapper
BANKING_WRAPPER_ENABLED=true

#Materialise incoming external transaction flag
MATERIALISE_INCOMING_EXTERNAL_NON_APPROVED_TRANSACTION=true

# Selfie Verification
SELFIE_CHECK_VERIFICATION_ENABLED=true

# Payment Gateway Service
PGS_API_ENDPOINT_SSM=dev-shared-endpoint

# Multi Entity use case
MULTI_ENTITY_ENABLED=true

# Hubspot Pipeline IDs
CHARGEBACK_PIPELINE_ID=********
COMPLIANCE_EXTRADOC_PIPELINE_ID=9368734

# Databricks VPC
DATA_BRICKS_VPC=vpce-03bb8344c8a339824

DOMICILE_LOOKUP_ENABLED=true