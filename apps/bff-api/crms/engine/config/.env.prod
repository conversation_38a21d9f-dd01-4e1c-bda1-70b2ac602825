COMPONENT_NAME=crms
PART_NAME=engine

LAMBDA_TIMEOUT_IN_SECONDS=30
LOG_LEVEL=info
LAMBDA_MEMORY_DEFAULT=512
IDENTITY_AUTH0_AUDIENCE=https://devices.myzeller.com
IDENTITY_AUTH0_TENANT=https://zeller.au.auth0.com
OPENID_ISSUER_URL=https://zeller.au.auth0.com/

# hubspot
HUBSPOT_API_ENDPOINT=https://api.hubapi.com
HUBSPOT_SPA_URL=https://crms-crm.myzeller.com
HUBSPOT_SPA_ORIGIN=https://crms-crm.myzeller.com
API_VERSION=v1

# hubspot private api
HUBSPOT_PRIVATE_API_VERSION=v1

# AMS
AMS_API_ENDPOINT_VERSION=v1

NODE_JS_RUNTIME=nodejs18.x

# CMS
CMS_API_ENDPOINT_VERSION=v1

#fee service
FS_API_ENDPOINT_VERSION=v1

STATIC_ENV_NAME=prod

#Keep warm
KEEP_WARM_SCHEDULER=cron(0/5 * ? * * *)
KEEP_WARM_SOURCE=keep.warm
LAMBDA_KEEP_WARM_STATE=ENABLED

# VPC
VPC_ENV_NAME=prod
SHOULD_CREATE_CUSTOM_DOMAIN=true
DOMAIN_NAME=*.crms.myzeller.com
CRMS_API_ENDPOINT=api.crms.myzeller.com
CERTIFICATE_NAME=*.crms.myzeller.com
HOSTED_ZONE_ID=Z09148823AJ9X31E7XA94
CERTIFICATE_ARN=arn:aws:acm:ap-southeast-2:477082626021:certificate/b07b89a4-1871-4015-b65d-8c1d336bc1a9

# Frontend assets
SCREENSAVER_LOGO_BASE_URL=https://dashboard.myzeller.com/screensaver/assets/

# Dynamodb
COMPONENT_TABLE=Entities
TRANSACTION_GSI=transactionGsi
ACCESS_TOKEN_GSI=accessTokenGsi
ENTITY_GSI=entityGsi
BILL_STATUS_GSI=billStatusGsi
SHORT_ID_GSI=shortIdGsi
TYPE_GSI=typeGsiV2
SITE_GSI=siteGsi
SORT_KEY_GSI=sortKeyGsi
SITE_NAME_GSI=siteNameGsi
DEPOSIT_GSI=depositGsi
DEVICE_GSI=deviceGsi
CUSTOMER_GSI=customerGsi
CARDHOLDER_GSI=cardholderGsi
HUBSPOT_TOKEN_GSI=hubspotTokenGsi
HUBSPOT_COMPANY_GSI=hubspotCompanyGsi
HUBSPOT_CONTACT_GSI=hubspotContactGsi
DEVICE_SERIALMODEL_GSI=deviceSerialModelGsi
TRANSACTION_DATE_GSI=transactionDateGsi
WAS_OFFLINE_APPROVED_GSI=wasOfflineApprovedGsi
METRIC_OUTSTANDING_AMOUNT_GSI=outstandingAmountGsi
METRIC_AMOUNT_GSI=amountGsi
METRIC_LIFETIME_OUTSTANDING_AMOUNT_GSI=lifetimeOutstandingAmountGsi
METRIC_LIFETIME_AMOUNT_GSI=lifetimeAmountGsi
METRIC_LIFETIME_GPV_GSI=lifetimeGpvGsi
METRIC_AVERAGE_VALUE_GSI=averageValueGsi
ORIGINAL_TRANSACTION_GSI=originalTransactionGsi
DEBIT_CARD_ID_GSI=debitCardIdGsi
SECONDARY_GSI_V1=secondaryGsiV1
CONTACT_UUID_GSI=contactUuidGsi
ENTITY_SORT_KEY_GSI=entitySortKeyGsi

MERCHANT_TABLE=Merchant
BANKING_PRODUCT_TABLE=BankingProduct

CRON_JOB_OAUTH_REFRESH="rate(5 minutes)"
BACKUP_SCHEDULER=cron(0 16 ? * * *)
DEPOSITS_PENDING_GSI=depositsPendingGsi
DEPOSITS_PENDING_SCHEDULER=cron(0 15 ? * 1 *)
STAND_IN_METRIC_30_DAY_SCHEDULER=cron(0 0 * * ? *)

EXPORT_BUCKET_ALLOWED_ORIGINS=https://dashboard.myzeller.com,https://crms-crm.myzeller.com
UPLOAD_BUCKET_ALLOWED_ORIGINS=https://dashboard.myzeller.com,https://crms-crm.myzeller.com
GROWSURF_ENABLE=true

#Risk engine ticket creation for pipeline Risk Operations - ZRE Alerts - v2
HUBSPOT_RISK_ENGINE_PIPELINE=4482967
HUBSPOT_RISK_ENGINE_PIPELINE_STAGE=********

#Entity Secure Document ticket creation - Compliance Operations - Extra documents from Merchant
HUBSPOT_SECURE_TICKET_PIPELINE=9358435
HUBSPOT_SECURE_TICKET_PIPELINE_STAGE=********

#Fortiro envs for document scanning
FORTIRO_ENABLE=true

# global event bus name
GLOBAL_EVENT_BUS_NAME=prod-eventBus-global

# Billing
BILLING_API_ENDPOINT_VERSION=v1

# Multiple region
DEPLOY_MULTI_REGION=false

# Banking Wrapper
BANKING_WRAPPER_ENABLED=true

#Materialise incoming external transaction flag
MATERIALISE_INCOMING_EXTERNAL_NON_APPROVED_TRANSACTION=true

# Selfie Verification
SELFIE_CHECK_VERIFICATION_ENABLED=true

# Payment Gateway Service
PGS_API_ENDPOINT_SSM=prod-pgs-api-cnp-apiGatewayEndpoint

# Multi Entity use case
MULTI_ENTITY_ENABLED=true

# Hubspot Pipeline IDs
CHARGEBACK_PIPELINE_ID=1745239
COMPLIANCE_EXTRADOC_PIPELINE_ID=2493331

# Databricks VPC
DATA_BRICKS_VPC=vpce-0063f22becdb1fa90

DOMICILE_LOOKUP_ENABLED=false