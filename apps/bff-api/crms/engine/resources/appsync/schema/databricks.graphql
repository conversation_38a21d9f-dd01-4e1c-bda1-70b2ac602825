type Query {
  getTransactionOutlierChartData(entityUuid: ID!, filter: GetTransactionOutlierChartDataInput!): [TransactionOutlierChartDataPoint]
}

enum TransactionOutlierCategory {
  NORMAL
  OUTLIER
}

input GetTransactionOutlierChartDataInput {
  dateFilter: DateFilterInput!
}

input DateFilterInput {
  startDate: AWSDate!
  endDate: AWSDate
}

type TransactionOutlierChartDataPoint {
  volume: Int!
  date: AWSDate!
  amount: Int!
  outlierCategory: TransactionOutlierCategory!
}