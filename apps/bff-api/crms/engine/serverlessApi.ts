import {
  ApiAppServerlessStack,
  ServerlessPlugin,
  ssmSharedVpcImport as vpcImport,
  SYDNEY_REGION,
} from '@npco/component-bff-serverless';

import { envConfig, pluginsApp } from './resources/common';
import { api, outputs } from './resources/hubspot/api';
import { cloudwatch } from './resources/hubspot/cloudwatch';
import { iam } from './resources/hubspot/iam';
import { lambdas } from './resources/hubspot/lambdas';

// lambda env size limits reached
const env = Object.keys(envConfig.dotenvConfig).reduce((acc: { [env: string]: string }, key) => {
  if (
    ![
      'SCREENSAVER_LOGO_BASE_URL',
      'WAS_OFFLINE_APPROVED_GSI',
      'METRIC_OUTSTANDING_AMOUNT_GSI',
      'METRIC_AMOUNT_GSI',
      'METRIC_LIFETIME_OUTSTANDING_AMOUNT_GSI',
      'METRIC_LIFETIME_AMOUNT_GSI',
      'METRIC_LIFETIME_GPV_GSI',
      'METRIC_AVERAGE_VALUE_GSI',
      'DEPOSITS_PENDING_GSI',
      'DEPOSITS_PENDING_SCHEDULER',
      'STAND_IN_METRIC_30_DAY_SCHEDULER',
      'EXPORT_BUCKET_ALLOWED_ORIGINS',
      'UPLOAD_BUCKET_ALLOWED_ORIGINS',
      'GROWSURF_ENABLE',
      'GROWSURF_API_ENDPOINT',
      'FORTIRO_ENABLE',
      'BILLING_API_ENDPOINT_VERSION',
      'BANKING_WRAPPER_ENABLED',
      'MATERIALISE_INCOMING_EXTERNAL_NON_APPROVED_TRANSACTION',
      'SELFIE_CHECK_VERIFICATION_ENABLED',
    ].includes(key)
  ) {
    acc[key] = envConfig.dotenvConfig[key];
  }
  return acc;
}, {});

const { AWS_REGION: region } = process.env;
const sls = new ApiAppServerlessStack('api', envConfig, {
  plugins: [...pluginsApp, ServerlessPlugin.DomainManager],
  provider: {
    name: 'aws',
    tracing: {
      apiGateway: true,
      lambda: true,
    },
    apiGateway: {
      metrics: true,
    },
    endpointType: 'regional',
    logs: {
      restApi: {
        accessLogging: true,
        level: 'ERROR',
      },
    },
  },

  custom: {
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAmsApi(),
    ...envConfig.getAppsync(),
    ...envConfig.getComponentCqrs(),
    ...envConfig.getDefaults(),
    customDomain: {
      enabled: region === SYDNEY_REGION ? '${env:SHOULD_CREATE_CUSTOM_DOMAIN}' : false,
      domainName: '${env:DOMAIN_NAME}',
      stage: '${opt:stage}',
      certificateName: '${env:CERTIFICATE_NAME}',
      createRoute53Record: 'false',
      endpointType: 'regional',
      securityPolicy: 'tls_1_2',
      apiType: 'rest',
      autoDomain: 'true',
      hostedZoneId: '${env:HOSTED_ZONE_ID}',
      certificateArn: '${self:custom.certificateArn.${opt:region}}',
    },
    certificateArn: {
      'ap-southeast-2': '${env:CERTIFICATE_ARN, ""}',
      'ap-southeast-1': '${env:CERTIFICATE_ARN_SINGAPORE, ""}',
      'eu-west-2': '',
    },
    vpcImport,
    apiVersion: '${env:API_VERSION}',
    hubspotApiKey: envConfig.ssmParam('HUBSPOT_API_KEY'),
    hubspotAppClientId: envConfig.ssmParam('HUBSPOT_APP_CLIENT_ID'),
    hubspotAppClientSecret: envConfig.ssmParam('HUBSPOT_APP_CLIENT_SECRET'),
    hubspotCustomObjectAppClientId: envConfig.ssmParam('HUBSPOT_CUSTOM_OBJECT_APP_CLIENT_ID'),
    hubspotCustomObjectAppClientSecret: envConfig.ssmParam('HUBSPOT_CUSTOM_OBJECT_APP_CLIENT_SECRET'),
    hubspotBulkUpdateAppClientId: envConfig.ssmParam('HUBSPOT_BULK_UPDATE_APP_CLIENT_ID'),
    hubspotBulkUpdateAppClientSecret: envConfig.ssmParam('HUBSPOT_BULK_UPDATE_APP_CLIENT_SECRET'),
    hubspotPrivateApiAppClientId: envConfig.ssmParam('HUBSPOT_PRIVATE_API_APP_CLIENT_ID'),
    hubspotPrivateApiAppClientSecret: envConfig.ssmParam('HUBSPOT_PRIVATE_API_APP_CLIENT_SECRET'),
    hubspotDocScanningAppClientId: envConfig.ssmParam('HUBSPOT_DOC_SCANNING_APP_CLIENT_ID'),
    hubspotDocScanningAppClientSecret: envConfig.ssmParam('HUBSPOT_DOC_SCANNING_APP_CLIENT_SECRET'),
    hubspotLowPriorityWebhookAppClientSecret: envConfig.ssmParam('HUBSPOT_LOW_PRIORITY_WEBHOOK_APP_CLIENT_SECRET'),
    hubspotNotificationsAppClientId: envConfig.ssmParam('HUBSPOT_NOTIFICATION_APP_CLIENT_ID'),
    hubspotNotificationsAppClientSecret: envConfig.ssmParam('HUBSPOT_NOTIFICATION_APP_CLIENT_SECRET'),
    hubspotApiGatewayEndpoint: 'https://${env:CRMS_API_ENDPOINT}',
    entityGsi: envConfig.entityGsi,
    appsyncUri: '${ssm:${self:custom.appsyncStackName}-endpoint}',
    hubspotTokenGsi: '${env:HUBSPOT_TOKEN_GSI}',
    hubspotTokenCronJob: '${env:CRON_JOB_OAUTH_REFRESH}',
    hubspotCompanyGsi: '${env:HUBSPOT_COMPANY_GSI}',
    hubspotContactGsi: '${env:HUBSPOT_CONTACT_GSI}',
    typeGsi: envConfig.typeGsi,
    hubspotSpaUrl: '${env:HUBSPOT_SPA_URL}',
    hubspotSpaOrigin: '${env:HUBSPOT_SPA_ORIGIN}',
    hubspotWebhookSqsArn: '${cf:${self:custom.service}-iac-sqs.WebhookQueueARN}',
    hubspotWebhookSqsUrl: '${cf:${self:custom.service}-iac-sqs.WebhookQueueURL}',
    hubspotWebhookLowPrioritySqsArn: '${cf:${self:custom.service}-iac-sqs.WebhookLowPriorityQueueARN}',
    hubspotWebhookLowPrioritySqsUrl: '${cf:${self:custom.service}-iac-sqs.WebhookLowPriorityQueueURL}',
    apiWebhookRequestPath: '/${self:custom.apiVersion}/hubspot/queue/webhook',
    apiWebhookLowPriorityRequestPath: '/${self:custom.apiVersion}/hubspot/queue/webhook/low-priority',
    cqrsCommandHandler: '${self:custom.cqrsStackName}-commandHandlers-handler',
    batchBucketAllowedOrigins: '${env:EXPORT_BUCKET_ALLOWED_ORIGINS}',
    tmsEntityThreeDSApiEndpoint: "${ssm:/${env:STATIC_ENV_NAME}-tms-es/entity3DSecurity/3DS_ENDPOINT, ''}",
    domicileLookupTableReadRolePolicyArn: {
      'Fn::ImportValue': '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn',
    },
  },
  functions: lambdas,
  resources: {
    ...iam,
    ...api,
    ...cloudwatch,
  },
  outputs: {
    ...outputs,
  },

  environment: {
    ...env,
    COMPONENT_TABLE: envConfig.componentTableName,
    HUBSPOT_API_ENDPOINT: '${env:HUBSPOT_API_ENDPOINT}',
    HUBSPOT_WEBHOOK_LOW_PRIORITY_REQUEST_PATH: '${self:custom.apiWebhookLowPriorityRequestPath}',
    HUBSPOT_WEBHOOK_REQUEST_PATH: '${self:custom.apiWebhookRequestPath}',
  },
});

module.exports = sls.build();
