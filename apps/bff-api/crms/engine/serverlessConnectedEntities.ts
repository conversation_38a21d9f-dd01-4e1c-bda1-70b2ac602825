import { ApiAppServerlessStack, ssmSharedVpcImport as vpcImport } from '@npco/component-bff-serverless';

import { envConfig, esbuildWithSharpAndPdfkit } from './resources/common/config';
import { pluginsApp } from './resources/common/plugins';
import { lambdas } from './resources/connectedEntities/lambdas';
import {
    publishConnectedEntitiesGraphResolver,
    publishConnectedEntitiesGraphDataSource,
    publishConnectedEntitiesReportResolver,
    publishConnectedEntitiesReportDataSource,
} from './resources/connectedEntities/resolvers';

type envVars = {
  [name: string]: string;
};

const envVarsToIgnore = ['DATA_BRICKS_VPC'];

const ignoreEnvVarsFromList = (fullEnvVarList: envVars, envVarsToIgnore: string[]) => {
  const filteredVars: envVars = {};

  Object.keys(fullEnvVarList).forEach((key) => {
    if (envVarsToIgnore.includes(key)) {
      return;
    }
    filteredVars[key] = fullEnvVarList[key];
  });

  return filteredVars;
};

const sls = new ApiAppServerlessStack('connected-entities', envConfig, {
  plugins: pluginsApp,
  package: {
    individually: true,
    patterns: ['!node_modules/aws-sdk', 'src/fonts/*'],
  },
  custom: {
    ...envConfig.getDefaults(),
    ...envConfig.getAppsync(),
    esbuild: esbuildWithSharpAndPdfkit,
    vpcImport,
    iamUserKey: envConfig.iamUserKey,
    iamUserSecret: envConfig.iamUserSecret,
    ceApiEndpoint: '${ssm:${env:STATIC_ENV_NAME}-ce-api-endpoint}',
    domicileLookupTableReadRolePolicyArn: {
      'Fn::ImportValue': '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn',
    },
  },
  functions: lambdas,
  resources: {
    publishConnectedEntitiesGraphResolver,
    publishConnectedEntitiesGraphDataSource,
    publishConnectedEntitiesReportResolver,
    publishConnectedEntitiesReportDataSource,
  },
  environment: {
    ...ignoreEnvVarsFromList(envConfig.dotenvConfig, envVarsToIgnore),
    CE_API_ENDPOINT: '${self:custom.ceApiEndpoint}',
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
  },
});

module.exports = sls.build();
