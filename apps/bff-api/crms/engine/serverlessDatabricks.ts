import { ApiAppServerlessStack, ssmSharedVpcImport as vpcImport } from '@npco/component-bff-serverless';

import { envConfig, pluginsApp } from './resources/common';
import { lambdas } from './resources/databricks/lambdas';

// lambda env size limits reached
const env = Object.keys(envConfig.dotenvConfig).reduce((acc: { [env: string]: string }, key) => {
  if (
    ![
      'HUBSPOT_API_ENDPOINT',
      'HUBSPOT_SPA_URL',
      'HUBSPOT_SPA_ORIGIN',
      'API_VERSION',
      'HUBSPOT_PRIVATE_API_VERSION',
      'AMS_API_ENDPOINT_VERSION',
      'CMS_API_ENDPOINT_VERSION',
      'FS_API_ENDPOINT_VERSION',
      'SCREENSAVER_LOGO_BASE_URL',
      'WAS_OFFLINE_APPROVED_GSI',
      'METRIC_OUTSTANDING_AMOUNT_GSI',
      'METRIC_AMOUNT_GSI',
      'METRIC_LIFETIME_OUTSTANDING_AMOUNT_GSI',
      'METRIC_LIFETIME_AMOUNT_GSI',
      'METRIC_LIFETIME_GPV_GSI',
      'METRIC_AVERAGE_VALUE_GSI',
      'DEPOSITS_PENDING_GSI',
      'DEPOSITS_PENDING_SCHEDULER',
      'STAND_IN_METRIC_30_DAY_SCHEDULER',
      'EXPORT_BUCKET_ALLOWED_ORIGINS',
      'UPLOAD_BUCKET_ALLOWED_ORIGINS',
      'GROWSURF_ENABLE',
      'GROWSURF_API_ENDPOINT',
      'FORTIRO_ENABLE',
      'BILLING_API_ENDPOINT_VERSION',
      'BANKING_WRAPPER_ENABLED',
      'MATERIALISE_INCOMING_EXTERNAL_NON_APPROVED_TRANSACTION',
      'SELFIE_CHECK_VERIFICATION_ENABLED',
    ].includes(key)
  ) {
    acc[key] = envConfig.dotenvConfig[key];
  }
  return acc;
}, {});

const esbuild = {
  esbuild: {
    bundle: true,
    external: ['lz4'],
    minify: false,
    sourcemap: false,
    exclude: [],
    target: 'node18',
  },
};

const sls = new ApiAppServerlessStack('databricks', envConfig, {
  plugins: [...pluginsApp],
  custom: {
    ...envConfig.getAppsync(),
    vpcImport,
    crmsDatabrickVpc: envConfig.ssmParam('DATABRICKS_CRMS_VPC'),
    databricksEndpoint: envConfig.ssmParam('DATABRICKS_CRMS_ENDPOINT'),
    databricksClientId: envConfig.ssmParam('DATABRICKS_CRMS_CLIENTID'),
    databricksClientSecretSsm: '/${opt:stage}-crms-engine/DATABRICKS_CRMS_SERVICE_PRINCIPAL_KEY',
    lambdaParameterExtensionArn: 'arn:aws:lambda:${opt:region}:${ssm:Parameters-and-Secrets-Lambda-Extension}:layer:AWS-Parameters-and-Secrets-Lambda-Extension:17',
    domicileLookupTableReadRolePolicyArn: {
      'Fn::ImportValue': '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn',
    },
    ...esbuild,
  },
  functions: lambdas,
  environment: {
    ...env,
  },
});

module.exports = sls.build();
