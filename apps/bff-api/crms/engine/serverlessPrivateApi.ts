import { ApiAppServerlessStack, ssmSharedVpcImport as vpcImport } from '@npco/component-bff-serverless';

import { envConfig, pluginsApp } from './resources/common';
import { lambdas } from './resources/hubspotPrivate/lambdas';

type envVars = {
  [name: string]: string;
};

const envVarsToIgnore = ['ENTITY_SORT_KEY_GSI', 'SORT_KEY_GSI', 'DOMICILE_LOOKUP_ENABLED'];

const ignoreEnvVarsFromList = (fullEnvVarList: envVars, envVarsToIgnore: string[]) => {
  const filteredVars: envVars = {};

  Object.keys(fullEnvVarList).forEach((key) => {
    if (envVarsToIgnore.includes(key)) {
      return;
    }
    filteredVars[key] = fullEnvVarList[key];
  });

  return filteredVars;
};

const sls = new ApiAppServerlessStack('private-api', envConfig, {
  plugins: [...pluginsApp, 'serverless-api-gateway-throttling'],
  provider: {
    name: 'aws',
    tracing: {
      apiGateway: true,
      lambda: true,
    },
    apiGateway: {
      metrics: true,
      resourcePolicy: [
        {
          Effect: 'Deny',
          Principal: '*',
          Action: 'execute-api:Invoke',
          Resource: 'execute-api:/*/*/*',
          Condition: {
            StringNotEquals: {
              'aws:sourceVpce': '${self:custom.crmsDatabrickVpc}',
            },
          },
        },
        {
          Effect: 'Allow',
          Principal: '*',
          Action: 'execute-api:Invoke',
          Resource: 'execute-api:/*/*/*',
        },
      ],
    },
    endpointType: 'private',
    logs: {
      restApi: {
        accessLogging: true,
        level: 'ERROR',
      },
    },
  },
  custom: {
    apiGatewayThrottling: {
      maxRequestsPerSecond: 20,
      maxConcurrentRequests: 20,
    },
    vpcImport,
    ...envConfig.getDefaults(),
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    entityGsi: envConfig.entityGsi,
    apiVersion: '${env:HUBSPOT_PRIVATE_API_VERSION}',
    hubspotApiKey: envConfig.ssmParam('HUBSPOT_API_KEY'),
    hubspotPrivateApiAppClientId: envConfig.ssmParam('HUBSPOT_PRIVATE_API_APP_CLIENT_ID'),
    hubspotPrivateApiAppClientSecret: envConfig.ssmParam('HUBSPOT_PRIVATE_API_APP_CLIENT_SECRET'),
    hubspotCompanyGroupWhitelist: '${self:custom.serviceName}/HUBSPOT_COMPANY_GROUP_WHITELIST',
    hubspotApiGatewayEndpoint: 'https://${env:CRMS_API_ENDPOINT}',
    hubspotTokenGsi: '${env:HUBSPOT_TOKEN_GSI}',
    hubspotCompanyGsi: '${env:HUBSPOT_COMPANY_GSI}',
    hubspotContactGsi: '${env:HUBSPOT_CONTACT_GSI}',
    // Cross account vpce link to network-office
    crmsDatabrickVpc: '${env:DATA_BRICKS_VPC}',
    lambdaPararameterExtensionAccountId: '${ssm:Parameters-and-Secrets-Lambda-Extension}',
    domicileLookupTableReadRolePolicyArn: {
      'Fn::ImportValue': '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn',
    },
  },
  functions: lambdas,

  resources: {
    apiGatewayEndpoint: {
      Type: 'AWS::SSM::Parameter',
      Properties: {
        Name: '${self:provider.stackName}-endpoint',
        Description: 'Private API Gateway Endpoint',
        Type: 'String',
        Value: {
          'Fn::Join': [
            '',
            [
              'https://',
              {
                Ref: 'ApiGatewayRestApi',
              },
              '.execute-api.${self:provider.region}.amazonaws.com/',
              '${opt:stage}',
            ],
          ],
        },
      },
    },
  },
  outputs: {
    apiGatewayId: {
      Value: {
        Ref: 'ApiGatewayRestApi',
      },
      Export: {
        Name: '${self:provider.stackName}-apiGatewayId',
      },
    },
    apiGatewayRootId: {
      Value: {
        Ref: 'ApiGatewayResourceV1',
      },
      Export: {
        Name: '${self:provider.stackName}-apiGatewayRootResourceId',
      },
    },
  },
  environment: {
    ...ignoreEnvVarsFromList(envConfig.dotenvConfig, envVarsToIgnore),
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
  },
});

module.exports = sls.build();
