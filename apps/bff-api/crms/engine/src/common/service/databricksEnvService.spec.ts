import { DatabricksEnvironmentService } from './databricksEnvService';

describe('DatabricksEnvironmentService test suite', () => {
  let envService: DatabricksEnvironmentService;
  let allEnvs: any;

  beforeAll(() => {
    allEnvs = process.env;
    process.env.DATABRICKS_CRMS_ENDPOINT = 'https://databricks-host/path';
    process.env.DATABRICKS_CRMS_CLIENTID = 'databricks-client-id';
    process.env.DATABRICKS_CRMS_CLIENT_SECRET_SSM = '/dev-crms-engine/dev-databricks-client-secret-name';
    process.env.DATABRICKS_CRMS_QUERY_SENSITIVITY = '2';
  });

  afterAll(() => {
    process.env = allEnvs;
  });

  it('should load env vars', () => {
    envService = new DatabricksEnvironmentService();

    expect(envService.databricksEndpoint).toBe('https://databricks-host/path');
    expect(envService.databricksClientId).toBe('databricks-client-id');
    expect(envService.databricksClientSecretSsm).toBe('/dev-crms-engine/dev-databricks-client-secret-name');
    expect(envService.databricksQuerySensitivity).toBe(2);
  });

  it('should load default env vars', () => {
    delete process.env.DATABRICKS_CRMS_QUERY_SENSITIVITY;
    envService = new DatabricksEnvironmentService();
    
    expect(envService.databricksEndpoint).toBe('https://databricks-host/path');
    expect(envService.databricksClientId).toBe('databricks-client-id');
    expect(envService.databricksClientSecretSsm).toBe('/dev-crms-engine/dev-databricks-client-secret-name');
    expect(envService.databricksQuerySensitivity).toBe(1.5);
  });
});
