import { EnvironmentService as BaseEnvironmentService } from '@npco/component-bff-core/dist/config/envService';

export class DatabricksEnvironmentService extends BaseEnvironmentService {
  region: string;

  databricksEndpoint: string;

  databricksClientId: string;

  databricksClientSecretSsm: string;

  databricksQuerySensitivity: number;

  constructor() {
    super();
    this.region = this.configService.get('AWS_REGION', 'ap-southeast-2');
    this.databricksEndpoint = this.configService.get('DATABRICKS_CRMS_ENDPOINT', '');
    this.databricksClientId = this.configService.get('DATABRICKS_CRMS_CLIENTID', '');
    this.databricksClientSecretSsm = this.configService.get('DATABRICKS_CRMS_CLIENT_SECRET_SSM', '');
    this.databricksQuerySensitivity = parseFloat(this.configService.get('DATABRICKS_CRMS_QUERY_SENSITIVITY', '1.5'));
  }
}
