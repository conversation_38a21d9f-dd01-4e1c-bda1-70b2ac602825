import { DatabricksAuthService } from './databricksAuthService';

const mockGetParameter = jest.fn().mockReturnValue({});
const mockSsm = {
  getParameter: mockGetParameter,
};

jest.mock('@npco/component-bff-core/dist/aws/ssmClient', () => ({
  SsmClient: jest.fn(() => mockSsm),
}));

jest.mock('@databricks/sql', () => {
    return {
        DBSQLClient: jest.fn().mockImplementation(() => {
            return {
                connect: jest.fn().mockResolvedValue('mocked-session'),
            };
        }),
    };
});

describe('DatabricksAuthService test suite', () => {
  let databricksService: DatabricksAuthService;
  const databricksClientId = 'databricks-client-id';
  const databricksEndpoint = 'databricks-host/databricks-path';
  
  beforeEach(() => {
    databricksService = new DatabricksAuthService(databricksClientId, databricksEndpoint);
  });

  it('should execute a query', async () => {
    const query = 'SELECT * FROM test_table';
    const params = { param1: 'value1' };

    const mockConnect = jest.fn().mockResolvedValue({
      openSession: jest.fn().mockResolvedValue({
        executeStatement: jest.fn().mockResolvedValue({
          fetchAll: jest.fn().mockResolvedValue([{ date: '1970-01-01', amount: 1, volume: 1, outlierCategory: 'NORMAL' }]),
          close: jest.fn(),
        }),
        close: jest.fn(),
      }),
      close: jest.fn(),
    });
    (databricksService as any).databricksClient.connect = mockConnect;

    const result = await databricksService.executeQuery(query, 'databricks-client-secret', params);

    expect(result).toEqual([{ date: '1970-01-01', amount: 1, volume: 1, outlierCategory: 'NORMAL' }]);
  });

  it('should throw an error if query execution fails', async () => {
    const query = 'SELECT * FROM test_table';
    const params = { param1: 'value1' };

    const mockConnect = jest.fn().mockResolvedValue({
      openSession: jest.fn().mockResolvedValue({
        executeStatement: jest.fn().mockRejectedValue(new Error('Query execution error')),
        close: jest.fn(),
      }),
      close: jest.fn(),
    });
    (databricksService as any).databricksClient.connect = mockConnect;

    await expect(databricksService.executeQuery(query, 'databricks-client-secret' ,params)).rejects.toThrow('Query execution error');
  });

  it('should close the session after executing the query', async () => {
    const query = 'SELECT * FROM test_table';
    const params = { param1: 'value1' };

    const mockCloseConnect = jest.fn();
    const mockCloseSession = jest.fn();
    const mockCloseQueryOperation = jest.fn();
    const mockConnect = jest.fn().mockResolvedValue({
      openSession: jest.fn().mockResolvedValue({
        executeStatement: jest.fn().mockResolvedValue({
          fetchAll: jest.fn().mockResolvedValue([{ date: '1970-01-01', amount: 1, volume: 1, outlierCategory: 'NORMAL' }]),
          close: mockCloseQueryOperation,
        }),
        close: mockCloseSession,
      }),
      close: mockCloseConnect,
    });
    (databricksService as any).databricksClient.connect = mockConnect;

    await databricksService.executeQuery(query,  'databricks-client-secret', params);

    expect(mockCloseConnect).toHaveBeenCalled();
    expect(mockCloseSession).toHaveBeenCalled();
    expect(mockCloseQueryOperation).toHaveBeenCalled();
  });
});
