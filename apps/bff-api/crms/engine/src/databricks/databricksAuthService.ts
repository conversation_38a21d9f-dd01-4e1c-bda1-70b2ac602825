import { error, info } from '@npco/component-bff-core/dist/utils/logger';

import { DBSQLClient } from '@databricks/sql';
import type IDBSQLSession from '@databricks/sql/dist/contracts/IDBSQLSession';
import type IOperation from '@databricks/sql/dist/contracts/IOperation';
import { debug } from 'console';

import type { TransactionOutlierChartDataOutput } from './types';
import { AuthType } from './types';

export class DatabricksAuthService {
  private readonly databricksClient: DBSQLClient = new DBSQLClient();

  private readonly databricksHost: string;

  private readonly databricksPath: string;

  constructor(private readonly databricksClientId: string, private readonly databricksEndpoint: string) {
    if (!this.databricksEndpoint.includes('/')) {
      throw new Error('Databricks endpoint must include a host and path');
    }
    const [host, ...path] = this.databricksEndpoint.split('/');
    this.databricksHost = host;
    this.databricksPath = `/${path.join('/')}`;
  }

  public async executeQuery(query: string, databricksClientSecret: string, params: Record<string, any>) {
    const connectOptions = {
      authType: AuthType.databricksOauthType,
      host: this.databricksHost,
      path: this.databricksPath,
      oauthClientId: this.databricksClientId,
      oauthClientSecret: databricksClientSecret,
    };

    debug(`Databricks connecting with options: ${JSON.stringify(connectOptions, null, 2)}`);
    const connection = await this.databricksClient.connect(connectOptions);
    debug(`Databricks opening session...`);
    const session: IDBSQLSession = await connection.openSession();

    try {
      info(`Executing databricks query with params: ${JSON.stringify(params, null, 2)}`);
      const queryOperation: IOperation = await session.executeStatement(query, params);
      const result = (await queryOperation.fetchAll()) as TransactionOutlierChartDataOutput[];
      info(`Databricks execute query result length: ${result.length}`);

      await queryOperation.close();
      return result.map((row) => ({
        ...row,
        date: new Date(row.date).toISOString().split('T')[0],
      }));
    } catch (err) {
      error(`Error executing databricks query: ${err}`);
      throw err;
    } finally {
      await session.close();
      await connection.close();
    }
  }
}
