import { DatabricksEnvironmentService } from '../common/service/databricksEnvService';
import { DatabricksService } from './databricksService';
import { TransactionOutlierChartDataInputFilter } from './types';

jest.mock('@npco/component-bff-core/dist/lambda/lambdaExtension', () => {
  return {
    getCachedSsmParameter: jest.fn().mockResolvedValue('test-client-secret'),
  };
});

const executeQueryMock = jest.fn().mockResolvedValue({
          data: [
            { transaction_uuid: 'uuid1', transaction_amount_dollar: 100 },
            { transaction_uuid: 'uuid2', transaction_amount_dollar: 200 },
          ],
        })

jest.mock('./databricksAuthService', () => {
  return {
    DatabricksAuthService: jest.fn().mockImplementation(() => {
      return {
        connect: jest.fn().mockResolvedValue({
          openSession: jest.fn().mockResolvedValue({
            executeStatement: jest.fn(),
            close: jest.fn(),
          }),
          close: jest.fn(),
        }),
        executeQuery: executeQueryMock,
      };
    }),
  };
});

describe('DatabricksService test suites', () => {
  let databricksService: DatabricksService;

  beforeAll(() => {
    const envService = {
      databricksClientId: 'test-client-id',
      databricksEndpoint: 'test-host/test-path',
      databricksQuerySensitivity: 1.5,
    } as DatabricksEnvironmentService;

    databricksService = new DatabricksService(envService);
  });

  it('should be able to fetch data using getTransactionOutlierChartData', async () => {
    const entityUuid = 'test-entity-uuid';
    const filter: TransactionOutlierChartDataInputFilter = {
      dateFilter: {
        endDate: '2023-10-01',
        startDate: '2023-09-01',
      }
    };

    const result = await databricksService.getTransactionOutlierChartData(entityUuid, filter);

    expect(result).toEqual({
      data: [
        { transaction_uuid: 'uuid1', transaction_amount_dollar: 100 },
        { transaction_uuid: 'uuid2', transaction_amount_dollar: 200 },
      ],
    });
    expect (executeQueryMock).toHaveBeenCalledWith(expect.any(String), 'test-client-secret', {
      namedParameters: {
        entity_uuid: entityUuid,
        date_start: filter.dateFilter.startDate,
        date_end: filter.dateFilter.endDate,
        sensitivity: 1.5,
      }
    });
  });

    it('should be able to fetch data using getTransactionOutlierChartData without endDate', async () => {
    const entityUuid = 'test-entity-uuid';
    const filter: TransactionOutlierChartDataInputFilter = {
      dateFilter: {
        startDate: '2023-09-01',
      }
    };

    const result = await databricksService.getTransactionOutlierChartData(entityUuid, filter);

    expect(result).toEqual({
      data: [
        { transaction_uuid: 'uuid1', transaction_amount_dollar: 100 },
        { transaction_uuid: 'uuid2', transaction_amount_dollar: 200 },
      ],
    });
    expect (executeQueryMock).toHaveBeenCalledWith(expect.any(String), 'test-client-secret', {
      namedParameters: {
        entity_uuid: entityUuid,
        date_start: filter.dateFilter.startDate,
        date_end: new Date().toISOString().split('T')[0],
        sensitivity: 1.5,
      }
    });
  });
});
