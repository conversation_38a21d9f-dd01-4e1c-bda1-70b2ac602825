export enum AuthType {
  databricksOauthType = 'databricks-oauth',
}

export type TransactionOutlierChartDataInputFilter = {
  dateFilter: TransactionOutlierChartDateFilter;
};

export type TransactionOutlierChartDateFilter = {
  startDate: string;
  endDate?: string;
};

export type TransactionOutlierChartDataOutput = {
  date: string;
  amount: number;
  outlierCategory: string;
  volume: number;
};
