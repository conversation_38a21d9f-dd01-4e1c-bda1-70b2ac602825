Resources:
  GetMyPersonalInfoHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt GetMyPersonalInfoHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  GetCustomerHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt GetCustomerHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  GetCustomerSitesHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt GetCustomerSitesHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  GetCustomersHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt GetCustomersHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  CreateCustomerHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt CreateCustomerHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  UpdateCustomerHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt UpdateCustomerHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  DeleteCustomerHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt DeleteCustomerHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  CustomerProjectionHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt CustomerProjectionHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  RequestCustomerIDVHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt RequestCustomerIDVHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  GetCustomerEntityMappingHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt GetCustomerEntityMappingHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'
