Resources:
  GetMyPersonalInfoHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt GetMyPersonalInfoHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'
  
  GetCustomerHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt GetCustomerHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  GetCustomerSitesHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt GetCustomerSitesHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  GetCustomersHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt GetCustomersHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  GetReferralCodeHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt GetReferralCodeHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  CreateCustomerHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt CreateCustomerHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  UpdateCustomerHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt UpdateCustomerHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  DeleteCustomerHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt DeleteCustomerHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  CustomerProjectionHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt CustomerProjectionHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  RequestCustomerIDVHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt RequestCustomerIDVHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  ResponseCustomerIDVHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt ResponseCustomerIDVHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  VerifyCustomerDocumentsHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt VerifyCustomerDocumentsHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  GetDocumentUploadUrlHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt GetDocumentUploadUrlHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  SendInviteEmailHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt SendInviteEmailHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  RequestEmailChangeHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt RequestEmailChangeHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  CompleteEmailChangeHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt CompleteEmailChangeHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  OnIconUploadInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt OnIconUploadLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  RemoveIconHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt RemoveIconHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  GetIconUploadFormHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt GetIconUploadFormHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  FinaliseCustomerKYCHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt FinaliseCustomerKYCHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  UpdateKycCheckpointHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt UpdateKycCheckpointHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  GenerateBVCheckTokenHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt GenerateBVCheckTokenHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  RequireBVCheckHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt RequireBVCheckHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  CreateBVCheckHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt CreateBVCheckHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  GetCustomerEntityMappingHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt GetCustomerEntityMappingHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  UpdateCustomerEntityMappingHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt UpdateCustomerEntityMappingHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  UpdateVisibleTabsHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt UpdateVisibleTabsHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  CreateRegisteringIndividualHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !GetAtt CreateRegisteringIndividualHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'