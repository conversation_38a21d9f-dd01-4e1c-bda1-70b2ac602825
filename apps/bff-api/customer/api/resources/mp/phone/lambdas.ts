import { Action, Arn, ManagedPolicy, ServerlessFunctions } from '@npco/component-bff-serverless';
import { lambdaExtensions } from '@npco/component-bff-serverless/dist/aws/lambda/extensions';

export const lambdaFirebaseEnvironment = {
  ZELLER_APP_FIREBASE_ADMIN_EMAIL_SSM_NAME: '${self:custom.firebaseAdminEmailSsmName}',
  ZELLER_APP_FIREBASE_ADMIN_PRIVATE_KEY_SSM_NAME: '${self:custom.firebaseAdminPrivateKeySsmName}',
  ZELLER_APP_AUTH0_CLIENT_ID: '${self:custom.zellerAppAuth0ClientId}',
};

export const lambdas: ServerlessFunctions = {
  identityPhoneRegisterHandler: {
    handler: 'src/lambda/mp/phoneLambda.identityPhoneRegister',
    name: 'identityPhoneRegister',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SMSCODE_TABLE: '${self:custom.smscodeTableName}',
      SMSCODE_TTL: '${self:custom.smscodeTtl}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      MESSAGE_MEDIA_API_KEY_SSM_NAME: '${self:custom.messageMediaApiKeySsmName}',
      MESSAGE_MEDIA_API_SECRET_SSM_NAME: '${self:custom.messageMediaApiSecretSsmName}',
      ...lambdaFirebaseEnvironment,
      DEPOSITS_PENDING_SCHEDULER: '',
      CRMS_API_ENDPOINT: '',
      ENTITY_TRANSACTION_TOTAL_GSI: '',
      LOGO_UPLOAD_BUCKET_ALLOWED_ORIGINS: '',
    },
    appsync: {
      fieldName: 'identityPhoneRegister',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.firebaseAdminParameterPolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        identityPhoneRegisterPolicy: [
          {
            effect: 'Deny',
            actions: ['sns:Publish'],
            resources: ['arn:aws:sns:*:*:*'],
          },
          {
            actions: [Action.sns.Publish],
            resources: ['*'],
          },
          {
            actions: [Action.dynamodb.Query, Action.dynamodb.PutItem, Action.dynamodb.UpdateItem],
            resources: [Arn.dynamodb.table('${self:custom.smscodeTableName}')],
          },
          {
            actions: [Action.ssm.GetParameter],
            resources: [
              Arn.ssm.parameter('/${self:custom.messageMediaApiKeySsmName}'),
              Arn.ssm.parameter('/${self:custom.messageMediaApiSecretSsmName}'),
            ],
          },
        ],
      },
    },
    layers: [lambdaExtensions.parametersAndSecretsLambdaLayer],
  },
  identityPhoneVerifyHandler: {
    handler: 'src/lambda/mp/phoneLambda.identityPhoneVerify',
    name: 'identityPhoneVerify',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SMSCODE_TABLE: '${self:custom.smscodeTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      MFA_ENROLMENT_ENABLED: '${env:MFA_ENROLMENT_ENABLED}',
    },
    appsync: {
      fieldName: 'identityPhoneVerify',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        phoneVerifyInvokeLambdaPolicy: [
          {
            actions: [Action.lambda.InvokeFunction],
            resources: [Arn.lambda.function('${self:custom.mpCqrsCommandHandler}*')],
          },
          {
            actions: [Action.dynamodb.Query, Action.dynamodb.DeleteItem],
            resources: [Arn.dynamodb.table('${self:custom.smscodeTableName}')],
          },
        ],
      },
    },
  },
  checkSensitiveAccessHandler: {
    handler: 'src/lambda/mp/phoneLambda.checkSensitiveAccess',
    name: 'checkSensitiveAccess',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    },
    appsync: {
      fieldName: 'checkSensitiveAccess',
      typeName: 'Query',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  identityPhoneChallengeHandler: {
    handler: 'src/lambda/mp/phoneLambda.identityPhoneChallenge',
    name: 'identityPhoneChallenge',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SMSCODE_TABLE: '${self:custom.smscodeTableName}',
      SMSCODE_TTL: '${self:custom.smscodeTtl}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      MESSAGE_MEDIA_API_KEY_SSM_NAME: '${self:custom.messageMediaApiKeySsmName}',
      MESSAGE_MEDIA_API_SECRET_SSM_NAME: '${self:custom.messageMediaApiSecretSsmName}',
    },
    appsync: {
      fieldName: 'identityPhoneChallenge',
      typeName: 'Mutation',
      template: {
        request: `{
            "version" : "2018-05-29",
            "operation": "Invoke",
            "payload": { "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.toJson($context.source),"info": $util.toJson($context.info) }
          }`,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        IdentityPhoneChallengePolicy: [
          {
            effect: 'Deny',
            actions: ['sns:Publish'],
            resources: ['arn:aws:sns:*:*:*'],
          },
          {
            actions: [Action.sns.Publish],
            resources: ['*'],
          },
          {
            actions: [Action.dynamodb.Query, Action.dynamodb.PutItem, Action.dynamodb.UpdateItem],
            resources: [Arn.dynamodb.table('${self:custom.smscodeTableName}')],
          },
          {
            actions: [Action.ssm.GetParameter],
            resources: [
              Arn.ssm.parameter('/${self:custom.messageMediaApiKeySsmName}'),
              Arn.ssm.parameter('/${self:custom.messageMediaApiSecretSsmName}'),
            ],
          },
        ],
      },
    },
    layers: [lambdaExtensions.parametersAndSecretsLambdaLayer],
  },
  identityPhoneChallengeVerifyHandler: {
    handler: 'src/lambda/mp/phoneLambda.identityPhoneChallengeVerify',
    name: 'identityPhoneChallengeVerify',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SMSCODE_TABLE: '${self:custom.smscodeTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'identityPhoneChallengeVerify',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        identityPhoneChallengeVerifyDbPolicy: [
          {
            actions: [Action.dynamodb.Query, Action.dynamodb.DeleteItem],
            resources: [Arn.dynamodb.table('${self:custom.smscodeTableName}')],
          },
        ],
      },
    },
  },
  initiateForcedLogoffProjectionHandler: {
    handler: 'src/lambda/mp/publisher/identityLambda.initiateForcedLogoffProjection',
    name: 'initiateForcedLogoffProjection',
    tracing: true,
    environment: {
      AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
      AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      IAM_USER_KEY: '${self:custom.iamUserKey}',
      IAM_USER_SECRET: '${self:custom.iamUserSecret}',
      APP_SYNC_ENDPOINT: '${self:custom.appSyncEndpoint}',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        eventBridgeDomainProjectionHandlerDLQPolicy: [
          {
            actions: [Action.sqs.SendMessage],
            resources: ['${self:custom.mpCqrsProjectionDLQArn}'],
          },
        ],
        initiateForcedLogoffProjectionDbLogPolicy: [
          {
            actions: [Action.dynamodb.DeleteItem],
            resources: [Arn.dynamodb.table('${self:custom.sessionCacheTableName}')],
          },
        ],
      },
    },
    memorySize: 512,
    deadLetter: { targetArn: '${self:custom.mpCqrsProjectionDLQArn}' },
  } as any,
  identityForcedLogoffHandler: {
    handler: 'src/lambda/mp/publisher/identityLambda.identityForcedLogoffHandler',
    name: 'identityForcedLogoff',
    tracing: true,
    environment: {
      AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
      AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    appsync: {
      fieldName: 'identityForcedLogoff',
      typeName: 'Subscription',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
};
