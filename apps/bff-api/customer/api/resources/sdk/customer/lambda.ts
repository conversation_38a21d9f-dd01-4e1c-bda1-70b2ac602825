import { Action, Arn, ManagedPolicy, ServerlessFunctions } from '@npco/component-bff-serverless';
import { lambdaCommon } from '../../common';

export const lambdas: ServerlessFunctions = {
  getCustomerEntityMappingHandler: {
    handler: 'src/lambda/sdk/lambdas.getCustomerEntityMappingHandler',
    name: 'getCustomerEntityMapping',
    ...lambdaCommon,
    appsync: {
      fieldName: 'getCustomerEntityMapping',
      typeName: 'Query',
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
};
