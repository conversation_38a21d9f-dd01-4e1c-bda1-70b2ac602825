Resources:
  customerProjectionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-customerProjectionLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:*:log-group:/aws/lambda/${self:provider.stackName}-customerProjectionHandler:*'
        - PolicyName: ${self:provider.stackName}-customerProjectionSqsPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 'sqs:SendMessage'
                Resource: ${self:custom.sisCqrsProjectionDLQArn}
        - PolicyName: ${self:provider.stackName}-projectionDBPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:   
              - Effect: Allow
                Action:
                  - dynamodb:BatchGetItem
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}'
              - Effect: Allow
                Action:
                  - dynamodb:DeleteItem
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}'
