service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-customer

plugins:
  - serverless-esbuild
  - serverless-dotenv-plugin
  - serverless-plugin-tracing
  - serverless-pseudo-parameters
  - serverless-plugin-resource-tagging
  - serverless-plugin-lambda-dead-letter
  - serverless-prune-plugin

useDotenv: true
variablesResolutionMode: ********
frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region}
  accountId: '#{AWS::AccountId}'
  vpc:
    securityGroupIds:
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg'
    subnetIds:
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01'
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02'
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03'
  stackName: ${self:service}
  serviceName: ${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}
  service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  timeout: ${env:LAMBDA_TIMEOUT_IN_SECONDS}
  dynamodbStackName: ${self:provider.serviceName}-dynamodb
  appsyncStackName: ${self:provider.service}-appsync
  deviceTableName: ${self:provider.dynamodbStackName}-${env:COMPONENT_TABLE}
  accessTokenGsi: ${env:ACCESS_TOKEN_GSI}
  siteGsi: ${env:SITE_GSI}
  entityGsi: ${env:ENTITY_GSI}
  entityCacheGsi: ${env:ENTITY_CACHE_GSI}
  dbsCqrsStackName: ${opt:stage}-dbs-cqrs
  cqrsStackName:
    dev: '${opt:stage}-dbs-cqrs'
    staging: '${opt:stage}-dbs-cqrs'
    prod: '${opt:stage}-dbs-cqrs'
    st: '${opt:stage}-dbs-customer-cqrs'
  dbsCqrsSqsArn: '${cf:${self:provider.cqrsStackName.${opt:stage}, "${self:provider.cqrsStackName.st}"}-iac-sqs.QueueARN}'
  dbsCqrsSqsUrl: '${cf:${self:provider.cqrsStackName.${opt:stage}, "${self:provider.cqrsStackName.st}"}-iac-sqs.QueueURL}'
  dbsCqrsEventBus: '${cf:${self:provider.cqrsStackName.${opt:stage}, "${self:provider.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionArn}'
  dbsCqrsProjectionDLQArn: '${cf:${self:provider.cqrsStackName.${opt:stage}, "${self:provider.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionDLQArn}'
  dbsCqrsCommandHandler: ${self:provider.dbsCqrsStackName}-commandHandlers-handler
  cacheTableName: ${self:provider.dynamodbStackName}-${env:SESSION_CACHE_TABLE}
  auth0Tenant: ${env:IDENTITY_AUTH0_TENANT}
  auth0ClientId: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/AUTH0_CLIENT_ID}
  auth0ClientSecret: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/AUTH0_CLIENT_SECRET}
  auth0IssuerUrl: ${env:OPENID_ISSUER_URL}
  appSyncApiId:
    Fn::ImportValue: !Sub '${self:provider.appsyncStackName}-graphQlApiId'
  bucketStackName: ${env:COMPONENT_NAME}-${env:PART_NAME}-iac-s3
  deploymentBucket:
    name: ${cf:${self:provider.bucketStackName}.deploymentBucket}
    maxPreviousDeploymentArtifacts: 100
    blockPublicAccess: true
  amsApiEndpoint: ${ssm:${opt:stage}-ams-engine-api-endpoint, 'http://localhost:3000'}
  amsApiEndpointVersion: ${env:AMS_API_ENDPOINT_VERSION}
  multiEntityEnabled: ${env:MULTI_ENTITY_ENABLED}

  environment:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    AUTH0_CLIENT_ID: ${self:provider.auth0ClientId}
    AUTH0_CLIENT_SECRET: ${self:provider.auth0ClientSecret}
    AWS_NODEJS_CONNECTION_REUSE_ENABLED: 1
    IS_RBAC_ENFORCED: ${env:IS_RBAC_ENFORCED}
    IS_RBAC_ENFORCE_ROLE: ${env:IS_RBAC_ENFORCE_ROLE}
    GLOBAL_ACCOUNT_ID: '${env:SYDNEY_ACCOUNT_ID}'
  tags:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    service: ${env:COMPONENT_NAME}-${env:PART_NAME}
    env: ${opt:stage}
  stackTags: ${self:provider.tags}

package:
  individually: true
  exclude:
    - node_modules/**

custom:
  esbuild:
    bundle: true
    keepNames: true
    plugins: esbuild_plugin.js
    exclude:
      - 'cache-manager'
      - 'class-transformer'
      - 'class-validator'
      - '@nestjs/microservices'
      - '@nestjs/websockets/socket-module'
      - '@nestjs/platform-express'
  prune:
    automatic: true
    includeLayers: true
    number: 5
  serviceName: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  appsyncDataSourceRoleArn: ${cf:${self:provider.appsyncStackName}.DataSourceLambdaRole}
  dbsCqrsProjectionDLQArn: ${cf:${env:STATIC_ENV_NAME}-dbs-cqrs-iac-eventBridge.EventBusProjectionDLQArn}
  remoteAccountId:
    ap-southeast-2: ${env:LONDON_ACCOUNT_ID}
    eu-west-2: ${env:SYDNEY_ACCOUNT_ID}


functions:
  - ${file(resources/dbs/customer/lambdas.yml)}

resources:
  - ${file(resources/dbs/customer/resolvers.yml)}
  - ${file(resources/dbs/customer/iam.yml)}
  - ${file(resources/dbs/customer/eventBridgeRule.yml)}
