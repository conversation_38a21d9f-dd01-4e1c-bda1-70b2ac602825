service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-customer

plugins:
  - serverless-esbuild
  - serverless-dotenv-plugin
  - serverless-plugin-tracing
  - serverless-pseudo-parameters
  - serverless-plugin-resource-tagging
  - serverless-plugin-lambda-dead-letter
  - serverless-prune-plugin

useDotenv: true
variablesResolutionMode: ********
frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region}
  accountId: '#{AWS::AccountId}'
  vpc:
    securityGroupIds:
      - '${ssm:${env:VPC_ENV_NAME}-vpc-shared-01-lambda-sg}'
    subnetIds:
      - '${ssm:${env:VPC_ENV_NAME}-vpc-shared-01-lambda-subnet01}'
      - '${ssm:${env:VPC_ENV_NAME}-vpc-shared-01-lambda-subnet02}'
      - '${ssm:${env:VPC_ENV_NAME}-vpc-shared-01-lambda-subnet03}'
  stackName: ${self:service}
  serviceName: ${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}
  service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  dynamodbStackName: ${self:provider.serviceName}-dynamodb
  entityTableName: ${self:provider.dynamodbStackName}-${env:COMPONENT_TABLE}
  sisCqrsStackName: ${opt:stage}-sis-cqrs
  bucketStackName: ${env:COMPONENT_NAME}-${env:PART_NAME}-iac-s3
  deploymentBucket:
    name: ${cf:${self:provider.bucketStackName}.deploymentBucket}
    maxPreviousDeploymentArtifacts: 100
    blockPublicAccess: true

  environment:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    AWS_NODEJS_CONNECTION_REUSE_ENABLED: 1
  tags:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    service: ${env:COMPONENT_NAME}-${env:PART_NAME}
    env: ${opt:stage}
  stackTags: ${self:provider.tags}

package:
  individually: true
  exclude:
    - node_modules/**

custom:
  esbuild:
    bundle: true
    keepNames: true
    plugins: esbuild_plugin.js
    exclude:
      - 'cache-manager'
      - 'class-transformer'
      - 'class-validator'
      - '@nestjs/microservices'
      - '@nestjs/websockets/socket-module'
      - '@nestjs/platform-express'
  prune:
    automatic: true
    includeLayers: true
    number: 5
  serviceName: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  sisCqrsCommandHandler: ${self:provider.sisCqrsStackName}-commandHandlers-handler
  componentStack:
    dev: '${opt:stage}-sis'
    staging: '${opt:stage}-sis'
    prod: '${opt:stage}-sis'
    st: '${opt:stage}-sis-customer'
  cqrsStackName: ${self:custom.componentStack.${opt:stage}, "${self:custom.componentStack.st}"}-cqrs
  sisCqrsEventBus: '${cf:${self:custom.cqrsStackName}-iac-eventBridge.EventBusProjectionArn}'
  sisCqrsProjectionDLQArn: '${cf:${self:custom.cqrsStackName}-iac-eventBridge.EventBusProjectionDLQArn}'
  remoteAccountId:
    ap-southeast-2: ${env:LONDON_ACCOUNT_ID}
    eu-west-2: ${env:SYDNEY_ACCOUNT_ID}


functions:
  - ${file(resources/sis/customer/lambdas.yml)}

resources:
  - ${file(resources/sis/customer/iam.yml)}
  - ${file(resources/sis/customer/eventBridgeRule.yml)}
  - ${file(resources/sis/customer/crossAccountPolicy.yml)}
