import { ApiAppServerlessStack, esbuildNestCommon, pluginsAppDefault, vpcImport } from '@npco/component-bff-serverless';

import { eventBridgeRule } from './resources/billingaccount/eventBridgeRule';
import { lambdas } from './resources/billingaccount/lambdas';
import { envConfig } from './resources/common';

const sls = new ApiAppServerlessStack('billing', envConfig, {
  plugins: pluginsAppDefault,
  custom: {
    ...envConfig.getDynamoDb(),
    ...envConfig.getAuth0(),
    ...envConfig.getAmsApi(),
    ...envConfig.getComponentCqrs(),
    ...envConfig.getAppsync(),
    vpcImport,
    dynamodbStackName: '${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-dynamodb',
    deviceTableName: '${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
    sessionCacheTableName: '${self:custom.dynamodbStackName}-${env:SESSION_CACHE_TABLE}',
    entityGsi: '${env:ENTITY_GSI}',
    entityCacheGsi: '${env:ENTITY_CACHE_GSI}',
    accessTokenGsi: '${env:ACCESS_TOKEN_GSI}',
    auth0Tenant: '${env:IDENTITY_AUTH0_TENANT}',
    dbsCqrsStackName: '${opt:stage}-dbs-cqrs',
    dbsCqrsEventBus: '${cf:${self:custom.dbsCqrsStackName}-iac-eventBridge.EventBusProjectionArn}',
    dbsCqrsCommandHandler: '${self:custom.dbsCqrsStackName}-commandHandlers-handler',
    cacheTableName: '${self:custom.dynamodbStackName}-${env:SESSION_CACHE_TABLE}',
    iamUserKey: '${ssm:/aws/reference/secretsmanager/${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}/IAM_USER_KEY}',
    iamUserSecret:
      '${ssm:/aws/reference/secretsmanager/${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}/IAM_USER_SECRET}',
    billingApiEndpoint: '${ssm:${env:STATIC_ENV_NAME}-ss-cqrs-api-endpoint}/${env:BILLING_API_ENDPOINT_VERSION}',
    cmsStackName: '${env:STATIC_ENV_NAME}-cms-cqrs-apiHandlers',
    esbuild: esbuildNestCommon,
    dbsCqrsProjectionDLQArn: '${cf:${opt:stage}-dbs-cqrs-iac-eventBridge.EventBusProjectionDLQArn}',
    permissionsTableReadRolePolicyArn: envConfig.permissionsTableReadRolePolicyArn,
    domicileLookupTableReadRolePolicyArn: envConfig.domicileLookupTableReadRolePolicyArn,
  },
  functions: lambdas,
  resources: {
    ...eventBridgeRule.Resources,
  },
  environment: {
    ...envConfig.dotenvConfig,
    AWS_NODEJS_CONNECTION_REUSE_ENABLED: 1,
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
    AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
    AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    AUTH0_TENANT: '${self:custom.auth0Tenant}',
  },
});
module.exports = sls.build();
