import {
  ApiAppServerlessStack,
  vpcImport,
  pluginsAppDefault,
  esbuildNestWithSharp,
} from '@npco/component-bff-serverless';
import { envConfig } from './resources/common';
import { lambdas } from './resources/assets/lambdas';
import { s3AccessLogs } from './resources/assets/s3AccessLogs';
import { s3DocumentUpload } from './resources/assets/s3DocumentUpload';
import { s3ReceiptLogo } from './resources/assets/s3ReceiptLogo';
import { s3TransactionDepositExport } from './resources/assets/s3TransactionDepositExport';
import { s3ReceiptDocument } from './resources/assets/s3ReceiptDocument';
import { s3Common } from './resources/assets/s3Common';
import { resolvers } from './resources/assets/resolvers';
import { s3CardLogo } from './resources/assets/s3CardLogo';

const sls = new ApiAppServerlessStack('assets', envConfig, {
  plugins: pluginsAppDefault,
  custom: {
    ...envConfig.getDynamoDb(),
    ...envConfig.getAppsync(),
    ...envConfig.getAmsApi(),
    ...envConfig.getAuth0(),
    vpcImport,
    cmsStackName: '${env:STATIC_ENV_NAME}-cms-cqrs-apiHandlers',
    entityTableName: '${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
    mpCqrsStackName: '${opt:stage}-mp-cqrs',
    mpCqrsCommandHandler: '${self:custom.mpCqrsStackName}-commandHandlers-handler',
    sessionCacheTableName: '${self:custom.dynamodbStackName}-${env:SESSION_CACHE_TABLE}',
    entityGsi: '${env:ENTITY_GSI}',
    shortIdGsi: '${env:SHORT_ID_GSI}',
    siteGsi: '${env:SITE_GSI}',
    typeGsi: '${env:TYPE_GSI}',
    entityCacheGsi: '${env:ENTITY_CACHE_GSI}',
    mpCqrsEventBus: '${cf:${self:custom.mpCqrsStackName}-iac-eventBridge.EventBusProjectionArn}',
    esbuild: esbuildNestWithSharp,
    cmsEndpoint: '${ssm:${env:STATIC_ENV_NAME}-bankingwrapper-engine-api-endpoint}/',
    primaryRegion: '${env:PRIMARY_REGION}',
    deployMultiRegion: '${env:DEPLOY_MULTI_REGION}',
    s3DeletedTopic: '${ssm:s3-sns-notification-arn}',
    receiptLogoBaseUrl:
      '${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/DASHBOARD_URL}/receipt/assets/',
    receiptLogoUploadBucketNameV2:
      '${self:provider.stackName}-logo-uploads-${self:provider.region}-${self:custom.accountId}',
    receiptLogoProcessedBucket: '${ssm:${env:STATIC_ENV_NAME}-s3-mp-logo-download}',
    receiptDocumentUploadBucketName:
      '${self:provider.stackName}-rcdoc-upld-${self:provider.region}-${self:custom.accountId}',
    receiptDocumentDownloadBucketName:
      '${self:provider.stackName}-rcdoc-dld-${self:provider.region}-${self:custom.accountId}',
    accessLogsBucketNameV2: '${self:provider.stackName}-s3-logs-${self:provider.region}-${self:custom.accountId}',
    allowedOrigins: '${env:LOGO_UPLOAD_BUCKET_ALLOWED_ORIGINS}',
    documentUploadBucketNameV2: '${self:provider.stackName}-doc-uplds-${self:provider.region}-${self:custom.accountId}',
    documentUploadBucketName: '${self:provider.stackName}-document-uploads-${self:custom.accountId}',
    documentEncryptionKeyArn: "${ssm:${env:STATIC_ENV_NAME}-document-encryption-key-arn, 'none'}",
    transactionDepositExportBucketName: '${self:provider.stackName}-txn-de-${opt:region}-${self:custom.accountId}',
    merchantPortalDocumentUploadBucketName:
      '${self:provider.stackName}-mpdoc-upld-${self:provider.region}-${self:custom.accountId}',
    screensaverLogoBaseUrl:
      '${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/DASHBOARD_URL}/screensaver/assets/',
    onReceiptLogoUploadedHandlerDlqArn: { 'Fn::GetAtt': ['onReceiptLogoUploadedHandlerDlq', 'Arn'] },
    commonBucketName: '${self:provider.stackName}-common-${self:provider.region}-${self:custom.accountId}',
    permissionsTableReadRolePolicyArn: envConfig.permissionsTableReadRolePolicyArn,
    domicileLookupTableReadRolePolicyArn: envConfig.domicileLookupTableReadRolePolicyArn,
    onCardLogoUploadedHandlerDlqArn: { 'Fn::GetAtt': ['onCardLogoUploadedHandlerDlq', 'Arn'] },
    cardLogoBaseUrl: '${ssm:/${self:custom.serviceName}/DASHBOARD_URL}/cardlogo/assets/',
    cardLogoUploadBucket: '${ssm:${env:VPC_ENV_NAME}-mp-api-assets-logo-uploads}',
    cardLogoProcessedBucket: '${ssm:${env:VPC_ENV_NAME}-mp-spa-iac-s3cloudfront-download-ssm}',
    cardLogoOnUploadS3Prefix: {
      dev: 'cardlogo/',
      prod: 'cardlogo/',
      staging: 'cardlogo/',
      systemTest: '${opt:stage}/cardlogo/',
    },
    iamUserKey: '${ssm:/aws/reference/secretsmanager/${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}/IAM_USER_KEY}',
    iamUserSecret: '${ssm:/aws/reference/secretsmanager/${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}/IAM_USER_SECRET}'
  },
  functions: lambdas,
  resources: {
    ...s3CardLogo.Resources,
    ...s3AccessLogs.Resources,
    ...s3DocumentUpload.Resources,
    ...s3ReceiptLogo.Resources,
    ...s3TransactionDepositExport.Resources,
    ...s3ReceiptDocument.Resources,
    ...s3Common.Resources,
    ...resolvers,
  },
  conditions: {
    ...s3CardLogo.Conditions,
    ...s3AccessLogs.Conditions,
    ...s3DocumentUpload.Conditions,
    ...s3ReceiptLogo.Conditions,
  },
  outputs: {
    ...s3TransactionDepositExport.Outputs,
  },
  environment: {
    ...envConfig.dotenvConfig,
    CQRS_COMMAND_HANDLER: '${self:custom.mpCqrsCommandHandler}',
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
    AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
    AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    AUTH0_TENANT: '${self:custom.auth0Tenant}',
  },
});
module.exports = sls.build();
