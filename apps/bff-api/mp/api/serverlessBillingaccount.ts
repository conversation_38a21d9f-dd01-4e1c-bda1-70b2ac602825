import { ApiAppServerlessStack, vpcImport, esbuildNestCommon, pluginsAppDefault } from '@npco/component-bff-serverless';
import { envConfig } from './resources/common';
import { lambdas } from './resources/billingaccount/lambdas';
import { eventBridgeRule } from './resources/billingaccount/eventBridgeRule';

const sls = new ApiAppServerlessStack('billing', envConfig, {
  plugins: pluginsAppDefault,
  custom: {
    vpcImport,
    ...envConfig.getDynamoDb(),
    ...envConfig.getAppsync(),
    ...envConfig.getAmsApi(),
    ...envConfig.getAuth0(),
    entityTableName: '${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
    sessionCacheTableName: '${self:custom.dynamodbStackName}-${env:SESSION_CACHE_TABLE}',
    entityGsi: '${env:ENTITY_GSI}',
    entityCacheGsi: '${env:ENTITY_CACHE_GSI}',
    auth0Tenant: '${env:IDENTITY_AUTH0_TENANT}',
    mpCqrsStackName: '${opt:stage}-mp-cqrs',
    mpCqrsEventBus: '${cf:${self:custom.mpCqrsStackName}-iac-eventBridge.EventBusProjectionArn}',
    mpCqrsCommandHandler: '${self:custom.mpCqrsStackName}-commandHandlers-handler',
    cmsStackName: '${env:STATIC_ENV_NAME}-cms-cqrs-apiHandlers',
    iamUserKey: envConfig.iamUserKey,
    iamUserSecret: envConfig.iamUserSecret,
    esbuild: esbuildNestCommon,
    mpCqrsProjectionDLQArn: '${cf:${opt:stage}-mp-cqrs-iac-eventBridge.EventBusProjectionDLQArn}',
    bankingWrapperEndpoint: '${ssm:${env:STATIC_ENV_NAME}-bankingwrapper-engine-api-endpoint}/',
    bankingWrapperEnabled: '${env:BANKING_WRAPPER_ENABLED}',
    env: {
      billingApiEndpoint: '${ssm:${env:STATIC_ENV_NAME}-ss-cqrs-api-endpoint}/${env:BILLING_API_ENDPOINT_VERSION}',
    },
    permissionsTableReadRolePolicyArn: envConfig.permissionsTableReadRolePolicyArn,
    domicileLookupTableReadRolePolicyArn: envConfig.domicileLookupTableReadRolePolicyArn,
  },
  functions: lambdas,
  resources: { ...eventBridgeRule.Resources },
  environment: {
    ...envConfig.dotenvConfig,
    CQRS_COMMAND_HANDLER: '${self:custom.mpCqrsCommandHandler}',
    SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
    AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
    AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    AUTH0_TENANT: '${self:custom.auth0Tenant}',
  },
});
module.exports = sls.build();
