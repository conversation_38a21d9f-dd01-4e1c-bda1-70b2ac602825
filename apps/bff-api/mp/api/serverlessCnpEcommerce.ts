import { ApiAppServerlessStack, vpcImport, esbuildNestCommon, pluginsAppDefault } from '@npco/component-bff-serverless';
import { envConfig } from './resources/common';
import { lambdas } from './resources/cnpEcommerce/lambdas';
import { eventBridgeRule } from './resources/cnpEcommerce/eventBridgeRule';

const sls = new ApiAppServerlessStack('cnpEcommerce', envConfig, {
  plugins: [...pluginsAppDefault, 'serverless-plugin-lambda-dead-letter'],
  custom: {
    vpcImport,
    ...envConfig.getDynamoDb(),
    ...envConfig.getAuth0(),
    entityTableName: '${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
    entityGsi: '${env:ENTITY_GSI}',
    typeGsi: '${env:TYPE_GSI}',
    mpCqrsStackName: '${opt:stage}-mp-cqrs',
    mpCqrsEventBus: '${cf:${self:custom.mpCqrsStackName}-iac-eventBridge.EventBusProjectionArn}',
    esbuild: esbuildNestCommon,
    mpCqrsProjectionDLQArn: '${cf:${opt:stage}-mp-cqrs-iac-eventBridge.EventBusProjectionDLQArn}',
  },
  functions: lambdas,
  resources: { ...eventBridgeRule.Resources },
  environment: {
    ...envConfig.dotenvConfig,
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
    AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
    AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    AUTH0_TENANT: '${self:custom.auth0Tenant}',
  },
});
module.exports = sls.build();
