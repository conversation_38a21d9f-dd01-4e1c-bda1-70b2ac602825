import { ApiAppServerlessStack, vpcImport, pluginsAppDefault, esbuildNestCommon } from '@npco/component-bff-serverless';
import { envConfig } from './resources/common';
import { lambdas } from './resources/posInterface/lambdas';
import { eventBridgeRule } from './resources/posInterface/eventBridgeRule';

const sls = new ApiAppServerlessStack('posInterface', envConfig, {
  plugins: pluginsAppDefault,
  custom: {
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAuth0(),
    vpcImport,
    entityTableName: '${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
    sessionCacheTableName: '${self:custom.dynamodbStackName}-${env:SESSION_CACHE_TABLE}',
    entityGsi: '${env:ENTITY_GSI}',
    typeGsi: '${env:TYPE_GSI}',
    entityCacheGsi: '${env:ENTITY_CACHE_GSI}',
    mpCqrsStackName: '${opt:stage}-mp-cqrs',
    mpCqrsEventBus: '${cf:${self:custom.mpCqrsStackName}-iac-eventBridge.EventBusProjectionArn}',
    mpCqrsCommandHandler: '${self:custom.mpCqrsStackName}-commandHandlers-handler',
    esbuild: esbuildNestCommon,
    mpCqrsProjectionDLQArn: '${cf:${opt:stage}-mp-cqrs-iac-eventBridge.EventBusProjectionDLQArn}',
    oraclePosCertTrustStoreBucket:
      '${env:STATIC_ENV_NAME}-oraclepos-${self:provider.region}-${self:custom.accountId}-truststore',
    auth0Tenant: '${env:IDENTITY_AUTH0_TENANT}',
  },
  functions: lambdas,
  resources: {
    ...eventBridgeRule.Resources,
  },
  environment: {
    ...envConfig.dotenvConfig,

    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
    AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
    AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    AUTH0_TENANT: '${self:custom.auth0Tenant}',
  },
});
module.exports = sls.build();
