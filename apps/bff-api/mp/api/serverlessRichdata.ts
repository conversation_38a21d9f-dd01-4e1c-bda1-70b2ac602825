import { ApiAppServerlessStack, vpcImport, pluginsAppDefault, esbuildNestCommon } from '@npco/component-bff-serverless';
import { envConfig } from './resources/common';
import { lambdas } from './resources/richdata/lambdas';

const sls = new ApiAppServerlessStack('richdata', envConfig, {
  plugins: pluginsAppDefault,
  custom: {
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAuth0(),
    vpcImport,
    entityTableName: '${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
    sessionCacheTableName: '${self:custom.dynamodbStackName}-${env:SESSION_CACHE_TABLE}',
    merchantTableName: '${self:custom.dynamodbStackName}-${env:MERCHANT_TABLE}',
    entityGsi: '${env:ENTITY_GSI}',
    entityCacheGsi: '${env:ENTITY_CACHE_GSI}',
    auth0Tenant: '${env:IDENTITY_AUTH0_TENANT}',
    modelSerialGsi: '${env:ENTITY_MODELSERIAL_GSI}',
    siteGsi: '${env:SITE_GSI}',
    auth0ClientSecret: '${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/AUTH0_CLIENT_SECRET}',
    dcaTransactionLookupSqsQueueArn:
      'arn:aws:sqs:${self:provider.region}:${self:custom.accountId}:${self:custom.service}-banking-sqs-dcaTransactionLookup',
    dcaTransactionLookupSqsQueueUrl:
      'https://sqs.${self:provider.region}.amazonaws.com/${self:custom.accountId}/${self:custom.service}-banking-sqs-dcaTransactionLookup',
    esbuild: esbuildNestCommon,
    permissionsTableReadRolePolicyArn: envConfig.permissionsTableReadRolePolicyArn,
    domicileLookupTableReadRolePolicyArn: envConfig.domicileLookupTableReadRolePolicyArn,
    merchantTableQueryRolePolicyArn: envConfig.merchantTableQueryRolePolicyArn,
    env: {
      streamArn: '${param:dbStreamArn, "${cf:${self:custom.service}-dynamodb.MerchantTableStreamArn, \'\'}"}',
    },
  },
  functions: lambdas,
  environment: {
    ...envConfig.dotenvConfig,
    IS_RBAC_ENFORCED: '${env:IS_RBAC_ENFORCED}',
    IS_RBAC_ENFORCE_ROLE: '${env:IS_RBAC_ENFORCE_ROLE}',
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
    AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
    AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    AUTH0_TENANT: '${self:custom.auth0Tenant}',
  },
});
module.exports = sls.build();
