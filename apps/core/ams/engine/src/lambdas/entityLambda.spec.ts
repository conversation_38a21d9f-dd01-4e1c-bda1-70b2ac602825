import './testcases/globalMocks';
import { Timezone } from '@npco/bff-common/dist/utils/timezoneUtils';
import { DomicileLookupDb } from '@npco/component-bff-core/dist/domicile';
import { LambdaService } from '@npco/component-bff-core/dist/lambda';
import { AwsRegions, Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import * as Logger from '@npco/component-bff-core/dist/utils/logger';
import { info } from '@npco/component-bff-core/dist/utils/logger';
import { ContactType } from '@npco/component-dto-addressbook/dist/types';
import {
  AddressState,
  AmexAcquisitionStatus,
  EntityType,
  ISO4217,
  KYCScreeningResult,
  MutationAttributionPlatform,
  MutationAttributionTokenGrant,
  MutationAttributionUserRole,
  PaymentType,
  ScreeningRequestedType,
  ScreeningStatus,
  StandInField,
  StandInOperation,
} from '@npco/component-dto-core';
import type { DocumentVerificationDetails } from '@npco/component-dto-customer';
import { CustomerDocumentVerificationResult, CustomerMedicareCardColours } from '@npco/component-dto-customer';
import type {
  EntityAmexMerchantSubmissionCompletedDto,
  EntityCreateRequestedEventDto,
  EntityDailyLimits,
  EntityFullSearchCompletedEventDto,
  EntityMetricUpdatedEventDto,
  EntityOnboardingStatusUpdatedEventDto,
  EntityScreeningCompletedEventDto,
  EntityUpdatedEventDto,
} from '@npco/component-dto-entity';
import { OnboardingStatus, RiskRating, RiskReviewResult, RiskReviewStatus } from '@npco/component-dto-entity';
import { SiteType, TenderType } from '@npco/component-dto-site';

import type { INestApplicationContext } from '@nestjs/common';
import 'jest-json';
import * as typeorm from 'typeorm';
import { QueryFailedError } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import { EnvironmentService } from '../config';
import type { Customer, Device, Entity } from '../entities';
import { Status } from '../entities';
import { BadRequestError, ServerError } from '../services/base/error';
import { ContactService } from '../services/contact/contactService';
import { DeviceService } from '../services/device/deviceService';
import { EntityService } from '../services/entity/entityService';
import { GlobalConfigService } from '../services/globalConfig/globalConfigService';
import { ReferralService } from '../services/referral/referralService';
import { SiteService } from '../services/site/siteService';
import * as util from '../services/util';

import {
  addReferredByHandler,
  adminUpdateEntityHandler,
  amexMerchantOnboardingCronJobHandler,
  attachEntityDomicileAndCurrencyHandler,
  createEntityHandler,
  entityEventParams,
  entityGetAllCustomersHandler,
  entityIdParams,
  entityPathParams,
  finalisedEntitiesExistWithAbnHandler,
  finaliseEntityOnboardingHandler,
  getEntityDailyLimitConfigHandler,
  getEntityHandler,
  initializeEntityCanAcquireAmexFlagHandler,
  selectDepositAccountHandler,
  triggerAmexOnBoardingEventHandler,
  updateEntityByHubspotIdHandler,
  updateEntityDailyLimitHandler,
  updateEntityHandler,
  updatePaymentSettingsHandler,
  updatePrimaryAccountHolderHandler,
  updateStandInRulesHandler,
} from './entityLambda';
import { projectionDomainEventHandler } from './projectionLambda';
import { geofencingJSON } from './testcases/constants';
import { initiateNestModule } from './testcases/mockNestModule';
import {
  assignDeviceToSite,
  closeDatabaseConnections,
  createBankAccount,
  createCustomer,
  createCustomerRequestedDto,
  createDevice,
  createDeviceDto,
  createEntity,
  createEntityCreatedDto,
  createEntityUpdatedDto,
  createSite,
  createSiteDto,
  decodeBase64,
  defaultDailyLimits,
  defaultDevicePaymentSettings,
  defaultPaymentLimits,
  defaultPaymentSettings,
  defaultStandInRules,
  findMockLambdaPayload,
  getEntity,
  getMockLambdaPayload,
  getSurchargeTaxSettingsDefault,
  lcr,
  mockDomicile,
  mockLambdaInvoke,
  updateEntity,
  updateEntityOnboardingStatusWithoutRestriction,
} from './testcases/testUtils';

jest.mock('../services/entity/cbs/cbsApi', () => {
  return {
    CbsApi: jest.fn(() => ({
      post: jest.fn().mockResolvedValue({ id: 'dc-id', accountName: 'test' }),
    })),
  };
});

jest.mock('../services/entity/ms/msApi', () => {
  return {
    MsApi: jest.fn(() => ({
      post: jest.fn().mockResolvedValue({ status: 200, data: { statusCode: 200, body: 'success' } }),
    })),
  };
});

jest.mock('../services/entity/pgsEntity/pgsEntityApi', () => {
  return {
    PgsEntityApi: jest.fn(() => ({
      patch: jest.fn().mockResolvedValue({ status: 200, data: { statusCode: 200, body: 'success' } }),
    })),
  };
});

jest.mock('typeorm', () => {
  const originalModule = jest.requireActual('typeorm');
  return {
    __esModule: true, // Use it when dealing with esModules
    ...originalModule,
  };
});

jest.mock('@npco/component-bff-core/dist/utils/logger', () => {
  const originalModule = jest.requireActual('@npco/component-bff-core/dist/utils/logger');
  return {
    __esModule: true, // Use it when dealing with esModules
    ...originalModule,
    info: jest.fn(),
  };
});

jest.mock('@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient');

const defaultAccountStatusDto = {
  hadForcedRefund: false,
  hasChargeback: false,
  hadDirectDebitFailure: false,
  hasDirectDebitRequest: false,
  canAcquireCnp: true,
  canAcquireMobile: true,
  canAcquireMoto: true,
  canAcquire: true,
  canAcquireVt: true,
  canCreateAccount: true,
  canCreateCard: true,
  canPayByCard: true,
  canRefund: true,
  canSettle: true,
  canUpdateSettlementAccount: true,
  canStandIn: true,
  canTransferIn: true,
  canTransferOut: true,
  canAcquireAmex: true,
};

const defaultAccountStatus = { ...defaultAccountStatusDto, hasAmexPreviousCancelled: false };

const defaultOutstandingTransactionRequirementConfig = {
  note: true,
  attachments: true,
  category: true,
  accountingCategory: true,
};

const defaultDeviceSettingsUpdatePayload = {
  caid: expect.any(String),
  entitySettings: {
    canAcquire: true,
    canAcquireAmex: true,
    canAcquireCnp: true,
    canAcquireMobile: true,
    canAcquireMoto: true,
    canAcquireVt: true,
    canCreateAccount: true,
    canCreateCard: true,
    canPayByCard: true,
    canRefund: true,
    canSettle: true,
    canStandIn: true,
    canTransferIn: true,
    canTransferOut: true,
    canUpdateSettlementAccount: true,
    hadDirectDebitFailure: false,
    hadForcedRefund: false,
    hasAmexPreviousCancelled: false,
    hasChargeback: false,
    hasDirectDebitRequest: false,
    domicile: 'AUS',
    currency: 'AUD',
  },
  geofencing: expect.any(String),
  mcc: '',
  paymentLimits: [
    { maximum: '5000000', minimum: '100', paymentType: 'CNP' },
    { maximum: '5000000', minimum: '100', paymentType: 'CP' },
    { maximum: '2500000', minimum: '100', paymentType: 'MOTO' },
    { maximum: '2500000', minimum: '100', paymentType: 'CPOC' },
  ],
  standInRules: [{ field: 'transaction_amount', operation: 'below', value: '5999999' }],
};

const createMockRegisteringIndividual = async (entityUuid: string) => {
  const customer = createCustomerRequestedDto();
  customer.entityUuid = entityUuid;
  customer.registeringIndividual = true;
  await createCustomer(customer);
  return customer;
};

describe('entity lambda test suite', () => {
  const context: any = {};
  let appContext: INestApplicationContext;
  let lambdaService: any;
  let envService: EnvironmentService;

  beforeAll(async () => {
    appContext = await initiateNestModule();
  });

  beforeEach(async () => {
    lambdaService = appContext.get(LambdaService);
    envService = appContext.get(EnvironmentService);
    envService.region = AwsRegions.SYDNEY;
    mockDomicile();
    mockLambdaInvoke(lambdaService);
  });

  afterAll(async () => {
    await closeDatabaseConnections();
  });

  it('should get params', () => {
    expect(entityIdParams.getAggregateId({ id: 'entityId' })).toEqual('entityId');
    expect(entityEventParams.getAggregateId({ detail: { entityUuid: 'entityId' } })).toEqual('entityId');
    expect(entityPathParams.getAggregateId({ path: { id: 'entityId' } })).toEqual('entityId');
  });

  it('should throw error if querying a non eiting customer', async () => {
    await new Promise<void>(async (done) => {
      getEntityHandler({ id: uuidv4() }, context, async (error) => {
        expect(error).toBeDefined();
        done();
      });
    });
  });

  describe('should be able to handle create entity event', () => {
    it('without manualEntry parameter', async () => {
      await new Promise<void>(async (done) => {
        const entity: EntityCreateRequestedEventDto = createEntityCreatedDto();
        const customer = await createMockRegisteringIndividual(entity.entityUuid);
        lambdaService.lambda.send.mockClear();
        createEntityHandler(
          {
            detail: entity,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const one = await entityService.findOne(entity.entityUuid);
            expect(one).toBeDefined();
            expect(one?.entityUuid).toBe(entity.entityUuid);
            expect(one?.name).toBe(entity.name);
            expect(one?.acn).toBe(entity.acn);
            expect(one?.abn).toBe(entity.abn);
            expect(one?.type).toBe(entity.type);
            expect(one?.manualEntry).toBe(false);
            expect(one?.createdSourceIp).toBe(entity.sourceIp);
            expect(one?.createdTime).toBeDefined();
            expect(one?.screening.status).toBe(ScreeningStatus.REQUIRED);
            expect(one?.caid).toBeDefined();
            expect(one?.geofencing).toEqual(geofencingJSON);
            expect(one?.caid).toHaveLength(15);
            expect(one?.accountStatus).toEqual(defaultAccountStatus);
            expect(one?.standInRules).toEqual(defaultStandInRules);
            expect(one?.paymentLimits).toEqual(defaultPaymentLimits);
            expect(one?.shortId).toBeDefined();
            expect(one?.dailyLimits).toEqual(defaultDailyLimits);
            expect(one?.hasEverOnboarded).toEqual(false);
            expect(one?.outstandingTransactionRequirementConfig).toEqual(
              defaultOutstandingTransactionRequirementConfig,
            );
            expect(one?.countryOfOrigin).toEqual('AUS');
            expect(one?.domicile).toEqual(Domicile.AU);
            expect(one?.currency).toEqual('AUD');
            expect(lambdaService.lambda.send).toBeCalledTimes(6);
            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              uri: 'Entity.Created',
              dto: {
                entityUuid: entity.entityUuid,
                name: entity.name,
                acn: entity.acn,
                abn: entity.abn,
                type: entity.type,
                caid: one?.caid,
                onboardingStatus: OnboardingStatus.ENTITY_ESTABLISHED,
                geofencing: geofencingJSON,
                accountStatus: defaultAccountStatusDto,
                status: Status.ACTIVE,
                riskRating: RiskRating.LOW_NEW,
                countryOfOrigin: 'AUS',
                screening: {
                  status: ScreeningStatus.REQUIRED,
                },
                sourceIp: entity.sourceIp,
                manualEntry: false,
                standInRules: one?.standInRules,
                shortId: one?.shortId,
                paymentSettings: defaultPaymentSettings,
                domicile: Domicile.AU,
                currency: 'AUD',
                outstandingTransactionRequirementConfig: {
                  note: true,
                  attachments: true,
                  category: true,
                  accountingCategory: true,
                },
                primaryAccountHolder: customer.customerUuid,
              },
            });
            expect(getMockLambdaPayload(lambdaService, 2).uri).toEqual('Site.Created');
            expect(getMockLambdaPayload(lambdaService, 3)).toEqual({
              uri: 'Entity.Updated',
              dto: {
                entityUuid: entity.entityUuid,
                dailyLimits: defaultDailyLimits,
              },
            });
            expect(getMockLambdaPayload(lambdaService, 4)).toEqual({
              uri: 'Entity.PaymentSettingsUpdated',
              dto: {
                entityUuid: entity.entityUuid,
                paymentLimits: defaultPaymentLimits,
              },
            });
            expect(getMockLambdaPayload(lambdaService, 1)).toEqual({
              domicile: Domicile.AU,
              entityUuid: entity.entityUuid,
              requestPath: expect.any(String),
              ...defaultDeviceSettingsUpdatePayload,
            });
            done();
          },
        );
      });
    });

    it('should create entity with default zeller site', async () => {
      await new Promise<void>(async (done) => {
        const entity: EntityCreateRequestedEventDto = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        lambdaService.lambda.send.mockClear();
        createEntityHandler(
          {
            detail: entity,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const one = await entityService.findOne(entity.entityUuid);
            expect(one).toBeDefined();
            expect(one?.entityUuid).toBe(entity.entityUuid);
            expect(one?.name).toBe(entity.name);
            const siteService = app.find(SiteService);
            const site = await siteService.findOneWithOption({
              entityUuid: entity.entityUuid,
              type: SiteType.CNP_ZELLER_INVOICE,
            });
            expect(site).toBeDefined();
            expect(site?.entityUuid).toBe(entity.entityUuid);
            expect(site?.name).toBe('Zeller Invoice');
            expect(site?.type).toBe(SiteType.CNP_ZELLER_INVOICE);
            expect(site?.invoice).toEqual({
              bccEmail: '',
              businessAddress: '',
              discountsEnabled: false,
              itemsApplyGst: false,
              itemsGstPriceInclusive: false,
              pdfIncludesAddress: false,
              reminderOnDue: false,
              remindersDaysAfterDue: [],
              remindersDaysBeforeDue: [],
              sendBccCopy: false,
            });
            expect(site.receipt.name).toEqual('');
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(6);
            expect(getMockLambdaPayload(lambdaService, 0).uri).toEqual('Entity.Created');
            expect(getMockLambdaPayload(lambdaService, 2).uri).toEqual('Site.Created');
            expect(getMockLambdaPayload(lambdaService, 3).uri).toEqual('Entity.Updated');
            expect(getMockLambdaPayload(lambdaService, 4).uri).toEqual('Entity.PaymentSettingsUpdated');
            expect(getMockLambdaPayload(lambdaService, 2)).toEqual({
              uri: 'Site.Created',
              dto: {
                entityUuid: entity.entityUuid,
                name: 'Zeller Invoice',
                siteUuid: site?.siteUuid,
                currency: 'AUD',
                domicile: Domicile.AU,
                receipt: {
                  merchantCopy: false,
                  name: '',
                  number: expect.any(String),
                  printDeclinedTransaction: false,
                  printLogo: true,
                  printSocials: false,
                },
                reportingDayStartHour: 0,
                schemesCnp: [
                  {
                    name: 'VISA',
                  },
                  {
                    name: 'MC',
                  },
                  {
                    name: 'AMEX',
                  },
                  {
                    name: 'JCB',
                  },
                  {
                    name: 'CUP',
                  },
                  {
                    name: 'DINERS',
                  },
                  {
                    name: 'OTHER',
                  },
                ],
                surchargesTaxes: getSurchargeTaxSettingsDefault(),
                type: SiteType.CNP_ZELLER_INVOICE,
                vtEnabled: false,
                invoice: {
                  bccEmail: '',
                  businessAddress: '',
                  discountsEnabled: false,
                  itemsApplyGst: false,
                  itemsGstPriceInclusive: false,
                  pdfIncludesAddress: false,
                  reminderOnDue: false,
                  remindersDaysAfterDue: [],
                  remindersDaysBeforeDue: [],
                  sendBccCopy: false,
                },
                timezone: Timezone.AUSTRALIA_MELBOURNE,
              },
            });
            expect(getMockLambdaPayload(lambdaService, 1)).toEqual({
              domicile: Domicile.AU,
              entityUuid: entity.entityUuid,
              requestPath: expect.any(String),
              ...defaultDeviceSettingsUpdatePayload,
            });
            done();
          },
        );
      });
    });

    it.each([Domicile.AU, Domicile.GB])(
      'should match globalConfig Entity domicile, currency and regionalOptions',
      async (domicileToUse) => {
        envService.region = domicileToUse === Domicile.AU ? AwsRegions.SYDNEY : AwsRegions.LONDON;
        mockDomicile(domicileToUse);
        await new Promise<void>(async (done) => {
          const entity: EntityCreateRequestedEventDto = createEntityCreatedDto();
          await createMockRegisteringIndividual(entity.entityUuid);
          const globalConfigService = new GlobalConfigService();
          const { domicile, currency } = globalConfigService.getValue(
            'Entity',
            'international',
            await globalConfigService.findAll('Entity'),
          );

          // regionalOptions.surchargeAllowed should return false in GBR (returns true in AUS)
          const regionalOptions = globalConfigService.getValue(
            'Entity',
            'regionalOptions',
            await globalConfigService.findAll('Entity'),
          );

          lambdaService.lambda.send.mockClear();
          createEntityHandler(
            {
              detail: entity,
            },
            context,
            async () => {
              const { app } = context;
              const entityService = app.find(EntityService);
              const one = await entityService.findOne(entity.entityUuid);
              expect(one).toBeDefined();
              expect(one?.entityUuid).toBe(entity.entityUuid);
              expect(one?.name).toBe(entity.name);
              expect(one?.domicile).toEqual(domicile);
              expect(one?.currency).toEqual(currency);
              console.log(`regionalOptions value when domicile: ${domicileToUse} ->`, one?.regionalOptions);
              expect(one?.regionalOptions).toEqual(regionalOptions);
              done();
            },
          );
        });
      },
    );

    describe('entity attached devices', () => {
      it('should update existing devices with entity info on create entity', async () => {
        await new Promise<void>(async (done) => {
          const entity = createEntityCreatedDto();
          await createMockRegisteringIndividual(entity.entityUuid);
          const device1 = createDeviceDto(entity.entityUuid);
          const device2 = createDeviceDto(entity.entityUuid);
          const device3 = createDeviceDto(entity.entityUuid);
          const device4 = createDeviceDto(uuidv4());
          device2.terminalConfig = '{}';
          (device3 as any).status = 'DELETED';
          await createDevice(device1);
          await createDevice(device2);
          await createDevice(device3);
          await createDevice(device4);
          mockLambdaInvoke(lambdaService);
          createEntityHandler(
            {
              detail: { ...entity, name: null },
            },
            context,
            async () => {
              expect(lambdaService.lambda.send).toBeCalledTimes(6);
              expect(findMockLambdaPayload(lambdaService, (event) => event.entityUuid === device1.entityUuid)).toEqual({
                domicile: Domicile.AU,
                entityUuid: entity.entityUuid,
                requestPath: expect.any(String),
                ...defaultDeviceSettingsUpdatePayload,
              });
              done();
            },
          );
        });
      });
      const siteDeviceUpdateDefaults = {
        features: {
          declineSoundEnabled: true,
          restrictReportAccessEnabled: true,
          splitPaymentEnabled: false,
        },
        moto: {
          defaultEntryMethod: false,
          enabled: true,
          requiresPin: false,
        },
        receipt: {
          merchantCopy: false,
          printDeclinedTransaction: false,
          printLogo: true,
          printSocials: false,
        },
        schemes: [
          {
            name: 'VISA',
          },
          {
            name: 'MC',
          },
          {
            name: 'EFTPOS',
          },
          {
            name: 'AMEX',
          },
          {
            name: 'JCB',
          },
        ],
        schemesMoto: [
          {
            name: 'VISA',
          },
          {
            name: 'MC',
          },
          {
            name: 'AMEX',
          },
          {
            name: 'JCB',
          },
        ],

        surchargesTaxes: getSurchargeTaxSettingsDefault(),
        tipping: {
          customTipAllowed: true,
          enabled: false,
          tipPercent1: 500,
          tipPercent2: 1000,
          tipPercent3: 1500,
        },
      };

      it('should update existing devices with entity info on update entity', async () => {
        const entity = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        const device1 = createDeviceDto(entity.entityUuid);
        const device2 = createDeviceDto(entity.entityUuid);
        const site = createSiteDto(entity.entityUuid);
        await createSite(site);
        await createDevice(device1);
        await createDevice(device2);
        await assignDeviceToSite(device1.deviceUuid, site.siteUuid);
        await assignDeviceToSite(device2.deviceUuid, site.siteUuid);
        await createEntity(entity);
        const entityUpdate = createEntityUpdatedDto();
        entityUpdate.abn = uuidv4();
        entityUpdate.tradingName = uuidv4();
        entityUpdate.entityUuid = entity.entityUuid;
        mockLambdaInvoke(lambdaService);
        await new Promise((resolve) => {
          updateEntityHandler(
            {
              detail: { ...entityUpdate },
              id: entity.entityUuid,
            },
            context,
            async () => {
              const { app } = context;
              const deviceService = app.find(DeviceService);
              const d1: Device = await deviceService.findOneWithOption({
                where: { deviceUuid: device1.deviceUuid, domicile: Domicile.AU },
              });
              expect(JSON.parse(d1.terminalConfig as string)).toEqual({
                caid: expect.any(String),
                catid: expect.any(String),
                mcc: '',
                lcrBinList: lcr.lcrBinList,
                lcrAmount: lcr.lcrAmount,
                lcrEnabled: lcr.lcrEnabled,
                countryCode: '0036',
                currencyCode: '0036',
                currencyExponent: '02',
              });
              expect(d1.standInRules).toMatchObject(device1.standInRules as any);
              expect(d1.paymentSettings).toEqual(defaultDevicePaymentSettings);
              const d2: Device = await deviceService.findOneWithOption({
                where: { deviceUuid: device2.deviceUuid, domicile: Domicile.AU },
              });
              expect(JSON.parse(d2.terminalConfig as string)).toEqual({
                caid: expect.any(String),
                catid: expect.any(String),
                mcc: '',
                lcrBinList: lcr.lcrBinList,
                lcrAmount: lcr.lcrAmount,
                lcrEnabled: lcr.lcrEnabled,
                countryCode: '0036',
                currencyCode: '0036',
                currencyExponent: '02',
              });
              expect(d2.standInRules).toEqual(device2.standInRules as any);
              expect(d2.paymentSettings).toEqual(defaultDevicePaymentSettings);
              expect(lambdaService.lambda.send).toHaveBeenCalledTimes(6);
              expect(getMockLambdaPayload(lambdaService, 0).uri).toEqual('Entity.Updated');
              expect(getMockLambdaPayload(lambdaService, 1)).toEqual({
                uri: 'Entity.NameUpdated',
                dto: {
                  entityUuid: entityUpdate.entityUuid,
                  name: entityUpdate.name,
                },
              });
              expect(getMockLambdaPayload(lambdaService, 3)).toEqual({
                entityUuid: entity.entityUuid,
                domicile: Domicile.AU,
                requestPath: expect.any(String),
                ...defaultDeviceSettingsUpdatePayload,
                entitySettings: {
                  canAcquire: false,
                  canAcquireMobile: false,
                  canAcquireMoto: false,
                  canRefund: false,
                  canStandIn: false,
                  domicile: 'AUS',
                  currency: 'AUD',
                },
                mcc: '8299',
              });
              const zellerInvoiceSettings = getMockLambdaPayload(lambdaService, 4);
              expect(zellerInvoiceSettings).toEqual({
                dto: {
                  entityUuid: entity.entityUuid,
                  receipt: {
                    merchantCopy: false,
                    name: entityUpdate.tradingName,
                    number: entityUpdate.abn,
                    printDeclinedTransaction: false,
                    printLogo: true,
                    printSocials: false,
                  },
                  siteUuid: expect.any(String),
                  type: 'CNP_ZELLER_INVOICE',
                },
                uri: 'Site.Updated',
              });
              const deviceSite = {
                name: site.name,
                pin: site.pin,
                refundPin: site.refundPin,
                refundPinType: site.refundPinType,
                refundRequiresPin: false,
                discountPin: site.discountPin,
                discountPinType: site.discountPinType,
                discountRequiresPin: false,
                screensaver: null,
                siteUuid: site.siteUuid,
                type: site.type,
                posSettings: {
                  zellerPosSettings: {
                    tenderTypes: [TenderType.CARD],
                  },
                },
                timezone: Timezone.AUSTRALIA_MELBOURNE,
              };
              expect(getMockLambdaPayload(lambdaService, 5)).toEqual(
                expect.arrayContaining([
                  {
                    aggregateId: site.siteUuid,
                    event: {
                      entityUuid: entityUpdate.entityUuid,
                      receipt: {
                        merchantCopy: false,
                        name: expect.any(String), // custom site name
                        number: entityUpdate.abn,
                        printDeclinedTransaction: false,
                        printLogo: true,
                        printSocials: false,
                      },
                      siteUuid: site.siteUuid,
                    },
                    uri: 'Site.Updated',
                  },
                  {
                    aggregateId: device1.deviceUuid,
                    event: {
                      deviceUuid: device1.deviceUuid,
                      entityUuid: entityUpdate.entityUuid,
                      ...siteDeviceUpdateDefaults,
                      receipt: {
                        ...siteDeviceUpdateDefaults.receipt,
                        name: site.businessName,
                        number: entityUpdate.abn,
                      },
                      site: deviceSite,
                      siteUuid: site.siteUuid,
                    },
                    uri: 'Device.Updated',
                  },
                  {
                    aggregateId: device2.deviceUuid,
                    event: {
                      deviceUuid: device2.deviceUuid,
                      entityUuid: entityUpdate.entityUuid,
                      ...siteDeviceUpdateDefaults,
                      receipt: {
                        ...siteDeviceUpdateDefaults.receipt,
                        name: site.businessName,
                        number: entityUpdate.abn,
                      },
                      site: deviceSite,
                      siteUuid: site.siteUuid,
                    },
                    uri: 'Device.Updated',
                  },
                ]),
              );
              resolve(null);
            },
          );
        });
      });

      it('should update existing devices with entity info', async () => {
        const entity = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        const device = createDeviceDto(entity.entityUuid);
        const site = createSiteDto(entity.entityUuid);
        await createSite(site);
        await createDevice(device);
        await assignDeviceToSite(device.deviceUuid, site.siteUuid);
        await createEntity(entity);
        const entityUpdate = createEntityUpdatedDto();
        entityUpdate.abn = uuidv4();
        entityUpdate.tradingName = uuidv4();
        entityUpdate.entityUuid = entity.entityUuid;
        mockLambdaInvoke(lambdaService);
        await new Promise((resolve) => {
          updateEntityHandler(
            {
              detail: { ...entityUpdate },
              id: entity.entityUuid,
            },
            context,
            async () => {
              const { app } = context;
              const deviceService = app.find(DeviceService);
              const dev: Device = await deviceService.findOneWithOption({
                where: { deviceUuid: device.deviceUuid, domicile: Domicile.AU },
              });
              expect(JSON.parse(dev.terminalConfig as string)).toEqual({
                caid: expect.any(String),
                catid: expect.any(String),
                mcc: '',
                lcrBinList: lcr.lcrBinList,
                lcrAmount: lcr.lcrAmount,
                lcrEnabled: lcr.lcrEnabled,
                countryCode: '0036',
                currencyCode: '0036',
                currencyExponent: '02',
              });
              expect(lambdaService.lambda.send).toHaveBeenCalledTimes(6);
              expect(getMockLambdaPayload(lambdaService, 0).uri).toEqual('Entity.Updated');
              expect(getMockLambdaPayload(lambdaService, 1).uri).toEqual('Entity.NameUpdated');
              expect(getMockLambdaPayload(lambdaService, 3)).toEqual({
                caid: expect.any(String),
                requestPath: expect.any(String),
                domicile: Domicile.AU,
                entitySettings: {
                  canAcquire: false,
                  canAcquireMobile: false,
                  canAcquireMoto: false,
                  canRefund: false,
                  canStandIn: false,
                  domicile: 'AUS',
                  currency: 'AUD',
                },
                entityUuid: entity.entityUuid,
                geofencing: expect.any(String),
                mcc: '8299',
                paymentLimits: [
                  { maximum: '5000000', minimum: '100', paymentType: 'CNP' },
                  { maximum: '5000000', minimum: '100', paymentType: 'CP' },
                  { maximum: '2500000', minimum: '100', paymentType: 'MOTO' },
                  { maximum: '2500000', minimum: '100', paymentType: 'CPOC' },
                ],
                standInRules: [{ field: 'transaction_amount', operation: 'below', value: '5999999' }],
              });
              const zellerInvoiceSettings = getMockLambdaPayload(lambdaService, 4);
              expect(zellerInvoiceSettings).toEqual({
                dto: {
                  entityUuid: entity.entityUuid,
                  receipt: {
                    merchantCopy: false,
                    name: entityUpdate.tradingName,
                    number: entityUpdate.abn, // old abn
                    printDeclinedTransaction: false,
                    printLogo: true,
                    printSocials: false,
                  },
                  siteUuid: expect.any(String),
                  type: 'CNP_ZELLER_INVOICE',
                },
                uri: 'Site.Updated',
              });
              expect(getMockLambdaPayload(lambdaService, 5)).toEqual(
                expect.arrayContaining([
                  {
                    aggregateId: site.siteUuid,
                    event: {
                      entityUuid: entity.entityUuid,
                      receipt: {
                        merchantCopy: false,
                        name: expect.any(String), // custom site name
                        number: entityUpdate.abn,
                        printDeclinedTransaction: false,
                        printLogo: true,
                        printSocials: false,
                      },
                      siteUuid: site.siteUuid,
                    },
                    uri: 'Site.Updated',
                  },
                  {
                    aggregateId: device.deviceUuid,
                    event: {
                      deviceUuid: device.deviceUuid,
                      entityUuid: entity.entityUuid,
                      ...siteDeviceUpdateDefaults,
                      receipt: {
                        ...siteDeviceUpdateDefaults.receipt,
                        name: site.businessName,
                        number: entityUpdate.abn,
                      },
                      site: {
                        name: site.name,
                        pin: site.pin,
                        refundPin: site.refundPin,
                        refundPinType: site.refundPinType,
                        refundRequiresPin: false,
                        discountPin: site.discountPin,
                        discountPinType: site.discountPinType,
                        discountRequiresPin: false,
                        screensaver: null,
                        siteUuid: site.siteUuid,
                        type: site.type,
                        posSettings: {
                          zellerPosSettings: {
                            tenderTypes: [TenderType.CARD],
                          },
                        },
                        timezone: Timezone.AUSTRALIA_MELBOURNE,
                      },
                      siteUuid: site.siteUuid,
                    },
                    uri: 'Device.Updated',
                  },
                ]),
              );
              const nameUpdatedArgs = getMockLambdaPayload(lambdaService, 1).dto;
              expect(nameUpdatedArgs).toMatchObject({
                entityUuid: entityUpdate.entityUuid,
                name: entityUpdate.name,
              });
              resolve(null);
            },
          );
        });
        const update2 = createEntityUpdatedDto();
        update2.categoryGroup = '03';
        update2.entityUuid = entity.entityUuid;
        mockLambdaInvoke(lambdaService);
        await new Promise((resolve, reject) => {
          updateEntityHandler(
            {
              detail: { ...update2 },
              id: entity.entityUuid,
            },
            context,
            async () => {
              try {
                const { app } = context;
                const deviceService = app.find(DeviceService);
                const dev: Device = await deviceService.findOneWithOption({
                  where: { deviceUuid: device.deviceUuid, domicile: Domicile.AU },
                });
                expect(dev.terminalConfig).toMatchJSON({
                  caid: expect.any(String),
                  catid: expect.any(String),
                  mcc: '',
                  lcrBinList: lcr.lcrBinList,
                  lcrAmount: lcr.lcrAmount,
                  lcrEnabled: lcr.lcrEnabled,
                  countryCode: '0036',
                  currencyCode: '0036',
                  currencyExponent: '02',
                });
                expect(lambdaService.lambda.send).toHaveBeenCalledTimes(3);
                expect(getMockLambdaPayload(lambdaService, 0).uri).toEqual('Entity.Updated');
                expect(getMockLambdaPayload(lambdaService, 1)).toEqual({
                  uri: 'Entity.NameUpdated',
                  dto: { entityUuid: update2.entityUuid, name: update2.name },
                });
                expect(getMockLambdaPayload(lambdaService, 2)).toEqual({
                  entityUuid: update2.entityUuid,
                  domicile: Domicile.AU,
                  requestPath: expect.any(String),
                  ...defaultDeviceSettingsUpdatePayload,
                  entitySettings: {
                    canAcquire: false,
                    canAcquireMobile: false,
                    canAcquireMoto: false,
                    canRefund: false,
                    canStandIn: false,
                    domicile: 'AUS',
                    currency: 'AUD',
                  },
                  mcc: '8699',
                });
                resolve(null);
              } catch (e) {
                reject(e);
              }
            },
          );
        });
      });
    });

    it('should update existing sites with entity info on update entity', async () => {
      const entity = createEntityCreatedDto();
      await createMockRegisteringIndividual(entity.entityUuid);
      await createEntity(entity);
      const update = {
        ...createEntityUpdatedDto(),
        abn: '0000000',
        entityUuid: entity.entityUuid,
        tradingName: 'updated-trading-name',
      };
      const site = createSiteDto();
      site.entityUuid = entity.entityUuid;
      await createSite(site);
      mockLambdaInvoke(lambdaService);
      await new Promise((resolve) => {
        updateEntityHandler(
          {
            detail: { ...update },
            id: entity.entityUuid,
          },
          context,
          async (e) => {
            expect(e).toBeFalsy();
            const { app } = context;
            const entityService = app.find(EntityService);
            const one = await entityService.findOne(entity.entityUuid);
            expect(one).toBeDefined();
            expect(one?.abn).toBe(update.abn);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(6);
            const siteService = app.find(SiteService);
            const invoice = await siteService.findOneWithOption({
              entityUuid: entity.entityUuid,
              type: SiteType.CNP_ZELLER_INVOICE,
            });
            expect(invoice.receipt.name).toEqual('updated-trading-name');
            const updated = await siteService.findOne(site.siteUuid);
            expect(updated.receipt.number).toEqual(update.abn);
            resolve(null);
          },
        );
      });
    });

    it('with manualEntry parameter', async () => {
      await new Promise<void>(async (done) => {
        const entity: EntityCreateRequestedEventDto = {
          ...createEntityCreatedDto(),
          manualEntry: true,
        };
        const customer = await createMockRegisteringIndividual(entity.entityUuid);
        lambdaService.lambda.send.mockClear();
        createEntityHandler(
          {
            detail: entity,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const one = await entityService.findOne(entity.entityUuid);
            expect(one).toBeDefined();
            expect(one?.entityUuid).toBe(entity.entityUuid);
            expect(one?.name).toBe(entity.name);
            expect(one?.acn).toBe(entity.acn);
            expect(one?.abn).toBe(entity.abn);
            expect(one?.type).toBe(entity.type);
            expect(one?.manualEntry).toBe(entity.manualEntry);
            expect(one?.createdSourceIp).toBe(entity.sourceIp);
            expect(one?.createdTime).toBeDefined();
            expect(one?.screening.status).toBe(ScreeningStatus.REQUIRED);
            expect(one?.caid).toBeDefined();
            expect(one?.caid).toHaveLength(15);
            expect(one?.standInRules).toEqual(defaultStandInRules);
            expect(one?.paymentLimits).toEqual(defaultPaymentLimits);
            expect(one?.shortId).toBeDefined();
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(6);
            expect(one?.domicile).toEqual(Domicile.AU);
            expect(one?.currency).toEqual('AUD');
            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              uri: 'Entity.Created',
              dto: {
                entityUuid: entity.entityUuid,
                name: entity.name,
                acn: entity.acn,
                abn: entity.abn,
                type: entity.type,
                caid: one?.caid,
                onboardingStatus: OnboardingStatus.ENTITY_ESTABLISHED,
                geofencing: geofencingJSON,
                accountStatus: defaultAccountStatusDto,
                status: Status.ACTIVE,
                riskRating: RiskRating.LOW_NEW,
                screening: {
                  status: ScreeningStatus.REQUIRED,
                },
                sourceIp: entity.sourceIp,
                manualEntry: true,
                standInRules: one.standInRules,
                shortId: one.shortId,
                paymentSettings: defaultPaymentSettings,
                countryOfOrigin: 'AUS',
                outstandingTransactionRequirementConfig: {
                  note: true,
                  attachments: true,
                  category: true,
                  accountingCategory: true,
                },
                primaryAccountHolder: customer.customerUuid,
                domicile: Domicile.AU,
                currency: 'AUD',
              },
            });
            done();
          },
        );
      });
    });

    it('should be able to update entity account status partially', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        mockLambdaInvoke(lambdaService);
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        const update = createEntityUpdatedDto();
        update.entityUuid = entity.entityUuid;
        update.accountStatus = {
          canAcquire: false,
        } as any;
        updateEntityHandler(
          {
            detail: update,
            id: entity.entityUuid,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const one = await entityService.findOne(entity.entityUuid);
            expect(one.accountStatus).toEqual({
              ...defaultAccountStatus,
              ...update.accountStatus,
            });
            done();
          },
        );
      });
    });

    it('should be able to update entity account status canStandIn partially', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        mockLambdaInvoke(lambdaService);
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        const update = createEntityUpdatedDto();
        update.entityUuid = entity.entityUuid;
        update.accountStatus = {
          canStandIn: false,
        } as any;
        updateEntityHandler(
          {
            detail: update,
            id: entity.entityUuid,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const one = await entityService.findOne(entity.entityUuid);
            expect(one.accountStatus).toEqual({
              ...defaultAccountStatus,
              ...update.accountStatus,
            });
            done();
          },
        );
      });
    });

    it('should be able to update entity account status canAcquireCnp partially', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        mockLambdaInvoke(lambdaService);
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        const update = createEntityUpdatedDto();
        update.entityUuid = entity.entityUuid;
        update.accountStatus = {
          canAcquireCnp: false,
        } as any;
        updateEntityHandler(
          {
            detail: update,
            id: entity.entityUuid,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const one = await entityService.findOne(entity.entityUuid);
            expect(one.accountStatus).toEqual({
              ...defaultAccountStatus,
              ...update.accountStatus,
            });
            done();
          },
        );
      });
    });

    it('should be able to update entity account status canAcquireMobile partially', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        mockLambdaInvoke(lambdaService);
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        const update = createEntityUpdatedDto();
        update.entityUuid = entity.entityUuid;
        update.accountStatus = {
          canAcquireMobile: false,
        } as any;
        updateEntityHandler(
          {
            detail: update,
            id: entity.entityUuid,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const one = await entityService.findOne(entity.entityUuid);
            expect(one.accountStatus).toEqual({
              ...defaultAccountStatus,
              ...update.accountStatus,
            });
            done();
          },
        );
      });
    });

    it('should be able to update entity account status canAcquireVt partially', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        mockLambdaInvoke(lambdaService);
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        const update = createEntityUpdatedDto();
        update.entityUuid = entity.entityUuid;
        update.accountStatus = {
          canAcquireVt: false,
        } as any;
        updateEntityHandler(
          {
            detail: update,
            id: entity.entityUuid,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const one = await entityService.findOne(entity.entityUuid);
            expect(one.accountStatus).toEqual({
              ...defaultAccountStatus,
              ...update.accountStatus,
            });
            done();
          },
        );
      });
    });

    describe('should be able to update entity account status canAcquireAmex partially', () => {
      it.each(
        Object.keys(OnboardingStatus).filter(
          (status) => ![OnboardingStatus.RC_ONBOARDED, OnboardingStatus.ONBOARDED].includes(status as OnboardingStatus),
        ),
      )(
        'should be able to update entity account status canAcquireAmex partially with OnboardingStatus: %s ',
        async (onboardingStatus) => {
          await new Promise<void>(async (done) => {
            const entity = createEntityCreatedDto();
            mockLambdaInvoke(lambdaService);
            await createMockRegisteringIndividual(entity.entityUuid);
            await createEntity(entity);
            await updateEntityOnboardingStatusWithoutRestriction({
              entityUuid: entity.entityUuid,
              onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
            });
            const update = createEntityUpdatedDto();
            update.entityUuid = entity.entityUuid;
            update.accountStatus = {
              canAcquireAmex: false,
            } as any;
            updateEntityHandler(
              {
                detail: update,
                id: entity.entityUuid,
              },
              context,
              async () => {
                const { app } = context;
                const entityService = app.find(EntityService);
                const one = await entityService.findOne(entity.entityUuid);
                expect(one.accountStatus).toEqual({
                  ...defaultAccountStatus,
                  ...update.accountStatus,
                  hasAmexPreviousCancelled: false,
                });
                done();
              },
            );
          });
        },
      );

      it.each([OnboardingStatus.RC_ONBOARDED, OnboardingStatus.ONBOARDED])(
        'should be able to update entity account status canAcquireAmex partially with OnboardingStatus: %s ',
        async (onboardingStatus) => {
          await new Promise<void>(async (done) => {
            const entity = createEntityCreatedDto();
            mockLambdaInvoke(lambdaService);
            await createMockRegisteringIndividual(entity.entityUuid);
            await createEntity(entity);
            await updateEntityOnboardingStatusWithoutRestriction({
              entityUuid: entity.entityUuid,
              onboardingStatus,
            });
            const update = createEntityUpdatedDto();
            update.entityUuid = entity.entityUuid;
            update.accountStatus = {
              canAcquireAmex: false,
            } as any;
            updateEntityHandler(
              {
                detail: update,
                id: entity.entityUuid,
              },
              context,
              async () => {
                const { app } = context;
                const entityService = app.find(EntityService);
                const one = await entityService.findOne(entity.entityUuid);
                expect(one.accountStatus).toEqual({
                  ...defaultAccountStatus,
                  ...update.accountStatus,
                  hasAmexPreviousCancelled: true,
                });
                done();
              },
            );
          });
        },
      );
    });

    it('should throw error on 4th shortId collision retry.', async () => {
      await new Promise<void>(async (done) => {
        const entity: EntityCreateRequestedEventDto = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        await new Promise((resolve) => {
          createEntityHandler(
            {
              detail: entity,
            },
            context,
            async () => {
              resolve(null);
            },
          );
        });
        const response = await new Promise<Entity>((resolve) => {
          getEntityHandler({ id: entity.entityUuid }, context, (_, r) => {
            resolve(r);
          });
        });

        const mockShortId = jest.spyOn(util, 'generateShortId');
        mockShortId.mockReturnValue(response.shortId!);

        const entityDto: EntityCreateRequestedEventDto = createEntityCreatedDto();
        await createMockRegisteringIndividual(entityDto.entityUuid);
        createEntityHandler(
          {
            detail: entityDto,
          },
          context,
          async (err) => {
            expect(err).toBeInstanceOf(QueryFailedError);
            expect(mockShortId).toBeCalledTimes(1);
            mockShortId.mockRestore();
            done();
          },
        );
      });
    });

    it('should throw error on entity recreate', async () => {
      await new Promise<void>(async (done) => {
        const entity: EntityCreateRequestedEventDto = createEntityCreatedDto();
        await new Promise((resolve) => {
          createEntityHandler(
            {
              detail: entity,
            },
            context,
            async () => {
              resolve(null);
            },
          );
        });

        createEntityHandler(
          {
            detail: entity,
          },
          context,
          async (err) => {
            expect(err).toBeInstanceOf(BadRequestError);
            done();
          },
        );
      });
    });

    it('should be able to save entity referred record with short Id', async () => {
      await new Promise<void>(async (done) => {
        const referringEntity = createEntityCreatedDto();
        mockLambdaInvoke(lambdaService);
        await createMockRegisteringIndividual(referringEntity.entityUuid);
        await createEntity(referringEntity);
        const referrer: any = await getEntity(referringEntity.entityUuid);

        const entity = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        entity.referredBy = referrer.shortId;
        createEntityHandler(
          {
            detail: entity,
          },
          context,
          async () => {
            const { app } = context;
            const referralService = app.find(ReferralService);
            const referral = await referralService.findOneWithOption({
              referred: entity.entityUuid,
              domicile: Domicile.AU,
            });

            expect(referral.entityUuid).toBe(referringEntity.entityUuid);
            expect(referral.referred).toBe(entity.entityUuid);
            expect(referral.createdAt).toBeDefined();
            done();
          },
        );
      });
    });

    it('should be able to save entity referred record with entity uuid', async () => {
      await new Promise<void>(async (done) => {
        const referringEntity = createEntityCreatedDto();
        mockLambdaInvoke(lambdaService);
        await createMockRegisteringIndividual(referringEntity.entityUuid);
        await createEntity(referringEntity);

        const entity = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        entity.referredBy = referringEntity.entityUuid;
        createEntityHandler(
          {
            detail: entity,
          },
          context,
          async () => {
            const { app } = context;
            const referralService = app.find(ReferralService);
            const referral = await referralService.findOneWithOption({
              referred: entity.entityUuid,
              domicile: Domicile.AU,
            });

            expect(referral.entityUuid).toBe(referringEntity.entityUuid);
            expect(referral.referred).toBe(entity.entityUuid);
            expect(referral.createdAt).toBeDefined();
            done();
          },
        );
      });
    });

    it.each(['11111', uuidv4(), ''])(
      'should be able to save entity and ignore invalid referred code: %s',
      async (referralCode: string) => {
        await new Promise<void>(async (done) => {
          const entity = createEntityCreatedDto();
          await createMockRegisteringIndividual(entity.entityUuid);
          entity.referredBy = referralCode;
          createEntityHandler(
            {
              detail: entity,
            },
            context,
            async () => {
              const { app } = context;
              const entityService = app.find(EntityService);
              const one = await entityService.findOne(entity.entityUuid);
              expect(one).toBeDefined();
              expect(one?.entityUuid).toBe(entity.entityUuid);

              const referralService = app.find(ReferralService);
              expect(await referralService.findOne(entity.entityUuid)).toBeUndefined();
              done();
            },
          );
        });
      },
    );
  });

  describe('create entity with query runner', () => {
    let domicileLookupDb: DomicileLookupDb;

    beforeAll(async () => {
      domicileLookupDb = new DomicileLookupDb();
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('should commit transaction and return entity if DynamoDB insert is successful', async () => {
      const entity: EntityCreateRequestedEventDto = createEntityCreatedDto();
      await createMockRegisteringIndividual(entity.entityUuid);
      await new Promise((resolve) => {
        createEntityHandler(
          {
            detail: entity,
          },
          context,
          async () => {
            resolve(null);
          },
        );
      });
      const response = await new Promise<Entity>((resolve) => {
        getEntityHandler({ id: entity.entityUuid }, context, (_, r) => {
          resolve(r);
        });
      });
      expect(response.entityUuid).toBe(entity.entityUuid);

      // check dynamodb record
      const result = await domicileLookupDb.getDomicileByEntityId(entity.entityUuid);
      expect(result).toBe(Domicile.AU);
    });

    it('should handle DynamoDB failure and rollback PostgreSQL transaction', async () => {
      const spyError = jest.spyOn(Logger, 'error');
      const entity: EntityCreateRequestedEventDto = createEntityCreatedDto();
      await createMockRegisteringIndividual(entity.entityUuid);
      const createDomicileRecordSpy = jest.spyOn(DomicileLookupDb.prototype, 'createDomicileRecord');
      createDomicileRecordSpy.mockRejectedValue(new Error('DynamoDB insert failed'));
      await new Promise((resolve) => {
        createEntityHandler(
          {
            detail: entity,
          },
          context,
          async () => {
            resolve(null);
          },
        );
      });
      const response = await new Promise<Entity>((resolve) => {
        getEntityHandler({ id: entity.entityUuid }, context, (_, r) => {
          resolve(r);
        });
      });
      expect(response).toBeUndefined();
      expect(spyError).toHaveBeenCalledWith(
        '[Create Entity] Failed to insert domicile record in DynamoDB, rolling back PostgreSQL changes. Error: [500] Failed to create domicile record in DynamoDB Error: DynamoDB insert failed',
      );

      // check dynamodb record
      const result = await domicileLookupDb.getDomicileByEntityId(entity.entityUuid);
      expect(result).toBe(null);
    });

    it('should throw server error if domicile record insert fails', async () => {
      const entity: EntityCreateRequestedEventDto = createEntityCreatedDto();
      await createMockRegisteringIndividual(entity.entityUuid);

      const createDomicileRecordSpy = jest.spyOn(DomicileLookupDb.prototype, 'createDomicileRecord');
      createDomicileRecordSpy.mockRejectedValue(new Error('DynamoDB insert failed'));
      await expect(createEntity(entity)).rejects.toThrow(ServerError);
    });
  });

  it('should be able to query a entity', async () => {
    const entity: EntityCreateRequestedEventDto = createEntityCreatedDto();
    await createMockRegisteringIndividual(entity.entityUuid);
    await new Promise((resolve) => {
      createEntityHandler(
        {
          detail: entity,
        },
        context,
        async () => {
          resolve(null);
        },
      );
    });
    const response = await new Promise<Entity>((resolve) => {
      getEntityHandler({ id: entity.entityUuid }, context, (_, r) => {
        resolve(r);
      });
    });
    expect(response.entityUuid).toBe(entity.entityUuid);
    expect(response.abn).toBe(entity.abn);
    expect(response.acn).toBe(entity.acn);
    expect(response.type).toBe(entity.type);
    expect(response.riskRating).toBe(RiskRating.LOW_NEW);
    expect(response.shortId).toBeDefined();
  });

  it('should be able to handle update entity event', async () => {
    await new Promise<void>(async (done) => {
      const newEntity = {
        entityUuid: uuidv4(),
        name: uuidv4(),
        acn: uuidv4(),
        abn: uuidv4(),
        type: EntityType.COMPANY,
      };
      await createMockRegisteringIndividual(newEntity.entityUuid);
      await new Promise((resolve) => {
        createEntityHandler(
          {
            detail: newEntity,
          },
          context,
          async () => {
            resolve(null);
          },
        );
      });
      const updatedEntity = createEntityUpdatedDto();
      updatedEntity.entityUuid = newEntity.entityUuid;
      updatedEntity.status = Status.DISABLED;
      mockLambdaInvoke(lambdaService);
      updateEntityHandler(
        {
          id: updatedEntity.entityUuid,
          detail: updatedEntity,
        },
        context,
        async (error) => {
          expect(error).toBeNull();
          const { app } = context;
          const entityService = app.find(EntityService);
          const one = await entityService.findOne(newEntity.entityUuid);
          expect(one).toBeDefined();
          expect(one?.entityUuid).toBe(newEntity.entityUuid);
          // acn is the not updated
          expect(one?.acn).toBe(newEntity.acn);
          expect(one?.name).toBe(updatedEntity.name);
          expect(one?.type).toBe(updatedEntity.type);
          expect(one?.caid).toBeDefined();
          expect(one?.registeredAddress).toEqual(updatedEntity.registeredAddress);
          expect(one?.businessAddress).toEqual(updatedEntity.businessAddress);
          expect(one?.categoryGroup).toBe(updatedEntity.categoryGroup);
          expect(one?.category).toBe(updatedEntity.category);
          expect(parseInt(one?.estimatedAnnualRevenue, 10)).toBe(updatedEntity.estimatedAnnualRevenue);
          expect(one?.debitCardAccountUuid).toBe(updatedEntity.debitCardAccountUuid);
          expect(one?.goodsServicesProvided).toBe(updatedEntity.goodsServicesProvided);
          expect(one?.customerDiscovery).toBe(updatedEntity.customerDiscovery);
          expect(one?.website).toBe(updatedEntity.website);
          expect(one?.instagram).toBe(updatedEntity.instagram);
          expect(one?.facebook).toBe(updatedEntity.facebook);
          expect(one?.twitter).toBe(updatedEntity.twitter);
          expect(one?.regulatorBody).toStrictEqual(updatedEntity.regulatorBody);
          expect(one?.status).toStrictEqual(updatedEntity.status);
          expect(one?.geofencing).toEqual(updatedEntity.geofencing);
          expect(one?.createdTime).toBeDefined();
          expect(one?.updatedTime).toBeDefined();
          expect(one?.accountStatus).toEqual({ ...updatedEntity.accountStatus, hasAmexPreviousCancelled: false });
          expect(Number(one?.updatedTime)).toBeGreaterThanOrEqual(Number(one?.createdTime));
          expect(one?.screening.status).toBe(ScreeningStatus.REQUIRED);
          expect(one?.riskRating).toEqual(updatedEntity.riskRating);
          expect(one?.riskReview).toEqual(updatedEntity.riskReview);
          expect(one?.standInRules).toEqual(defaultStandInRules);
          expect(one?.paymentLimits).toEqual(defaultPaymentLimits);
          expect(one?.termsOfService).toEqual(updatedEntity.termsOfService);
          expect(lambdaService.lambda.send).toHaveBeenCalledTimes(4);
          const entityUpdatedCall = lambdaService.lambda.send.mock.calls[0][0];
          const EntityNameUpdatedCall = lambdaService.lambda.send.mock.calls[1][0];
          expect(entityUpdatedCall.FunctionName).toEqual(envService.cqrsCommandHandler);
          expect(JSON.parse(decodeBase64(entityUpdatedCall.Payload))).toEqual({
            uri: 'Entity.Updated',
            dto: updatedEntity,
          });
          expect(EntityNameUpdatedCall.FunctionName).toEqual(envService.cqrsCommandHandler);
          expect(JSON.parse(decodeBase64(EntityNameUpdatedCall.Payload))).toEqual({
            uri: 'Entity.NameUpdated',
            dto: {
              entityUuid: updatedEntity.entityUuid,
              name: updatedEntity.name,
            },
          });
          done();
        },
      );
    });
  });

  it('should be able to dispatch device/customer force logoff on entity disabled', async () => {
    await new Promise<void>(async (done, reject) => {
      const newEntity = {
        entityUuid: uuidv4(),
        name: uuidv4(),
        type: EntityType.COMPANY,
      };
      const customer = await createMockRegisteringIndividual(newEntity.entityUuid);
      await new Promise((resolve) => {
        createEntityHandler(
          {
            detail: newEntity,
          },
          context,
          resolve,
        );
      });

      // create a device for the entity
      const device = createDeviceDto(newEntity.entityUuid);
      await createDevice(device);
      const updatedEntity = createEntityUpdatedDto();
      updatedEntity.entityUuid = newEntity.entityUuid;
      updatedEntity.status = Status.DISABLED;

      mockLambdaInvoke(lambdaService);
      updateEntityHandler(
        {
          id: updatedEntity.entityUuid,
          detail: updatedEntity,
        },
        context,
        async (error) => {
          try {
            expect(error).toBeNull();

            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              uri: 'Entity.Updated',
              dto: updatedEntity,
            });

            expect(getMockLambdaPayload(lambdaService, 1)).toEqual({
              uri: 'Entity.NameUpdated',
              dto: {
                entityUuid: updatedEntity.entityUuid,
                name: updatedEntity.name,
              },
            });

            // expect(getMockLambdaEmitterPayload(lambdaService, 0)).toEqual({
            //   uri: 'Device.Updated',
            //   dto: expect.any(Object),
            // });

            expect(getMockLambdaPayload(lambdaService, 3)).toEqual({
              uri: 'Device.ForcedLogoff',
              dto: { deviceUuid: device.deviceUuid, reason: 'Account is disabled.' },
            });

            expect(getMockLambdaPayload(lambdaService, 4)).toEqual({
              uri: 'Customer.ForcedLogoff',
              dto: {
                customerUuid: customer.customerUuid,
                entityUuid: updatedEntity.entityUuid,
                reason: 'Account is disabled.',
              },
            });

            done();
          } catch (e) {
            reject(e);
          }
        },
      );
    });
  });

  it('should be able to handle update partial address', async () => {
    const newEntity = {
      entityUuid: uuidv4(),
      name: uuidv4(),
      type: EntityType.COMPANY,
    };
    await createMockRegisteringIndividual(newEntity.entityUuid);
    await new Promise((resolve) => {
      createEntityHandler(
        {
          detail: newEntity,
        },
        context,
        async () => {
          resolve(null);
        },
      );
    });
    mockLambdaInvoke(lambdaService);
    const businessAddress = {
      suburb: uuidv4(),
      street1: uuidv4(),
      street2: uuidv4(),
      postcode: uuidv4(),
      country: uuidv4(),
      state: uuidv4(),
    };
    const updatedEvent: any = { entityUuid: newEntity.entityUuid, businessAddress };
    await new Promise((resolve) => {
      updateEntityHandler(
        {
          id: updatedEvent.entityUuid,
          detail: updatedEvent,
        },
        context,
        async (error) => {
          expect(error).toBeNull();
          const { app } = context;
          const entityService = app.find(EntityService);
          resolve(entityService);
        },
      );
    });
    const updatedEvent2: any = {
      entityUuid: newEntity.entityUuid,
      businessAddress: {
        country: uuidv4(),
        state: uuidv4(),
      },
    };
    const entityService: any = await new Promise((resolve) => {
      updateEntityHandler(
        {
          id: updatedEvent2.entityUuid,
          detail: updatedEvent2,
        },
        context,
        async (error) => {
          expect(error).toBeNull();
          const { app } = context;
          const service = app.find(EntityService);
          resolve(service);
        },
      );
    });
    const entity = await entityService.findOne(newEntity.entityUuid);
    expect(entity.businessAddress).toEqual({
      ...updatedEvent.businessAddress,
      ...updatedEvent2.businessAddress,
    });
  });

  it('should be able to handle addresses with trailing spaces', async () => {
    const newEntity = {
      entityUuid: uuidv4(),
      name: uuidv4(),
      type: EntityType.COMPANY,
    };
    await createMockRegisteringIndividual(newEntity.entityUuid);
    await new Promise((resolve) => {
      createEntityHandler(
        {
          detail: newEntity,
        },
        context,
        async () => {
          resolve(null);
        },
      );
    });
    mockLambdaInvoke(lambdaService);

    const businessAddress = {
      suburb: '    suburb      ',
      street1: 'street1     ',
      street2: '    street2',
      postcode: uuidv4(),
      country: uuidv4(),
      state: uuidv4(),
    };
    const trimmedBusinessAddress = {
      suburb: 'suburb',
      street1: 'street1',
      street2: 'street2',
      postcode: businessAddress.postcode,
      country: businessAddress.country,
      state: businessAddress.state,
    };
    const registeredAddress = {
      suburb: '    suburb      ',
      street1: 'street1     ',
      street2: '    street2',
      postcode: uuidv4(),
      country: uuidv4(),
      state: uuidv4(),
    };
    const trimmedRegisteredAddress = {
      suburb: 'suburb',
      street1: 'street1',
      street2: 'street2',
      postcode: registeredAddress.postcode,
      country: registeredAddress.country,
      state: registeredAddress.state,
    };

    const updatedEvent: any = { entityUuid: newEntity.entityUuid, businessAddress, registeredAddress };
    await new Promise((resolve) => {
      updateEntityHandler(
        {
          id: updatedEvent.entityUuid,
          detail: updatedEvent,
        },
        context,
        async (error) => {
          expect(error).toBeNull();

          expect(lambdaService.lambda.send).toHaveBeenCalledTimes(1);
          const lastInvoke = lambdaService.lambda.send.mock.calls[0][0];
          expect(lastInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
          expect(JSON.parse(decodeBase64(lastInvoke.Payload))).toEqual({
            uri: 'Entity.Updated',
            dto: {
              ...updatedEvent,
              businessAddress: trimmedBusinessAddress,
              registeredAddress: trimmedRegisteredAddress,
            },
          });
          resolve({});
        },
      );
    });
    const entityService: any = await new Promise((resolve) => {
      updateEntityHandler({}, context, async () => {
        const { app } = context;
        resolve(app.find(EntityService));
      });
    });

    const entity = await entityService.findOne(newEntity.entityUuid);
    expect(entity.businessAddress).toEqual(trimmedBusinessAddress);
    expect(entity.registeredAddress).toEqual(trimmedRegisteredAddress);
  });

  it('should not update a none eit entity', async () => {
    await new Promise<void>(async (done) => {
      const entity: EntityUpdatedEventDto = {
        entityUuid: uuidv4(),
      } as any;
      updateEntityHandler(
        {
          id: entity.entityUuid,
          detail: entity,
        },
        context,
        async (error) => {
          expect(error).not.toBeNull();
          expect((error as Error).message).toContain('[400]');
          const { app } = context;
          const entityService = app.find(EntityService);
          const one = await entityService.findOne(entity.entityUuid);
          expect(one).not.toBeDefined();
          expect(lambdaService.lambda.send).toBeCalledTimes(0);
          done();
        },
      );
    });
  });

  describe('screening test suite', () => {
    it('should not send screening event but set screening status to REQUIRED in create', async () => {
      await new Promise<void>(async (done) => {
        const dto: any = createEntityCreatedDto();
        await createMockRegisteringIndividual(dto.entityUuid);
        mockLambdaInvoke(lambdaService);
        delete dto.name;
        createEntityHandler(
          {
            detail: dto,
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const entityService = app.find(EntityService);
            const entity = await entityService.findOne(dto.entityUuid);
            expect(entity.screening.status).toBe(ScreeningStatus.REQUIRED);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(6);

            done();
          },
        );
      });
    });

    it('should be not to emit screening request when update entity name but did not call the finalise API yet', async () => {
      await new Promise<void>(async (done) => {
        const entity: any = createEntityCreatedDto();
        delete entity.name;
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        mockLambdaInvoke(lambdaService);
        updateEntityHandler(
          {
            id: entity.entityUuid,
            detail: { name: uuidv4() },
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(entity.entityUuid);
            expect(item.screening.status).toBe(ScreeningStatus.REQUIRED);
            expect(lambdaService.lambda.send).toBeCalledTimes(2);
            done();
          },
        );
      });
    });

    it('should be able to emit screening request when update entity name if the status has called Finalise API', async () => {
      await new Promise<void>(async (done) => {
        const entity: any = createEntityCreatedDto();
        delete entity.name;
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        const entityService = appContext.get(EntityService);
        const originalFunction = entityService.getEntity;
        // note - temp replacing service function with mock
        entityService.getEntity = jest.fn().mockResolvedValue({
          ...entity,
          screening: {
            status: ScreeningStatus.NOT_REQUIRED,
          },
          onboardingStatus: OnboardingStatus.REVIEW,
        });
        entity.name = uuidv4();
        mockLambdaInvoke(lambdaService);
        updateEntityHandler(
          {
            id: entity.entityUuid,
            detail: { name: 'John Wick' },
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const service = app.find(EntityService);
            const item = await service.findOne(entity.entityUuid);
            expect(item.screening.status).toBe(ScreeningStatus.REQUIRED);
            expect(item.name).toBe('John Wick');
            expect(lambdaService.lambda.send).toBeCalledTimes(3);
            const lastInvoke = lambdaService.lambda.send.mock.calls[1][0];
            expect(lastInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            service.getEntity = originalFunction;
            done();
          },
        );
      });
    });

    it('should be able to emit device force logoff when update entity with abandoned risk review result', async () => {
      await new Promise<void>(async (done) => {
        const entity: any = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);

        // create a device for the entity
        const device = createDeviceDto(entity.entityUuid);
        await createDevice(device);

        const updateEntityDto = {
          entityUuid: entity.entityUuid,
          riskReview: {
            status: RiskReviewStatus.COMPLETED,
            result: RiskReviewResult.ABANDONED,
          },
        };
        mockLambdaInvoke(lambdaService);
        updateEntityHandler(
          {
            id: entity.entityUuid,
            detail: updateEntityDto,
          },
          context,
          async (error) => {
            expect(error).toBeNull();

            expect(JSON.parse(decodeBase64((lambdaService.lambda.send as jest.Mock).mock.calls[0][0].Payload))).toEqual(
              {
                uri: 'Entity.Updated',
                dto: updateEntityDto,
              },
            );

            expect(JSON.parse(decodeBase64((lambdaService.lambda.send as jest.Mock).mock.calls[1][0].Payload))).toEqual(
              {
                uri: 'Entity.Updated',
                dto: {
                  entityUuid: entity.entityUuid,
                  onboardingStatus: OnboardingStatus.RC_ABANDONED,
                },
              },
            );

            expect(JSON.parse(decodeBase64((lambdaService.lambda.send as jest.Mock).mock.calls[2][0].Payload))).toEqual(
              {
                uri: 'Entity.OnboardingStatusUpdated',
                dto: {
                  entityUuid: entity.entityUuid,
                  onboardingStatus: OnboardingStatus.RC_ABANDONED,
                },
              },
            );

            expect(JSON.parse(decodeBase64((lambdaService.lambda.send as jest.Mock).mock.calls[3][0].Payload))).toEqual(
              {
                uri: 'Device.ForcedLogoff',
                dto: { deviceUuid: device.deviceUuid, reason: 'Account is disabled.' },
              },
            );

            done();
          },
        );
      });
    });

    it('should be able to handle screening completed event', async () => {
      await new Promise<void>(async (done) => {
        const dto = createEntityCreatedDto();
        await createMockRegisteringIndividual(dto.entityUuid);
        await createEntity(dto);
        const screeningEvent: EntityScreeningCompletedEventDto = {
          entityUuid: dto.entityUuid,
          result: KYCScreeningResult.NO_MATCH,
          matchStatus: uuidv4(),
          searchIdentifier: uuidv4(),
        };
        mockLambdaInvoke(lambdaService);
        projectionDomainEventHandler(
          {
            'detail-type': 'ams.Entity.ScreeningCompleted',
            detail: screeningEvent,
          } as any,
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(dto.entityUuid);
            expect(item.screening).toEqual({
              result: screeningEvent.result,
              matchStatus: screeningEvent.matchStatus,
              searchIdentifier: screeningEvent.searchIdentifier,
              status: ScreeningStatus.COMPLETED,
            });

            const lastInvoke = lambdaService.lambda.send.mock.calls[0][0];
            expect(lastInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);

            expect(decodeBase64(lastInvoke.Payload)).toMatchJSON({
              uri: 'Entity.Updated',
              dto: {
                entityUuid: dto.entityUuid,
                screening: {
                  result: screeningEvent.result,
                  matchStatus: screeningEvent.matchStatus,
                  searchIdentifier: screeningEvent.searchIdentifier,
                  status: ScreeningStatus.COMPLETED,
                },
              },
            });
            done();
          },
        );
      });
    });
  });

  describe('hubspot entity test suite', () => {
    it('should reject the update request if hubspot id not found', async () => {
      await new Promise<void>(async (done) => {
        updateEntityByHubspotIdHandler({ id: uuidv4(), detail: {} }, context, async (error) => {
          expect((error as Error).message).toContain('[400]');
          done();
        });
      });
    });
  });

  describe('search results test suite', () => {
    it('should be able to handle full search completed event', async () => {
      await new Promise<void>(async (done) => {
        const dto = createEntityCreatedDto();
        await createMockRegisteringIndividual(dto.entityUuid);
        await createEntity(dto);
        const event: EntityFullSearchCompletedEventDto = {
          entityUuid: dto.entityUuid,
          found: true,
          acn: 'acn',
          registeredAddress: {
            state: 'VIC',
            country: 'AUS',
            street1: 'street1',
            street2: 'street2',
            postcode: 'postcode',
            suburb: 'suburb',
          },
          businessAddress: {
            state: 'VIC',
            country: 'AUS',
            street1: 'street1',
            street2: 'street2',
            postcode: 'postcode',
            suburb: 'suburb',
          },
          members: [
            {
              type: EntityType.INDIVIDUAL,
              firstname: 'firstname',
              middlename: 'middlename',
              lastname: 'lastname',
              companyTrustName: 'companyTrustName',
              abn: 'abn',
              address: {
                street: 'street',
                postcode: 'postcode',
                country: 'country',
                suburb: 'suburb',
                state: 'state',
              },
              dob: 'dob',
              director: true,
              secretary: true,
              ceo: true,
              beneficialOwner: true,
              beneficialOwnerAlt: true,
              beneficiary: true,
              shareholder: true,
              partner: true,
              trustee: true,
              settlor: true,
            },
          ],
          error: 'errors',
        };
        mockLambdaInvoke(lambdaService);
        projectionDomainEventHandler(
          { 'detail-type': 'ams.Entity.FullSearchCompleted', detail: event } as any,
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(dto.entityUuid);
            expect(item.fullSearchResult).toEqual({
              found: event.found,
              acn: event.acn,
              registeredAddress: event.registeredAddress,
              businessAddress: event.businessAddress,
              members: event.members,
              error: event.error,
            });
            done();
          },
        );
      });
    });
  });

  it('should be able to get all customers related to entity', async () => {
    await new Promise<void>(async (done) => {
      const customerA = createCustomerRequestedDto();
      const customerB = createCustomerRequestedDto();
      const customerC = createCustomerRequestedDto();
      const entityA = createEntityCreatedDto();
      const entityB = createEntityCreatedDto();
      customerA.entityUuid = entityA.entityUuid;
      customerA.registeringIndividual = true;
      customerB.entityUuid = entityB.entityUuid;
      customerB.registeringIndividual = true;
      customerC.entityUuid = entityA.entityUuid;
      await createCustomer(customerA);
      await createCustomer(customerB);
      await createCustomer(customerC);
      await createEntity(entityA);
      await createEntity(entityB);
      entityGetAllCustomersHandler({ id: entityA.entityUuid }, context, async (error, dto) => {
        expect(error).toBeNull();
        expect(dto.length).toBe(2);
        done();
      });
    });
  });

  describe('entity metric update', () => {
    it('should throw error if entity not found', (done) => {
      projectionDomainEventHandler(
        {
          'detail-type': 'ams.EntityMetric.Updated',
          detail: {
            entityUuid: 'not exist',
            metricsId: 'ID-001',
            metricsValue: 100,
            rawPayload: { a: 1, b: 2, c: 3.3 },
          },
        } as any,
        context,
        async (error) => {
          expect(error).not.toBeNull();
          done();
        },
      );
    });

    it('should be able to save metrics', async () => {
      const entity = createEntityCreatedDto();
      await createMockRegisteringIndividual(entity.entityUuid);
      await createEntity(entity);
      const updated1: EntityMetricUpdatedEventDto = {
        entityUuid: entity.entityUuid,
        metricsId: 'ID-001',
        metricsValue: 100,
        rawPayload: { a: 1, b: 2, c: 3.3 },
      };
      await new Promise((resolve) => {
        projectionDomainEventHandler(
          { 'detail-type': 'ams.EntityMetric.Updated', detail: updated1 } as any,
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(entity.entityUuid);
            expect(item.metrics).toEqual([{ metricsId: updated1.metricsId, metricsValue: updated1.metricsValue }]);
            resolve(null);
          },
        );
      });

      // add new metrics
      let updated2 = {
        entityUuid: entity.entityUuid,
        metricsId: 'ID-002',
        metricsValue: 200,
        rawPayload: { b: 2, c: 3.3 },
      };
      await new Promise((resolve) => {
        projectionDomainEventHandler(
          { 'detail-type': 'ams.EntityMetric.Updated', detail: updated2 } as any,
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(entity.entityUuid);
            expect(item.metrics).toEqual([
              { metricsId: updated1.metricsId, metricsValue: updated1.metricsValue },
              { metricsId: updated2.metricsId, metricsValue: updated2.metricsValue },
            ]);
            resolve(null);
          },
        );
      });

      // update existing metrics
      updated2 = {
        entityUuid: entity.entityUuid,
        metricsId: 'ID-002',
        metricsValue: 300,
        rawPayload: { b: 3, c: 4.3 },
      };
      await new Promise((resolve) => {
        projectionDomainEventHandler(
          { 'detail-type': 'ams.EntityMetric.Updated', detail: updated2 } as any,
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(entity.entityUuid);
            expect(item.metrics).toEqual([
              { metricsId: updated1.metricsId, metricsValue: updated1.metricsValue },
              { metricsId: updated2.metricsId, metricsValue: updated2.metricsValue },
            ]);
            resolve(null);
          },
        );
      });
    });
  });

  it('should be able to handle Amex Merchant Submission Completed Event', async () => {
    await new Promise<void>(async (done) => {
      const entity = createEntityCreatedDto();
      await createMockRegisteringIndividual(entity.entityUuid);
      await createEntity(entity);
      const amexSubmissionResult = {
        status: AmexAcquisitionStatus.ERROR,
        result: {
          errors: [{ err_msg: 'err_msg' }],
        },
      };
      const dto: EntityAmexMerchantSubmissionCompletedDto = {
        entityUuid: entity.entityUuid,
        ...amexSubmissionResult,
      };
      projectionDomainEventHandler(
        {
          'detail-type': 'ams.Entity.AmexMerchantSubmissionCompleted',
          id: entity.entityUuid,
          detail: dto,
        } as any,
        context,
        async (error) => {
          expect(error).toBeNull();
          const { app } = context;
          const entityService = app.find(EntityService);
          const item = await entityService.findOne(entity.entityUuid);
          expect(item.amexSubmission).toStrictEqual(amexSubmissionResult);
          done();
        },
      );
    });
  });

  it('should be able to call the AMEX Merchant Cron Job', async () => {
    (info as any).mockReset();
    const entity = createEntityCreatedDto();

    const getManagerSpy = jest.spyOn(typeorm, 'getManager').mockReturnValue({
      query: jest
        .fn()
        .mockResolvedValueOnce([entity.entityUuid]) // query entityUuids
        .mockResolvedValueOnce([{ nextval: '123' }]), // generateAmexMerchantSubmissionDto nextval
    } as unknown as typeorm.EntityManager);

    const entityService = appContext.get(EntityService);
    const originalGetEntityFn = entityService.getEntity;
    const originalGetRegisteringIndividualFn = entityService.getRegisteringIndividual;

    entityService.getEntity = jest.fn().mockResolvedValue(entity);
    entityService.getRegisteringIndividual = jest.fn().mockResolvedValue({} as Customer);

    expect(info).not.toBeCalled();
    await new Promise<void>(async (done) => {
      amexMerchantOnboardingCronJobHandler({}, context, async (error) => {
        expect(error).toBeNull();
        expect(info).toBeCalledWith('AMEX Merchant Onboarding Cron Job Completed');

        getManagerSpy.mockRestore();
        entityService.getEntity = originalGetEntityFn;
        entityService.getRegisteringIndividual = originalGetRegisteringIndividualFn;

        done();
      });
    });
  });

  describe('finaliseEntityOnboarding test suite', () => {
    it('should be able to call the finaliseEntityOnboarding and screening for three documents', async () => {
      await new Promise<void>(async (done) => {
        const documentVerificationRequestPayload = {
          firstName: 'firstName',
          middleName: 'middleName',
          lastName: 'lastName',
          address: {
            street: 'street',
            suburb: 'suburb',
            postcode: 'postcode',
            country: 'country',
          },
          dob: 'dob',
          driversLicenceState: AddressState.VIC,
          driversLicenceFirstName: 'driversLicenceFirstName',
          driversLicenceMiddleName: 'driversLicenceMiddleName',
          driversLicenceLastName: 'driversLicenceLastName',
          passportCountry: 'passportCountry',
          passportFirstName: 'passportFirstName',
          passportMiddleName: 'passportMiddleName',
          passportLastName: 'passportLastName',
          medicareCardColour: CustomerMedicareCardColours.BLUE,
          medicareFirstName: 'medicareFirstName',
          medicareMiddleName: 'medicareMiddleName',
          medicareLastName: 'medicareLastName',
          medicareCardPosition: 1,
          medicareCardExpiry: 'medicareCardExpiry',
        };

        const documentVerificationDetails: DocumentVerificationDetails = {
          ...documentVerificationRequestPayload,
          resultDriversLicence: CustomerDocumentVerificationResult.ACCEPTED,
          resultPassport: CustomerDocumentVerificationResult.ACCEPTED,
          resultMedicareCard: CustomerDocumentVerificationResult.ACCEPTED,
          tokenisedDriversLicenseNumber: 'tokenisedDriversLicenseNumber',
          tokenisedPassportNumber: 'tokenisedPassportNumber',
          tokenisedMedicareCard: 'tokenisedMedicareCard',
        };
        const entity = createEntityCreatedDto();
        const customer = createCustomerRequestedDto();
        customer.documents = documentVerificationDetails;
        customer.entityUuid = entity.entityUuid;
        customer.registeringIndividual = true;
        customer.director = true;
        await createCustomer(customer);
        await createEntity(entity);
        const getManagerSpy = jest.spyOn(typeorm, 'getManager').mockReturnValue({
          query: () => Promise.resolve([customer]),
        } as unknown as typeorm.EntityManager);
        mockLambdaInvoke(lambdaService);
        finaliseEntityOnboardingHandler(
          {
            id: entity.entityUuid,
            domicile: Domicile.AU,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(entity.entityUuid);
            expect(item.onboardingStatus).toBe(OnboardingStatus.REVIEW);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(5);
            const lambdaCalls = lambdaService.lambda.send.mock.calls;
            const firstInvoke = lambdaCalls[0][0];
            expect(firstInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            const secondInvoke = lambdaCalls[1][0];
            expect(secondInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            const thirdInvoke = lambdaCalls[2][0];
            expect(thirdInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);

            const defaultCustomerHandler = lambdaCalls.find((lambda: any) => {
              const payload = JSON.parse(decodeBase64(lambda[0].Payload)).dto;
              if (payload.screeningType === 'DEFAULT') {
                return lambda;
              }
              return null;
            })[0];

            expect(defaultCustomerHandler.FunctionName).toBe(envService.cqrsCommandHandler);

            expect(JSON.parse(decodeBase64(defaultCustomerHandler.Payload))).toEqual({
              uri: 'Customer.ScreeningRequested',
              dto: {
                firstName: customer.firstname,
                lastName: customer.lastname,
                middleName: customer.middlename,
                customerUuid: customer.customerUuid,
                dob: customer.dob,
                entityType: EntityType.INDIVIDUAL,
                screeningType: ScreeningRequestedType.DEFAULT,
              },
            });

            getManagerSpy.mockRestore();
            done();
          },
        );
      });
    });

    it('should be able to call the finaliseOnboarding and screening for multiple documents and compare unique customer info', async () => {
      await new Promise<void>(async (done) => {
        const documentVerificationRequestPayload = {
          firstName: 'firstName',
          middleName: 'middleName',
          lastName: 'lastName',
          address: {
            street: 'street',
            suburb: 'suburb',
            postcode: 'postcode',
            country: 'country',
          },
          dob: 'dob',
          driversLicenceState: AddressState.VIC,
          driversLicenceFirstName: 'driversLicenceFirstName',
          driversLicenceMiddleName: 'driversLicenceMiddleName',
          driversLicenceLastName: 'driversLicenceLastName',
          passportCountry: 'passportCountry',
          passportFirstName: 'driversLicenceFirstName',
          passportMiddleName: 'driversLicenceMiddleName',
          passportLastName: 'driversLicenceLastName',
          medicareCardColour: CustomerMedicareCardColours.BLUE,
          medicareCardPosition: 1,
          medicareCardExpiry: 'medicareCardExpiry',
        };

        const documentVerificationDetails: DocumentVerificationDetails = {
          ...documentVerificationRequestPayload,
          resultDriversLicence: CustomerDocumentVerificationResult.ACCEPTED,
          resultPassport: CustomerDocumentVerificationResult.ACCEPTED,
          resultMedicareCard: CustomerDocumentVerificationResult.ACCEPTED,
          tokenisedDriversLicenseNumber: 'tokenisedDriversLicenseNumber',
          tokenisedPassportNumber: 'tokenisedPassportNumber',
          tokenisedMedicareCard: 'tokenisedMedicareCard',
        };
        const entity = createEntityCreatedDto();
        const customer = createCustomerRequestedDto();
        customer.documents = documentVerificationDetails;
        customer.entityUuid = entity.entityUuid;
        customer.registeringIndividual = true;
        customer.director = true;
        await createCustomer(customer);
        await createEntity(entity);
        const getManagerSpy = jest.spyOn(typeorm, 'getManager').mockReturnValue({
          query: () => Promise.resolve([customer]),
        } as unknown as typeorm.EntityManager);
        mockLambdaInvoke(lambdaService);
        finaliseEntityOnboardingHandler(
          {
            id: entity.entityUuid,
            domicile: Domicile.AU,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(entity.entityUuid);
            expect(item.onboardingStatus).toBe(OnboardingStatus.REVIEW);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(5);
            const lambdaCalls = lambdaService.lambda.send.mock.calls;
            const firstInvoke = lambdaCalls[0][0];
            expect(firstInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            const secondInvoke = lambdaCalls[1][0];
            expect(secondInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            const thirdInvoke = lambdaCalls[2][0];
            expect(thirdInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);

            const defaultCustomerHandler = lambdaCalls.find((lambda: any) => {
              const payload = JSON.parse(decodeBase64(lambda[0].Payload)).dto;
              if (payload.screeningType === 'DEFAULT') {
                return lambda;
              }
              return null;
            })[0];

            expect(defaultCustomerHandler.FunctionName).toBe(envService.cqrsCommandHandler);

            expect(JSON.parse(decodeBase64(defaultCustomerHandler.Payload))).toEqual({
              uri: 'Customer.ScreeningRequested',
              dto: {
                firstName: customer.firstname,
                lastName: customer.lastname,
                middleName: customer.middlename,
                customerUuid: customer.customerUuid,
                dob: customer.dob,
                entityType: EntityType.INDIVIDUAL,
                screeningType: ScreeningRequestedType.DEFAULT,
              },
            });

            getManagerSpy.mockRestore();
            done();
          },
        );
      });
    });

    it('should be able to call the finaliseOnboarding and screening for only drivers licence - as rest are duplicate names', async () => {
      await new Promise<void>(async (done) => {
        const documentVerificationRequestPayload = {
          firstName: 'firstName',
          middleName: 'middleName',
          lastName: 'lastName',
          address: {
            street: 'street',
            suburb: 'suburb',
            postcode: 'postcode',
            country: 'country',
          },
          driversLicenceState: AddressState.VIC,
          passportCountry: 'passportCountry',
          medicareCardColour: CustomerMedicareCardColours.BLUE,
          medicareCardPosition: 1,
          medicareCardExpiry: 'medicareCardExpiry',
        };

        const documentVerificationDetails: DocumentVerificationDetails = {
          ...documentVerificationRequestPayload,
          resultDriversLicence: CustomerDocumentVerificationResult.ACCEPTED,
          resultPassport: CustomerDocumentVerificationResult.ACCEPTED,
          resultMedicareCard: CustomerDocumentVerificationResult.ACCEPTED,
          tokenisedDriversLicenseNumber: 'tokenisedDriversLicenseNumber',
          tokenisedPassportNumber: 'tokenisedPassportNumber',
          tokenisedMedicareCard: 'tokenisedMedicareCard',
        };
        const entity = createEntityCreatedDto();
        const customer = createCustomerRequestedDto();
        customer.documents = documentVerificationDetails;
        customer.entityUuid = entity.entityUuid;
        customer.registeringIndividual = true;
        customer.director = true;
        await createCustomer(customer);
        await createEntity(entity);
        const getManagerSpy = jest.spyOn(typeorm, 'getManager').mockReturnValue({
          query: () => Promise.resolve([customer]),
        } as unknown as typeorm.EntityManager);
        mockLambdaInvoke(lambdaService);
        finaliseEntityOnboardingHandler(
          {
            id: entity.entityUuid,
            domicile: Domicile.AU,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(entity.entityUuid);
            expect(item.onboardingStatus).toBe(OnboardingStatus.REVIEW);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(5);
            const lambdaCalls = lambdaService.lambda.send.mock.calls;
            const firstInvoke = lambdaCalls[0][0];
            expect(firstInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            const secondInvoke = lambdaCalls[1][0];
            expect(secondInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            const thirdInvoke = lambdaCalls[2][0];
            expect(thirdInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);

            const defaultCustomerHandler = lambdaCalls.find((lambda: any) => {
              const payload = JSON.parse(decodeBase64(lambda[0].Payload)).dto;
              if (payload.screeningType === 'DEFAULT') {
                return lambda;
              }
              return null;
            })[0];

            expect(defaultCustomerHandler.FunctionName).toBe(envService.cqrsCommandHandler);

            expect(JSON.parse(decodeBase64(defaultCustomerHandler.Payload))).toEqual({
              uri: 'Customer.ScreeningRequested',
              dto: {
                firstName: customer.firstname,
                lastName: customer.lastname,
                middleName: customer.middlename,
                customerUuid: customer.customerUuid,
                dob: customer.dob,
                entityType: EntityType.INDIVIDUAL,
                screeningType: ScreeningRequestedType.DEFAULT,
              },
            });

            getManagerSpy.mockRestore();
            done();
          },
        );
      });
    });

    it('should be able to trigger referral event on finaliseOnboarding', async () => {
      await new Promise<void>(async (done) => {
        const referralEntity = createEntityCreatedDto();
        await createMockRegisteringIndividual(referralEntity.entityUuid);
        await createEntity(referralEntity);

        const entity = createEntityCreatedDto();
        entity.referredBy = referralEntity.entityUuid;
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        const getManagerSpy = jest.spyOn(typeorm, 'getManager').mockReturnValue({
          query: () => Promise.resolve([]),
        } as unknown as typeorm.EntityManager);
        mockLambdaInvoke(lambdaService);
        finaliseEntityOnboardingHandler(
          {
            id: entity.entityUuid,
            domicile: Domicile.AU,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(entity.entityUuid);
            expect(item.onboardingStatus).toBe(OnboardingStatus.REVIEW);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(5);

            const firstInvoke = lambdaService.lambda.send.mock.calls[0][0];
            expect(firstInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            expect(JSON.parse(firstInvoke.Payload.toString()).uri).toEqual('Customer.Updated');

            const secondInvoke = lambdaService.lambda.send.mock.calls[1][0];
            expect(secondInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            expect(JSON.parse(secondInvoke.Payload.toString()).uri).toEqual('Entity.Updated');

            const thirdInvoke = lambdaService.lambda.send.mock.calls[2][0];
            expect(thirdInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            expect(JSON.parse(thirdInvoke.Payload.toString()).uri).toEqual('Entity.OnboardingStatusUpdated');

            const fourthInvoke = lambdaService.lambda.send.mock.calls[3][0];
            expect(fourthInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            expect(JSON.parse(fourthInvoke.Payload.toString()).uri).toEqual('Entity.Referred');

            const fifthInvoke = lambdaService.lambda.send.mock.calls[4][0];
            expect(fifthInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            expect(JSON.parse(fifthInvoke.Payload.toString()).uri).toEqual('Entity.ScreeningRequested');
            getManagerSpy.mockRestore();
            done();
          },
        );
      });
    });
  });

  describe('adminUpdateEntity test suite', () => {
    it('should be able to call the finaliseOnboarding', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        const getManagerSpy = jest.spyOn(typeorm, 'getManager').mockReturnValue({
          query: () => Promise.resolve([]),
        } as unknown as typeorm.EntityManager);
        mockLambdaInvoke(lambdaService);
        const update: EntityUpdatedEventDto = {
          entityUuid: entity.entityUuid,
          onboardingStatus: OnboardingStatus.REVIEW,
        };

        adminUpdateEntityHandler(
          {
            id: entity.entityUuid,
            detail: update,
          },
          context,
          async () => {
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(entity.entityUuid);
            expect(item.onboardingStatus).toBe(OnboardingStatus.REVIEW);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(2);
            const firstInvoke = lambdaService.lambda.send.mock.calls[0][0];
            expect(firstInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            const secondInvoke = lambdaService.lambda.send.mock.calls[1][0];
            expect(secondInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            getManagerSpy.mockRestore();
            done();
          },
        );
      });
    });
  });

  describe('select account test suite', () => {
    const getDepositAccountDto = () => {
      return {
        thirdPartyBankAccountUuid: uuidv4(),
        entityUuid: uuidv4(),
        accountNumber: uuidv4().slice(0, 9),
        accountName: uuidv4(),
        accountBsb: uuidv4(),
        name: uuidv4(),
      };
    };
    beforeEach(() => {
      mockLambdaInvoke(lambdaService);
    });

    it('should select remit to card', async () => {
      await new Promise<void>(async (done) => {
        const entityDto = createEntityCreatedDto();
        await createMockRegisteringIndividual(entityDto.entityUuid);
        mockLambdaInvoke(lambdaService);
        await createEntity(entityDto);
        const depositAccountUuid = uuidv4();
        selectDepositAccountHandler(
          {
            id: entityDto.entityUuid,
            depositAccountUuid,
            detail: { remitToCard: true },
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const service = app.find(EntityService);
            const one = await service.findOne(entityDto.entityUuid);
            expect(one).toBeDefined();
            expect(one?.entityUuid).toBe(entityDto.entityUuid);
            expect(one?.debitCardAccountUuid).toBe(depositAccountUuid);
            expect(one?.remitToCard).toBe(true);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(7);
            expect(getMockLambdaPayload(lambdaService, 0).uri).toEqual('Entity.Created');
            expect(getMockLambdaPayload(lambdaService, 1)).toEqual({
              domicile: Domicile.AU,
              entityUuid: entityDto.entityUuid,
              requestPath: expect.any(String),
              ...defaultDeviceSettingsUpdatePayload,
            });
            expect(getMockLambdaPayload(lambdaService, 2).uri).toEqual('Site.Created');
            expect(getMockLambdaPayload(lambdaService, 3).uri).toEqual('Entity.Updated');
            expect(getMockLambdaPayload(lambdaService, 4)).toEqual({
              dto: {
                entityUuid: entityDto.entityUuid,
                paymentLimits: [
                  {
                    maximum: '5000000',
                    minimum: '100',
                    paymentType: 'CNP',
                  },
                  {
                    maximum: '5000000',
                    minimum: '100',
                    paymentType: 'CP',
                  },
                  {
                    maximum: '2500000',
                    minimum: '100',
                    paymentType: 'MOTO',
                  },
                  {
                    maximum: '2500000',
                    minimum: '100',
                    paymentType: 'CPOC',
                  },
                ],
              },
              uri: 'Entity.PaymentSettingsUpdated',
            });
            expect(getMockLambdaPayload(lambdaService, 6)).toEqual({
              uri: 'Entity.Updated',
              dto: {
                entityUuid: entityDto.entityUuid,
                debitCardAccountUuid: depositAccountUuid,
                remitToCard: true,
              },
            });
            done();
          },
        );
      });
    });

    it('should select deposit account', async () => {
      await new Promise<void>(async (done) => {
        const entityDto = createEntityCreatedDto();
        await createMockRegisteringIndividual(entityDto.entityUuid);
        await createEntity(entityDto);
        const dto = getDepositAccountDto();
        dto.entityUuid = entityDto.entityUuid;
        await createBankAccount(dto);
        mockLambdaInvoke(lambdaService);
        selectDepositAccountHandler(
          {
            id: entityDto.entityUuid,
            depositAccountUuid: dto.thirdPartyBankAccountUuid,
            detail: { remitToCard: false },
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const service = app.find(EntityService);
            const one = await service.findOne(entityDto.entityUuid);
            expect(one).toBeDefined();
            expect(one?.entityUuid).toBe(dto.entityUuid);
            expect(one?.depositAccountUuid).toBe(dto.thirdPartyBankAccountUuid);
            expect(one?.debitCardAccountUuid).toBeDefined();
            expect(one?.remitToCard).toBe(false);
            expect(lambdaService.lambda.send).toBeCalledTimes(1);
            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              uri: 'Entity.Updated',
              dto: {
                entityUuid: entityDto.entityUuid,
                depositAccountUuid: dto.thirdPartyBankAccountUuid,
                depositAccountBsb: dto.accountBsb,
                depositAccountNumber: dto.accountNumber,
                depositAccountName: dto.accountName,
                remitToCard: false,
              },
            });
            done();
          },
        );
      });
    });

    it('should select remit to card with attribution', async () => {
      await new Promise<void>(async (done) => {
        const entityDto = createEntityCreatedDto();
        await createMockRegisteringIndividual(entityDto.entityUuid);
        await createEntity(entityDto);
        const depositAccountUuid = uuidv4();
        const mutationAttribution = {
          userIdentifier: uuidv4(),
          tokenGrant: MutationAttributionTokenGrant.USER_PASS,
          userRole: MutationAttributionUserRole.ADMIN,
          platform: MutationAttributionPlatform.DASHBOARD,
          createdTimestamp: new Date().getTime(),
          sessionKey: uuidv4(),
          reason: uuidv4(),
        };
        mockLambdaInvoke(lambdaService);
        selectDepositAccountHandler(
          {
            id: entityDto.entityUuid,
            depositAccountUuid,
            detail: { remitToCard: true },
            headers: {
              'x-zeller-session-id': mutationAttribution.sessionKey,
              'x-zeller-platform': mutationAttribution.platform,
              'x-zeller-token-grant': mutationAttribution.tokenGrant,
              'x-zeller-user-identifier': mutationAttribution.userIdentifier,
              'x-zeller-mutation-created-timestamp': `${mutationAttribution.createdTimestamp}`,
              'x-zeller-user-role': mutationAttribution.userRole,
              'x-zeller-mutation-reason': mutationAttribution.reason,
            },
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const service = app.find(EntityService);
            const one = await service.findOne(entityDto.entityUuid);
            expect(one).toBeDefined();
            expect(one?.entityUuid).toBe(entityDto.entityUuid);
            expect(one?.depositAccountUuid).toBe(null);
            expect(one?.debitCardAccountUuid).toBe(depositAccountUuid);
            expect(one?.remitToCard).toBe(true);
            expect(lambdaService.lambda.send).toBeCalledTimes(1);
            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              uri: 'Entity.Updated',
              dto: {
                entityUuid: entityDto.entityUuid,
                debitCardAccountUuid: depositAccountUuid,
                remitToCard: true,
              },
              mutationAttribution,
            });
            done();
          },
        );
      });
    });

    it('should select remit to card for hubspot user when canUpdateSettlementAccount is false', async () => {
      await new Promise<void>(async (done) => {
        const entityDto = createEntityCreatedDto();
        await createMockRegisteringIndividual(entityDto.entityUuid);
        await createEntity(entityDto);
        await updateEntity({
          entityUuid: entityDto.entityUuid,
          accountStatus: { canUpdateSettlementAccount: false } as any,
        });
        const depositAccountUuid = uuidv4();
        const mutationAttribution = {
          userIdentifier: uuidv4(),
          tokenGrant: MutationAttributionTokenGrant.USER_PASS,
          userRole: 'HUBSPOT' as any,
          platform: MutationAttributionPlatform.SYSTEM,
          createdTimestamp: new Date().getTime(),
          sessionKey: uuidv4(),
          reason: uuidv4(),
        };
        mockLambdaInvoke(lambdaService);
        selectDepositAccountHandler(
          {
            id: entityDto.entityUuid,
            depositAccountUuid,
            detail: { remitToCard: true },
            headers: {
              'x-zeller-session-id': mutationAttribution.sessionKey,
              'x-zeller-platform': mutationAttribution.platform,
              'x-zeller-token-grant': mutationAttribution.tokenGrant,
              'x-zeller-user-identifier': mutationAttribution.userIdentifier,
              'x-zeller-mutation-created-timestamp': `${mutationAttribution.createdTimestamp}`,
              'x-zeller-user-role': mutationAttribution.userRole,
              'x-zeller-mutation-reason': mutationAttribution.reason,
            },
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const service = app.find(EntityService);
            const one = await service.findOne(entityDto.entityUuid);
            expect(one).toBeDefined();
            expect(one?.entityUuid).toBe(entityDto.entityUuid);

            expect(one?.depositAccountUuid).toBe(null);
            expect(one?.debitCardAccountUuid).toBe(depositAccountUuid);
            expect(one?.remitToCard).toBe(true);
            expect(lambdaService.lambda.send).toBeCalledTimes(1);
            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              uri: 'Entity.Updated',
              dto: {
                entityUuid: entityDto.entityUuid,
                debitCardAccountUuid: depositAccountUuid,
                remitToCard: true,
              },
              mutationAttribution,
            });
            done();
          },
        );
      });
    });

    it('should handle error if canUpdateSettlementAccount is false', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        await updateEntity({
          entityUuid: entity.entityUuid,
          accountStatus: { canUpdateSettlementAccount: false } as any,
        });
        const dto = getDepositAccountDto();
        await createBankAccount(dto);
        mockLambdaInvoke(lambdaService);
        selectDepositAccountHandler(
          {
            id: dto.entityUuid,
            depositAccountUuid: dto.thirdPartyBankAccountUuid,
          },
          context,
          async (error) => {
            expect(error).toBeDefined();
            expect((error as Error).message).toContain('[400] Cannot update deposit account');
            expect(lambdaService.lambda.send).toBeCalledTimes(0);
            done();
          },
        );
      });
    });

    it('should handle error if account does not belong to entity', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        const dto = getDepositAccountDto();
        await createBankAccount(dto);
        mockLambdaInvoke(lambdaService);
        selectDepositAccountHandler(
          {
            id: entity.entityUuid,
            depositAccountUuid: dto.thirdPartyBankAccountUuid,
          },
          context,
          async (error) => {
            expect(error).toBeDefined();
            expect((error as Error).message).toContain('[400] Cant find bank account');
            expect(lambdaService.lambda.send).toBeCalledTimes(0);
            done();
          },
        );
      });
    });

    it('should handle error if entity not found', async () => {
      await new Promise<void>(async (done) => {
        const dto = getDepositAccountDto();
        await createBankAccount(dto);
        mockLambdaInvoke(lambdaService);
        selectDepositAccountHandler(
          {
            id: dto.entityUuid,
            depositAccountUuid: dto.thirdPartyBankAccountUuid,
          },
          context,
          async (error) => {
            expect((error as Error).message).toContain('[400] Cannot update deposit account');
            expect(lambdaService.lambda.send).toBeCalledTimes(0);
            done();
          },
        );
      });
    });
  });

  describe('select stand in rules suite', () => {
    const getStandInRulesUpdatedDto = () => {
      return {
        entityUuid: uuidv4(),
        standInRules: [
          {
            operation: StandInOperation.EQUAL,
            field: StandInField.OFFLINE_AMOUNT,
            value: uuidv4(),
          },
          {
            operation: StandInOperation.BELOW,
            field: StandInField.TRANSACTION_AMOUNT,
            value: uuidv4(),
          },
        ],
      };
    };

    beforeEach(() => {
      mockLambdaInvoke(lambdaService);
    });

    it('should update StandInRules', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        const dto = getStandInRulesUpdatedDto();
        dto.entityUuid = entity.entityUuid;
        await createMockRegisteringIndividual(entity.entityUuid);
        mockLambdaInvoke(lambdaService);
        await createEntity(entity);

        updateStandInRulesHandler(
          {
            id: dto.entityUuid,
            detail: dto,
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(entity.entityUuid);
            expect(item.standInRules).toEqual(dto.standInRules);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(8);
            done();
          },
        );
      });
    });

    it('should emit to update stand in rules on devices', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        const device = createDeviceDto(entity.entityUuid);
        const dto = getStandInRulesUpdatedDto();
        dto.entityUuid = entity.entityUuid;
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        await createDevice(device);
        mockLambdaInvoke(lambdaService);
        updateStandInRulesHandler(
          {
            id: dto.entityUuid,
            detail: dto,
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            expect(lambdaService.lambda.send).toBeCalledTimes(2);
            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              entityUuid: entity.entityUuid,
              domicile: Domicile.AU,
              requestPath: expect.any(String),
              standInRules: [
                {
                  field: 'offline_amount',
                  operation: 'equal',
                  value: dto.standInRules[0].value,
                },
                {
                  field: 'transaction_amount',
                  operation: 'below',
                  value: dto.standInRules[1].value,
                },
              ],
            });
            expect(getMockLambdaPayload(lambdaService, 1)).toEqual({
              dto: {
                entityUuid: entity.entityUuid,
                standInRules: [
                  {
                    field: 'offline_amount',
                    operation: 'equal',
                    value: dto.standInRules[0].value,
                  },
                  {
                    field: 'transaction_amount',
                    operation: 'below',
                    value: dto.standInRules[1].value,
                  },
                ],
              },
              uri: 'Entity.StandInRulesUpdated',
            });
            done();
          },
        );
      });
    });

    it('should handle error if entity not found', async () => {
      await new Promise<void>(async (done) => {
        const dto = getStandInRulesUpdatedDto();
        updateStandInRulesHandler(
          {
            id: dto.entityUuid,
            detail: dto,
          },
          context,
          async (error) => {
            expect((error as Error).message).toContain('[400] Entity');
            expect(lambdaService.lambda.send).toBeCalledTimes(0);
            done();
          },
        );
      });
    });
  });

  describe('entity daily limits suite', () => {
    beforeEach(() => {
      mockLambdaInvoke(lambdaService);
    });

    it('should get daily limits config', async () => {
      const response = await new Promise((resolve) => {
        getEntityDailyLimitConfigHandler({}, context, (_, r) => {
          resolve(r);
        });
      });

      expect(response).toEqual({
        defaultLimit: { currency: 'AUD', value: '15000000' },
        maximumAllowedLimit: { currency: 'AUD', value: '300000000' },
      });
    });

    it('should update daily limits', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        const limit = { value: '25000000', currency: ISO4217.AUD };
        const zellerUserId = 'zellerUserId';
        const reason = 'risk update';
        const dailyLimits: EntityDailyLimits = {
          riskLimit: limit,
          merchantLimit: limit,
          appliedLimit: limit,
          riskLastUpdated: {
            zellerUserId,
            lastUpdatedAt: expect.any(String),
            reason,
          },
        };
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        mockLambdaInvoke(lambdaService);
        updateEntityDailyLimitHandler(
          {
            id: entity.entityUuid,
            detail: { limit, zellerUserId, reason },
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(entity.entityUuid);
            expect(item.dailyLimits).toEqual(dailyLimits);
            expect(lambdaService.lambda.send).toBeCalledTimes(1);
            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              uri: 'Entity.Updated',
              dto: {
                entityUuid: entity.entityUuid,
                dailyLimits,
              },
            });
            done();
          },
        );
      });
    });
  });

  describe('entity payment settings suite', () => {
    const getPaymentLimitsUpdatedDto = () => {
      return {
        entityUuid: uuidv4(),
        paymentLimits: [
          {
            paymentType: PaymentType.CNP,
            maximum: '300000',
            minimum: '300',
          },
          {
            paymentType: PaymentType.CP,
            maximum: '200000',
            minimum: '200',
          },
          {
            paymentType: PaymentType.MOTO,
            maximum: '100000',
            minimum: '100',
          },
          {
            paymentType: PaymentType.CPOC,
            maximum: '400000',
            minimum: '400',
          },
        ],
      };
    };

    beforeEach(() => {
      mockLambdaInvoke(lambdaService);
    });

    it('should update PaymentLimits', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        const dto = getPaymentLimitsUpdatedDto();
        dto.entityUuid = entity.entityUuid;
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        mockLambdaInvoke(lambdaService);
        updatePaymentSettingsHandler(
          {
            id: entity.entityUuid,
            detail: dto,
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const entityService = app.find(EntityService);
            const item = await entityService.findOne(entity.entityUuid);
            expect(item.paymentLimits).toEqual(dto.paymentLimits);
            expect(lambdaService.lambda.send).toBeCalledTimes(2);
            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              uri: 'Entity.PaymentSettingsUpdated',
              dto,
            });
            done();
          },
        );
      });
    });

    it('should update devices', async () => {
      await new Promise<void>(async (done, reject) => {
        const entity = createEntityCreatedDto();
        const device = createDeviceDto(entity.entityUuid);
        const dto = getPaymentLimitsUpdatedDto();
        dto.entityUuid = entity.entityUuid;
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        await createDevice(device);
        mockLambdaInvoke(lambdaService);
        updatePaymentSettingsHandler(
          {
            id: entity.entityUuid,
            detail: dto,
          },
          context,
          async (error) => {
            try {
              expect(error).toBeNull();
              expect(lambdaService.lambda.send).toHaveBeenCalledTimes(2);
              expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
                uri: 'Entity.PaymentSettingsUpdated',
                dto,
              });
              expect(getMockLambdaPayload(lambdaService, 1)).toEqual({
                ...dto,
                requestPath: expect.any(String),
                domicile: Domicile.AU,
              });
              done();
            } catch (e) {
              reject(e);
            }
          },
        );
      });
    });
  });

  describe('referred by test suite', () => {
    beforeEach(() => {
      mockLambdaInvoke(lambdaService);
    });

    it('Should add referred by', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        const referralEntity = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        await createMockRegisteringIndividual(referralEntity.entityUuid);
        await createEntity(entity);
        await createEntity(referralEntity);
        lambdaService.lambda.send.mockClear();
        addReferredByHandler(
          {
            id: entity.entityUuid,
            detail: { referredBy: referralEntity.entityUuid },
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            expect(lambdaService.lambda.send).toBeCalledTimes(1);
            const firstInvoke = lambdaService.lambda.send.mock.calls[0][0];
            expect(firstInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);

            const referralService = app.find(ReferralService);
            const referral = await referralService.findOneWithOption({
              referred: entity.entityUuid,
              domicile: Domicile.AU,
            });
            expect(referral).toEqual({
              entityUuid: referralEntity.entityUuid,
              referred: entity.entityUuid,
              createdAt: expect.any(Date),
              domicile: Domicile.AU,
            });
            done();
          },
        );
      });
    });

    it('Should add referred by with short id', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        const referralEntity = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        await createMockRegisteringIndividual(referralEntity.entityUuid);
        await createEntity(entity);
        await createEntity(referralEntity);
        const referrer: any = await getEntity(referralEntity.entityUuid);
        lambdaService.lambda.send.mockClear();
        addReferredByHandler(
          {
            id: entity.entityUuid,
            detail: { referredBy: referrer.shortId },
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            expect(lambdaService.lambda.send).toBeCalledTimes(1);
            const firstInvoke = lambdaService.lambda.send.mock.calls[0][0];
            expect(firstInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);

            const referralService = app.find(ReferralService);
            const referral = await referralService.findOneWithOption({
              referred: entity.entityUuid,
              domicile: Domicile.AU,
            });
            expect(referral).toEqual({
              entityUuid: referralEntity.entityUuid,
              referred: entity.entityUuid,
              createdAt: expect.any(Date),
              domicile: Domicile.AU,
            });
            done();
          },
        );
      });
    });

    it('Should throw error if no entity on add referred by', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        const referredBy = uuidv4();
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        lambdaService.lambda.send.mockClear();
        addReferredByHandler(
          {
            id: entity.entityUuid,
            detail: { referredBy },
          },
          context,
          async (error) => {
            expect(error).toBeDefined();
            expect((error as Error).message).toBe(`[400] No entity found with ${referredBy}`);
            expect(lambdaService.lambda.send).toBeCalledTimes(0);
            done();
          },
        );
      });
    });

    it('Should throw error if no entity short id on add referred by', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        lambdaService.lambda.send.mockClear();
        addReferredByHandler(
          {
            id: entity.entityUuid,
            detail: { referredBy: '111111' },
          },
          context,
          async (error) => {
            expect(error).toBeDefined();
            expect((error as Error).message).toBe(`[400] No entity found with 111111`);
            expect(lambdaService.lambda.send).toBeCalledTimes(0);
            done();
          },
        );
      });
    });

    it('Should trigger referral event if referral all ready exists', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        const referralEntity = createEntityCreatedDto();
        entity.referredBy = referralEntity.entityUuid;
        await createMockRegisteringIndividual(entity.entityUuid);
        await createMockRegisteringIndividual(referralEntity.entityUuid);
        await createEntity(referralEntity);
        await createEntity(entity);
        lambdaService.lambda.send.mockClear();
        addReferredByHandler(
          {
            id: entity.entityUuid,
            detail: { referredBy: referralEntity.entityUuid },
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            expect(lambdaService.lambda.send).toBeCalledTimes(1);
            const firstInvoke = lambdaService.lambda.send.mock.calls[0][0];
            expect(firstInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);

            const referralService = app.find(ReferralService);
            const referral = await referralService.findOneWithOption({
              referred: entity.entityUuid,
              domicile: Domicile.AU,
            });
            expect(referral).toEqual({
              entityUuid: referralEntity.entityUuid,
              referred: entity.entityUuid,
              createdAt: expect.any(Date),
              domicile: Domicile.AU,
            });
            done();
          },
        );
      });
    });

    it('Should throw error if entity is all ready referred', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        const referralEntity = createEntityCreatedDto();
        entity.referredBy = referralEntity.entityUuid;
        await createMockRegisteringIndividual(entity.entityUuid);
        await createMockRegisteringIndividual(referralEntity.entityUuid);
        await createEntity(referralEntity);
        await createEntity(entity);
        lambdaService.lambda.send.mockClear();
        addReferredByHandler(
          {
            id: entity.entityUuid,
            detail: { referredBy: uuidv4() },
          },
          context,
          async (error) => {
            expect(error).toBeDefined();
            expect((error as Error).message).toContain(
              `[400] Entity it all ready referred by ${referralEntity.entityUuid}`,
            );
            const { app } = context;
            expect(lambdaService.lambda.send).toBeCalledTimes(1);
            const firstInvoke = lambdaService.lambda.send.mock.calls[0][0];
            expect(firstInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);

            const referralService = app.find(ReferralService);
            const referral = await referralService.findOneWithOption({
              referred: entity.entityUuid,
              domicile: Domicile.AU,
            });
            expect(referral).toEqual({
              entityUuid: entity.referredBy,
              referred: entity.entityUuid,
              createdAt: expect.any(Date),
              domicile: Domicile.AU,
            });
            done();
          },
        );
      });
    });
  });

  describe('finalisedEntitiesExistWithAbnHandler test suite', () => {
    it('should throw error if entityUuid is undefined', async () => {
      const response = new Promise((_, reject) => {
        finalisedEntitiesExistWithAbnHandler({ detail: { abn: uuidv4() } }, context, async (err) => {
          if (err) {
            reject(err);
          }
        });
      });
      await expect(response).rejects.toThrowError();
    });

    it('should return false if there does not exist an entity with matching ABN', async () => {
      const response = await new Promise((resolve) => {
        finalisedEntitiesExistWithAbnHandler(
          { detail: { abn: uuidv4(), entityUuid: uuidv4() } },
          context,
          async (_, result) => {
            resolve(result);
          },
        );
      });
      expect(response).toBe(false);
    });

    it('should return false if there exists entity with matching ABN but not finalised onboarding status', async () => {
      const entity = createEntityCreatedDto();
      await createMockRegisteringIndividual(entity.entityUuid);
      const entityService: EntityService = await new Promise((resolve) => {
        createEntityHandler(
          {
            detail: entity,
          },
          context,
          async () => resolve(context.app.find(EntityService)),
        );
      });

      await entityService.update(entity.entityUuid, { onboardingStatus: OnboardingStatus.FINALISING_ONBOARDING });
      const response = await new Promise((resolve) => {
        finalisedEntitiesExistWithAbnHandler(
          { detail: { abn: entity.abn ?? '', entityUuid: uuidv4() } },
          context,
          async (_, result) => {
            resolve(result);
          },
        );
      });
      expect(response).toEqual(false);
    });

    it('should return false if entity itself is the matching ABN', async () => {
      const entity = createEntityCreatedDto();
      await createMockRegisteringIndividual(entity.entityUuid);
      const entityService: EntityService = await new Promise((resolve) => {
        createEntityHandler(
          {
            detail: entity,
          },
          context,
          async () => resolve(context.app.find(EntityService)),
        );
      });

      await entityService.update(entity.entityUuid, { onboardingStatus: OnboardingStatus.REVIEW });
      const response = await new Promise((resolve) => {
        finalisedEntitiesExistWithAbnHandler(
          { detail: { abn: entity.abn ?? '', entityUuid: entity.entityUuid } },
          context,
          async (_, result) => {
            resolve(result);
          },
        );
      });
      expect(response).toEqual(false);
    });

    it.each([
      OnboardingStatus.ONBOARDED,
      OnboardingStatus.RC_ONBOARDED,
      OnboardingStatus.REVIEW,
      OnboardingStatus.RC_REJECTED,
      OnboardingStatus.RC_ABANDONED,
      OnboardingStatus.RC_DEPLATFORMED,
    ])(
      'it should return true if there exists entity with matching ABN and finalised status %s',
      async (onboardingStatus) => {
        const entity = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        const entityService: EntityService = await new Promise((resolve) => {
          createEntityHandler(
            {
              detail: entity,
            },
            context,
            async () => resolve(context.app.find(EntityService)),
          );
        });

        await entityService.update(entity.entityUuid, {
          onboardingStatus,
        });
        const response = await new Promise((resolve) => {
          finalisedEntitiesExistWithAbnHandler(
            { detail: { abn: entity.abn ?? '', entityUuid: uuidv4() } },
            context,
            async (_, result) => {
              resolve(result);
            },
          );
        });
        expect(response).toEqual(true);
      },
    );

    const existingAbn = uuidv4();
    it.each([
      [
        'leading spaces',
        {
          abn: `   ${existingAbn}`,
        },
      ],
      [
        'leading spaces and trailing spaces',
        {
          abn: `   ${existingAbn}   `,
        },
      ],
      [
        'spaces inbetween ABN',
        {
          abn: `${existingAbn.slice(0, existingAbn.length / 2)}    ${existingAbn.slice(
            existingAbn.length / 2,
            existingAbn.length,
          )}`,
        },
      ],
    ])('it should be able to return true if search ABN has %s', async (_, { abn }) => {
      const entity = { ...createEntityCreatedDto(), abn: existingAbn };
      await createMockRegisteringIndividual(entity.entityUuid);
      const entityService: EntityService = await new Promise((resolve) => {
        createEntityHandler(
          {
            detail: entity,
          },
          context,
          async () => resolve(context.app.find(EntityService)),
        );
      });

      await entityService.update(entity.entityUuid, {
        onboardingStatus: OnboardingStatus.ONBOARDED,
      });
      const response = await new Promise((resolve) => {
        finalisedEntitiesExistWithAbnHandler({ detail: { abn, entityUuid: uuidv4() } }, context, async (__, result) => {
          resolve(result);
        });
      });
      expect(response).toEqual(true);
    });
  });

  describe('onboardingStatusUpdatedProjection', () => {
    describe('updating hasEverOnboarded', () => {
      const onboardStatus = [OnboardingStatus.ONBOARDED, OnboardingStatus.RC_ONBOARDED];

      it.each(onboardStatus)('should set DC and remitToCard details if status is %s', async (onboardingStatus) => {
        const entity = createEntityCreatedDto();
        const customer = createCustomerRequestedDto();
        customer.entityUuid = entity.entityUuid;
        customer.registeringIndividual = true;
        await createCustomer(customer);
        await createEntity(entity);
        const dto: EntityOnboardingStatusUpdatedEventDto = {
          entityUuid: entity.entityUuid,
          onboardingStatus,
        };
        await new Promise((resolve) => {
          projectionDomainEventHandler(
            {
              'detail-type': 'ams.Entity.OnboardingStatusUpdated',
              detail: dto,
            } as any,
            context,
            async () => {
              const { app } = context;
              const service = app.find(EntityService);
              const item = await service.findOne(dto.entityUuid);

              expect(item?.hasEverOnboarded).toBe(true);
              expect(item?.remitToCard).toBe(true);
              expect(item?.debitCardAccountUuid).toEqual('dc-id');
              resolve(null);
            },
          );
        });
      });

      it.each(onboardStatus)('should create personal and business contacts is %s', async (onboardingStatus) => {
        const entity = createEntityCreatedDto();
        const customer = createCustomerRequestedDto();
        customer.entityUuid = entity.entityUuid;
        customer.registeringIndividual = true;
        await createCustomer(customer);
        await createEntity(entity);
        const dto: EntityOnboardingStatusUpdatedEventDto = {
          entityUuid: entity.entityUuid,
          onboardingStatus,
        };
        await new Promise((resolve) => {
          projectionDomainEventHandler(
            {
              'detail-type': 'ams.Entity.OnboardingStatusUpdated',
              detail: dto,
            } as any,
            context,
            async () => {
              const { app } = context;
              const contactService = app.find(ContactService);
              const person = await contactService.getRepository().findOne({
                entityUuid: entity.entityUuid,
                contactType: ContactType.PERSON,
                isSelf: true,
              });
              expect(person).toEqual(
                expect.objectContaining({
                  contactUuid: person.contactUuid,
                  entityUuid: entity.entityUuid,
                  contactType: ContactType.PERSON,
                  isSelf: true,
                  firstName: customer.firstname,
                  lastName: customer.lastname,
                  email: customer.email,
                }),
              );
              const business = await contactService.getRepository().findOne({
                entityUuid: entity.entityUuid,
                contactType: ContactType.BUSINESS,
                isSelf: true,
              });
              expect(business).toEqual(
                expect.objectContaining({
                  contactUuid: business.contactUuid,
                  entityUuid: entity.entityUuid,
                  contactType: ContactType.BUSINESS,
                  email: customer.email,
                  isSelf: true,
                  businessName: entity.name,
                  abn: entity.abn,
                  acn: entity.acn,
                }),
              );
              resolve(null);
            },
          );
        });
      });

      it.each(Object.values(OnboardingStatus).filter((status) => !onboardStatus.includes(status)))(
        'it should return false if %s',
        async (onboardingStatus) => {
          const entity = createEntityCreatedDto();
          await createMockRegisteringIndividual(entity.entityUuid);
          const entityService: EntityService = await new Promise((resolve) => {
            createEntityHandler(
              {
                detail: entity,
              },
              context,
              async () => resolve(context.app.find(EntityService)),
            );
          });
          const customer = createCustomerRequestedDto();
          customer.entityUuid = entity.entityUuid;
          customer.registeringIndividual = true;
          await createCustomer(customer);

          await entityService.update(entity.entityUuid, {
            onboardingStatus,
          });

          const dto: EntityOnboardingStatusUpdatedEventDto = {
            entityUuid: entity.entityUuid,
            onboardingStatus,
          };

          await new Promise((resolve) => {
            projectionDomainEventHandler(
              {
                'detail-type': 'ams.Entity.OnboardingStatusUpdated',
                detail: dto,
              } as any,
              context,
              async () => {
                const item = await entityService.findOne(dto.entityUuid);

                expect(item?.hasEverOnboarded).toBe(false);
                expect(item?.remitToCard).toBe(null);
                expect(item?.debitCardAccountUuid).toBe(null);
                resolve(null);
              },
            );
          });
        },
      );
    });
  });

  describe('initialize entity canAcquireAmex test suite', () => {
    it('should initialize entity canAcquireAmex', async () => {
      const entity = createEntityCreatedDto();
      const customer = createCustomerRequestedDto();
      customer.entityUuid = entity.entityUuid;
      customer.registeringIndividual = true;
      await createCustomer(customer);
      await createEntity(entity);

      const response = await new Promise((resolve) => {
        initializeEntityCanAcquireAmexFlagHandler(
          {
            id: entity.entityUuid,
            detail: { canAcquireAmex: true },
          },
          context,
          (err) => {
            resolve(err);
          },
        );
      });

      expect(response).toBeNull();
    });

    it('should throw error if entity not found', async () => {
      const entity = createEntityCreatedDto();

      const response = await new Promise((resolve) => {
        initializeEntityCanAcquireAmexFlagHandler(
          {
            id: entity.entityUuid,
            detail: { canAcquireAmex: true },
          },
          context,
          async (err) => {
            resolve(err);
          },
        );
      });

      expect(response).toBeDefined();
      expect((response as Error).message).toContain(`[400]`);
    });
  });

  describe('updatePrimaryAccountHolderHandler', () => {
    it('should not updated entity if requestCustomer is already primaryAccountHolder', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        const customer = createCustomerRequestedDto();
        customer.entityUuid = entity.entityUuid;
        customer.registeringIndividual = true;
        await createCustomer(customer);
        await createEntity(entity);
        mockLambdaInvoke(lambdaService);
        updatePrimaryAccountHolderHandler(
          {
            id: entity.entityUuid,
            customerUuid: customer.customerUuid,
          },
          context,
          async () => {
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(0);
            done();
          },
        );
      });
    });

    it('should be able to update primaryAccountHolder', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        const customer = await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        const newCustomer = createCustomerRequestedDto();
        newCustomer.entityUuid = entity.entityUuid;
        await createCustomer(newCustomer);
        mockLambdaInvoke(lambdaService);
        updatePrimaryAccountHolderHandler(
          {
            id: newCustomer.entityUuid,
            customerUuid: newCustomer.customerUuid,
          },
          context,
          async () => {
            const { app } = context;
            const service = app.find(EntityService);
            const updatedEntity = await service.findOne(newCustomer.entityUuid);
            expect(updatedEntity?.primaryAccountHolder).toBe(newCustomer.customerUuid);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(1);
            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              uri: 'Entity.PrimaryAccountHolderChanged',
              dto: {
                entityUuid: newCustomer.entityUuid,
                primaryAccountHolder: newCustomer.customerUuid,
                previousPrimaryAccountHolder: customer.customerUuid,
              },
            });
            done();
          },
        );
      });
    });
  });

  describe('triggerAmexOnBoardingEventHandler', () => {
    it('should be able to call the cqrs with the correct data', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        const customer = createCustomerRequestedDto();
        customer.entityUuid = entity.entityUuid;
        customer.registeringIndividual = true;
        await createCustomer(customer);
        await createEntity(entity);

        mockLambdaInvoke(lambdaService);
        triggerAmexOnBoardingEventHandler(
          {
            entityUuid: entity.entityUuid,
          },
          context,
          async () => {
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(1);
            expect(getMockLambdaPayload(lambdaService, 0).uri).toEqual('Entity.AmexMerchantSubmissionRequested');
            done();
          },
        );
      });
    });

    it('should not be able to call lambda if entity is not exists', async () => {
      await new Promise<void>(async (done) => {
        const entityUuid = uuidv4();

        mockLambdaInvoke(lambdaService);
        triggerAmexOnBoardingEventHandler(
          {
            entityUuid,
          },
          context,
          async () => {
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(0);
            done();
          },
        );
      });
    });
  });

  describe('attachEntityDomicileAndCurrency', () => {
    it('should be able to attach domicile and currency', async () => {
      await new Promise<void>(async (done) => {
        const entity = createEntityCreatedDto();
        await createMockRegisteringIndividual(entity.entityUuid);
        await createEntity(entity);
        const newCustomer = createCustomerRequestedDto();
        newCustomer.entityUuid = entity.entityUuid;
        await createCustomer(newCustomer);
        mockLambdaInvoke(lambdaService);
        attachEntityDomicileAndCurrencyHandler(
          {
            id: newCustomer.entityUuid,
            detail: { domicile: Domicile.AU },
          },
          context,
          async () => {
            const { app } = context;
            const service = app.find(EntityService);
            const updatedEntity = await service.findOne(newCustomer.entityUuid);
            expect(updatedEntity?.currency).toBe('AUD');
            expect(updatedEntity?.domicile).toBe('AUS');
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(1);
            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              uri: 'Entity.DomicileCurrencyAttached',
              dto: {
                entityUuid: newCustomer.entityUuid,
                domicile: Domicile.AU,
                currency: 'AUD',
              },
            });
            done();
          },
        );
      });
    });
  });
});
