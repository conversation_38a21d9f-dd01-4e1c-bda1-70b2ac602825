import { LambdaService } from '@npco/component-bff-core/dist/lambda';
import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { debug, error, info } from '@npco/component-bff-core/dist/utils/logger';
import { CustomerRole, EntityType, Status } from '@npco/component-dto-core';
import { CustomerMarketingUpdatedEventDto, KYCStatus } from '@npco/component-dto-customer';
import type { CustomerPermissions, CustomerUpdatedEventDto } from '@npco/component-dto-customer';
import type { CustomerCreateRequestedEventDto } from '@npco/component-dto-customer/dist/customerCreateRequestedEventDto';
import { CustomerEntityInvitedEventDto } from '@npco/component-dto-customer/dist/customerEntityInvitedEventDto';
import { CustomerEntityLinkedEventDto } from '@npco/component-dto-customer/dist/customerEntityLinkedEventDto';
import { CustomerEntityUnlinkedEventDto } from '@npco/component-dto-customer/dist/customerEntityUnlinkedEventDto';

import { Injectable } from '@nestjs/common';
import type { EntityManager, QueryRunner, Repository } from 'typeorm';
import { IsNull, Not, getRepository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import { EnvironmentService } from '../../config';
import { Customer, Entity } from '../../entities';
import { CustomerEntity } from '../../entities/customerEntity';
import { BaseService } from '../base/baseService';
import { BadRequestError, ServerError } from '../base/error';
import { getDeleteCustomerEngine } from '../customer/rules';
import { DomicileService } from '../domicile/domicileService';
import { EventsEmitterService } from '../events/eventsEmitterService';

type PartialCustomerEntity = Partial<CustomerEntity>;
@Injectable()
export class CustomerEntityService extends BaseService<CustomerEntity> {
  static readonly CUSTOMER_ENTITY_FIELDS = [
    'companyProfileData',
    'role',
    'director',
    'secretary',
    'ceo',
    'beneficialOwner',
    'beneficialOwnerAlt',
    'beneficiary',
    'partner',
    'trustee',
    'settlor',
    'generalContact',
    'financialContact',
    'registeringIndividual',
    'shareholder',
    'chair',
    'treasurer',
    'governmentRole',
    'invitationPending',
    'invitedBy',
    'permissions',
    'marketingModalSeenList',
    'marketingModalName',
  ];

  constructor(
    protected readonly envService: EnvironmentService,
    protected readonly lambdaService: LambdaService,
    private readonly emitterService: EventsEmitterService,
  ) {
    super('Customers', 'customerUuid', lambdaService, envService);
  }

  buildCustomerModel = (
    customerEntityUuid: string,
    entityUuid: string,
    event: CustomerCreateRequestedEventDto | CustomerUpdatedEventDto,
    domicile: Domicile,
  ): CustomerEntity => ({
    customerEntityUuid,
    customerUuid: event.customerUuid,
    entityUuid,
    role: event.role?.toString(),
    director: event.director,
    secretary: event.secretary,
    ceo: event.ceo,
    beneficialOwner: event.beneficialOwner,
    beneficialOwnerAlt: event.beneficialOwnerAlt,
    beneficiary: event.beneficiary,
    shareholder: event.shareholder,
    partner: event.partner,
    trustee: event.trustee,
    settlor: event.settlor,
    generalContact: event.generalContact,
    financialContact: event.financialContact,
    registeringIndividual: 'registeringIndividual' in event ? event.registeringIndividual : undefined,
    chair: event.chair,
    treasurer: event.treasurer,
    governmentRole: event.governmentRole,
    invitationPending: 'isInvitationPending' in event ? event.isInvitationPending : undefined,
    invitedBy:
      'invitedBy' in event && event.invitedBy?.customerUuid
        ? {
            customerUuid: event.invitedBy.customerUuid,
          }
        : undefined,
    companyProfileData: event.companyProfileData,
    permissions: event.permissions,
    domicile,
  });

  prepareAndInsertCustomerEntity = async (
    customerEntity: CustomerEntity,
    queryRunner: QueryRunner,
  ): Promise<CustomerEntity> => {
    const customerEntityModel = { ...customerEntity };
    customerEntityModel.createdTime = `${new Date().getTime()}`;
    customerEntityModel.status = Status.ACTIVE;
    customerEntityModel.domicile = DomicileService.getInstance().getDomicile();

    info(
      `Creating customer entity record for customerUuid: ${customerEntityModel.customerUuid}, entityUuid: ${customerEntityModel.entityUuid}`,
    );
    const existing = await this.findCustomerEntityWithQueryRunner(
      queryRunner,
      customerEntityModel.customerUuid,
      customerEntityModel.entityUuid,
    );
    if (existing) {
      throw new BadRequestError(
        `Customer entity already exists for customerUuid: ${customerEntityModel.customerUuid}, entityUuid: ${customerEntityModel.entityUuid}`,
      );
    }
    const repository = queryRunner.manager.getRepository(CustomerEntity);
    await repository.insert(customerEntityModel);

    return customerEntityModel;
  };

  createRegisteringIndividualCustomerEntity = async (customerEntity: CustomerEntity, queryRunner: QueryRunner) => {
    const customerEntityModel = await this.prepareAndInsertCustomerEntity(customerEntity, queryRunner);
    const linkedEvent = new CustomerEntityLinkedEventDto({
      ...customerEntity,
      role: customerEntityModel.role as CustomerRole,
    });
    info(
      `[Create Entity] Emitting CustomerEntityLinkedEvent for customerUuid: ${
        customerEntityModel.customerUuid
      }, linkedEvent: ${JSON.stringify(linkedEvent)}`,
    );
    this.emitterService.addEventToEmit<CustomerEntityLinkedEventDto>(
      customerEntityModel.customerUuid,
      this.envService.cqrsCmds.CustomerEntity.Linked,
      linkedEvent,
    );
    return customerEntityModel;
  };

  createCustomerEntity = async (
    customer: Customer,
    customerEntity: CustomerEntity,
    queryRunner: QueryRunner,
    assignedSites?: string[],
  ) => {
    if (!customerEntity.entityUuid) {
      throw new BadRequestError(`Customer entity doesnt have entity uuid: ${customer.customerUuid}`);
    }
    const customerEntityModel = await this.prepareAndInsertCustomerEntity(customerEntity, queryRunner);

    if (
      customer.email &&
      customerEntityModel.role &&
      customerEntityModel.invitedBy &&
      customerEntityModel.invitationPending
    ) {
      const event = new CustomerEntityInvitedEventDto({
        customerUuid: customerEntityModel.customerUuid,
        entityUuid: customerEntityModel.entityUuid,
        email: customer.email,
        role: customerEntityModel.role as CustomerRole,
        isInvitationPending: customerEntityModel.invitationPending,
        invitedBy: customerEntityModel.invitedBy,
        phone: customer.phone,
        firstname: customer.firstname,
        lastname: customer.lastname,
        middlename: customer.middlename,
      });
      this.emitterService.addEventToEmit<CustomerEntityInvitedEventDto>(
        customerEntityModel.customerUuid,
        this.envService.cqrsCmds.CustomerEntity.Invited,
        event,
      );
    }

    // MultiEntityToDo: only emit linked event when accepting the invitation: if (!customerEntity.invitedBy) {
    const linkedEvent = new CustomerEntityLinkedEventDto({
      ...customerEntity,
      sites: assignedSites,
      role: customerEntityModel.role as CustomerRole,
    });
    this.emitterService.addEventToEmit<CustomerEntityLinkedEventDto>(
      customerEntityModel.customerUuid,
      this.envService.cqrsCmds.CustomerEntity.Linked,
      linkedEvent,
    );
    return customerEntityModel;
  };

  updateCustomerEntity = async (
    customer: CustomerUpdatedEventDto,
    customerEntities: CustomerEntity[],
    queryRunner: QueryRunner,
    customerKycStatus?: KYCStatus,
  ) => {
    const updatedEvent = { ...customer };
    const domicile = this.getDomicile();
    if (this.kycStatusManagerRoleChange(updatedEvent, customerKycStatus)) {
      await queryRunner.manager
        .createQueryBuilder()
        .update(CustomerEntity)
        .set({ role: CustomerRole.MANAGER })
        .where(
          `customerUuid = :customerUuid AND status != :status AND (registeringIndividual = false or registeringIndividual is null) ${
            domicile ? 'AND domicile = :domicile' : ''
          }`,
          {
            customerUuid: updatedEvent.customerUuid,
            status: Status.DELETED,
            ...(domicile ? { domicile } : {}),
          },
        )
        .execute();

      if (!updatedEvent.entityUuid) {
        return updatedEvent;
      }
    }

    const customerEntityParital = this.getCustomerEntityPartial(updatedEvent);
    if (!customerEntityParital || Object.keys(customerEntityParital).length === 0) {
      return updatedEvent;
    }
    if (!updatedEvent.entityUuid) {
      throw new BadRequestError(`Customer doesnt have entity uuid: ${updatedEvent.customerUuid}`);
    }
    const entityUuid = updatedEvent.entityUuid;

    const existing = customerEntities.find((entity) => entity.entityUuid === entityUuid);
    if (!existing) {
      info(`Customer entity not found for customerUuid: ${updatedEvent.customerUuid}, entityUuid: ${entityUuid}`);
      return updatedEvent;
    }
    await this.checkPAHRole(updatedEvent, existing, queryRunner);
    const customerEntityModel = this.buildCustomerModel(
      existing.customerEntityUuid,
      entityUuid,
      updatedEvent,
      existing.domicile as Domicile,
    );
    customerEntityModel.updatedTime = `${new Date().getTime()}`;
    const roleChanged = this.customerRoleChange(updatedEvent, existing);
    const customerEntity = this.mergedObject(customerEntityModel, existing);
    if (roleChanged) {
      customerEntity.permissions = this.getDefaultPermissionFields(updatedEvent);
      updatedEvent.permissions = customerEntity.permissions;
    }
    if (updatedEvent.marketingModalName) {
      const marketingModalSeenList = customerEntity.marketingModalSeenList ?? [];
      marketingModalSeenList.push(updatedEvent.marketingModalName);
      this.addAllUpdateCustomerEntityMarketingModalSeenList(
        customerEntities.map((entity) => entity.entityUuid),
        customer.customerUuid,
        marketingModalSeenList,
      );
      const marketingModalSeenListOutput = await this.bulkUpdateCustomerEntityMarketingModalSeenList(
        queryRunner,
        customer.customerUuid,
        marketingModalSeenList,
      );
      debug(
        `update customer entity marketing modal seen list response ${JSON.stringify(marketingModalSeenListOutput)}`,
      );
      customerEntity.marketingModalSeenList = marketingModalSeenList;
      delete updatedEvent.marketingModalName;
    }
    debug(`update customer entity ${JSON.stringify(customerEntity)}`);
    const output = await this.updateCustomerEntityWithLockCheck(queryRunner, customerEntity);
    debug(`update customer entity response ${JSON.stringify(output)}`);
    return updatedEvent;
  };

  getCustomerEntity = async (
    customerUuid: string,
    entityUuid?: string,
  ): Promise<CustomerCreateRequestedEventDto | CustomerEntityInvitedEventDto> => {
    const customerEntity = await this.findOneWithOption({
      where: { customerUuid, entityUuid, status: Not(Status.DELETED), domicile: this.getDomicile() },
    });
    const customer = await getRepository(Customer).findOne({ where: { customerUuid, domicile: this.getDomicile() } });
    if (!customerEntity || !customer) {
      throw new BadRequestError(
        `Customer entity not found for customerUuid: ${customerUuid}, entityUuid: ${entityUuid}`,
      );
    }
    return {
      customerUuid: customerEntity.customerUuid,
      entityUuid: customerEntity.entityUuid,
      defaultEntityUuid: customer.defaultEntityUuid,
      status: customerEntity.status,
      identityUserId: customer.identityUserId,
      firstname: customer.firstname,
      middlename: customer.middlename,
      lastname: customer.lastname,
      nickname: customer.nickname,
      address: customer.address,
      dob: customer.dob,
      email: customer.email,
      emailVerified: customer.emailVerified,
      phone: customer.phone,
      phoneVerified: customer.phoneVerified,
      role: CustomerRole[customerEntity.role as keyof typeof CustomerRole],
      director: customerEntity.director,
      secretary: customerEntity.secretary,
      ceo: customerEntity.ceo,
      beneficialOwner: customerEntity.beneficialOwner,
      shareholder: customerEntity.shareholder,
      beneficialOwnerAlt: customerEntity.beneficialOwnerAlt,
      beneficiary: customerEntity.beneficiary,
      partner: customerEntity.partner,
      trustee: customerEntity.trustee,
      settlor: customerEntity.settlor,
      generalContact: customerEntity.generalContact,
      financialContact: customerEntity.financialContact,
      registeringIndividual: !!customerEntity.registeringIndividual,
      permissions: customerEntity.permissions,
      invitedBy: customerEntity.invitedBy,
      isInvitationPending: customerEntity.invitationPending,
      companyProfileData: customerEntity.companyProfileData,
      governmentRole: customerEntity.governmentRole,
      type: EntityType[customer.type as keyof typeof EntityType],
      companyTrustName: customer.companyTrustName,
      abn: customer.abn,
      acn: customer.acn,
      documents: customer.documents,
      chair: customerEntity.chair,
      treasurer: customerEntity.treasurer,
      idv: customer.idv,
      safeharbour: customer.safeharbour,
      screening: customer.screening,
      productTourStatus: customer.productTourStatus,
      icon: customer.icon ?? undefined,
      kyc: customer.kyc,
      idvAttempts: customer.idvAttempts ?? undefined,
      safeHarbourVerification: customer.safeHarbourVerification ?? undefined,
    };
  };

  deleteCustomerFromEntity = async (customerUuid: string, customerEntity: CustomerEntity, queryRunner: QueryRunner) => {
    info(`Deleting customer entity record for customerUuid: ${customerUuid}, entityUuid: ${customerEntity.entityUuid}`);
    const engine = getDeleteCustomerEngine();
    const { events } = await engine.run(customerEntity);
    if (events.length > 0) {
      throw new BadRequestError(`Customer ${customerUuid} is not allowed to be deleted.`);
    }
    await queryRunner.manager
      .getRepository(CustomerEntity)
      .update(
        { customerUuid, entityUuid: customerEntity.entityUuid, domicile: this.getDomicile() },
        { status: Status.DELETED, updatedTime: `${new Date().getTime()}` },
      );
    const event = new CustomerEntityUnlinkedEventDto({
      customerUuid,
      entityUuid: customerEntity.entityUuid,
    });
    await this.callCommandHandler<CustomerEntityUnlinkedEventDto>(
      customerUuid,
      this.envService.cqrsCmds.CustomerEntity.Unlinked,
      event,
    );
  };

  getCustomerEntityPartial = (dto: CustomerUpdatedEventDto) => {
    const customerEntityDto: PartialCustomerEntity = CustomerEntityService.CUSTOMER_ENTITY_FIELDS.reduce(
      (prev: any, current) => {
        const accumulator = prev;
        const value = dto[current as keyof CustomerUpdatedEventDto];
        if (value !== undefined) {
          accumulator[current] = value;
        }
        return accumulator;
      },
      {},
    );
    if ('isInvitationPending' in dto && dto.isInvitationPending !== undefined) {
      customerEntityDto.invitationPending = dto.isInvitationPending;
    }
    return customerEntityDto;
  };

  getRepository = () => getRepository(CustomerEntity);

  isLockUpdateRequired = (dto: any) => {
    const jsonFields = ['permissions'];
    return Object.keys(dto).some((key) => jsonFields.includes(key));
  };

  updateDBEntity = (mgr: EntityManager, aggregateField: Record<string, any>, object: CustomerEntity) =>
    mgr.update(CustomerEntity, aggregateField, object);

  findCustomerEntityWithQueryRunner = async (queryRunner: QueryRunner, customerUuid: string, entityUuid?: string) => {
    const repository = queryRunner.manager.getRepository(CustomerEntity);
    return repository.findOne({
      where: [
        {
          customerUuid,
          entityUuid,
          status: Not(Status.DELETED),
          domicile: this.getDomicile(),
        },
      ],
    });
  };

  findCustomerEntity = async (customerUuid: string, entityUuid?: string) => {
    const domicile = this.getDomicile();
    return this.findOneWithOption({
      where: { customerUuid, entityUuid, status: Not(Status.DELETED), ...(domicile ? { domicile } : {}) },
    });
  };

  findAllEntitiesWithQueryRunner = async (queryRunner: QueryRunner, customerUuid: string) => {
    const repository = queryRunner.manager.getRepository(CustomerEntity);
    return repository.find({
      where: [
        {
          customerUuid,
          status: Not(Status.DELETED),
          domicile: this.getDomicile(),
        },
      ],
    });
  };

  findAllEntities = async (customerUuid: string) => {
    const domicile = this.getDomicile();
    return this.find({
      where: [
        {
          customerUuid,
          status: Not(Status.DELETED),
          ...(domicile ? { domicile } : {}),
        },
        {
          customerUuid,
          status: IsNull(),
          ...(domicile ? { domicile } : {}),
        },
      ],
    });
  };

  findAllCustomers = async (entityUuid: string) =>
    this.find({
      where: [
        {
          entityUuid,
          status: Not(Status.DELETED),
          domicile: this.getDomicile(),
        },
      ],
    });

  getRegisteringIndividualByEntityUuid = async (entityUuid: string): Promise<Customer | undefined> =>
    this.findOneWithOption({
      where: { entityUuid, registeringIndividual: true },
    });

  customerRoleChange = (event: CustomerUpdatedEventDto, customerEntity?: CustomerEntity): boolean => {
    return !!event.role && event.role !== customerEntity?.role;
  };

  isCustomerEntityUpdate = (event: CustomerUpdatedEventDto): boolean =>
    CustomerEntityService.CUSTOMER_ENTITY_FIELDS.some((field) => field in event);

  findEntityByUuid = async (entityUuid: string, queryRunner: QueryRunner): Promise<Entity | null> => {
    const domicile = this.getDomicile();
    const entity = await queryRunner.manager
      .getRepository(Entity)
      .findOne({ where: { entityUuid, ...(domicile ? { domicile } : {}) } });
    if (!entity) {
      info(`Entity not found for entityUuid: ${entityUuid}`);
      return null;
    }
    return entity;
  };

  creatingCustomerEntity = async (
    event: CustomerCreateRequestedEventDto,
    queryRunner: QueryRunner,
    domicile: Domicile,
  ) => {
    try {
      const permissions = this.getDefaultPermissionFields(event);
      debug(`[Create Entity] Default permission fields: ${JSON.stringify(permissions)}`);
      const eventWithPermissions = { ...event, permissions };
      const customerEntityModel: CustomerEntity = this.buildCustomerModel(
        uuidv4(),
        event.entityUuid,
        eventWithPermissions,
        domicile,
      );
      debug(
        `[Create Entity] Customer entity model for entityUuid ${event.entityUuid}: ${JSON.stringify(
          customerEntityModel,
        )}`,
      );
      await this.createRegisteringIndividualCustomerEntity(customerEntityModel, queryRunner);
    } catch (err) {
      error(`[Create Entity] Error creating customer entity: ${err}`);
      throw new ServerError(`Failed to create customer entity: ${err}`);
    }
  };

  readonly kycStatusManagerRoleChange = (
    event: CustomerUpdatedEventDto,
    customerKycStatus: KYCStatus | undefined,
  ): boolean => {
    return event.kyc?.status === KYCStatus.RC_ABANDONED && customerKycStatus === KYCStatus.REVIEW;
  };

  readonly getDefaultPermissionFields = (
    inputEvent: CustomerCreateRequestedEventDto | CustomerUpdatedEventDto,
  ): CustomerPermissions => {
    const { role } = inputEvent;
    const defaultPermissions =
      role === CustomerRole.ADMIN
        ? {
            allowItemManagement: true,
            allowDiscountManagement: true,
            allowZellerInvoices: true,
            allowXeroPaymentServices: true,
          }
        : {
            allowItemManagement: false,
            allowDiscountManagement: false,
            allowZellerInvoices: false,
            allowXeroPaymentServices: false,
          };

    return {
      ...defaultPermissions,
      ...inputEvent.permissions,
    };
  };

  public getRepositoryFromManager(manager: EntityManager): Repository<CustomerEntity> {
    return manager.getRepository(CustomerEntity);
  }

  private readonly updateCustomerEntityWithLockCheck = async (queryRunner: QueryRunner, customer: CustomerEntity) => {
    const domicile = this.getDomicile();
    if (this.isLockUpdateRequired(customer)) {
      await this.updateDBEntity(
        queryRunner.manager,
        { customerEntityUuid: customer.customerEntityUuid, ...(domicile ? { domicile } : {}) },
        customer,
      );
    } else {
      await queryRunner.manager.getRepository(CustomerEntity).save(customer);
    }
  };

  private readonly addAllUpdateCustomerEntityMarketingModalSeenList = async (
    entities: string[],
    customerUuid: string,
    marketingModalSeenList: string[],
  ) => {
    entities.forEach((entityUuid) => {
      this.emitterService.addEventToEmit<CustomerMarketingUpdatedEventDto>(
        customerUuid,
        this.envService.cqrsCmds.Customer.MarketingUpdated,
        new CustomerMarketingUpdatedEventDto({
          marketingModalSeenList,
          entityUuid,
          customerUuid,
        }),
      );
    });
  };

  private readonly bulkUpdateCustomerEntityMarketingModalSeenList = async (
    queryRunner: QueryRunner,
    customerUuid: string,
    marketingModalSeenList: string[],
  ) => {
    const domicile = this.getDomicile();
    return queryRunner.manager.getRepository(CustomerEntity).update(
      {
        customerUuid,
        status: Not(Status.DELETED),
        ...(domicile ? { domicile } : {}),
      },
      { marketingModalSeenList },
    );
  };

  private readonly checkPAHRole = async (
    event: CustomerUpdatedEventDto,
    customerEntity: CustomerEntity,
    queryRunner: QueryRunner,
  ) => {
    debug(`checkPAHRole: changing role from ${customerEntity.role} to ${event.role}`);
    if (event.role && customerEntity.role !== event.role) {
      const entity = await this.findEntityByUuid(event.entityUuid as string, queryRunner);
      if (!entity) {
        throw new BadRequestError(`Entity does not exists for entityUuid: ${event.entityUuid!}`);
      }
      if (entity.primaryAccountHolder === customerEntity.customerUuid && event.role !== CustomerRole.ADMIN) {
        throw new BadRequestError('Primary Account Holder role cannot be changed.');
      }
    }
  };
}
