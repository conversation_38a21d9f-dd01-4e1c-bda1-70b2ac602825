import { LambdaClient } from '@aws-sdk/client-lambda';
import { Client } from 'pg';
import { anything, instance, mock, verify, when } from 'ts-mockito';

import {
  checkCustomerEntityQuery,
  checkCustomerQuery,
  checkEntityQuery,
  linkCustomers,
  validateCustomerEntity,
} from './migrate';

describe('test migrate', () => {
  let mockClient = mock(Client);
  let mockLambda = mock(LambdaClient);

  beforeEach(() => {
    mockClient = mock(Client);
    mockLambda = mock(LambdaClient);
  });

  it('should validate right customer entity', async () => {
    when(mockClient.query(checkCustomerQuery, anything())).thenResolve({
      rows: [{ customerUuid: '001' }],
    } as any);

    when(mockClient.query(checkCustomerEntityQuery, anything())).thenResolve({ rows: [] } as any);
    when(mockClient.query(checkEntityQuery, anything())).thenResolve({ rows: [{ entityUuid: '001' }] } as any);

    const client = instance(mockClient);
    await validateCustomerEntity(client, '001', {
      entityUuid: '123',
      role: 'AD<PERSON>N',
      director: true,
      secretary: true,
      ceo: true,
      beneficialOwner: true,
      beneficialOwnerAlt: true,
      beneficiary: true,
      partner: true,
      trustee: true,
      settlor: true,
      generalContact: true,
      financialContact: true,
      registeringIndividual: true,
      shareholder: true,
      chair: true,
      treasurer: true,
      governmentRole: 'role',
    });
    verify(mockClient.query(anything(), anything())).atLeast(3);
  });

  it('should throw error if customer entity already exists', async () => {
    when(mockClient.query(anything(), anything())).thenResolve({ rows: [{}] } as any);
    await expect(
      validateCustomerEntity(instance(mockClient), '001', {
        entityUuid: '',
        role: 'ADMIN',
      }),
    ).rejects.toThrow(Error);
  });

  it('should throw error if customer doesnt have role', async () => {
    await expect(validateCustomerEntity(instance(mockClient), '001', { entityUuid: '' } as any)).rejects.toThrow(Error);
  });

  it('should throw error if customer doesnt have the right role value', async () => {
    await expect(validateCustomerEntity(instance(mockClient), '001', { entityUuid: '', role: 'READ' })).rejects.toThrow(
      Error,
    );
  });

  it('should throw error if customer doesnt have entity uuid', async () => {
    await expect(validateCustomerEntity(instance(mockClient), '001', { role: 'MANAGER' } as any)).rejects.toThrow(
      Error,
    );
  });

  it('should throw error if customer doesnt have customer uuid', async () => {
    await expect(
      validateCustomerEntity(instance(mockClient), '', { role: 'MANAGER', entityUuid: '001' }),
    ).rejects.toThrow(Error);
  });

  it('should use false as default value', async () => {
    when(mockClient.query(checkEntityQuery, anything())).thenResolve({ rows: [{ entityUuid: '001' }] } as any);
    when(mockClient.query(checkCustomerQuery, anything())).thenResolve({
      rows: [{ customerUuid: '001' }],
    } as any);

    when(mockClient.query(checkCustomerEntityQuery, anything())).thenResolve({ rows: [] } as any);
    const client = instance(mockClient);
    const lambda = instance(mockLambda);
    await linkCustomers(lambda, client, [
      {
        customerUuid: '123',
        entities: [
          {
            entityUuid: '001',
            role: 'MANAGER',
          },
        ],
      },
    ]);
    verify(mockClient.query(anything())).atLeast(1);
    verify(mockLambda.send(anything())).once();
  });

  it('should use false as default value for Admin', async () => {
    when(mockClient.query(checkEntityQuery, anything())).thenResolve({ rows: [{ entityUuid: '001' }] } as any);
    when(mockClient.query(checkCustomerQuery, anything())).thenResolve({
      rows: [{ customerUuid: '001' }],
    } as any);

    when(mockClient.query(checkCustomerEntityQuery, anything())).thenResolve({ rows: [] } as any);
    const client = instance(mockClient);
    const lambda = instance(mockLambda);
    await linkCustomers(lambda, client, [
      {
        customerUuid: '123',
        entities: [
          {
            entityUuid: '001',
            role: 'ADMIN',
          },
        ],
      },
    ]);
    verify(mockClient.query(anything())).atLeast(1);
    verify(mockLambda.send(anything())).once();
  });

  it('should skip the wrong entity migration', async () => {
    when(mockClient.query(checkCustomerQuery, anything())).thenResolve({
      rows: [{ customerUuid: '001' }],
    } as any);
    when(mockClient.query(checkEntityQuery, anything())).thenResolve({ rows: [{ entityUuid: '001' }] } as any);
    when(mockClient.query(checkCustomerEntityQuery, anything())).thenResolve({ rows: [] } as any);
    const client = instance(mockClient);
    const lambda = instance(mockLambda);
    const failed = await linkCustomers(lambda, client, [
      {
        customerUuid: '123',
        entities: [
          {
            entityUuid: '001',
            role: 'MANAGER',
          },
          {
            entityUuid: '002',
            role: 'READ',
          },
        ],
      },
    ]);
    verify(mockClient.query(anything())).atLeast(1);
    verify(mockLambda.send(anything())).once();
    expect(failed).toEqual([{ customerUuid: '123', entityUuid: '002' }]);
  });

  it('should throw error if customer doesnt exist', async () => {
    when(mockClient.query(checkCustomerQuery, anything())).thenResolve({ rows: [] } as any);
    await expect(
      validateCustomerEntity(instance(mockClient), '001', { entityUuid: '123', role: 'ADMIN' }),
    ).rejects.toThrow();
  });
  it('should throw error if entity doesnt exist', async () => {
    when(mockClient.query(checkCustomerQuery, anything())).thenResolve({
      rows: [{ customerUuid: '001' }],
    } as any);
    when(mockClient.query(checkEntityQuery, anything())).thenResolve({ rows: [] } as any);
    await expect(
      validateCustomerEntity(instance(mockClient), '001', { entityUuid: '123', role: 'ADMIN' }),
    ).rejects.toThrow();
  });
  it('should throw error if customer entity already exists', async () => {
    when(mockClient.query(checkCustomerQuery, anything())).thenResolve({
      rows: [{ customerUuid: '001' }],
    } as any);
    when(mockClient.query(checkEntityQuery, anything())).thenResolve({ rows: [{ entityUuid: '123' }] } as any);
    when(mockClient.query(checkCustomerEntityQuery, anything())).thenResolve({ rows: [{}] } as any);
    await expect(
      validateCustomerEntity(instance(mockClient), '001', { entityUuid: '123', role: 'ADMIN' }),
    ).rejects.toThrow();
  });
});
