import { CustomerRole } from '@npco/component-dto-core';
import { CustomerEntityLinkedEventDto } from '@npco/component-dto-customer';

import { InvokeCommand, LambdaClient } from '@aws-sdk/client-lambda';
import { Client } from 'pg';
import { v4 as uuidv4 } from 'uuid';

import { getDbWriteClient } from '../utils';
import { sleep } from '../utils/sleep';

import { loadCustomerEntityData } from './loadCustomerEntityData';
import { CustomerEntity, Entity } from './migrationTypes';

export const stage = process.env.STAGE || 'dev';

export const checkCustomerQuery =
  'SELECT "customerUuid" FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = \'AUS\' ';
export const checkEntityQuery = 'SELECT "entityUuid" FROM "Entity" WHERE "entityUuid" = $1 AND "domicile" = \'AUS\'';
export const checkCustomerEntityQuery =
  'SELECT "customerEntityUuid" from "CustomerEntity" WHERE "customerUuid" = $1 AND "entityUuid" = $2 AND "domicile" = \'AUS\'';

export const validateCustomerEntity = async (dbClient: Client, customerUuid: string, entity: Entity) => {
  if (!customerUuid) {
    throw new Error('customerUuid is required');
  }
  if (!entity.entityUuid) {
    throw new Error('entityUuid is required');
  }
  if (!['ADMIN', 'MANAGER'].includes(entity.role)) {
    throw new Error(`role must be either ADMIN or MANAGER: ${entity.role}`);
  }
  const customerExists = await dbClient.query(checkCustomerQuery, [customerUuid]);
  if (customerExists.rows.length === 0) {
    throw new Error(`customer ${customerUuid} does not exist`);
  }
  const entityExists = await dbClient.query(checkEntityQuery, [entity.entityUuid]);
  if (entityExists.rows.length === 0) {
    throw new Error(`entity ${entity.entityUuid} does not exist`);
  }
  const ret = await dbClient.query(checkCustomerEntityQuery, [customerUuid, entity.entityUuid]);
  if (ret.rows.length > 0) {
    throw new Error(`customer ${customerUuid} is already linked to entity ${entity.entityUuid}`);
  }
};

export const linkCustomers = async (lambda: LambdaClient, dbClient: Client, multiEntitiesData: CustomerEntity[]) => {
  const failed: { customerUuid: string; entityUuid: string }[] = [];
  for (const customerEntity of multiEntitiesData) {
    const customerUuid = customerEntity.customerUuid;
    const entities = customerEntity.entities;
    for (const entity of entities) {
      try {
        await validateCustomerEntity(dbClient, customerUuid, entity);
        const customerPermissions =
          entity.role === CustomerRole.ADMIN
            ? {
                allowZellerInvoices: true,
                allowXeroPaymentServices: true,
                allowItemManagement: true,
                allowDiscountManagement: true,
              }
            : {
                allowZellerInvoices: false,
                allowXeroPaymentServices: false,
                allowItemManagement: false,
                allowDiscountManagement: false,
              };

        await dbClient.query({
          text: `INSERT INTO "CustomerEntity" ("customerEntityUuid", "customerUuid", "entityUuid", "role", "director", "secretary", "ceo", "beneficialOwner", "beneficialOwnerAlt", "beneficiary", "partner", "trustee", "settlor", "generalContact", "financialContact", "shareholder", "chair", "treasurer", "governmentRole", "companyProfileData", "status", "domicile", "permissions") VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23)`,
          values: [
            uuidv4(),
            customerUuid,
            entity.entityUuid,
            entity.role,
            entity.director ?? false,
            entity.secretary ?? false,
            entity.ceo ?? false,
            entity.beneficialOwner ?? false,
            entity.beneficialOwnerAlt ?? false,
            entity.beneficiary ?? false,
            entity.partner ?? false,
            entity.trustee ?? false,
            entity.settlor ?? false,
            entity.generalContact ?? false,
            entity.financialContact ?? false,
            entity.shareholder ?? false,
            entity.chair ?? false,
            entity.treasurer ?? false,
            entity.governmentRole ?? null,
            entity.companyProfileData ?? null,
            'ACTIVE',
            'AUS',
            customerPermissions,
          ],
        });
        const dto = new CustomerEntityLinkedEventDto({
          customerUuid,
          entityUuid: entity.entityUuid,
          role: entity.role as CustomerRole,
          director: entity.director ?? false,
          secretary: entity.secretary ?? false,
          ceo: entity.ceo ?? false,
          beneficialOwner: entity.beneficialOwner ?? false,
          beneficialOwnerAlt: entity.beneficialOwnerAlt ?? false,
          beneficiary: entity.beneficiary ?? false,
          partner: entity.partner ?? false,
          trustee: entity.trustee ?? false,
          settlor: entity.settlor ?? false,
          generalContact: entity.generalContact ?? false,
          financialContact: entity.financialContact ?? false,
          shareholder: entity.shareholder ?? false,
          chair: entity.chair ?? false,
          treasurer: entity.treasurer ?? false,
          governmentRole: entity.governmentRole ?? undefined,
          companyProfileData: entity.companyProfileData ?? undefined,
          permissions: customerPermissions,
        });
        const invokeCommand = new InvokeCommand({
          InvocationType: 'RequestResponse',
          FunctionName: `${stage}-ams-cqrs-commandHandlers-handler`,
          Payload: JSON.stringify({
            uri: 'CustomerEntity.Linked',
            dto,
          }),
        });
        await lambda.send(invokeCommand);
        console.log(
          'emit event',
          JSON.stringify({
            uri: 'CustomerEntity.Linked',
            dto,
          }),
        );
        console.log(`Successfully linked customer ${customerUuid} to entity ${entity.entityUuid}`);
      } catch (error: any) {
        console.error(`Failed to link customer ${customerUuid} to entity ${entity.entityUuid}`, error);
        failed.push({ customerUuid, entityUuid: entity.entityUuid });
      }
      await sleep(1000);
    }
  }
  return failed;
};

export const migrate = async () => {
  const dbClient = await getDbWriteClient();
  const lambda = new LambdaClient();
  const jiraTicket = process.env.JIRA_TICKET;
  if (!jiraTicket) {
    throw new Error('JIRA_TICKET env must be supplied');
  }

  const multiEntitiesData: CustomerEntity[] = loadCustomerEntityData(stage, jiraTicket);
  const failed = await linkCustomers(lambda, dbClient, multiEntitiesData);
  if (failed.length > 0) {
    console.error(`Failed to link ${failed.length} customers to entities`, failed);
    throw new Error(`Failed to link ${failed.length} customers to entities`);
  } else {
    console.log(`Successfully linked all customers to entities ${JSON.stringify(multiEntitiesData)} `);
  }
};

/* istanbul ignore next */
if (require.main === module) {
  migrate()
    .then(() => {
      console.log('finished');
      process.exit(0);
    })
    .catch((err) => {
      console.error(err);
      process.exit(-1);
    });
}

export default migrate;
