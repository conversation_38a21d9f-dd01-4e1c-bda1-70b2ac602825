FROM node:18-alpine3.18

ARG NPM_REGISTRY_WITHOUT_HTTPS
ARG JFROG_AUTH_TOKEN
ARG CODEBUILD_BUILD_ID

WORKDIR /work

RUN apk add --no-cache python3 py3-pip make g++
RUN apk add aws-cli
RUN apk update && apk add bash

COPY [".", "."]

RUN yarn config set npmScopes.npco.npmRegistryServer https://$NPM_REGISTRY_WITHOUT_HTTPS
RUN yarn config set npmScopes.npco.npmAuthToken $JFROG_AUTH_TOKEN
RUN yarn config set npmScopes.npco.npmAlwaysAuth true
RUN yarn config set enableScripts false

RUN yarn workspaces focus component-bff bff-migration
RUN yarn nx reset

RUN yarn nx run bff-migration:build

WORKDIR apps/core/bff-migration/apps

CMD ["sh", "src/migrations/2025-05-22-emit-entity-update-to-populate-regionalOptions/execute.sh"]
