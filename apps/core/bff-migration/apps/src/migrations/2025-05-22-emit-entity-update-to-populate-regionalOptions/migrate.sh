#!/bin/sh

pwd
env
accountId=`aws sts get-caller-identity | jq .Account --raw-output`
aws ecr get-login-password --region ap-southeast-2 | docker login --username AWS --password-stdin $accountId.dkr.ecr.$AWS_REGION.amazonaws.com
echo accountId $accountId
migrationName=$MIGRATION_NAME

yarn workspaces focus component-bff bff-migration && yarn nx run bff-migration:build
cd ../../../../ || exit
echo "clear source from .nx folder to allow re-use by other machine"
echo "cd .nx && find . -path '*/source' -delete"
cd .nx || exit
find . -path '*/source' -delete
cd ../apps/core/bff-migration/apps || exit

export NPM_REGISTRY_WITHOUT_HTTPS=${NpmRegistry#"https://"}

repositoryUri=$accountId.dkr.ecr.ap-southeast-2.amazonaws.com/$STAGE-bff-migration
dockerTag=$repositoryUri:$migrationName

echo build docker image $dockerTag $NpmRegistry $NPM_REGISTRY_WITHOUT_HTTPS
docker build -f src/migrations/$migrationName/Dockerfile \
        -t $dockerTag --build-arg NPM_REGISTRY_WITHOUT_HTTPS="$NPM_REGISTRY_WITHOUT_HTTPS" \
        --build-arg CODEBUILD_BUILD_ID="$CODEBUILD_BUILD_ID" \
        --build-arg JFROG_AUTH_TOKEN="$JFROG_AUTH_TOKEN" ../../../../.

if [ "$?" -ne 0 ];then
  echo Failed to build docker image
  exit -1
else
  echo run serverless deploy migrationName=$migrationName codeBuildId=$CODEBUILD_BUILD_ID
  cp config/.env* ./
  yarn run sls deploy --config serverlessTask.yml --stage $STAGE --region $AWS_REGION --param="migrationName=$migrationName" --param="codeBuildId=$CODEBUILD_BUILD_ID" --verbose
  if [ "$?" -ne 0 ];then
    echo Failed to deploy serverless
    exit -1
  fi
  echo push to ecr $dockerTag task definition $STAGE-bff-migration-$migrationName
  docker push $dockerTag

  aws ecs run-task --cluster $STAGE-bff-migration --task-definition $STAGE-bff-migration-$migrationName \
    --network-configuration "{ \"awsvpcConfiguration\": { \"assignPublicIp\":\"ENABLED\", \"securityGroups\": [\"$AMS_LAMBDA_SG\"], \"subnets\": [\"$AMS_LAMBDA_SUBNET01\", \"$AMS_LAMBDA_SUBNET02\", \"$AMS_LAMBDA_SUBNET03\"]}}" \
    --overrides "{\"containerOverrides\":[{\"name\":\"${STAGE}-bff-migration-updateEntityRegionalOptions\",\"command\":[\"yarn\", \"exec\", \"ts-node\", \"src/migrations/$migrationName/migrate.ts\"],\"environment\":[{\"name\":\"TRIAL\",\"value\":\"${TRIAL}\"},{\"name\":\"ENTITY_UUID\",\"value\":\"${ENTITY_UUID}\"}]}]}" \
    --launch-type FARGATE --tags key=COMPONENT_NAME,value=bff key=STAGE,value=$STAGE key=PART_NAME,value=migration key=service,value=bff-migration
fi
