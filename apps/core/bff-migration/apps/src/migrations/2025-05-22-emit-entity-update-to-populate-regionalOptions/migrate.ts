import { AwsRegions, Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { EntityUpdatedEventDto } from '@npco/component-dto-entity';

import { getDbClient, getTrialEntityUuid, invokeAsyncLambda, isTrial } from '../utils';
import { sleep } from '../utils/sleep';

const stage = process.env.STAGE ?? 'dev';
const batch = 4;
type Entity = {
  entityUuid: string;
  hubspotCompanyId: string;
};

const getEntities = async (domicile: Domicile): Promise<Entity[]> => {
  const client = await getDbClient();
  let queryText = `
    SELECT "entityUuid"
    FROM "Entity"
    WHERE "regionalOptions" IS NULL 
    AND "status" != 'DELETED'
    AND "domicile" = $1
  `;

  if (isTrial()) {
    const entityUuidFilter = `AND "entityUuid" = '${getTrialEntityUuid(stage)}'`;
    queryText = `
      ${queryText}
      ${entityUuidFilter}
    `;
  }

  const result = await client.query({
    text: queryText,
    values: [domicile],
  });

  return result.rows;
};

const emitEntityUpdatedEvent = async (entity: Entity, region: string) => {
  // 'surchargeAllowed' is true in `AU` and false in `GBR`
  const surchargeAllowedValue = region === AwsRegions.SYDNEY;
  const timeZoneToUse = region === AwsRegions.SYDNEY ? 'Australia/Melbourne' : 'Europe/London';
  const payload = new EntityUpdatedEventDto({
    entityUuid: entity.entityUuid,
    regionalOptions: {
      surchargeAllowed: surchargeAllowedValue,
    },
  });

  try {
    const time = new Date().toLocaleString('en-GB', { timeZone: timeZoneToUse });
    console.log(`[${time}]`, 'invoking cmd handler with EntityUpdated with payload:', JSON.stringify(payload, null, 2));

    await invokeAsyncLambda(`${stage}-ams-cqrs-commandHandlers-handler`, {
      uri: 'Entity.Updated',
      dto: { ...payload },
    });
  } catch (err: any) {
    console.log(`Failed to invoke cmd handler with entityUuid: ${err?.message} ${JSON.stringify(err, null, 2)}`);
  }
};

export const migrate = async () => {
  const regionToUse = process.env.AWS_REGION ?? AwsRegions.SYDNEY;
  const domicileToUse = process.env.AWS_REGION === AwsRegions.SYDNEY ? Domicile.AU : Domicile.GB;
  if (isTrial()) {
    console.log(`
      ####
      TRIAL RUN. STAGE: ${stage}
      ####`);
  }

  const entities = await getEntities(domicileToUse);
  for (let i = 0; i < entities.length; i += batch) {
    const entityBatches = entities.slice(i, i + batch);
    await Promise.all(entityBatches.map((entityBatch) => emitEntityUpdatedEvent(entityBatch, regionToUse)));
    await sleep();
    console.log('count:', i + 1, 'of', entities.length);
    console.log('========');
  }
};

/* istanbul ignore next */
if (require.main === module) {
  migrate()
    .then(() => {
      console.log('finished migration');
      process.exit(0);
    })
    .catch((err) => {
      console.error(err);
      process.exit(-1);
    });
}

export default migrate;
