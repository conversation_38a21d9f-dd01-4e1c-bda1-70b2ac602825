import { getSsmValue } from '@npco/bff-systemtest-utils';

import { ManagementClient } from 'auth0';
import axios from 'axios';
import https from 'https';

import { AppClients, ComponentClients } from './clients';
import type { Auth0UserMetadata } from './types';

export class Auth0Helper {
  auth0ClientId = '';

  auth0ClientSecret = '';

  managementClientId = '';

  managementClientSecret = '';

  managementClient: ManagementClient | null = null;

  private readonly axiosInstance = axios.create({
    httpsAgent: new https.Agent({
      rejectUnauthorized: false,
    }),
  });

  constructor(private env: any) {}

  createUser = async (email: string, password: string, userMetadata?: Auth0UserMetadata) => {
    const body = {
      email,
      password,
      client_id: this.auth0ClientId,
      connection: 'Username-Password-Authentication',
      ...(userMetadata ? { user_metadata: userMetadata } : {}),
    };
    console.log('create user:', body);
    return this.request('post', `https://${this.env.auth0Domain}/dbconnections/signup`, { body });
  };

  async setClient(appClient: string) {
    console.log('set client');
    if (this.env.adminToken) {
      await this.setClientByAdminToken(this.env.adminToken, appClient);
    } else {
      await this.setClientByAuthId();
    }
  }

  setUserPassword = async (email: string, password: string) => {
    const users = await this.getUserByEmail(email);
    if (users && users[0]) {
      const update = await this.updateUser(users[0].user_id as string, { password });
      console.log('setUserPassword', update, users[0].user_id);
      return update;
    }
    console.warn('user not found', email, users);
  };

  getUser = async (accessToken: string) => {
    const response = await this.request('get', `https://${this.env.auth0Domain}/userinfo`, {
      headers: { authorization: `Bearer ${accessToken}` },
    });
    console.log('User info', response.data);
    return response.data;
  };

  getUserById = async (id: string) => {
    const client = await this.getManagementClient();
    const response = await client.getUser({ id });
    console.log('User info', response);
    return response;
  };

  getUserByEmail = async (email: string) => {
    const client = await this.getManagementClient();
    const response = await client.getUsersByEmail(email);
    console.log('User info', response, email);
    return response;
  };

  updateUser = async (userId: string, data: any) => {
    const client = await this.getManagementClient();
    await client.updateUser({ id: userId }, data).catch((e) => {
      console.error(e);
    });
  };

  async setClientByAuthId() {
    const { clientId, clientSecret } = this.env;
    if (!clientId || !clientSecret) {
      throw new Error('AUTH0_CLIENT_ID, AUTH0_CLIENT_SECRET not set.');
    }
    console.log(`/${this.env.stage}-${clientId}`, `/${this.env.stage}-${clientSecret}`);
    this.auth0ClientId = await getSsmValue(`/${this.env.stage}-${clientId}`);
    this.auth0ClientSecret = await getSsmValue(`/${this.env.stage}-${clientSecret}`);
    console.log(`client id = ${this.auth0ClientId}, client secret = ${this.auth0ClientSecret}`);
  }

  deleteUsers = async (emails: string[]) => {
    const auth0Management = await this.getManagementClient();
    for (let i = 0; i < emails.length; ++i) {
      const email = emails[i];
      const users = await auth0Management.getUsersByEmail(email);
      if (users && users[0] && users[0].user_id) {
        console.log(`Deleting test user ${email} from auth0`, users[0].user_id);
        await auth0Management.deleteUser({ id: users[0].user_id } as any);
      }
    }
  };

  async setClientByAdminToken(adminToken: string, appClient: string) {
    const appClients = await this.request('get', `https://${this.env.auth0Domain}/api/v2/clients`, {
      headers: { authorization: `Bearer ${adminToken}` },
      params: { fields: 'client_id,client_secret,name' },
    });
    const client = appClients.data.find((c: any) => c.name === appClient);
    const admin = appClients.data.find((c: any) => c.name === AppClients[ComponentClients.AmsEngine]);
    this.auth0ClientId = client.client_id;
    this.auth0ClientSecret = client.client_secret;
    this.managementClientId = admin.client_id;
    this.managementClientSecret = admin.client_secret;
    console.log('Client:', client);
  }

  getOrCreateUserByEmail = async (email: string, password: string) => {
    if (this.env.adminToken) {
      console.log('use admin token');
      const response = await this.request('get', `https://${this.env.auth0Domain}/api/v2/users-by-email`, {
        params: { email },
        headers: { authorization: `Bearer ${this.env.adminToken}` },
      });
      console.log('User info', response.data, email);
      if (!response || !response.data.length) {
        // new test group user
        console.warn('Creating new auth0 user', email);
        await this.createUser(email, password, { role: 'ADMIN' });
      }
    } else {
      console.log('use user name password token');
      const client = await this.getManagementClient();
      const response = await client.getUsersByEmail(email);
      console.log('User info', response, email);
      if (!response || !response.length) {
        // new test group user
        console.warn('Creating new auth0 user', email);
        await this.createUser(email, password, { role: 'ADMIN' });
      }
    }
  };

  public readonly getToken = async (
    email: string,
    password: string,
    scope = 'offline_access openid profile email',
  ): Promise<any> => {
    const body = {
      grant_type: 'password',
      username: email,
      password,
      refresh_token: '',
      scope,
      audience: `https://${this.env.auth0Audience}`,
      client_id: this.auth0ClientId,
      client_secret: this.auth0ClientSecret,
    };
    console.log('get access token:', `https://${this.env.auth0Domain}/oauth/token`, body);
    try {
      const response = await this.request('post', `https://${this.env.auth0Domain}/oauth/token`, {
        body,
      });
      console.log('User token', response.data.access_token);
      return response.data;
    } catch (e: any) {
      console.error(e);
      if (e.response && e.response.data.error === 'invalid_grant' && email.includes('test-integrations-bff-')) {
        // new test group user
        console.warn('New test group user login failed - creating new auth0 user', email);
        await this.createUser(email, password, { role: 'ADMIN' });
        return this.getToken(email, password);
      }
      throw new Error(e);
    }
  };

  public readonly getAccessToken = async (email: string, password: string) => {
    const token = await this.getToken(email, password);
    return token.access_token;
  };

  public readonly getAccessTokenWithDefinedScope = async (scope: string, email: string, password: string) => {
    const token = await this.getToken(email, password, scope);
    return token.access_token;
  };

  public async updateUserMetadata(accessToken: string, userMetadata: any) {
    const { sub: userId } = await this.getUser(accessToken);
    await (await this.getManagementClient()).updateUserMetadata({ id: userId }, userMetadata);
  }

  private readonly request = async (method: any, url: string, { body, params, headers }: any) => {
    try {
      return await this.axiosInstance.request({
        method,
        url,
        params,
        data: JSON.stringify(body),
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
      });
    } catch (e: any) {
      console.error(e);
      throw e;
    }
  };

  private readonly getManagementClient = async () => {
    if (!this.managementClient) {
      const clientId = this.env.managementClientId
        ? await getSsmValue(`/${this.env.stage}-${this.env.managementClientId}`)
        : this.managementClientId || this.auth0ClientId;
      const clientSecret = this.env.managementClientSecret
        ? await getSsmValue(`/${this.env.stage}-${this.env.managementClientSecret}`)
        : this.managementClientSecret || this.auth0ClientSecret;
      const options = {
        domain: this.env.managementDomain as string,
        clientId,
        clientSecret,
      };
      console.log('get mamangement client options:', options);
      this.managementClient = new ManagementClient(options);
    }
    return this.managementClient;
  };
}
