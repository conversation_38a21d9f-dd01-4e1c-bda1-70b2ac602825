// eslint-disable-next-line @typescript-eslint/no-var-requires
// require('custom-env').env(process.env.STAGE, 'config/');
import { DynamodbClient, EventBridgeService, invokeAsyncLambda } from '@npco/bff-systemtest-utils';

import type { UpdateCommandInput } from '@aws-sdk/lib-dynamodb';
import jwtDecode from 'jwt-decode';
import { v4 } from 'uuid';

import { Auth0Helper } from './auth0Helper';
import { AppClients, ComponentClients } from './clients';
import { TestData } from './gql/apiTestData';
import { retry } from './retry';

const stage = process.env.STAGE || 'dev';

export const region = process.env.AWS_REGION || 'ap-southeast-2';

abstract class ManagementEnv {
  managementDomain = `zeller-${stage}.au.auth0.com`;

  managementClientId = 'ams-engine/AUTH0_CLIENT_ID';

  managementClientSecret = 'ams-engine/AUTH0_CLIENT_SECRET';
}

class MpEnv extends ManagementEnv {
  stage = process.env.STAGE || 'dev';

  componentName = ComponentClients.MerchantPortal;

  adminToken = process.env.ADMIN_TOKEN || '';

  auth0Domain =
    process.env.AUTH0_MP_DOMAIN || (stage === 'staging' ? 'zeller-staging.au.auth0.com' : 'auth.myzeller.dev');

  auth0Audience = process.env.AUTH0_MP_AUDIENCE || 'dashboard.myzeller.com';

  clientId = 'mp-app/AUTH0_CLIENT_ID';

  clientSecret = 'mp-app/AUTH0_CLIENT_SECRET';

  entityTable = `${this.stage}-mp-api-dynamodb-Entities`;

  entityGsi = 'entityGsi';

  XERO_BANKFEED_CLIENT_ID = '96E8B87F01F1439DA57F06B38F9A283A';

  XERO_ECOMMERCE_CLIENT_ID = 'E0F2383BFE344A64BD3A6D4E3C4A0478';
}

class ZellerAppMpEnv extends MpEnv {
  clientId = 'mp-api/ZELLER_APP_AUTH0_CLIENT_ID';

  clientSecret = 'mp-api/ZELLER_APP_AUTH0_CLIENT_SECRET';
}

class DbsEnv extends ManagementEnv {
  stage = process.env.STAGE || 'dev';

  componentName = ComponentClients.DeviceBackend;

  adminToken = process.env.ADMIN_TOKEN || '';

  auth0Domain = process.env.AUTH0_DBS_DOMAIN || 'zeller-dev.au.auth0.com';

  auth0Audience = process.env.AUTH0_DBS_AUDIENCE || 'devices.myzeller.com';

  clientId = 'dbs-api/AUTH0_CLIENT_ID';

  clientSecret = 'dbs-api/AUTH0_CLIENT_SECRET';
}

class CrmsEnv extends ManagementEnv {
  stage = process.env.STAGE || 'dev';

  componentName = ComponentClients.CrmsEngine;

  adminToken = process.env.ADMIN_TOKEN || '';

  auth0Domain = process.env.AUTH0_DBS_DOMAIN || `zeller-${stage}.au.auth0.com`;

  auth0Audience = process.env.AUTH0_DBS_AUDIENCE || 'devices.myzeller.com';

  clientId = 'crms-engine/AUTH0_CLIENT_ID';

  clientSecret = 'crms-engine/AUTH0_CLIENT_SECRET';
}

class AmsEnv extends ManagementEnv {
  stage = process.env.STAGE || 'dev';

  componentName = ComponentClients.AmsEngine;

  adminToken = process.env.ADMIN_TOKEN || '';

  auth0Domain = process.env.AUTH0_AMS_DOMAIN || `zeller-${stage}.au.auth0.com`;

  auth0Audience = process.env.AUTH0_AMS_AUDIENCE || 'ams.myzeller.com';

  clientId = 'ams-engine/AUTH0_CLIENT_ID';

  clientSecret = 'ams-engine/AUTH0_CLIENT_SECRET';
}

export const EnvData = {
  [ComponentClients.ZellerApp]: ZellerAppMpEnv,
  [ComponentClients.MerchantPortal]: MpEnv,
  [ComponentClients.DeviceBackend]: DbsEnv,
  [ComponentClients.CrmsEngine]: CrmsEnv,
  [ComponentClients.AmsEngine]: AmsEnv,
};

export class ApiTestHelper {
  public readonly dbClient: DynamodbClient;

  public testData: TestData = new TestData();

  public eventBridge: EventBridgeService;

  public readonly env: any;

  private readonly auth0Helper: Auth0Helper;

  private appClient = '';

  constructor(componentName: ComponentClients) {
    this.appClient = AppClients[componentName];
    this.env = new EnvData[componentName]();
    this.auth0Helper = new Auth0Helper(this.env);
    this.eventBridge = new EventBridgeService();
    this.dbClient = new DynamodbClient();
  }

  beforeAll = async () => {
    await this.auth0Helper.setClient(this.appClient);
    console.log('Test setup complete');
  };

  getEntityUuid() {
    return this.testData.entityUuid;
  }

  setEntityUuid(entityUuid: string) {
    this.testData.entityUuid = entityUuid;
  }

  setTestData(testData: any) {
    this.testData = testData;
  }

  public getAuthHelper = () => this.auth0Helper;

  public setAuthHelperClientForComponent = async (componentName: ComponentClients) => {
    await this.auth0Helper.setClient(AppClients[componentName]);
  };

  public getTestData = () => this.testData;

  public getStage = () => this.env.stage;

  public getServiceName = () => this.env.componentName;

  public getUserInfo = async (accessToken = this.testData.accessToken) => this.auth0Helper.getUser(accessToken);

  public getUserByEmail = async (email: string) => this.auth0Helper.getUserByEmail(email);

  public updateUserInfo = async (userId: string, data: any) => this.auth0Helper.updateUser(userId, data);

  // mpAuthUser.spec, dbsAuthUser.spec
  hasAuth = async () => {
    try {
      const token = await this.auth0Helper.getAccessToken(this.testData.email, this.testData.password);
      this.setAccessTokenInTestData(token);
      return true;
    } catch (e: any) {
      console.error(e);
      return false;
    }
  };

  loginUserForAccessToken = async (entity?: string) => {
    if (entity) {
      this.testData.entityUuid = entity;
    }
    const entityUuid = entity || this.testData.entityUuid;
    const token = await this.getTokenFromSessionCache(entityUuid, `${this.getStage()}-mp-api-dynamodb-SessionCache`);
    if (token) {
      this.testData.accessToken = token;
      return token;
    }
    const accessToken = await this.auth0Helper.getAccessToken(this.testData.email, this.testData.password);
    this.testData.accessToken = accessToken;
    return accessToken;
  };

  getAccessTokenScope = async () => {
    const decoded: any = jwtDecode(this.testData.accessToken);
    console.log('decode token:', decoded);
    return decoded.scope;
  };

  getTokenFromSessionCache = async (entityUuid: string, tableName: string) => {
    const { Items: items } = await this.dbClient.query({
      TableName: tableName,
      IndexName: 'entityCacheGsi',
      KeyConditionExpression: 'entityUuid = :entityUuid and #type = :type',
      ExpressionAttributeNames: { '#type': 'type' },
      ExpressionAttributeValues: {
        ':entityUuid': entityUuid,
        ':type': 'identitySub',
      },
    });
    if (items && items.length > 0) {
      console.log('get access token from cache session:', items.length);
      const token = items[0].id;
      const decoded: any = jwtDecode(token);
      console.log('decode token:', decoded);
      const now = new Date().getTime();
      const exp = new Date(decoded.exp * 1000).getTime();
      if (exp > now + 60000) {
        this.testData.accessToken = token;
        return token;
      }
    }
    return null;
  };

  public setAccessTokenInTestData = (accessToken: string) => {
    this.testData.accessToken = accessToken;
  };

  public readonly setDeviceUuidInTestData = async (deviceUuid: string) => {
    this.testData.deviceUuid = deviceUuid;
  };

  getDeviceUuid = async () => {
    return this.testData.deviceUuid;
  };

  getAccessToken = async (email = this.testData.email, password = this.testData.password) => {
    return this.auth0Helper.getAccessToken(email, password);
  };

  getAccessTokenWithDefinedScope = async (
    scope: string,
    email = this.testData.email,
    password = this.testData.password,
  ) => {
    return this.auth0Helper.getAccessTokenWithDefinedScope(scope, email, password);
  };

  createAmsRegisteringIndividual = async (entityUuid: string, customerUuid: string, firstLast = {}) => {
    const customer = {
      customerUuid,
      entityUuid,
      dob: '1999-12-12',
      role: 'ADMIN',
      type: 'INDIVIDUAL',
      beneficialOwner: true,
      email: `test-integrations-bff+${v4()}@myzeller.com`, // email is generated different to auth0
      registeringIndividual: true, // ams does a check by email to update metadata
      phoneVerified: true,
      emailVerified: true,
      phone: '+************',
      director: true,
      companyTrustName: 'Trust Name',
      abn: '1234567890',
      ...firstLast,
    };
    await invokeAsyncLambda(`${this.getStage()}-ams-engine-customer-createCustomerHandler`, {
      id: customerUuid,
      detail: customer,
      requestPath: '/v1/',
    });
    await invokeAsyncLambda(`${this.getStage()}-ams-engine-customer-updateCustomerHandler`, {
      requestPath: '/v1/',
      id: customerUuid,
      detail: {
        customerUuid,
        entityUuid,
        isInvitationPending: false,
        productTourStatus: {
          showCorporateCardsAdminWelcome: false,
          showAdminMerchantPortalWelcome: false,
          showInvoiceInstructions: false,
        },
      },
    });
    await this.sleep(2000);
    return customer;
  };

  getCustomerCore = async () => {
    console.log('getCustomerCore', this.testData.customerUuid);
    const { Items: items } = await this.dbClient.query({
      TableName: `${this.getStage()}-mp-api-dynamodb-Entities`,
      KeyConditionExpression: 'id = :id and #type = :type',
      ExpressionAttributeNames: { '#type': 'type' },
      ExpressionAttributeValues: {
        ':id': this.testData.customerUuid,
        ':type': 'customer.core',
      },
    });
    if (items && items.length > 0) {
      return items[0];
    }
    return undefined;
  };

  getCustomerEntity = async () => {
    console.log('getCustomerEntity', this.testData.customerUuid, this.testData.entityUuid);
    const { Items: items } = await this.dbClient.query({
      TableName: `${this.getStage()}-mp-api-dynamodb-Entities`,
      KeyConditionExpression: 'id = :id and #type = :type',
      ExpressionAttributeNames: { '#type': 'type' },
      ExpressionAttributeValues: {
        ':id': this.testData.customerUuid,
        ':type': `customer.entity.${this.testData.entityUuid}`,
      },
    });
    if (items && items.length > 0) {
      return items[0];
    }
    return undefined;
  };

  getRegisteringIndividual = async () => {
    console.log('getRegisteringIndividual', this.testData.customerUuid, this.testData.entityUuid);
    const { Items: items } = await this.dbClient.query({
      TableName: `${this.getStage()}-mp-api-dynamodb-Entities`,
      KeyConditionExpression: 'id = :id and #type = :type',
      ExpressionAttributeNames: { '#type': 'type' },
      ExpressionAttributeValues: {
        ':id': this.testData.customerUuid,
        ':type': `customer.entity.${this.testData.entityUuid}`,
      },
    });
    if (items && items.length > 0) {
      const customer = items[0];
      if (customer.registeringIndividual) {
        return customer;
      }
    }
    return undefined;
  };

  validateCustomerCreated = async (customerUuid: string, entityUuid: string) => {
    console.log('validate customer created', customerUuid, entityUuid);
    const { Items: items } = await this.dbClient.query({
      TableName: `${this.getStage()}-mp-api-dynamodb-Entities`,
      KeyConditionExpression: 'id = :id and #type = :type',
      ExpressionAttributeNames: { '#type': 'type' },
      ExpressionAttributeValues: {
        ':id': customerUuid,
        ':type': `customer.entity.${entityUuid}`,
      },
    });
    if (items && items.length > 0) {
      return items[0];
    }
    return undefined;
  };

  createTestEntitySession = async (customerUuid: string, token = this.testData.accessToken, role = 'ADMIN') => {
    const item = {
      id: token,
      status: 'ACTIVE',
      entityUuid: this.testData.entityUuid,
      customerUuid,
      role,
      ttl: Math.floor(new Date().getTime() / 1000) + 86400,
    };
    await this.dbClient.put({
      TableName: `${this.getStage()}-mp-api-dynamodb-SessionCache`,
      Item: {
        type: 'identitySub',
        ...item,
      },
    });
    await this.dbClient.put({
      TableName: `${this.getStage()}-mp-api-dynamodb-SessionCache`,
      Item: {
        type: 'onboarding',
        ...item,
      },
    });
    console.log('Created session cache', item);
    //  find session has been created
    await retry(async () => {
      const { Items: identitySubItems } = await this.dbClient.query({
        TableName: `${this.getStage()}-mp-api-dynamodb-SessionCache`,
        KeyConditionExpression: 'id = :id and #type = :type',
        ExpressionAttributeNames: { '#type': 'type' },
        ExpressionAttributeValues: {
          ':id': token,
          ':type': 'identitySub',
        },
      });
      expect(identitySubItems?.length).toBeGreaterThan(0);
      const { Items: onboardingItems } = await this.dbClient.query({
        TableName: `${this.getStage()}-mp-api-dynamodb-SessionCache`,
        KeyConditionExpression: 'id = :id and #type = :type',
        ExpressionAttributeNames: { '#type': 'type' },
        ExpressionAttributeValues: {
          ':id': token,
          ':type': 'onboarding',
        },
      });
      expect(onboardingItems?.length).toBeGreaterThan(0);
    });
  };

  setDeviceEntitySession = async (deviceUuid: string, entityUuid?: string) => {
    const params: UpdateCommandInput = {
      TableName: `${this.getStage()}-dbs-api-dynamodb-SessionCache`,
      Key: {
        id: deviceUuid,
        type: 'deviceUuid',
      },
      ...(entityUuid
        ? {
            UpdateExpression: 'set entityUuid = :entityUuid',
            ExpressionAttributeValues: {
              ':entityUuid': entityUuid,
            },
          }
        : { UpdateExpression: 'remove entityUuid' }),
    };
    console.log('set up entityUuid ', params);
    await this.dbClient.update(params);
  };

  createMockDeviceEntitySession = async (
    entityUuid: string,
    deviceUuid: string,
    customerUuid: string,
    tableName = `${this.getStage()}-dbs-api-dynamodb-SessionCache`,
  ) => {
    console.log('deviceUuid', deviceUuid);
    // attach the entity id to the cache, empty status is new device
    const params: UpdateCommandInput = {
      TableName: tableName,
      Key: {
        id: deviceUuid,
        type: 'deviceUuid',
      },
      UpdateExpression: 'set entityUuid = :entityUuid, customerUuid = :customerUuid, #role = :role',
      ExpressionAttributeNames: { '#role': 'role' },
      ExpressionAttributeValues: {
        ':entityUuid': entityUuid,
        ':customerUuid': customerUuid,
        ':role': 'ADMIN',
      },
    };
    console.log('set up session ', params);
    await this.dbClient.update(params);
  };

  sleep = async (time: number) =>
    new Promise((resolve) => {
      setTimeout(() => resolve(null), time);
    });
}
