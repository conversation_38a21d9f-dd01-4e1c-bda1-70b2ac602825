Resources:
  xrayPolicy:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - xray:PutTraceSegments
              - xray:PutTelemetryRecords
              - xray:GetSamplingRules
              - xray:GetSamplingTargets
              - xray:GetSamplingStatisticSummaries
            Resource: '*'
  lambdaVpcPolicy:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - ec2:CreateNetworkInterface
              - ec2:DescribeNetworkInterfaces
              - ec2:DeleteNetworkInterface
            Resource: '*'
  entityTableQueryRolePolicy:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - dynamodb:Query
              - dynamodb:BatchGetItem
            Resource:
              - 'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.mpEntityTable}'
              - 'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.sisEntityTable}'

  entityTableWriteItemRolePolicy:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - dynamodb:UpdateItem
              - dynamodb:PutItem
            Resource:
              - 'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.mpEntityTable}'
              - 'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.sisEntityTable}'

  crossAccountInvocationPolicy:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - lambda:InvokeFunction
            Resource: 'arn:aws:lambda:${self:custom.remoteRegion.${self:provider.region}}:${self:custom.remoteAccountId.${self:provider.region}}:function:\${self:provider.stackName}*'

Outputs:
  lambdaVpcPolicyArn:
    Value: !Ref lambdaVpcPolicy
    Export:
      Name: !Join ['', ['${self:custom.serviceName}', '-lambdaVpcPolicyArn']]
  xrayPolicyArn:
    Value: !Ref xrayPolicy
    Export:
      Name: !Join ['', ['${self:custom.serviceName}', '-xrayPolicyArn']]
  entityTableQueryRolePolicyArn:
    Value: !Ref entityTableQueryRolePolicy
    Export:
      Name: !Join ['', ['${self:custom.serviceName}', '-entityTableQueryRolePolicyArn']]
  entityTableWriteItemRolePolicyArn:
    Value: !Ref entityTableWriteItemRolePolicy
    Export:
      Name: !Join ['', ['${self:custom.serviceName}', '-entityTableWriteItemRolePolicyArn']]
  crossAccountInvocationPolicyArn:
    Value: !Ref crossAccountInvocationPolicy
    Export:
      Name: !Join ['', ['${self:custom.serviceName}', '-crossAccountInvocationPolicyArn']]
