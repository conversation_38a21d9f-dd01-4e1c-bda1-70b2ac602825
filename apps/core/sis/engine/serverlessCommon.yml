service: ${opt:stage}-${self:custom.service}
frameworkVersion: '3'
useDotenv: true
plugins:
  - serverless-dotenv-plugin
  - serverless-plugin-resource-tagging

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region}
  stackName: ${self:service}-common
  deploymentBucket:
    name: ${cf:${self:custom.bucketStackName}.deploymentBucket}
    maxPreviousDeploymentArtifacts: 100
    blockPublicAccess: true
  tags:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    service: ${self:custom.service}
    env: ${opt:stage}
  stackTags: ${self:provider.tags}

custom:
  accountId: ${aws:accountId}
  service: ${env:COMPONENT_NAME}-${env:PART_NAME}
  bucketStackName: ${self:custom.service}-iac-s3
  serviceName: ${opt:stage}-${self:custom.service}
  mpStackName: ${env:STATIC_ENV_NAME}-mp-api
  mpEntityTable: ${self:custom.mpStackName}-dynamodb-Entities
  sisEntityTable: ${env:STATIC_ENV_NAME}-sis-engine-dynamodb-Entities
  remoteAccountId:
    ap-southeast-2: ${env:LONDON_ACCOUNT_ID}
    eu-west-2: ${env:SYDNEY_ACCOUNT_ID}
  remoteRegion:
    ap-southeast-2: eu-west-2
    eu-west-2: ap-southeast-2

resources:
  - ${file(resources/common/managedPolicy.yml)}
  - ${file(resources/deploymentBucket.yml)}
