import { Auth0Urls } from '../authzero/utils/auth0Urls';

import { ConfigService } from './configService';

let envService: EnvironmentService;
export class EnvironmentService {
  public stage = '';

  public componentTableName = '';

  public cacheTableName = '';

  public sessionCacheTtlInSeconds = 86400;

  public cacheModelSerialGsi = '';

  public cacheEntityModelSerialGsi = '';

  public deviceSerialModelGsi = '';

  public cqrsCommandHandler = '';

  public shortIdGsi = '';

  public entityGsi = '';

  public typeGsi = '';

  public entityCacheGsi = '';

  public accessTokenGsi = '';

  public secondaryGsiV1 = '';

  public contactUuidGsi = '';

  public entitySortKeyGsi = '';

  public auth0ClientKey = '';

  public auth0ClientSecret = '';

  public auth0Tenant = '';

  public auth0JwtMaxAge = '';

  public auth0Audience = '';

  public auth0ApiUrls: Auth0Urls = {} as Auth0Urls;

  public isInLambda!: boolean;

  public maxRetries = 3;

  public dynamodbTimeout = 5000;

  public dynamodbConnectTimeout = 5000;

  public lambdaHttpTimeout = 5000;

  public lambdaConnectTimeout = 2000;

  public zellerSessionIdTtlInSeconds = 28800; // 8 hours

  public zellerSessionIdCheckEnabled = false;

  public mfaEnrolmentEnabled = false;

  public sensitiveAccessTokenMaxAge = 300;

  public zellerAppAuth0ClientKey = '';

  public zellerAppFirebaseCredentials = {} as { projectId: string; adminClientEmail: string; privateKey: string };

  public awsRegion = '';

  public allowZellerAppFirebaseTestToken = false;

  public deviceGsi = '';

  public amsEndpoint = '';

  public amsEndpointVersion = '';

  public domicileLookupEnabled = false;

  public dynamoDbMarshallOptions = {
    convertEmptyValues: false,
    removeUndefinedValues: true,
    convertClassInstanceToMap: true,
  };

  public auth0MetadataFromToken = false;

  public domicileLookupTableName = '';

  public globalAccountId = '';

  protected readonly configService: ConfigService = new ConfigService();

  constructor() {
    this.setupAwsEnv();
    this.setupDbEnv();
    this.setupAuth0Env();
    this.setupCqrsEnv();
    this.setupAms();
    this.setupMiddleware();
    this.setupMfa();
    this.setupFirebase();
    this.setupAuth0MetadataFromToken();
  }

  setupDbEnv(): void {
    this.componentTableName = this.configService.get('COMPONENT_TABLE', 'Entities');
    this.stage = this.configService.get('STAGE', '');
    this.cacheTableName = this.configService.get('SESSION_CACHE_TABLE', 'SessionCache');
    this.shortIdGsi = this.configService.get('SHORT_ID_GSI', 'shortIdGsi');
    this.cacheModelSerialGsi = this.configService.get('CACHE_MODELSERIAL_GSI', 'modelSerialGsi');
    this.cacheEntityModelSerialGsi = this.configService.get('ENTITY_MODEL_SERIAL_GSI', 'entityModelSerialGsi');
    this.deviceSerialModelGsi = this.configService.get('DEVICE_SERIALMODEL_GSI', 'deviceSerialModelGsi');
    this.entityGsi = this.configService.get('ENTITY_GSI', 'entityGsi');
    this.typeGsi = this.configService.get('TYPE_GSI', 'typeGsi');
    this.accessTokenGsi = this.configService.get('ACCESS_TOKEN_GSI', 'accessTokenGsiv2');
    this.entityCacheGsi = this.configService.get('ENTITY_CACHE_GSI', 'entityCacheGsi');
    this.secondaryGsiV1 = this.configService.get('SECONDARY_GSI_V1', 'secondaryGsiV1');
    this.contactUuidGsi = this.configService.get('CONTACT_UUID_GSI', 'contactUuidGsi');
    this.entitySortKeyGsi = this.configService.get('ENTITY_SORT_KEY_GSI', 'entitySortKeyGsi');
    this.deviceGsi = this.configService.get('DEVICE_GSI', 'deviceGsi');
  }

  setupAms(): void {
    this.domicileLookupEnabled = this.configService.get('DOMICILE_LOOKUP_ENABLED', 'false') === 'true';
    this.domicileLookupTableName = this.configService.get('DOMICILE_LOOKUP_TABLE', '');
    this.globalAccountId = this.configService.get('GLOBAL_ACCOUNT_ID', '');

    this.amsEndpoint = this.configService.get('AMS_API_ENDPOINT', '');
    this.amsEndpointVersion = this.configService.get('AMS_API_ENDPOINT_VERSION', 'v1');
  }

  setupAwsEnv(): void {
    this.awsRegion = this.configService.get('AWS_REGION', '');
  }

  setupCqrsEnv(): void {
    this.cqrsCommandHandler = this.configService.get('CQRS_COMMAND_HANDLER', '');
    this.isInLambda = !!this.configService.get('LAMBDA_TASK_ROOT', '');
  }

  setupMiddleware(): void {
    this.zellerSessionIdCheckEnabled = this.configService.get('IS_ZELLER_SESSION_ID_ENABLED', false) === 'true';
  }

  setupMfa(): void {
    this.mfaEnrolmentEnabled = this.configService.get('MFA_ENROLMENT_ENABLED', false) === 'true';
  }

  setupFirebase(): void {
    this.zellerAppFirebaseCredentials.privateKey = this.configService.get(
      'ZELLER_APP_FIREBASE_ADMIN_PRIVATE_KEY_SSM_NAME',
      '',
    );
    this.zellerAppFirebaseCredentials.adminClientEmail = this.configService.get(
      'ZELLER_APP_FIREBASE_ADMIN_EMAIL_SSM_NAME',
      '',
    );
    this.zellerAppFirebaseCredentials.projectId = this.configService.get('ZELLER_APP_FIREBASE_PROJECT_ID', '');
    this.allowZellerAppFirebaseTestToken =
      this.configService.get('ALLOW_ZELLER_APP_FIREBASE_TEST_TOKEN', false) === 'true';
  }

  setupAuth0MetadataFromToken(): void {
    this.auth0MetadataFromToken = this.configService.get('AUTH0_METADATA_FROM_TOKEN', false) === 'true';
  }

  setupAuth0Env(): void {
    this.auth0ClientKey = this.configService.get('AUTH0_CLIENT_ID', '');
    this.auth0ClientSecret = this.configService.get('AUTH0_CLIENT_SECRET', '');
    this.auth0Tenant = this.configService.get('AUTH0_TENANT', '');
    this.auth0JwtMaxAge = this.configService.get('AUTH0_JWT_MAX_AGE', '86400');
    this.sessionCacheTtlInSeconds = this.configService.get('SESSION_TTL', 86400); // 24 hrs
    this.auth0ApiUrls = new Auth0Urls(this.auth0Tenant);
    this.auth0ApiUrls.jwtIssuer = this.configService.get('OPENID_ISSUER_URL', '');
    this.zellerAppAuth0ClientKey = this.configService.get('ZELLER_APP_AUTH0_CLIENT_ID', '');
    this.auth0Audience = this.configService.get('AUTH0_AUDIENCE', '');
    this.auth0JwtMaxAge = this.configService.get('AUTH0_JWT_MAX_AGE', '86400');
    this.sensitiveAccessTokenMaxAge = this.configService.get('SENSITIVE_AUTH0_JWT_MAX_AGE', 300);
  }
}

export const getEnvService = () => {
  envService ??= new EnvironmentService();
  return envService;
};
