import { convertUTCToAEST } from './dateUtils';

describe('convertUTCToAEST', () => {
  it('should convert a valid UTC date string to AEST', () => {
    const utcDateString = '2025-03-10T13:00:00.000Z';
    const result = convertUTCToAEST(utcDateString);
    expect(result).toBe('2025-03-11T00:00:00+11:00');
  });

  it('should convert a valid UTC date string to AEST with 23hr', () => {
    const utcDateString = '2025-03-10T11:24:00.000Z';
    const result = convertUTCToAEST(utcDateString);
    expect(result).toBe('2025-03-10T22:24:00+11:00');
  });

  it('should convert a valid UTC date string to AEST', () => {
    const utcDateString = '2025-03-14T12:59:59.999Z';
    const result = convertUTCToAEST(utcDateString);
    expect(result).toBe('2025-03-14T23:59:59+11:00');
  });

  it('should handle invalid date strings', () => {
    const invalidDateString = 'invalid-date';
    const result = convertUTCToAEST(invalidDateString);
    expect(result).toBe('');
  });
});
