const getUtcOffset = (date: Date) => {
  const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }));
  const tzDate = new Date(date.toLocaleString('en-US', { timeZone: 'Australia/Melbourne' }));
  return (tzDate.getTime() - utcDate.getTime()) / 6e4;
};

const pad = (n: number, l = 2) => `${Math.floor(Math.abs(n))}`.padStart(l, '0');

// Convert to AEST/AEDT (Australia/Sydney timezone) and format as ISO-like string
export const convertUTCToAEST = (utcISOString: string) => {
  const utcDate = new Date(utcISOString);

  if (Number.isNaN(utcDate.getTime())) {
    return '';
  }
  const tzOffset = getUtcOffset(utcDate);
  const diff = tzOffset >= 0 ? '+' : '-';

  // '26/03/2025, 24:00:00'
  const aestDate = utcDate
    .toLocaleString('en-AU', {
      timeZone: 'Australia/Melbourne',
      year: 'numeric',
      hourCycle: 'h23', // es2020.intl
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    })
    .replace(', 24', ', 00');

  const [day, month, year, , time] = aestDate.split(/[/, ]/);
  return `${year}-${month}-${day}T${time}${diff}${pad(tzOffset / 60)}:${pad(tzOffset % 60)}`;
};
