/* eslint-disable */
import fs from 'fs';
import { yamlParse } from 'yaml-cfn';
import path from 'path';

const getSls = (stackName: string, config: any) =>
  `import { ApiAppServerlessStack,  vpcImport } from '@npco/component-bff-serverless';
import { envConfig } from './resources/crms/config';
import { plugins } from './resources/crms/plugins';\n
const sls = new ApiAppServerlessStack('${stackName}', envConfig, ${config.replace(
    /{/,
    '{',
  )});\nmodule.exports = sls.build();`;

const providerRemove = [
  'name',
  'runtime',
  'region',
  'vpc',
  'stackName',
  'bucketStackName',
  'deploymentBucket',
  'tags',
  'environment',
  'stackTags',
];

const customRemove = [
  'componentName',
  'partName',
  'serviceName',
  'timeout',
  'accountId',
  'bucketStackName',
  'timeout',
  'componentTableName',
  'amsApiEndpoint',
  'amsApiEndpointVersion',
];

const removeFromMap = (obj: any, remove: string[]) => {
  return Object.keys(obj)
    .filter((key) => !remove.includes(key))
    .reduce((p: any, key: any) => {
      p[key] = obj[key];
      return p;
    }, {});
};

const getProvider = (parsedConfig: any) => {
  return removeFromMap(parsedConfig.provider, providerRemove);
};
const stringObj = (obj: any) => (obj ? JSON.stringify(obj).replace(/^{|}$/gi, '') : '');

const getCustom = (parsedConfig: any, providerConfig: any) => {
  const customConfig = parsedConfig.custom;
  const custom = removeFromMap(customConfig, customRemove);
  return `{\n
  ${customConfig.dynamodbStackName ? '...envConfig.getDynamoDb(),' : ''}
  ${customConfig.auth0ClientId ? '...envConfig.getAuth0(),' : ''}
  ${customConfig.amsApiEndpoint ? '...envConfig.getAmsApi(),' : ''}
  ${customConfig.cqrsStackName ? '...envConfig.getComponentCqrs(),' : ''}
  vpcImport, ${stringObj({ ...providerConfig, ...custom })}}`;
};

const getPlugins = (parsedConfig: any) => {};

const envRemove = [
  'COMPONENT_NAME',
  'PART_NAME',
  'LOG_LEVEL',
  'GLOBAL_ACCOUNT_ID',
  'COMPONENT_TABLE',
];

const getEnv = (parsedConfig: any) => {
  if (!parsedConfig?.provider?.environment) {
    return '';
  }
  const environment = removeFromMap(parsedConfig.provider.environment, envRemove);
  return `environment: {
      ...envConfig.dotenvConfig,
    ${stringObj(environment)}
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
  }`;
};

const assembleConfig = (parsedConfig: any) => {
  console.log('parsedConfig', parsedConfig);
  const stackName = parsedConfig.service.split('-').pop();
  const plugins = getPlugins(parsedConfig);
  const provider = getProvider(parsedConfig);
  const custom = getCustom(parsedConfig, provider);
  const sls = getSls(stackName, `{ plugins, custom: ${custom},${getEnv(parsedConfig)}}`);
  console.log('sls', sls);
  return sls;
};

const parseSlsConfig = (filePath: string) => {
  console.log('file', filePath);
  const dir = path.join(process.cwd(), filePath);
  const out = assembleConfig(yamlParse(fs.readFileSync(dir, 'utf8')));
  const ts = dir.replace('.yml', '.ts').replace('.yaml', '.ts');
  fs.writeFileSync(ts, out);
};

if (process.argv.length < 3) {
  console.error('file name must be provided');

  process.exit(1);
}

parseSlsConfig(process.argv[2]);
