import { AwsRegions } from '../../param';

import { createManagedPolicies, ManagedPolicy } from './managedPolicy';

describe('test managedPolicy', () => {
  const originalRegion = process.env.AWS_REGION;
  const originalLondonAccountId = process.env.LONDON_ACCOUNT_ID;

  beforeAll(() => {
    process.env.AWS_REGION = AwsRegions.SYDNEY;
    process.env.LONDON_ACCOUNT_ID = '123456';
  });

  afterAll(() => {
    process.env.AWS_REGION = originalRegion;
    process.env.LONDON_ACCOUNT_ID = originalLondonAccountId;
  });

  it('should build empty managed policies', () => {
    expect(createManagedPolicies([])).toEqual({});
  });

  it('should throw error on none defined policies', () => {
    expect(() => createManagedPolicies(['notDefined' as any])).toThrowError();
  });

  it('should build managed policies', () => {
    expect(createManagedPolicies([ManagedPolicy.lambdaVpcPolicy], false)).toEqual({
      resources: {
        Resources: {
          lambdaVpcPolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: [
                      'ec2:CreateNetworkInterface',
                      'ec2:DescribeNetworkInterfaces',
                      'ec2:DeleteNetworkInterface',
                    ],
                    Resource: '*',
                  },
                ],
              },
            },
          },
        },
        Outputs: {
          lambdaVpcPolicyArn: {
            Value: {
              Ref: 'lambdaVpcPolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.service}', '-lambdaVpcPolicyArn']],
              },
            },
          },
        },
      },
    });
  });

  it('should build managed policies for BFF configuration', () => {
    expect(
      createManagedPolicies(
        [
          ManagedPolicy.lambdaVpcPolicy,
          ManagedPolicy.xrayPolicy,
          ManagedPolicy.entityTableQueryRolePolicy,
          ManagedPolicy.entityTableWriteItemRolePolicy,
          ManagedPolicy.sessionCacheTableDBPolicy,
          ManagedPolicy.deviceTableWriteItemRolePolicy,
          ManagedPolicy.deviceTableQueryRolePolicy,
          ManagedPolicy.crmsEntityTableWriteRolePolicy,
          ManagedPolicy.allGsiQueryRolePolicyArn,
          ManagedPolicy.permissionsTableReadRolePolicyArn,
          ManagedPolicy.pgsV2RootCaSecretPolicyArn,
          ManagedPolicy.domicileLookupTableReadRolePolicyArn,
          ManagedPolicy.crossAccountInvocationPolicyArn,
        ],
        true,
      ),
    ).toEqual({
      resources: {
        Resources: {
          xrayPolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: [
                      'xray:PutTraceSegments',
                      'xray:PutTelemetryRecords',
                      'xray:GetSamplingRules',
                      'xray:GetSamplingTargets',
                      'xray:GetSamplingStatisticSummaries',
                    ],
                    Resource: '*',
                  },
                ],
              },
            },
          },
          lambdaVpcPolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: [
                      'ec2:CreateNetworkInterface',
                      'ec2:DescribeNetworkInterfaces',
                      'ec2:DeleteNetworkInterface',
                    ],
                    Resource: '*',
                  },
                ],
              },
            },
          },
          entityTableQueryRolePolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: ['dynamodb:Query', 'dynamodb:GetItem', 'dynamodb:BatchGetItem'],
                    Resource: [
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}/index/${env:ENTITY_GSI}',
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}/index/${env:SHORT_ID_GSI}',
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}/index/${env:TYPE_GSI}',
                    ],
                  },
                ],
              },
            },
          },
          entityTableWriteItemRolePolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: ['dynamodb:UpdateItem', 'dynamodb:PutItem'],
                    Resource: [
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
                    ],
                  },
                ],
              },
            },
          },
          sessionCacheTableDBPolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: ['dynamodb:Query', 'dynamodb:UpdateItem', 'dynamodb:PutItem'],
                    Resource: [
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:SESSION_CACHE_TABLE}',
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:SESSION_CACHE_TABLE}/index/${env:ACCESS_TOKEN_GSI}',
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:SESSION_CACHE_TABLE}/index/${env:ENTITY_CACHE_GSI}',
                    ],
                  },
                  {
                    Effect: 'Allow',
                    Action: ['dynamodb:Query'],
                    Resource: [
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
                    ],
                  },
                ],
              },
            },
          },
          deviceTableWriteItemRolePolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: ['dynamodb:UpdateItem', 'dynamodb:PutItem'],
                    Resource: [
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
                    ],
                  },
                ],
              },
            },
          },
          deviceTableQueryRolePolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: ['dynamodb:Query', 'dynamodb:GetItem', 'dynamodb:BatchGetItem'],
                    Resource: [
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}/index/${env:ENTITY_GSI}',
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}/index/${env:SHORT_ID_GSI}',
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}/index/${env:TYPE_GSI}',
                    ],
                  },
                ],
              },
            },
          },
          entityTableWriteRolePolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: ['dynamodb:UpdateItem', 'dynamodb:PutItem'],
                    Resource: [
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
                    ],
                  },
                ],
              },
            },
          },
          allGsiQueryRolePolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: ['dynamodb:Query'],
                    Resource: [
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}/index/*',
                    ],
                  },
                ],
              },
            },
          },
          permissionsTableReadRolePolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: ['dynamodb:Query', 'dynamodb:BatchGetItem', 'dynamodb:GetItem'],
                    Resource: [
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.permissionsTableName}',
                    ],
                  },
                ],
              },
            },
          },
          pgsV2RootCaSecretPolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: ['secretsmanager:GetSecretValue'],
                    Resource: [
                      'arn:aws:secretsmanager:ap-southeast-2:************:secret:devops-aws-private-ca-rootCA-certificate-7Mbp8I',
                    ],
                  },
                  {
                    Effect: 'Allow',
                    Action: ['kms:Decrypt', 'kms:DescribeKey', 'kms:GenerateDataKey'],
                    Resource: ['arn:aws:kms:ap-southeast-2:************:key/mrk-f2547cc4d5624aaba45441baf16789e7'],
                  },
                ],
              },
            },
          },
          domicileLookupTableReadRolePolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: ['dynamodb:Query', 'dynamodb:BatchGetItem', 'dynamodb:GetItem'],
                    Resource: [
                      'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.domicileLookupTableName}',
                    ],
                  },
                ],
              },
            },
          },
          crossAccountInvocationPolicy: {
            Type: 'AWS::IAM::ManagedPolicy',
            Properties: {
              PolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Action: ['lambda:InvokeFunction'],
                    Resource: [
                      `arn:aws:lambda:${AwsRegions.LONDON}:123456:function:\${env:COMPONENT_NAME}-\${env:PART_NAME}*`,
                    ],
                  },
                ],
              },
            },
          },
        },
        Outputs: {
          xrayPolicyArn: {
            Value: {
              Ref: 'xrayPolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-xrayPolicyArn']],
              },
            },
          },
          lambdaVpcPolicyArn: {
            Value: {
              Ref: 'lambdaVpcPolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-lambdaVpcPolicyArn']],
              },
            },
          },
          entityTableQueryRolePolicyArn: {
            Value: {
              Ref: 'entityTableQueryRolePolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-entityTableQueryRolePolicyArn']],
              },
            },
          },
          entityTableWriteItemRolePolicyArn: {
            Value: {
              Ref: 'entityTableWriteItemRolePolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-entityTableWriteItemRolePolicyArn']],
              },
            },
          },
          sessionCacheTableDBPolicyArn: {
            Value: {
              Ref: 'sessionCacheTableDBPolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-sessionCacheTableDBPolicyArn']],
              },
            },
          },
          deviceTableWriteItemRolePolicyArn: {
            Value: {
              Ref: 'deviceTableWriteItemRolePolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-deviceTableWriteItemRolePolicyArn']],
              },
            },
          },
          deviceTableQueryRolePolicyArn: {
            Value: {
              Ref: 'deviceTableQueryRolePolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-deviceTableQueryRolePolicyArn']],
              },
            },
          },
          entityTableWriteRolePolicyArn: {
            Value: {
              Ref: 'entityTableWriteRolePolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-entityTableWriteRolePolicyArn']],
              },
            },
          },
          allGsiQueryRolePolicyArn: {
            Value: {
              Ref: 'allGsiQueryRolePolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-allGsiQueryRolePolicyArn']],
              },
            },
          },
          permissionsTableReadRolePolicyArn: {
            Value: {
              Ref: 'permissionsTableReadRolePolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-permissionsTableReadRolePolicyArn']],
              },
            },
          },
          domicileLookupTableReadRolePolicyArn: {
            Value: {
              Ref: 'domicileLookupTableReadRolePolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-domicileLookupTableReadRolePolicyArn']],
              },
            },
          },
          pgsV2RootCaSecretPolicyArn: {
            Value: {
              Ref: 'pgsV2RootCaSecretPolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-pgsV2RootCaSecretPolicyArn']],
              },
            },
          },
          crossAccountInvocationPolicyArn: {
            Value: {
              Ref: 'crossAccountInvocationPolicy',
            },
            Export: {
              Name: {
                'Fn::Join': ['', ['${self:custom.serviceName}', '-crossAccountInvocationPolicyArn']],
              },
            },
          },
        },
      },
    });
  });
});
