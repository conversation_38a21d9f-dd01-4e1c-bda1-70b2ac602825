import type { IamRoleStatement, Resources } from 'serverless/aws';

import { Action, Arn, Env, self } from '../../param';
import { getRemoteAccountId, getRemoteRegion } from '../../param/util';
import { pgsRootCaSsmArn } from '../../serverless/common';

export enum ManagedPolicy {
  lambdaVpcPolicy = 'lambdaVpcPolicyArn',
  xrayPolicy = 'xrayPolicyArn',
  entityTableQueryRolePolicy = 'entityTableQueryRolePolicyArn',
  entityTableWriteItemRolePolicy = 'entityTableWriteItemRolePolicyArn',
  sessionCacheTableDBPolicy = 'sessionCacheTableDBPolicyArn',
  deviceTableWriteItemRolePolicy = 'deviceTableWriteItemRolePolicyArn',
  deviceTableQueryRolePolicy = 'deviceTableQueryRolePolicyArn',
  crmsEntityTableWriteRolePolicy = 'entityTableWriteRolePolicyArn',
  entityTableDeleteRolePolicy = 'entityTableDeleteRolePolicyArn',
  allGsiQueryRolePolicyArn = 'allGsiQueryRolePolicyArn',
  permissionsTableReadRolePolicyArn = 'permissionsTableReadRolePolicyArn',
  pgsV2RootCaSecretPolicyArn = 'pgsV2RootCaSecretPolicyArn',
  domicileLookupTableReadRolePolicyArn = 'domicileLookupTableReadRolePolicyArn',
  crossAccountInvocationPolicyArn = 'crossAccountInvocationPolicyArn',
}

const CACHE_TABLE_NAME = `${self.custom.dynamodbStackName}-${Env.SESSION_CACHE_TABLE}` as const;
const COMPONENT_TABLE_NAME = `${self.custom.dynamodbStackName}-${Env.COMPONENT_TABLE}` as const;

const ManagedPolicyStatement = (
  managedPolicy: ManagedPolicy,
  accountId?: string,
  regions: string[] = [],
): IamRoleStatement[] => {
  switch (managedPolicy) {
    case ManagedPolicy.xrayPolicy:
      return [
        {
          Effect: 'Allow',
          Action: [
            Action.xray.PutTraceSegments,
            Action.xray.PutTelemetryRecords,
            Action.xray.GetSamplingRules,
            Action.xray.GetSamplingTargets,
            Action.xray.GetSamplingStatisticSummaries,
          ],
          Resource: '*',
        },
      ];
    case ManagedPolicy.lambdaVpcPolicy:
      return [
        {
          Effect: 'Allow',
          Action: [
            Action.ec2.CreateNetworkInterface,
            Action.ec2.DescribeNetworkInterfaces,
            Action.ec2.DeleteNetworkInterface,
          ],
          Resource: '*',
        },
      ];
    case ManagedPolicy.sessionCacheTableDBPolicy:
      return [
        {
          Effect: 'Allow',
          Action: [Action.dynamodb.Query, Action.dynamodb.UpdateItem, Action.dynamodb.PutItem],
          Resource: [
            Arn.dynamodb.table(CACHE_TABLE_NAME),
            Arn.dynamodb.gsi(CACHE_TABLE_NAME, Env.ACCESS_TOKEN_GSI),
            Arn.dynamodb.gsi(CACHE_TABLE_NAME, Env.ENTITY_CACHE_GSI),
          ],
        },
        {
          Effect: 'Allow',
          Action: [Action.dynamodb.Query],
          Resource: [Arn.dynamodb.table(COMPONENT_TABLE_NAME)],
        },
      ];
    case ManagedPolicy.deviceTableQueryRolePolicy:
    case ManagedPolicy.entityTableQueryRolePolicy:
      return [
        {
          Effect: 'Allow',
          Action: [Action.dynamodb.Query, Action.dynamodb.GetItem, Action.dynamodb.BatchGetItem],
          Resource: [
            Arn.dynamodb.table(COMPONENT_TABLE_NAME),
            Arn.dynamodb.gsi(COMPONENT_TABLE_NAME, Env.ENTITY_GSI),
            Arn.dynamodb.gsi(COMPONENT_TABLE_NAME, Env.SHORT_ID_GSI),
            Arn.dynamodb.gsi(COMPONENT_TABLE_NAME, Env.TYPE_GSI),
          ],
        },
      ];
    case ManagedPolicy.deviceTableWriteItemRolePolicy:
    case ManagedPolicy.entityTableWriteItemRolePolicy:
    case ManagedPolicy.crmsEntityTableWriteRolePolicy:
      return [
        {
          Effect: 'Allow',
          Action: [Action.dynamodb.UpdateItem, Action.dynamodb.PutItem],
          Resource: [Arn.dynamodb.table(COMPONENT_TABLE_NAME)],
        },
      ];
    case ManagedPolicy.entityTableDeleteRolePolicy:
      return [
        {
          Effect: 'Allow',
          Action: [Action.dynamodb.DeleteItem],
          Resource: [Arn.dynamodb.table(COMPONENT_TABLE_NAME)],
        },
      ];
    case ManagedPolicy.allGsiQueryRolePolicyArn:
      return [
        {
          Effect: 'Allow',
          Action: [Action.dynamodb.Query],
          Resource: [Arn.dynamodb.gsi(COMPONENT_TABLE_NAME, '*')],
        },
      ];
    case ManagedPolicy.permissionsTableReadRolePolicyArn: {
      return [
        {
          Effect: 'Allow',
          Action: [Action.dynamodb.Query, Action.dynamodb.BatchGetItem, Action.dynamodb.GetItem],
          Resource: [Arn.dynamodb.table(self.custom.permissionsTableName)],
        },
      ];
    }
    case ManagedPolicy.pgsV2RootCaSecretPolicyArn: {
      return [
        {
          Effect: 'Allow',
          Action: [Action.secretsmanager.GetSecretValue],
          Resource: [pgsRootCaSsmArn],
        },
        {
          Effect: 'Allow',
          Action: [Action.kms.Decrypt, Action.kms.DescribeKey, Action.kms.GenerateDataKey],
          Resource: ['arn:aws:kms:ap-southeast-2:************:key/mrk-f2547cc4d5624aaba45441baf16789e7'],
        },
      ];
    }
    case ManagedPolicy.domicileLookupTableReadRolePolicyArn: {
      return [
        {
          Effect: 'Allow',
          Action: [Action.dynamodb.Query, Action.dynamodb.BatchGetItem, Action.dynamodb.GetItem],
          Resource:
            regions?.map((region) => Arn.dynamodb.table(self.custom.domicileLookupTableName, accountId, region)) ?? [],
        },
      ];
    }
    case ManagedPolicy.crossAccountInvocationPolicyArn: {
      return [
        {
          Effect: 'Allow',
          Action: [Action.lambda.InvokeFunction],
          Resource: regions?.map(() => {
            const region = process.env.AWS_REGION;
            const remoteRegion = getRemoteRegion(region);
            const remoteAccountId = getRemoteAccountId(region);
            return `arn:aws:lambda:${remoteRegion}:${remoteAccountId}:function:\${env:COMPONENT_NAME}-\${env:PART_NAME}*`;
          }),
        },
      ];
    }
    default:
      throw new Error(`Managed Policy ${managedPolicy} doesn't have a configuration`);
  }
};

export const createManagedPolicies = (
  managedPolicies?: ManagedPolicy[],
  bffStack?: boolean,
  accountId?: string,
  regions: string[] = ['${self:provider.region}'],
): { resources?: Resources } => {
  if (managedPolicies?.length) {
    const resources = managedPolicies.reduce((policies: any, mp: ManagedPolicy) => {
      const policiesInstance = { ...policies };
      policiesInstance[mp.replace('Arn', '')] = {
        Type: 'AWS::IAM::ManagedPolicy',
        Properties: {
          PolicyDocument: {
            Version: '2012-10-17',
            Statement: ManagedPolicyStatement(mp, accountId, regions),
          },
        },
      };
      return policiesInstance;
    }, {});
    const service = bffStack ? self.custom.serviceName : self.custom.service;
    const outputs = managedPolicies.reduce((policies: any, mp: ManagedPolicy) => {
      const policiesInstance = { ...policies };
      policiesInstance[mp] = {
        Value: {
          Ref: mp.replace('Arn', ''),
        },
        Export: {
          Name: {
            'Fn::Join': ['', [service, `-${mp}`]],
          },
        },
      };
      return policiesInstance;
    }, {});

    return {
      resources: {
        Resources: resources,
        Outputs: outputs,
      },
    };
  }

  return {};
};
