import { SSMClient, GetParameterCommand } from '@aws-sdk/client-ssm';
import { NodeHttpHandler } from '@smithy/node-http-handler';

let client: SSMClient;
const region = process.env.AWS_REGION || 'ap-southeast-2';

const getClient = () => {
  if (!client) {
    client = new SSMClient({
      region,
      requestHandler: new NodeHttpHandler({
        requestTimeout: 2000,
        connectionTimeout: 2000,
      }),
      maxAttempts: 3,
    });
  }
  return client;
};

export const getSsmValue = async (name: string) => {
  try {
    const value = (
      await getClient().send(
        new GetParameterCommand({
          Name: name,
          WithDecryption: true,
        }),
      )
    ).Parameter?.Value;

    if (value) {
      return value;
    }
  } catch (error) {
    console.error(`Error fetching SSM Parameter ${name}:`, error);
    throw error;
  }
  throw new Error(`SSM Parameter ${name} is not set.`);
};
