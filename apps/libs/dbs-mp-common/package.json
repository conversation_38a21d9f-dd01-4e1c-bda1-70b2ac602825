{"name": "@npco/component-dbs-mp-common", "version": "3.1.282", "description": "Device Backend and Merchant Portal common module", "main": "dist/index.js", "types": "dist/index.d.ts", "compilerOptions": {"module": "commonjs", "declaration": false, "noImplicitAny": false, "noUnusedLocals": true, "removeComments": true, "noLib": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "ES2021", "sourceMap": true, "allowJs": true, "outDir": "./dist"}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"], "scripts": {"isPublished": "bash ./bin/is-published.sh", "test": "jest --no-cache --maxWorkers=75% --bail=1", "lint": "eslint src --quiet ", "run-audit": "yarn npm audit --environment production", "deploy": "mkdir dist/fonts && cp src/fonts/* dist/fonts/ && yarn npm publish", "clean": "rm -rf dist"}, "repository": {"type": "git", "url": "git+https://github.com/zeller-engineering/component-bff.git", "directory": "apps/libs/dbs-mp-common"}, "keywords": ["component", "dbs", "mp", "event", "events"], "license": "ISC", "homepage": "https://bitbucket.org/npco_dev/component-dbs-mp-common#readme", "dependencies": {"@aws-sdk/client-appsync": "3.435.0", "@aws-sdk/client-dynamodb-streams": "3.435.0", "@aws-sdk/lib-dynamodb": "3.435.0", "@aws-sdk/util-dynamodb": "3.435.0", "@nestjs/common": "^7.4.2", "@nestjs/config": "^0.5.0", "@nestjs/core": "^7.4.2", "@npco/bff-common": "workspace:*", "@npco/component-bff-core": "workspace:*", "@npco/component-dto-addressbook": "workspace:*", "@npco/component-dto-cardholder": "workspace:*", "@npco/component-dto-catalog": "workspace:*", "@npco/component-dto-cnp": "workspace:*", "@npco/component-dto-connection": "workspace:*", "@npco/component-dto-core": "workspace:*", "@npco/component-dto-cpoc": "workspace:*", "@npco/component-dto-customer": "workspace:*", "@npco/component-dto-deposit": "workspace:*", "@npco/component-dto-device": "workspace:*", "@npco/component-dto-digital-wallet-token": "workspace:*", "@npco/component-dto-entity": "workspace:*", "@npco/component-dto-invoice": "workspace:*", "@npco/component-dto-issuing-account": "workspace:*", "@npco/component-dto-issuing-card": "workspace:*", "@npco/component-dto-merchant": "workspace:*", "@npco/component-dto-notifications": "workspace:*", "@npco/component-dto-order": "workspace:*", "@npco/component-dto-payment-instrument": "workspace:*", "@npco/component-dto-pos-interface": "workspace:*", "@npco/component-dto-promotion": "workspace:*", "@npco/component-dto-richdata": "workspace:*", "@npco/component-dto-sim": "workspace:*", "@npco/component-dto-site": "workspace:*", "@npco/component-dto-subscription": "workspace:*", "@npco/component-dto-ticket": "workspace:*", "@npco/component-dto-transaction": "workspace:*", "apollo-client": "^2.6.10", "aws-appsync": "4.1.10", "aws-xray-sdk-core": "^3.5.0", "axios": "^1.8.2", "cross-fetch": "^3.1.4", "date-fns": "^2.29.3", "date-fns-legacy": "npm:date-fns@2.16.1", "date-fns-tz": "^2.0.0", "eslint": "^8.54.0", "exceljs": "^4.3.0", "graphql": "^15.7.0", "graphql-tag": "^2.12.5", "heic-convert": "1.2.4", "isomorphic-fetch": "^3.0.0", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.0.1", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "long": "^5.2.1", "pdfkit": "^0.13.0", "pngjs": "7.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.5.7", "semver": "^7.5.2", "sharp": "0.33.4", "uuid": "^9.0.0", "ws": "^8.17.1"}, "devDependencies": {"@aws-sdk/client-dynamodb": "3.435.0", "@aws-sdk/client-s3": "3.435.0", "@aws-sdk/client-sns": "3.435.0", "@aws-sdk/s3-presigned-post": "3.435.0", "@nestjs/testing": "^7.4.2", "@npco/component-dto-issuing-transaction": "workspace:*", "@npco/eslint-config-backend": "^1.0.8", "@nx/jest": "20.7.0", "@rushstack/eslint-patch": "^1.6.0", "@shelf/jest-dynamodb": "^3.4.1", "@smithy/protocol-http": "^3.0.4", "@smithy/types": "^2.3.2", "@types/aws-lambda": "^8.10.119", "@types/geojson": "^7946.0.10", "@types/heic-convert": "^1.2.0", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.6", "@types/lodash": "^4.17.5", "@types/long": "^5.0.0", "@types/mocha": "^10.0.1", "@types/node": "18.19.14", "@types/pdfkit": "^0.12.10", "@types/pngjs": "^6.0.1", "@types/semver": "^7.5.0", "@types/uuid": "^9.0.2", "apollo-client": "^2.6.10", "aws-sdk-client-mock": "^3.0.0", "bff-ecommerce-graphql": "workspace:*", "eslint-plugin-sonarjs": "^0.19.0", "fp-ts": "^2.9.5", "husky": "^8.0.3", "improved-yarn-audit": "^3.0.0", "jest": "^29.7.0", "jest-html-reporter": "^3.10.2", "mock-jwks": "^1.0.9", "nock": "^13.3.2", "nx": "20.7.0", "ts-jest": "^29.1.1", "ts-mockito": "^2.5.0", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "prettier": "@npco/eslint-config-backend/prettier", "jestSonar": {"reportPath": "dist"}}