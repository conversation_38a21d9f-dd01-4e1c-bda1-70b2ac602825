import { getAmsEndpoint } from '@npco/component-bff-core/dist/amsApi/amsUtils';
import { ServerError } from '@npco/component-bff-core/dist/error';
import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { debug, info, error } from '@npco/component-bff-core/dist/utils/logger';
import type { EntityCategories } from '@npco/component-dto-core';
import type { PhoneRegisterResponse, PhoneVerifyResponse, KycCheckpoint } from '@npco/component-dto-customer';

import type { AxiosError, AxiosResponse, AxiosRequestConfig } from 'axios';

import type { JsonObject } from '../types';

import { ApiService } from './apiService';

export class AmsApiService extends ApiService {
  static readonly DEFAULT_HTTP_CONFIG: AxiosRequestConfig = {
    headers: { 'Content-Type': 'application/json' },
  };

  httpConfig: AxiosRequestConfig = AmsApiService.DEFAULT_HTTP_CONFIG;

  protected override externalEndpoint = false;

  getHttpConfig = () => {
    return this.httpConfig;
  };

  // Override "GET" method to optionally pass 'domicile' when needed
  public get = async <ResponseDto = void>(
    uuid: string,
    path = '',
    params?: JsonObject<any>,
    headers?: { [key: string]: string },
    domicile?: Domicile,
  ): Promise<ResponseDto> => {
    const urlPath = `${uuid}${path}`;
    const urlEndpoint = this.getEndpoint(urlPath, domicile);
    const extraInfo = path ? `with path ${path}` : '';
    info(`Sending a GET "${this.modelType}" request to "${urlEndpoint}" ${extraInfo}`, uuid);
    return this.httpService
      .get(urlEndpoint, this.constructConfig(params, headers))
      .then((res: AxiosResponse<ResponseDto>) => {
        this.checkApiError(res);
        return res.data;
      })
      .catch((e) => this.handleApiError(e, `Failed to get "${this.modelType}" with id: "${uuid}"`, uuid));
  };

  // Override "CREATE" method to optionally pass 'domicile' when needed
  public create = async <CreateDto = void>(uuid: string, dto: CreateDto, domicile?: Domicile): Promise<any> => {
    const urlEndpoint = this.getEndpoint('', domicile);
    info(`Sending a CREATE "${this.modelType}" request to "${urlEndpoint}"`, uuid);
    return this.httpService
      .post(urlEndpoint, JSON.stringify(dto), this.constructConfig())
      .then((res: AxiosResponse<any>) => {
        info(`Response from CREATE "${this.modelType}" endpoint: ${JSON.stringify(res?.data)}`, uuid);
        this.checkApiError(res);
        const parsedRes = res?.data?.body ? JSON.parse(res.data.body) : {};
        info(`Returning response from CREATE: ${JSON.stringify({ id: uuid, ...parsedRes })}`, uuid);
        return { ...parsedRes, id: uuid };
      })
      .catch((e) => this.handleApiError(e, `Failed to create "${this.modelType}"`, uuid));
  };

  // "CREATE ENTITY" method when entityUuid not present and to optionally pass 'domicile' when needed
  public createEntity = async <CreateDto = void>(dto: CreateDto, domicile?: Domicile): Promise<any> => {
    info(`[Create Entity] Dto: ${JSON.stringify(dto)}, Domicile: ${domicile}`);
    const urlEndpoint = this.getEndpoint('', domicile);
    info(`Sending a createEntity "${this.modelType}" request to "${urlEndpoint}"`);
    return this.httpService
      .post(urlEndpoint, JSON.stringify(dto), this.constructConfig())
      .then((res: AxiosResponse<any>) => {
        info(`Response from createEntity "${this.modelType}" endpoint: ${JSON.stringify(res?.data)}`);
        this.checkApiError(res);
        const parsedRes = res?.data?.body ? JSON.parse(res.data.body) : {};
        info(`Returning from createEntity: ${JSON.stringify(parsedRes)}`);
        return parsedRes;
      })
      .catch((e) => this.handleApiError(e, `Failed to createEntity "${this.modelType}"`));
  };

  // Override "DELETE" method to optionally pass 'domicile' when needed
  public delete = async (
    uuid: string,
    path = '',
    headers: { [key: string]: string } = {},
    domicile?: Domicile,
  ): Promise<boolean> => {
    const urlPath = `${path}${uuid}`;
    const urlEndpoint = this.getEndpoint(urlPath, domicile);
    info(`Sending a DELETE "${this.modelType}" request to ${urlEndpoint}`, uuid);
    return this.httpService
      .delete(urlEndpoint, this.constructConfig(undefined, headers))
      .then((res: AxiosResponse) =>
        this.checkApiResponse(res, `Delete "${this.modelType}" request FAILED with id: "${uuid}"`, uuid),
      )
      .catch((e) => this.handleApiError(e, `Failed to delete "${this.modelType}" with id: "${uuid}"`, uuid));
  };

  // Override "UPDATE" method to optionally pass 'domicile' when needed
  public update = async <UpdateDto = void>(
    uuid: string,
    updateEventDto: UpdateDto,
    path = '',
    domicile?: Domicile,
  ): Promise<boolean> => {
    const urlPath = `${uuid}${path}`;
    const urlEndpoint = this.getEndpoint(urlPath, domicile);
    info(`Send ${this.modelType} update event to ${urlEndpoint}.`, uuid);
    return this.httpService
      .patch(urlEndpoint, JSON.stringify(updateEventDto), this.constructConfig())
      .then((res: AxiosResponse<any>) =>
        this.checkApiResponse(res, `Update "${this.modelType}" request FAILED ${uuid}`, uuid),
      )
      .catch((e) => this.handleApiError(e, `Failed to update "${this.modelType}" with id: "${uuid}"`, uuid));
  };

  public post = async <UpdateDto = void, OutputDto = any>(
    path: string,
    dto: UpdateDto,
    loggingInfo: {
      aggregateId?: string;
      errorMessage: string;
    },
    domicile?: Domicile,
  ): Promise<OutputDto> => {
    return this.httpService
      .post(this.getEndpoint(path, domicile), JSON.stringify(dto), this.constructConfig())
      .then((response: AxiosResponse<OutputDto>) => {
        return this.handleResponse(response, loggingInfo);
      })
      .catch((e) => this.handleApiError(e, loggingInfo.errorMessage, loggingInfo.aggregateId));
  };

  public patch = async <UpdateDto = void, OutputDto = any>(
    path: string,
    dto: UpdateDto,
    loggingInfo: {
      aggregateId?: string;
      errorMessage: string;
    },
    headers?: { [key: string]: string },
    domicile?: Domicile,
  ): Promise<OutputDto> => {
    return this.httpService
      .patch(this.getEndpoint(path, domicile), JSON.stringify(dto), this.constructConfig(undefined, headers))
      .then((response: AxiosResponse<OutputDto>) => {
        return this.handleResponse(response, loggingInfo);
      })
      .catch((e) => this.handleApiError(e, loggingInfo.errorMessage, loggingInfo.aggregateId));
  };

  public assign = async (
    assignUuid: string,
    targetUuid: string,
    targetType: string,
    body = {},
    headers: { [key: string]: string } = {},
    domicile?: Domicile,
  ): Promise<boolean> => {
    const err = `Failed to assign ${this.modelType} ${assignUuid} to ${targetType} ${targetUuid}`;
    const path = `${assignUuid}/${targetType}/${targetUuid}`;
    const url = this.getEndpoint(path, domicile);
    info(`Assign ${this.modelType} to ${targetType}. ${url}`);
    return this.httpService
      .post(url, JSON.stringify(body), this.constructConfig(undefined, headers))
      .then((res: AxiosResponse) => this.checkApiResponse(res, err, targetUuid))
      .catch((e) => this.handleApiError(e, err, targetUuid));
  };

  public unassign = async (
    assignUuid: string,
    targetUuid: string,
    targetType: string,
    headers: { [key: string]: string } = {},
    domicile?: Domicile,
  ): Promise<boolean> => {
    const err = `Failed to unassign ${this.modelType} ${assignUuid} from ${targetType} ${targetUuid}`;
    const path = `${assignUuid}/${targetType}/${targetUuid}`;
    const url = this.getEndpoint(path, domicile);
    info(`unassign ${this.modelType} from ${targetType}. ${url}`);
    return this.httpService
      .delete(url, this.constructConfig(undefined, headers))
      .then((res: AxiosResponse) => this.checkApiResponse(res, err, targetUuid))
      .catch((e) => this.handleApiError(e, err, targetUuid));
  };

  public addEntityTag = async (entityUuid: string, name: string, domicile?: Domicile): Promise<boolean> => {
    const err = `failed to add '${name}' tag from ${entityUuid}`;
    const path = `${entityUuid}/tag`;
    const url = this.getEndpoint(path, domicile);
    return this.httpService
      .post(url, JSON.stringify({ name }), this.constructConfig())
      .then((res: AxiosResponse) => this.checkApiResponse(res, err, entityUuid))
      .catch((e) => this.handleApiError(e, err, entityUuid));
  };

  public addEntitySubcategory = async (
    entityUuid: string,
    category: EntityCategories,
    subcategory: string,
    domicile?: Domicile,
  ): Promise<string> => {
    const err = `failed to add '${subcategory}' subcategory to ${entityUuid}`;
    const path = `${entityUuid}/subcategory`;
    const url = this.getEndpoint(path, domicile);
    return this.httpService
      .post(url, JSON.stringify({ category, subcategory }), this.constructConfig())
      .then((response: AxiosResponse) => {
        debug(response);
        info(`invoke ams endpoint response: ${JSON.stringify(response.data)} ${response.status}`, entityUuid);
        const e = this.readErrors(response);
        if (e) {
          error(err, entityUuid);
          error(e);
          return Promise.reject(e); // NOSONAR
        }
        return response.data;
      })
      .catch((e) => this.handleApiError(e, err, entityUuid));
  };

  public updateEntitySubcategory = async (
    entityUuid: string,
    subcategoryUuid: string,
    subcategory: string,
    domicile?: Domicile,
  ): Promise<boolean> => {
    const path = `${entityUuid}/subcategory/${subcategoryUuid}`;
    const url = this.getEndpoint(path, domicile);
    return this.customPatch(url, entityUuid, 'subcategory', { subcategory });
  };

  public linkTags = async (
    entityUuid: string,
    tagNames: string[],
    type: string,
    aggregateId: string,
    domicile?: Domicile,
  ): Promise<boolean> => {
    const path = `${entityUuid}/tag/link`;
    const url = this.getEndpoint(path, domicile);
    return this.customPost(url, entityUuid, 'link tags', { entityUuid, tagNames, type, aggregateId });
  };

  public selectDepositAccount = async (
    entityUuid: string,
    uuid: string,
    remitToCard: boolean,
    domicile?: Domicile,
  ): Promise<boolean> => {
    const path = `${entityUuid}/depositaccount/${uuid}`;
    const url = this.getEndpoint(path, domicile);
    return this.customPatch(url, uuid, 'select deposit account', { remitToCard });
  };

  public updateDeviceInfo = async <UpdateDto = void>(
    uuid: string,
    updateEventDto: UpdateDto,
    domicile?: Domicile,
  ): Promise<boolean> => {
    const path = `info/${uuid}`;
    const url = this.getEndpoint(path, domicile);
    return this.customPatch(url, uuid, 'Device info update', updateEventDto);
  };

  public updatePAH = async (customerUuid: string, entityUuid: string, domicile?: Domicile): Promise<boolean> => {
    const path = `${entityUuid}/updatePAH`;
    const url = this.getEndpoint(path, domicile);
    return this.httpService
      .put(url, { customerUuid }, this.constructConfig())
      .then(() => true)
      .catch((e) => this.handleApiError(e, 'Failed to set PAH', customerUuid));
  };

  public sendInviteEmail = async (
    uuid: string,
    entityUuid?: string,
    domicile?: Domicile,
  ): Promise<{ [key: string]: string }> => {
    const path = `${uuid}/email-invite`;
    const url = this.getEndpoint(path, domicile);
    info(`trigger reset password for customer ${uuid}`);
    return this.httpService
      .post(url, entityUuid ? { entityUuid } : null, this.constructConfig())
      .then(() => ({ id: uuid }))
      .catch((e) => this.handleApiError(e, 'Failed to send reset password email.', uuid));
  };

  public updateCustomerPassword = async (uuid: string, password: string, domicile?: Domicile): Promise<boolean> => {
    const path = `${uuid}/update-password`;
    const url = this.getEndpoint(path, domicile);
    info(`update password for customer ${uuid}`);
    return this.httpService
      .post(url, { password }, this.constructConfig())
      .then(() => true)
      .catch((e) => this.handleApiError(e, 'Failed to update customer password.', uuid));
  };

  public createResetPasswordLink = async (uuid: string, domicile?: Domicile): Promise<string> => {
    const path = `${uuid}/password-reset-link`;
    const url = this.getEndpoint(path, domicile);
    info(`create reset password link customer ${uuid}`);
    return this.httpService
      .post(url, null, this.constructConfig())
      .then((response) => response.data.link)
      .catch((e) => this.handleApiError(e, 'Failed to create password reset link.', uuid));
  };

  public requestEmailChange = async (
    customerUuid: string,
    newEmail: string,
    redirectUrl: string,
    domicile?: Domicile,
  ): Promise<string> => {
    const path = `${customerUuid}/request-email-change`;
    const url = this.getEndpoint(path, domicile);
    info(`request email change for customer ${customerUuid}`);
    return this.httpService
      .post(url, { newEmail, redirectUrl }, this.constructConfig())
      .then((response) => response.data)
      .catch((e) => this.handleApiError(e, 'Failed request email change.', customerUuid));
  };

  public completeEmailChange = async (code: string, newEmail: string, domicile?: Domicile): Promise<string> => {
    const path = 'complete-email-change';
    const url = this.getEndpoint(path, domicile);
    info(`complete email change for ${newEmail}`);
    return this.httpService
      .post(url, { code, newEmail }, this.constructConfig())
      .then((response) => response.data)
      .catch((e) => this.handleApiError(e, 'Failed complete email change.'));
  };

  public unlinkCustomer = async (customerUuid: string, entityUuid: string, domicile?: Domicile): Promise<boolean> => {
    const path = `${customerUuid}/from/${entityUuid}`;
    const url = this.getEndpoint(path, domicile);
    info(`send unlink customer ${customerUuid} from ${entityUuid} url ${url}`, customerUuid);
    return this.httpService
      .delete(url, this.constructConfig())
      .then((res: AxiosResponse) =>
        this.checkApiResponse(res, `Unlink customer ${customerUuid} from ${entityUuid} failed`, customerUuid),
      )
      .catch((e) =>
        this.handleApiError(e, `Failed to unlink customer ${customerUuid} from ${entityUuid}`, customerUuid),
      );
  };

  public optOutCardholder = async (code: string, shortId: string, domicile?: Domicile) => {
    const path = 'complete-opt-out-receipts';
    const url = this.getEndpoint(path, domicile);
    info('complete opt-out receipts');
    return this.httpService
      .post(url, { code, shortId }, this.constructConfig())
      .then((response) => response.data)
      .catch((e) => this.handleApiError(e, 'Failed complete opt-out.'));
  };

  public createTransactionNotes = async (uuid: string, notes: string, domicile?: Domicile): Promise<boolean> => {
    const path = `${uuid}/notes`;
    const url = this.getEndpoint(path, domicile);
    return this.customPost(url, uuid, 'Transaction notes create', { notes });
  };

  public updateTransactionNotes = async (uuid: string, notes: string, domicile?: Domicile): Promise<boolean> => {
    const path = `${uuid}/notes`;
    const url = this.getEndpoint(path, domicile);
    return this.customPatch(url, uuid, 'Transaction notes update', { notes });
  };

  public deleteTransactionNotes = async (uuid: string, domicile?: Domicile): Promise<boolean> => {
    const path = `${uuid}/notes`;
    const url = this.getEndpoint(path, domicile);
    info(`delete transaction notes ${uuid}`);
    const err = `Transaction notes deleted failed ${uuid}`;
    return this.httpService
      .delete(url, this.constructConfig())
      .then((res) => this.checkApiResponse(res, err, uuid))
      .catch((e: AxiosError) => {
        if (e.response) {
          this.checkApiError(e.response);
        }
        return Promise.reject(new ServerError(err)); // NOSONAR
      });
  };

  public enrolPhoneMfa = async (uuid: string, domicile?: Domicile): Promise<boolean> => {
    const path = `${uuid}/enrol-phone-mfa`;
    const url = this.getEndpoint(path, domicile);
    return this.customPost(url, uuid, 'Customer phone enrolment', undefined);
  };

  public createTransactionImage = async <ImageFile = void>(
    uuid: string,
    imageFile: ImageFile,
    domicile?: Domicile,
  ): Promise<boolean> => {
    const path = `${uuid}/images`;
    const url = this.getEndpoint(path, domicile);
    try {
      const response = await this.customPost(url, uuid, 'Transaction image create', { imageFile });
      info(`ams api response: ${JSON.stringify(response)}`);
      return true;
    } catch (e) {
      error(e);
      return false;
    }
  };

  public removeTransactionImage = async (uuid: string, fileUuid: string, domicile?: Domicile): Promise<boolean> => {
    const path = `${uuid}/images`;
    const url = this.getEndpoint(path, domicile);
    info(`remove transaction image ${uuid} - ${fileUuid}`);
    const err = `Transaction image delete failed ${uuid}`;
    return this.httpService
      .delete(url, {
        ...this.constructConfig(),
        data: {
          fileUuid,
        },
      })
      .then((res) => this.checkApiResponse(res, err, uuid))
      .catch((e) => this.handleApiError(e, 'Failed remove transaction image.', uuid));
  };

  public requestPhoneChange = async (
    customerUuid: string,
    newPhone: string,
    domicile?: Domicile,
  ): Promise<PhoneRegisterResponse> => {
    const path = `${customerUuid}/request-phone-change`;
    const url = this.getEndpoint(path, domicile);
    info(`request phone change for customer ${customerUuid}`);
    return this.httpService
      .post(url, { newPhone }, this.constructConfig())
      .then((response) => response.data)
      .catch((e) => this.handleApiError(e, 'Failed request phone change.', customerUuid));
  };

  public updateKycCheckpoint = async (
    customerUuid: string,
    kycCheckpoints: KycCheckpoint[],
    domicile?: Domicile,
  ): Promise<{ kycCheckpoints: KycCheckpoint[] }> => {
    const path = `${customerUuid}/updateKycCheckpoint`;
    const url = this.getEndpoint(path, domicile);
    info(`Update kyc checkpoint for customer ${customerUuid} with input ${JSON.stringify(kycCheckpoints)}`);
    return this.httpService
      .post(url, { kycCheckpoints }, this.constructConfig())
      .then((response) => {
        debug(`Updated kyc checkpoints response for customer ${customerUuid}, ${JSON.stringify(response.data)}`);
        return response.data;
      })
      .catch((e) => this.handleApiError(e, 'Failed request update kyc checkpoint.', customerUuid));
  };

  public completePhoneChange = async (
    customerUuid: string,
    otpCode: string,
    domicile?: Domicile,
  ): Promise<PhoneVerifyResponse> => {
    const path = `${customerUuid}/complete-phone-change`;
    const url = this.getEndpoint(path, domicile);
    info(`complete phone change for ${customerUuid}`);
    return this.httpService
      .post(url, { otpCode }, this.constructConfig())
      .then((response) => response.data)
      .catch((e) => this.handleApiError(e, 'Failed complete phone change.'));
  };

  public createRegisteringIndvidual = async (input: any, domicile?: Domicile) => {
    info(`calling createRegisteringIndvidual: ${JSON.stringify(input)}`);
    const path = `registeringIndividual`;
    const url = this.getEndpoint(path, domicile);
    return this.httpService
      .post(url, input, this.constructConfig())
      .then((response) => {
        debug(`createRegisteringIndvidual response: ${JSON.stringify(response.data)}`);
        return response.data;
      })
      .catch((e) => {
        error('Failed while calling createRegisteringIndvidual.', e);
        return this.handleApiError(e, 'Failed while calling createRegisteringIndvidual.');
      });
  };

  public generateDeviceUuid = async (input: { model: string; serial: string }): Promise<string> => {
    const url = `${this.envService.amsEndpoint}/v2/device/device-uuid/${input.model}/${input.serial}`;
    info(`send request to ${url}`);
    debug(`generate device uuid input: ${JSON.stringify(input)}`);
    const output = await this.httpService.post(url, JSON.stringify(input), this.getHttpConfig());
    info(`response status: ${output.status}`);
    return output.data;
  };

  public generateEntityDeviceUuid = async (input: {
    entityUuid: string;
    model: string;
    serial: string;
  }): Promise<string> => {
    const url = `${this.envService.amsEndpoint}/v2/device/entity-device-uuid/${input.entityUuid}/${input.model}/${input.serial}`;
    info(`send request to ${url}`);
    debug(`generate entity device uuid input: ${JSON.stringify(input)}`);
    const output = await this.httpService.post(url, JSON.stringify(input), this.getHttpConfig());
    info(`response status: ${output.status}`);
    return output.data;
  };

  protected readonly getEndpoint = (path?: string, domicile?: string): string => {
    return getAmsEndpoint(
      {
        amsEndpoint: this.envService.amsEndpoint,
        amsEndpointVersion: this.envService.amsEndpointVersion,
      },
      this.endpointPath,
      path,
      domicile,
    );
  };

  private handleResponse<OutputDto>(
    response: AxiosResponse<OutputDto>,
    loggingInfo: {
      aggregateId?: string;
      errorMessage: string;
    },
  ) {
    debug(response, loggingInfo.aggregateId);
    info(`invoke ams endpoint response: ${JSON.stringify(response.data)} ${response.status}`, loggingInfo.aggregateId);
    const e = this.readErrors(response);
    if (e) {
      error(loggingInfo.errorMessage, loggingInfo.aggregateId);
      error(e, loggingInfo.errorMessage);
      return Promise.reject(e); // NOSONAR
    }
    return response.data;
  }

  private readonly customPatch = async (url: string, uuid: string, modelType: string, body: any): Promise<boolean> => {
    const err = `${modelType} failed ${uuid}`;
    info(`Send ${modelType} event to ${url}.`);
    return this.httpService
      .patch(url, JSON.stringify(body), this.constructConfig())
      .then((res: AxiosResponse) => this.checkApiResponse(res, err, uuid))
      .catch((e) => this.handleApiError(e, err, uuid));
  };

  private readonly customPost = async (url: string, uuid: string, modelType: string, body: any): Promise<boolean> => {
    const err = `${modelType} failed ${uuid}`;
    info(`Send ${modelType} event to ${url}.`);
    return this.httpService
      .post(url, JSON.stringify(body), this.constructConfig())
      .then((res: AxiosResponse) => this.checkApiResponse(res, err, uuid))
      .catch((e) => this.handleApiError(e, err, uuid));
  };
}
