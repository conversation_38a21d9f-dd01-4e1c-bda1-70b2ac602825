import { error } from '@npco/component-bff-core/dist/utils/logger';
import type { CatalogDiscountConfig } from '@npco/component-dto-catalog/dist';
import { CatalogDiscountType, CatalogSaleSource, CatalogUnit } from '@npco/component-dto-catalog/dist';
import type {
  InvoiceDiscount,
  InvoiceDiscountDetail,
  InvoiceItem,
  InvoiceItemModifier,
} from '@npco/component-dto-invoice/dist';
import { InvoiceItemUnit } from '@npco/component-dto-invoice/dist';
import type { OrderDiscount, OrderItem, OrderItemModifier } from '@npco/component-dto-order/dist';
import { OrderItemType } from '@npco/component-dto-order/dist';

import { v4 } from 'uuid';

import { getPriceWithGst } from '../../../domain/models/utils';
import type { InvoicePaidEventWithId, OrderPaidDtoWithId } from '../../types';
import { CatalogDiscountSale, CatalogItemSale, CatalogSale } from '../entities';

import { ReportBaseModel } from './reportBaseModel';
import { safeGetNumber } from './utils';

export class CatalogSaleModel extends ReportBaseModel {
  convertOrderPaidEvent = (event: OrderPaidDtoWithId): CatalogSale => {
    const catalogSale = new CatalogSale();
    catalogSale.id = v4();
    catalogSale.entityUuid = event.entityUuid;
    catalogSale.saleReference = event.referenceNumber;
    catalogSale.transactionReference = event.newPayment?.transactionUuid ?? v4();
    catalogSale.source = CatalogSaleSource.ZELLER_POS;
    catalogSale.siteUuid = event.siteUuid;
    catalogSale.deviceUuid = event.createdFromDeviceUuid;
    catalogSale.catalogSettingsSnapshot = event.catalogSettings;
    catalogSale.salePaidTimeUTC = this.getPaidTimeOrThrowNotDefined(event);
    const { catalogItemSales, itemLevelDiscounts } = this.convertOderPaidEventItems(catalogSale, event);
    const orderLevelDiscounts = this.convertOderPaidEventOrderDiscounts(catalogSale, event);
    catalogSale.items = catalogItemSales;
    catalogSale.discounts = [...orderLevelDiscounts, ...itemLevelDiscounts];
    return catalogSale;
  };

  convertOderPaidEventItems = (catalogSale: CatalogSale, event: OrderPaidDtoWithId) => {
    const itemLevelDiscounts: CatalogDiscountSale[] = [];
    const modifierItems: CatalogItemSale[] = [];
    const items = (event.items ?? []).map((item: OrderItem) => {
      const catalogItemSale = new CatalogItemSale();
      catalogItemSale.id = v4();
      catalogItemSale.entityUuid = event.entityUuid;
      catalogItemSale.catalogSaleUuid = catalogSale.id;
      catalogItemSale.catalogItemUuid = item.catalogItem?.id;
      catalogItemSale.catalogItemType = item.type;
      catalogItemSale.soldQuantity = this.getItemQuantity(item);
      catalogItemSale.gstApplicable = this.isItemTaxApplicable(item);
      catalogItemSale.unitPriceWithGst = getPriceWithGst(
        {
          price: this.safeGetNumber(item.price.value),
          taxes: item.taxes,
        } as any,
        catalogSale.catalogSettingsSnapshot.itemsTaxInclusive,
      );
      catalogItemSale.salePrice = this.safeGetNumber(Number(item.totalAmount?.value));
      let totalDiscountedAmount = 0;
      let discountsNotApplied = 0;
      const itemRevenueBeforeDiscount = catalogItemSale.unitPriceWithGst * catalogItemSale.soldQuantity;
      item.discounts?.forEach((discount) => {
        totalDiscountedAmount += this.safeGetNumber(discount.discountedAmount.value);
      });
      itemLevelDiscounts.push(...this.convertOderPaidEventOrderItemDiscounts(catalogSale, item, catalogItemSale));
      if (totalDiscountedAmount <= itemRevenueBeforeDiscount) {
        catalogItemSale.discountAmount = totalDiscountedAmount;
        catalogItemSale.revenue = itemRevenueBeforeDiscount - totalDiscountedAmount;
      } else {
        catalogItemSale.revenue = 0;
        catalogItemSale.discountAmount = this.safeGetNumber(Number(item.subtotalAmount?.value));
        discountsNotApplied = totalDiscountedAmount - this.safeGetNumber(Number(item.subtotalAmount?.value));
      }
      catalogItemSale.discountUuids = (item.discounts ?? []).map((d) => d.catalogDiscountUuid!);

      catalogItemSale.saleItemSnapshot = item;
      catalogItemSale.salePaidTimeUTC = catalogSale.salePaidTimeUTC;
      catalogItemSale.saleReference = catalogSale.saleReference;
      catalogItemSale.siteUuid = catalogSale.siteUuid;
      catalogItemSale.source = catalogSale.source;
      catalogItemSale.deviceUuid = catalogSale.deviceUuid;
      const { modifierItems: modifiers, totalModifierAmount } = this.convertItemModifiers(
        catalogSale,
        item,
        discountsNotApplied,
      );

      modifierItems.push(...modifiers);
      catalogItemSale.modifierAmount = totalModifierAmount;

      return catalogItemSale;
    });
    return {
      catalogItemSales: [...items, ...modifierItems],
      itemLevelDiscounts,
    };
  };

  convertOderPaidEventOrderItemDiscounts = (
    catalogSale: CatalogSale,
    orderItem: OrderItem,
    catalogItemSale: CatalogItemSale,
  ): CatalogDiscountSale[] => {
    const itemDiscounts: CatalogDiscountSale[] = [];
    (orderItem.discounts ?? []).forEach((orderDiscount) => {
      const catalogDiscountSale = new CatalogDiscountSale();
      catalogDiscountSale.id = v4();
      catalogDiscountSale.entityUuid = catalogSale.entityUuid;
      catalogDiscountSale.catalogSaleUuid = catalogSale.id;
      catalogDiscountSale.catalogItemUuid = orderItem.catalogItem?.id;
      catalogDiscountSale.catalogItemSaleUuid = catalogItemSale.id;
      catalogDiscountSale.catalogDiscountUuid = orderDiscount.catalogDiscountUuid;
      catalogDiscountSale.discountType = this.getDiscountType(orderDiscount); // check why type is optional in event
      catalogDiscountSale.discountMethod = orderDiscount.config;
      catalogDiscountSale.discountAmount = this.safeGetNumber(orderDiscount.discountedAmount.value);
      catalogDiscountSale.saleDiscountSnapshot = orderDiscount;
      catalogDiscountSale.salePaidTimeUTC = catalogSale.salePaidTimeUTC;
      itemDiscounts.push(catalogDiscountSale);
    });
    return itemDiscounts;
  };

  convertOderPaidEventOrderDiscounts = (catalogSale: CatalogSale, order: OrderPaidDtoWithId): CatalogDiscountSale[] => {
    return (order.discounts ?? []).map((discount) => {
      const catalogDiscountSale = new CatalogDiscountSale();
      catalogDiscountSale.id = v4();
      catalogDiscountSale.entityUuid = catalogSale.entityUuid;
      catalogDiscountSale.catalogSaleUuid = catalogSale.id;
      catalogDiscountSale.catalogDiscountUuid = discount.catalogDiscountUuid;
      catalogDiscountSale.discountType = this.getDiscountType(discount); // check why type is optional in event
      catalogDiscountSale.discountMethod = discount.config;
      catalogDiscountSale.discountAmount = this.safeGetNumber(discount.discountedAmount.value);
      catalogDiscountSale.saleDiscountSnapshot = discount;
      catalogDiscountSale.salePaidTimeUTC = catalogSale.salePaidTimeUTC;
      return catalogDiscountSale;
    });
  };

  convertInvoicePaidEvent = (invoicePaidEvent: InvoicePaidEventWithId, invoiceSiteUuid: string) => {
    const catalogSale = new CatalogSale();
    const invoice = invoicePaidEvent.updatedInvoice;
    catalogSale.id = v4();
    catalogSale.entityUuid = invoicePaidEvent.entityUuid;
    catalogSale.saleReference = invoice.referenceNumber;
    catalogSale.transactionReference = undefined;
    catalogSale.source = CatalogSaleSource.ZELLER_INVOICE;
    catalogSale.siteUuid = invoiceSiteUuid;
    catalogSale.deviceUuid = undefined;
    catalogSale.catalogSettingsSnapshot = {
      itemsApplyTax: invoice.itemsApplyTax,
      itemsTaxInclusive: invoice.itemsTaxInclusive,
    };
    catalogSale.salePaidTimeUTC = this.getPaidTimeOrThrowNotDefined(invoice);
    const { catalogItemSales, itemLevelDiscounts, totalItemDiscountAmount } = this.convertInvoicePaidEventItems(
      catalogSale,
      invoicePaidEvent,
    );
    const invoiceLevelDiscounts = this.convertInvoicePaidEventInvoiceDiscount(
      catalogSale,
      invoicePaidEvent,
      totalItemDiscountAmount,
    );
    catalogSale.items = catalogItemSales;
    catalogSale.discounts = [...invoiceLevelDiscounts, ...itemLevelDiscounts];
    return catalogSale;
  };

  convertInvoicePaidEventItems = (catalogSale: CatalogSale, event: InvoicePaidEventWithId) => {
    const itemLevelDiscounts: CatalogDiscountSale[] = [];
    const modifierItems: CatalogItemSale[] = [];
    const invoice = event.updatedInvoice;
    let totalItemDiscountAmount = 0;
    const items = this.safeGetArray(invoice.items).map((item: InvoiceItem) => {
      const catalogItemSale = new CatalogItemSale();
      catalogItemSale.id = v4();
      catalogItemSale.entityUuid = event.entityUuid;
      catalogItemSale.catalogSaleUuid = catalogSale.id;
      catalogItemSale.catalogItemUuid = item.catalogItem?.id;
      catalogItemSale.catalogItemType = (item.catalogItem?.itemType ?? OrderItemType.SINGLE) as OrderItemType; // for all items before POS
      catalogItemSale.soldQuantity = this.getItemQuantity(item);
      catalogItemSale.gstApplicable = this.isItemTaxApplicable(item);
      catalogItemSale.unitPriceWithGst = getPriceWithGst(
        {
          price: this.safeGetNumber(item.price),
          taxes: item.taxes,
        } as any,
        catalogSale.catalogSettingsSnapshot.itemsTaxInclusive,
      );
      catalogItemSale.salePrice =
        Math.round(this.safeGetNumber(item.priceWithGst) * (item.quantity ?? 1)) -
        this.safeGetNumber(item.discountAmount);
      catalogItemSale.discountAmount = this.safeGetNumber(item.discountAmount);
      catalogItemSale.discountUuids = (item.discounts ?? []).map((d) => d.catalogDiscountUuid!);
      catalogItemSale.saleItemSnapshot = item;
      catalogItemSale.salePaidTimeUTC = catalogSale.salePaidTimeUTC;
      catalogItemSale.saleReference = catalogSale.saleReference;
      catalogItemSale.source = catalogSale.source;
      catalogItemSale.deviceUuid = catalogSale.deviceUuid;
      catalogItemSale.siteUuid = catalogSale.siteUuid;
      catalogItemSale.reportingCategoryUuid = item.reportingCategoryUuid;

      let totalDiscountedAmount = 0;
      let discountsNotApplied = 0;
      const catalogDiscountSales = this.convertInvoicePaidEventItemDiscounts(catalogSale, item, catalogItemSale);
      if (catalogDiscountSales) {
        itemLevelDiscounts.push(...catalogDiscountSales);
        catalogDiscountSales.forEach((catalogDiscountSale) => {
          totalItemDiscountAmount += catalogDiscountSale.discountAmount;
          totalDiscountedAmount += catalogDiscountSale.discountAmount;
        });
      }
      const itemRevenueBeforeDiscount = catalogItemSale.unitPriceWithGst * catalogItemSale.soldQuantity;

      if (totalDiscountedAmount <= itemRevenueBeforeDiscount) {
        catalogItemSale.discountAmount = totalDiscountedAmount;
        catalogItemSale.revenue = itemRevenueBeforeDiscount - totalDiscountedAmount;
      } else {
        catalogItemSale.revenue = 0;
        catalogItemSale.discountAmount = itemRevenueBeforeDiscount;
        discountsNotApplied = totalDiscountedAmount - itemRevenueBeforeDiscount;
      }

      const { modifierItems: modifiers, totalModifierAmount } = this.convertItemModifiers(
        catalogSale,
        item,
        discountsNotApplied,
      );

      modifierItems.push(...modifiers);
      catalogItemSale.modifierAmount = totalModifierAmount;

      return catalogItemSale;
    });
    return {
      catalogItemSales: [...items, ...modifierItems],
      itemLevelDiscounts,
      totalItemDiscountAmount,
    };
  };

  convertInvoicePaidEventItemDiscounts = (
    catalogSale: CatalogSale,
    invoiceItem: InvoiceItem,
    catalogItemSale: CatalogItemSale,
  ): CatalogDiscountSale[] => {
    const itemDiscounts: CatalogDiscountSale[] = [];

    const createCatalogDiscountSale = (discount: InvoiceDiscountDetail | InvoiceDiscount, discountedAmount: number) => {
      const catalogDiscountSale = new CatalogDiscountSale();
      catalogDiscountSale.id = v4();
      catalogDiscountSale.entityUuid = catalogSale.entityUuid;
      catalogDiscountSale.catalogItemUuid = invoiceItem.catalogItem?.id;
      catalogDiscountSale.catalogSaleUuid = catalogSale.id;
      catalogDiscountSale.catalogItemSaleUuid = catalogItemSale.id;
      catalogDiscountSale.catalogDiscountUuid =
        'catalogDiscountUuid' in discount ? discount.catalogDiscountUuid : undefined;
      catalogDiscountSale.discountType = CatalogDiscountType.BASIC;
      catalogDiscountSale.discountMethod = discount.config as any as CatalogDiscountConfig;
      catalogDiscountSale.discountAmount = this.safeGetNumber(discountedAmount);
      catalogDiscountSale.saleDiscountSnapshot = discount;
      catalogDiscountSale.salePaidTimeUTC = catalogSale.salePaidTimeUTC;
      return catalogDiscountSale;
    };

    (invoiceItem.discounts ?? []).forEach((discount) => {
      itemDiscounts.push(createCatalogDiscountSale(discount, this.safeGetNumber(discount.discountedAmount)));
    });

    if (itemDiscounts.length === 0 && invoiceItem.discount) {
      itemDiscounts.push(
        createCatalogDiscountSale(invoiceItem.discount, this.safeGetNumber(invoiceItem.discountAmount)),
      );
    }
    return itemDiscounts;
  };

  convertInvoicePaidEventInvoiceDiscount = (
    catalogSale: CatalogSale,
    event: InvoicePaidEventWithId,
    itemDiscountTotalAmount: number,
  ): CatalogDiscountSale[] => {
    const invoice = event.updatedInvoice;

    const invoiceDiscounts: CatalogDiscountSale[] = [];

    const createCatalogDiscountSale = (discount: InvoiceDiscountDetail | InvoiceDiscount, discountedAmount: number) => {
      const catalogDiscountSale = new CatalogDiscountSale();
      catalogDiscountSale.id = v4();
      catalogDiscountSale.entityUuid = catalogSale.entityUuid;
      catalogDiscountSale.catalogSaleUuid = catalogSale.id;
      catalogDiscountSale.catalogDiscountUuid =
        'catalogDiscountUuid' in discount ? discount.catalogDiscountUuid : undefined;
      catalogDiscountSale.discountType = CatalogDiscountType.BASIC;
      catalogDiscountSale.discountMethod = discount.config as any as CatalogDiscountConfig;
      catalogDiscountSale.discountAmount = this.safeGetNumber(discountedAmount);
      catalogDiscountSale.saleDiscountSnapshot = discount;
      catalogDiscountSale.salePaidTimeUTC = catalogSale.salePaidTimeUTC;
      return catalogDiscountSale;
    };

    (invoice.discounts ?? []).forEach((discount) => {
      invoiceDiscounts.push(createCatalogDiscountSale(discount, this.safeGetNumber(discount.discountedAmount)));
    });

    if (invoiceDiscounts.length === 0 && invoice.discount) {
      invoiceDiscounts.push(
        createCatalogDiscountSale(
          invoice.discount,
          this.safeGetNumber(this.safeGetNumber(invoice.totalDiscount) - itemDiscountTotalAmount),
        ),
      );
    }

    return invoiceDiscounts;
  };

  private readonly convertItemModifiers = (
    catalogSale: CatalogSale,
    item: InvoiceItem | OrderItem,
    discountsNotApplied: number,
  ) => {
    let totalModifierAmount = 0;
    const modifierItems: CatalogItemSale[] = [];
    let remainingDiscount = discountsNotApplied;
    item.modifiers?.forEach((modifier) => {
      const catalogItemModifierSale = new CatalogItemSale();
      catalogItemModifierSale.id = v4();
      catalogItemModifierSale.entityUuid = catalogSale.entityUuid;
      catalogItemModifierSale.catalogSaleUuid = catalogSale.id;
      catalogItemModifierSale.catalogItemUuid = modifier.catalogModifierUuid;
      catalogItemModifierSale.deviceUuid = catalogSale.deviceUuid;
      catalogItemModifierSale.siteUuid = catalogSale.siteUuid;
      catalogItemModifierSale.saleReference = catalogSale.saleReference;
      catalogItemModifierSale.source = catalogSale.source;
      catalogItemModifierSale.gstApplicable = this.isItemTaxApplicable(item);
      // modifier
      catalogItemModifierSale.modifierSoldWithCatalogItemUuid = item.catalogItem?.id;
      catalogItemModifierSale.modifierSetUuid = (modifier as OrderItemModifier).catalogModifier
        ? (modifier as OrderItemModifier).catalogModifier?.modifierSetUuid
        : (modifier as InvoiceItemModifier).catalogModifierSetUuid;

      catalogItemModifierSale.catalogItemType = OrderItemType.MODIFIER;
      catalogItemModifierSale.soldQuantity = this.getItemQuantity(modifier);
      const modifierPrice =
        modifier.price instanceof Object
          ? this.safeGetNumber(modifier.price.value)
          : this.safeGetNumber(modifier.price);
      const revenueBeforeDiscount = modifierPrice * (modifier.quantity ?? 1);
      if (revenueBeforeDiscount < remainingDiscount) {
        catalogItemModifierSale.revenue = 0;
        catalogItemModifierSale.discountAmount = revenueBeforeDiscount;
        remainingDiscount -= revenueBeforeDiscount;
      } else {
        catalogItemModifierSale.revenue = revenueBeforeDiscount - remainingDiscount;
        catalogItemModifierSale.discountAmount = remainingDiscount;
        remainingDiscount = 0;
      }
      totalModifierAmount += catalogItemModifierSale.revenue;
      catalogItemModifierSale.modifierAmount = 0;
      catalogItemModifierSale.saleItemSnapshot = modifier;
      catalogItemModifierSale.salePaidTimeUTC = catalogSale.salePaidTimeUTC;
      modifierItems.push(catalogItemModifierSale);
    });

    return { modifierItems, totalModifierAmount };
  };

  private readonly safeGetArray = <T>(arr: T[] | undefined): T[] => {
    return arr ?? [];
  };

  private readonly safeGetNumber = (num: number | undefined | string | null): number => safeGetNumber(num);

  private readonly getDiscountType = (discount: OrderDiscount | undefined): CatalogDiscountType => {
    return discount?.type ?? CatalogDiscountType.BASIC;
  };

  private isItemTaxApplicable(item: OrderItem | InvoiceItem): boolean {
    return item.taxes?.find((tax) => tax.name === 'GST')?.enabled ?? false;
  }

  private readonly getPaidTimeOrThrowNotDefined = (event: { id: string; paidTime?: number }): number => {
    if (!event.paidTime) {
      error(`event paid time is undefined for ${event.id}`);
      throw new Error(`event paid time is undefined`);
    }
    return event.paidTime;
  };

  private readonly getItemQuantity = (
    item: InvoiceItem | OrderItem | OrderItemModifier | InvoiceItemModifier,
  ): number => {
    if (item.unit === InvoiceItemUnit.QUANTITY || item.unit === CatalogUnit.QUANTITY) {
      return item.quantity;
    }
    return 1;
  };
}
