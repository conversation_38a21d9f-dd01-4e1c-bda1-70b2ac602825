import type { Config } from 'jest';

// eslint-disable-next-line import/no-relative-packages
import commonConfig from '../../../../jest.preset';

const config: Config = {
  ...commonConfig,
  roots: ['<rootDir>/tests'],
  testTimeout: 60000,
  collectCoverage: false,
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
  // Custom test environment 'steps'
  // skip remaining tests in file after first failure
  testEnvironment: 'steps',
  reporters: [
    'default',
    // ['github-actions', { silent: false }],
    [
      'jest-html-reporter',
      {
        pageTitle: 'Test Report',
        outputPath: 'dist/test-report.html',
        collapseSuites: true,
        includeConsoleOutput: true,
        detailedReport: true,
      },
    ],
  ],
};

export default config;
