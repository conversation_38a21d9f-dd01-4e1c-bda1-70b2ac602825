import { GraphQLError } from 'graphql';
import gql from 'graphql-tag';
import {
  paymentSettings as allPaymentSettings,
  deviceScreen,
  entity,
  posSettings,
  standInRules,
} from 'integration-tests/src/common';
import { deviceSettings, lcr } from 'integration-tests/src/mockTestData/deviceSettings';
import { validateEntityFromEventStore } from 'integration-tests/src/utils/eventStoreUtil';
import { CrmsApiClient } from 'integration-tests/src/utils/gql/crmsGqlApiClient';
import { DbsApiClient } from 'integration-tests/src/utils/gql/dbsGqlApiClient';
import { MpApiClient } from 'integration-tests/src/utils/gql/mpGqlApiClient';
import { retry } from 'integration-tests/src/utils/retry';
import { v4 } from 'uuid';

const paymentSettings: any = {
  cpocPaymentLimits: allPaymentSettings.cpocPaymentLimits,
  motoPaymentLimits: allPaymentSettings.motoPaymentLimits,
  paymentLimits: allPaymentSettings.paymentLimits,
};

describe('Device backend device integration test (mpDevice)', () => {
  const mpApiClient = new MpApiClient();
  const dbsApiClient = new DbsApiClient();
  const crmsApiClient = new CrmsApiClient();

  let entityId: string;
  let deviceUuid: string;

  let eventStoreValidation: any;

  const model = v4().slice(-16);
  const serial = v4().slice(-16);

  beforeAll(async () => {
    await crmsApiClient.beforeAll();
    await mpApiClient.beforeAll();
    await dbsApiClient.beforeAll();

    dbsApiClient.setTestData(mpApiClient.getTestData());
    const { entityUuid } = dbsApiClient.getTestData();
    entityId = entityUuid;
    await mpApiClient.loginAuth0User(entityUuid);
  });

  it('should have created new device on login for existing account created via Merchant Portal', async () => {
    const device = await dbsApiClient.createDeviceUuid(model, serial);
    await dbsApiClient.setupDeviceMockForMpLogin(entityId, device);
    await dbsApiClient.identityLogin(device);
    deviceUuid = device;
    deviceSettings.id = deviceUuid;
    eventStoreValidation = {
      id: deviceUuid,
      entityUuid: entityId,
      status: 'ACTIVE',
      posSettings,
      model,
      serial,
      screen: deviceScreen,
      entity: { ...entity },
      paymentSettings,
      emvCaKeys: expect.any(String),
      emvConfig: expect.any(String),
      emvTables: expect.any(String),
      terminalConfig: expect.any(String),
      geofencing: expect.any(String),
      standInRules,
    };
  });

  test.each(['dbs', 'mp', 'ams', 'crms'])(
    'should be able to save device created event in %p event store',
    async (componentName) =>
      validateEntityFromEventStore(mpApiClient.stage, componentName, eventStoreValidation, 'Created'),
  );

  test.each([
    {
      componentName: 'mp',
      client: mpApiClient,
    },
    {
      componentName: 'crms',
      client: crmsApiClient,
    },
  ])('should be able to query new device', async (data) => {
    await retry(async () => {
      const result = await data.client.getDeviceSettings(deviceUuid, entityId);
      console.log(`get ${data.componentName} query device response:`, result);
      expect(result.getDeviceSettings.id).toBe(deviceUuid);
      expect(result.getDeviceSettings.name).toBe(null); // name not set on creation
      expect(result.getDeviceSettings.screen).toEqual(deviceScreen);
      expect(result.getDeviceSettings.standInRules).toEqual(standInRules);
      expect(result.getDeviceSettings.posSettings).toEqual({
        ...posSettings,
        posSoftwareName: null,
        connectionMethod: null,
        clientAddress: null,
        ipAddress: null,
        port: null,
        posRegisterName: null,
        exitRequiresPin: null,
        posVenue: null,
        oracleSettings: null,
      });
    });
  });

  it('should be able to subscribe for deviceSettings update', async () => {
    console.log('subscribe deviceSettingsUpdate on device:', deviceUuid);

    /**
     * Setup subscription
     */
    const subscription = await mpApiClient.createDeviceSettingUpdateSubscriber(deviceUuid);

    /**
     * Mutate data
     */
    await mpApiClient.updateDeviceSettings(deviceSettings);
    console.log('device settings updated');

    /**
     * Verify message
     */
    await subscription.retryAndAssertMessages(async (messages: any[]) => {
      eventStoreValidation = {
        id: deviceUuid,
        ...deviceSettings,
        entityUuid: entityId,
      };

      expect(messages).toEqual(
        expect.arrayContaining([
          {
            ...deviceSettings,
            emvCaKeys: expect.any(String),
            emvConfig: expect.any(String),
            emvTables: expect.any(String),
            geofencing: expect.any(String),
            terminalConfig: expect.any(String),
            site: null,
            surchargesTaxes: null,
            moto: null,
            tipping: null,
            receipt: null,
            schemes: null,
            schemesMoto: null,
            entityUuid: entityId,
            customers: [],
            standInRules,
            posSettings: { ...deviceSettings.posSettings, posVenue: null },
            entity: { ...entity, ...paymentSettings },
            features: null,
          },
        ]),
      );
    });
  });

  test.each(['dbs', 'mp', 'ams', 'crms'])(
    'should be able to save Device Updated event in %p event store',
    async (componentName) =>
      validateEntityFromEventStore(mpApiClient.stage, componentName, eventStoreValidation, 'Updated'),
  );

  const baseResponse = {
    ...deviceSettings,
    appVersion: '',
    firmwareVersion: '',
    model,
    serial,
    deviceUser: null,
    emvCaKeys: expect.any(String),
    emvConfig: expect.any(String),
    emvTables: expect.any(String),
    geofencing: expect.any(String),
    terminalConfig: expect.any(String),
    tipping: null,
    entity: { ...entity, ...paymentSettings },
    site: null,
    moto: null,
    surchargesTaxes: null,
    receipt: null,
    schemes: null,
    schemesMoto: null,
    customers: [],
    standInRules,
    posSettings: { ...deviceSettings.posSettings, posVenue: null },
    features: null,
  };

  it('should be able to query projection update via appsync mp', async () => {
    await retry(async () => {
      const result = await mpApiClient.getDeviceSettings(deviceUuid);
      expect(result.getDeviceSettings).toEqual({
        ...baseResponse,
        id: deviceUuid,
        entityUuid: entityId,
      });
    });
  });

  it('should be able to query projection update via appsync crms', async () => {
    await retry(async () => {
      const result = await crmsApiClient.getDeviceSettings(deviceUuid, entityId);
      expect(result.getDeviceSettings).toEqual({
        ...baseResponse,
        id: deviceUuid,
        entityUuid: entityId,
      });
    });
  });

  it('should be able to create entity for existing device and subscribe for deviceSettings update', async () => {
    console.log('subscribe deviceSettingsUpdate on device:', deviceUuid);

    /**
     * Setup subscription
     */
    const subscription = await mpApiClient.createDeviceSettingUpdateSubscriber(deviceUuid);

    /**
     * Mutate data
     */
    await mpApiClient.createSoleTraderEntity(v4());

    /**
     * Verify message
     */
    await subscription.retryAndAssertMessages(async (messages: any[]) => {
      expect(messages).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            entity: {
              ...entity,
              ...paymentSettings,
            },
          }),
        ]),
      );
      const terminalConfigMatch = messages.some((message) => {
        expect(JSON.parse(message.terminalConfig)).toEqual({
          caid: expect.any(String),
          catid: expect.any(String),
          ...lcr,
          countryCode: '0036',
          currencyCode: '0036',
          currencyExponent: '02',
          mcc: '',
        });
        return true;
      });
      expect(terminalConfigMatch).toBeTruthy();

      await Promise.all(
        [mpApiClient, crmsApiClient].map((client) =>
          retry(async () => {
            const output = await client.getDeviceSettings(deviceUuid, entityId);
            expect(output.getDeviceSettings.entity).toEqual({
              ...entity,
              ...paymentSettings,
            });
            expect(output.getDeviceSettings.standInRules).toEqual(standInRules);
          }),
        ),
      );
    });
  });

  it('should be able to update entity subscribe for deviceSettings update', async () => {
    console.log('subscribe deviceSettingsUpdate on device:', deviceUuid);

    /**
     * Setup subscription
     */
    const subscription = await mpApiClient.createDeviceSettingUpdateSubscriber(deviceUuid);

    /**
     * Mutate data
     */
    await mpApiClient.invokeLambda('crms-engine-entity-updateEntityHandler', {
      args: {
        entity: {
          id: entityId,
          category: '013',
          categoryGroup: '04',
          accountStatus: {
            canAcquire: false,
            canAcquireMoto: false,
            canAcquireMobile: false,
            canRefund: false,
            canStandIn: false,
          },
        },
        domicile: 'AUS',
        currency: 'AUD',
      },
    });

    /**
     * Verify message
     */
    await subscription.retryAndAssertMessages(async (messages: any[]) => {
      expect(messages).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            entity: {
              canAcquire: false,
              canAcquireMoto: false,
              canAcquireMobile: false,
              canRefund: false,
              canStandIn: false,
              domicile: 'AUS',
              currency: 'AUD',
              ...paymentSettings,
            },
          }),
        ]),
      );

      const foundTerminalConfigMcc = messages.some((message) => JSON.parse(message.terminalConfig).mcc === '5462');
      expect(foundTerminalConfigMcc).toBeTruthy();
    });
  });

  it('should be able to update screen setting under device setting', async () => {
    console.log('subscribe deviceSettingsUpdate on device:', deviceUuid);

    const screenSetting = {
      sleepEnabled: true,
      sleepTimer: 1,
      standbyEnabled: true,
      standbyTimer: 1,
      brightness: 1,
      communicationsTimer: 3,
      inactivityTimer: 1,
      pinEntryTimer: 4,
      notHibernateWhenPluggedIn: false,
      theme: 'DARK_THEME',
    };

    eventStoreValidation = {
      id: deviceUuid,
      ...deviceSettings,
      screen: screenSetting,
      entityUuid: entityId,
    };

    /**
     * Setup subscription
     */
    const subscription = await mpApiClient.createDeviceSettingUpdateSubscriber(deviceUuid);

    /**
     * Mutate data
     */
    await mpApiClient.updateDeviceSettings({ ...deviceSettings, screen: screenSetting });
    console.log('device settings screen setting updated');

    /**
     * Verify message
     */
    await subscription.retryAndAssertMessages(async (messages: any[]) => {
      expect(messages).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            screen: screenSetting,
          }),
        ]),
      );

      await Promise.all(
        [mpApiClient, crmsApiClient].map((client) =>
          retry(async () => {
            const output = await client.getDeviceSettings(deviceUuid, entityId);
            expect(output.getDeviceSettings.screen).toEqual(screenSetting);
          }),
        ),
      );
    });
  });

  test.each(['dbs', 'mp', 'ams', 'crms'])(
    'should be able to save Device screen Updated event in %p event store',
    async (componentName) =>
      validateEntityFromEventStore(mpApiClient.stage, componentName, eventStoreValidation, 'Updated'),
  );

  xit('should be able to update site features when a device changes sites', async () => {
    /**
     * Setup subscription
     */
    const subscription = await mpApiClient.createDeviceSettingUpdateSubscriber(deviceUuid);

    /**
     * Mutate data
     */
    const site = await mpApiClient.createSite({
      name: `test-${v4()}`,
      address: {
        state: 'state',
        street: 'street1',
        suburb: '1234',
        postcode: 'Melbourne',
      },
      pin: '1234',
      type: 'FIXED',
    });
    const siteUuid = site.id;
    await retry(async () => {
      const newDeviceSettings = await mpApiClient.getDeviceSettings(deviceUuid);
      expect(newDeviceSettings.getDeviceSettings.features).toBeNull();
    });
    await mpApiClient.updateSite(siteUuid, {
      features: {
        declineSoundEnabled: false,
        splitPaymentEnabled: true,
        restrictReportAccessEnabled: false,
      },
    });
    await mpApiClient.assignDeviceToSite(deviceUuid, siteUuid);

    await retry(async () => {
      const newDeviceSettings = await mpApiClient.getDeviceSettings(deviceUuid);
      expect(newDeviceSettings.getDeviceSettings.features).toEqual({
        declineSoundEnabled: false,
        splitPaymentEnabled: true,
        restrictReportAccessEnabled: false,
      });
      expect(newDeviceSettings.getDeviceSettings.site.timezone).toEqual('Australia/Melbourne');
    });

    /**
     * Verify message
     */
    await subscription.retryAndAssertMessages(async (messages: any[]) => {
      expect(messages).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            features: {
              declineSoundEnabled: false,
              splitPaymentEnabled: true,
              restrictReportAccessEnabled: false,
            },
          }),
        ]),
      );
    });
  });

  xit('should throw GraphQLError if name is not unique', async () => {
    const newLogin = await dbsApiClient.deviceSignIn();
    await expect(
      mpApiClient.updateDeviceSettings({
        id: newLogin.deviceUuid,
        name: deviceSettings.name,
      }),
    ).rejects.toThrowError(new GraphQLError('GraphQL error: [400] Device name is not unique'));
  });

  xit('should create unique device name', async () => {
    const newLogin = await dbsApiClient.deviceSignIn();
    const response = await mpApiClient.createUniqueDeviceName({
      id: newLogin.deviceUuid,
      name: deviceSettings.name,
    });
    expect(response.createUniqueDeviceName).toEqual(`${deviceSettings.name} (1)`);
  });

  xit('should be able to update device name', async () => {
    const deviceName = v4();
    const client = await mpApiClient.getOpenIdClient();
    const res: any = await client.mutate({
      mutation: gql`
        mutation updateDeviceName($deviceUuid: ID!, $name: String!) {
          updateDeviceName(deviceUuid: $deviceUuid, name: $name)
        }
      `,
      variables: {
        name: deviceName,
        deviceUuid,
      },
    });
    expect(res.data.updateDeviceName).toBe(true);
    await retry(async () => {
      const result = await mpApiClient.getDeviceSettings(deviceUuid);
      expect(result.getDeviceSettings.name).toEqual(deviceName);
    });
  });
});
