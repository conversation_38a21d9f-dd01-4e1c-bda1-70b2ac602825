wsConnect:
  handler: bootstrap
  name: ${self:provider.stackName}-event
  timeout: ${self:custom.timeout}
  tracing: true
  role: wsConnectionRole
  environment:
    HANDLER_NAME: eventHandler
    PAIRING_TABLE: ${self:custom.pairingTableName}
    TTL_IN_HOURS: ${self:custom.pairingTableDataTtlInHours}
    SDK_POS_TTL_IN_HOURS: ${self:custom.sdkPosDeviceTypeTtlInHours}
  events:
    - websocket:
        route: $connect
        authorizer:
          name: wsAuthorizer
          identitySource: ''

    - websocket:
        route: $disconnect
    - websocket:
        route: $default

projection:
  handler: bootstrap
  name: ${self:provider.stackName}-projection
  timeout: 300
  tracing: true
  role: projectionRole
  reservedConcurrency: 10
  environment:
    HANDLER_NAME: projection
    PAIRING_TABLE: ${self:custom.pairingTableName}
    TTL_IN_HOURS: ${self:custom.pairingTableDataTtlInHours}
    PROJECTION_SQS_URL: ${self:custom.posconnectorCqrsSqsUrl}
  events:
    - sqs:
        arn: ${self:custom.posconnectorCqrsSqsArn}
        batchSize: 10

warmupHandler:
  handler: bootstrap
  name: ${self:provider.stackName}-warmup
  role: lambdaWarmerRole
  disableLogs: true
  memorySize: 512
  environment:
    HANDLER_NAME: warmup
    SERVICE_NAME: ${self:custom.baseName}

wsAuthorizer:
  handler: bootstrap
  name: ${self:provider.stackName}-ws-authorizer
  timeout: 30
  tracing: true
  role: wsAuthorizerRole
  environment:
    HANDLER_NAME: ws-authorizer
    DBS_SESSION_TABLE: ${self:custom.dbsSessionTable}
    SESSION_CACHE_TABLE: ${env:SESSION_CACHE_TABLE}
    PAIRING_TABLE: ${self:custom.pairingTableName}
    MANAGEMENT_AUTH0_CLIENT_ID: ${self:custom.managementAuth0ClientId}
    MANAGEMENT_AUTH0_CLIENT_SECRET: ${self:custom.managementAuth0ClientSecret}

cronJobHandler:
  handler: bootstrap
  name: ${self:provider.stackName}-cronjob
  role: cronJobRole
  timeout: 300
  tracing: true
  environment:
    HANDLER_NAME: cronjob
    SERVICE_NAME: ${self:custom.baseName}
    PAIRING_TABLE: ${self:custom.pairingTableName}
  events:
    - schedule:
        rate: rate(1 minute)
        enabled: true
        input: '{"cronJobType": "posConnectionStatus"}'
