webhook:
  handler: dist/api
  name: ${self:provider.stackName}-webhook
  timeout: ${self:custom.timeout}
  tracing: true
  role: restWebhookRole
  environment:
    HANDLER_NAME: webhook
    PAIRING_TABLE: ${self:custom.pairingTableName}
    TTL_IN_HOURS: ${self:custom.pairingTableDataTtlInHours}
  events:
    - http:
        path: '/v1/order'
        method: 'post'
    - http:
        path: '/v1/purchase'
        method: 'post'
    - http:
        path: '/v1/refund'
        method: 'post'
    - http:
        path: '/v1/reversal'
        method: 'post'
