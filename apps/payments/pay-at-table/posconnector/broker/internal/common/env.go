package common

import (
	"context"

	"github.com/spf13/viper"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
)

var vEnv = viper.New()

const (
	CONNECTION_TABLE               = "CONNECTION_TABLE"
	DBS_SESSION_TABLE              = "DBS_SESSION_TABLE"
	DBS_AUTH0_AUDIENCE             = "DBS_AUTH0_AUDIENCE"
	SDK_AUTH0_AUDIENCE             = "SDK_AUTH0_AUDIENCE"
	PAIRING_TABLE                  = "PAIRING_TABLE"
	PROJECTION_SQS_URL             = "PROJECTION_SQS_URL"
	SERVICE_NAME                   = "SERVICE_NAME"
	TTL_IN_HOURS                   = "TTL_IN_HOURS"
	AWS_REGION                     = "AWS_REGION"
	IDENTITY_AUTH0_TENANT          = "IDENTITY_AUTH0_TENANT"
	MANAGEMENT_AUTH0_CLIENT_ID     = "MANAGEMENT_AUTH0_CLIENT_ID"
	MANAGEMENT_AUTH0_CLIENT_SECRET = "MANAGEMENT_AUTH0_CLIENT_SECRET"
	SESSION_CACHE_TABLE            = "SESSION_CACHE_TABLE"
	SDK_POS_TTL_IN_HOURS           = "SDK_POS_TTL_IN_HOURS"
)

func init() {
	bindEnv(CONNECTION_TABLE)
	bindEnv(DBS_SESSION_TABLE)
	bindEnv(DBS_AUTH0_AUDIENCE)
	bindEnv(SDK_AUTH0_AUDIENCE)
	bindEnv(PAIRING_TABLE)
	bindEnv(PROJECTION_SQS_URL)
	bindEnv(SERVICE_NAME)
	bindEnv(TTL_IN_HOURS)
	bindEnv(AWS_REGION)
	bindEnv(IDENTITY_AUTH0_TENANT)
	bindEnv(MANAGEMENT_AUTH0_CLIENT_ID)
	bindEnv(MANAGEMENT_AUTH0_CLIENT_SECRET)
	bindEnv(SESSION_CACHE_TABLE)
	bindEnv(SDK_POS_TTL_IN_HOURS)
}

func bindEnv(key string) {
	err := vEnv.BindEnv(key)
	if err != nil {
		logger.Panic(context.Background(), err)
	}
}

func DbsSessionTableName() string {
	return vEnv.GetString(DBS_SESSION_TABLE)
}

func DbsAuth0Audience() string {
	return vEnv.GetString(DBS_AUTH0_AUDIENCE)
}

func SessionCacheTableName() string {
	return vEnv.GetString(SESSION_CACHE_TABLE)
}

func SdkAuth0Audience() string {
	return vEnv.GetString(SDK_AUTH0_AUDIENCE)
}

func PairingTableName() string {
	return vEnv.GetString(PAIRING_TABLE)
}

func ProjectionSqsUrl() string {
	return vEnv.GetString(PROJECTION_SQS_URL)
}

func ServiceName() string {
	return vEnv.GetString(SERVICE_NAME)
}

func TtlInHours() int64 {
	return vEnv.GetInt64(TTL_IN_HOURS)
}

func SdkPosTtlInHours() int64 {
	return vEnv.GetInt64(SDK_POS_TTL_IN_HOURS)
}

func AwsRegion() string {
	return vEnv.GetString(AWS_REGION)
}

func IdentityAuth0Tenant() string {
	return vEnv.GetString(IDENTITY_AUTH0_TENANT)
}

func ManagementAuth0ClientId() string {
	return vEnv.GetString(MANAGEMENT_AUTH0_CLIENT_ID)
}

func ManagementAuth0ClientSecret() string {
	return vEnv.GetString(MANAGEMENT_AUTH0_CLIENT_SECRET)
}
