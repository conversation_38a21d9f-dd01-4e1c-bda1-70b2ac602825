package common

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

var connectionTableName string
var pairingTableName string
var serviceName string
var ttlInHours string
var wsApiEndpoint string
var projectionSqsUrl string
var dbsSessionCache string

func beforeEach() {
	connectionTableName = os.Getenv("CONNECTION_TABLE")
	dbsSessionCache = os.Getenv("DBS_SESSION_TABLE")
	pairingTableName = os.Getenv("PAIRING_TABLE")
	projectionSqsUrl = os.Getenv("PROJECTION_SQS_URL")
	serviceName = os.Getenv("SERVICE_NAME")
	ttlInHours = os.Getenv("TTL_IN_HOURS")
	os.Setenv("CONNECTION_TABLE", "Connection")
	os.Setenv("DBS_SESSION_TABLE", "dbsSessionCache")
	os.Setenv("PAIRING_TABLE", "Pairing")
	os.Setenv("PROJECTION_SQS_URL", "https://sqs")
	os.Setenv("SERVICE_NAME", "test-posconnector-api")
	os.Setenv("TTL_IN_HOURS", "720")
	os.Setenv("AWS_REGION", "ap-southeast-2")
}

func afterEach() {
	os.Setenv("CONNECTION_TABLE", connectionTableName)
	os.Setenv("PAIRING_TABLE", pairingTableName)
	os.Setenv("PROJECTION_SQS_URL", projectionSqsUrl)
	os.Setenv("SERVICE_NAME", serviceName)
	os.Setenv("TTL_IN_HOURS", ttlInHours)
}

func TestEnv(t *testing.T) {
	beforeEach()
	defer afterEach()
	assert.Equal(t, "dbsSessionCache", DbsSessionTableName())
	assert.Equal(t, "Pairing", PairingTableName())
	assert.Equal(t, "https://sqs", ProjectionSqsUrl())
	assert.Equal(t, "test-posconnector-api", ServiceName())
	assert.Equal(t, int64(720), TtlInHours())
	assert.Equal(t, "ap-southeast-2", AwsRegion())
}
