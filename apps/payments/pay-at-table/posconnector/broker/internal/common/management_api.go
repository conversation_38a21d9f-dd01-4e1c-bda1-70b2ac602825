package common

import (
	"context"
	"fmt"
	"log"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
)

type ManagementApi interface {
	PostToConnection(ctx context.Context, input *apigatewaymanagementapi.PostToConnectionInput) (*apigatewaymanagementapi.PostToConnectionOutput, error)
	DeleteConnection(ctx context.Context, params *apigatewaymanagementapi.DeleteConnectionInput, optFns ...func(*apigatewaymanagementapi.Options)) (*apigatewaymanagementapi.DeleteConnectionOutput, error)
	GetConnection(ctx context.Context, params *apigatewaymanagementapi.GetConnectionInput, optFns ...func(*apigatewaymanagementapi.Options)) (*apigatewaymanagementapi.GetConnectionOutput, error)
}

type ManagementApiImpl struct {
	client *apigatewaymanagementapi.Client
}

func NewManagementApiFromCtx(ctx context.Context, mgrApiMap map[string]ManagementApi, url string) ManagementApi {
	var api ManagementApi
	a := ctx.Value(ManagementApiConfig{
		Endpoint: url,
	})
	if v, ok := a.(ManagementApi); ok {
		api = v
	} else {
		api = newManagementApi(ctx, url)
	}
	return api
}

func GetMgrApiFromMap(ctx context.Context, mgrApiMap map[string]ManagementApi, connectionEndpoint string) ManagementApi {
	var mgrApi ManagementApi

	mgrApi, hasEndpointMgrApi := mgrApiMap[connectionEndpoint]
	if !hasEndpointMgrApi {
		mgrApi = NewManagementApiFromCtx(ctx, mgrApiMap, connectionEndpoint)
		mgrApiMap[connectionEndpoint] = mgrApi
	}

	return mgrApi
}

func newManagementApi(ctx context.Context, endpoint string) ManagementApi {
	return &ManagementApiImpl{getApiClient(ctx, endpoint)}
}

func (api *ManagementApiImpl) GetConnection(ctx context.Context, input *apigatewaymanagementapi.GetConnectionInput, optFns ...func(*apigatewaymanagementapi.Options)) (*apigatewaymanagementapi.GetConnectionOutput, error) {
	logger.Debug(ctx, fmt.Sprintf("get connection %s", *input.ConnectionId))
	return api.client.GetConnection(ctx, input)
}

func (api *ManagementApiImpl) PostToConnection(ctx context.Context, input *apigatewaymanagementapi.PostToConnectionInput) (*apigatewaymanagementapi.PostToConnectionOutput, error) {
	logger.Debug(ctx, fmt.Sprintf("publish to connection %s", *input.ConnectionId))
	return api.client.PostToConnection(ctx, input)
}

func (api *ManagementApiImpl) DeleteConnection(ctx context.Context, params *apigatewaymanagementapi.DeleteConnectionInput, optFns ...func(*apigatewaymanagementapi.Options)) (*apigatewaymanagementapi.DeleteConnectionOutput, error) {
	logger.Info(ctx, fmt.Sprintf("Delete connection %s", *params.ConnectionId))
	return api.client.DeleteConnection(ctx, params, optFns...)
}

func getApiClient(ctx context.Context, endpoint string) *apigatewaymanagementapi.Client {
	cfg, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		log.Panic("cfg err")
	}

	return apigatewaymanagementapi.NewFromConfig(cfg, func(o *apigatewaymanagementapi.Options) {
		o.BaseEndpoint = aws.String(endpoint)
	})
}
