package common

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"reflect"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/aws/retry"
	"github.com/aws/aws-sdk-go-v2/config"
)

func LoadAwsConfig(ctx context.Context) aws.Config {
	cfg, ok := ctx.Value(AwsConfig{}).(aws.Config)
	if !ok {
		localcfg, e := config.LoadDefaultConfig(context.TODO(), config.WithRetryer(func() aws.Retryer { return retry.AddWithMaxAttempts(retry.NewStandard(), 5) }))
		if e != nil {
			log.Fatalf("unable to load SDK config, %v", ok)
		}
		cfg = localcfg
	}
	return cfg
}

func CheckRequiredFields(data map[string]any, requiredFields []string) error {
	errorMsg := ""
	for _, field := range requiredFields {
		if _, ok := data[field]; !ok {
			errorMsg += " " + field
		}
	}
	if len(errorMsg) == 0 {
		return nil
	}
	return fmt.Errorf("missing fields %s", errorMsg)
}

func GetTtl(hours int64) int64 {
	now := time.Now().UnixMilli() / 1000
	seconds := hours * 60 * 60
	return now + seconds
}

func GetMaskedString() string {
	return "N/A"
}

func CopyStruct[T any](s T, r *T) {
	byt, _ := json.Marshal(s)
	_ = json.Unmarshal(byt, &r)
}

func IsNil(c any) bool {
	return c == nil || (reflect.ValueOf(c).Kind() == reflect.Ptr && reflect.ValueOf(c).IsNil())
}

func GetHeaderValue(headerMap map[string]string, headerKey string) (string, bool) {
	headers := http.Header{}
	for key, value := range headerMap {
		headers.Set(key, value)
	}

	value := headers.Get(headerKey)

	if value == "" {
		return "", false
	}

	return value, true
}
