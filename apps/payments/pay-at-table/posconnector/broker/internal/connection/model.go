package connection

type ConnectionStatus string

const (
	CONNECTED    ConnectionStatus = "CONNECTED"
	DISCONNECTED ConnectionStatus = "DISCONNECTED"
)

type ConnectionModel struct {
	Id                 string `dynamodbav:"id"`
	Type               string `dynamodbav:"type"`
	Status             string `dynamodbav:"status"`
	EntityUuid         string `dynamodbav:"entityUuid"`
	SiteUuid           string `dynamodbav:"siteUuid"`
	DeviceUuid         string `dynamodbav:"deviceUuid"`
	CustomerUuid       string `dynamodbav:"customerUuid"`
	Provider           string `dynamodbav:"provider"`
	DeviceType         string `dynamodbav:"deviceType"`
	CreatedTime        string `dynamodbav:"createdTime"`
	UpdatedTime        string `dynamodbav:"updatedTime,omitempty"`
	DisconnectedTime   string `dynamodbav:"disconnectedTime,omitempty"`
	ZellerTraceId      string `dynamodbav:"zellerTraceId,omitempty"`
	ConnectionEndpoint string `dynamodbav:"connectionEndpoint,omitempty"`
	TTL                int64  `dynamodbav:"ttl,omitempty"`
}

type EventModel struct {
	Id           string         `dynamodbav:"id"`
	Topic        string         `dynamodbav:"topic"`
	Payload      map[string]any `dynamodbav:"payload"`
	Schema       map[string]any `dynamodbav:"schema"`
	ConnectionId string         `dynamodbav:"connectionId"`
	EntityUuid   string         `dynamodbav:"entityUuid"`
	SiteUuid     string         `dynamodbav:"siteUuid"`
	DeviceUuid   string         `dynamodbav:"deviceUuid"`
}

type SubscriberModel struct {
	Ttl          int64  `dynamodbav:"ttl"`
	Id           string `dynamodbav:"id"`
	Topic        string `dynamodbav:"topic"`
	ConnectionId string `dynamodbav:"connectionId"`
	EntityUuid   string `dynamodbav:"entityUuid"`
	SiteUuid     string `dynamodbav:"siteUuid"`
	DeviceUuid   string `dynamodbav:"deviceUuid"`
	Status       string `dynamodbav:"status"`
	EventId      string `dynamodbav:"eventId"`
}
