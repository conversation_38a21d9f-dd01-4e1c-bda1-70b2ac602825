package handler

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/session"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/auth"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/test"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func init() {
	test.SetTestEnv()
	aud = []string{common.DbsAuth0Audience()}
	ctx = test.LoadAwsMockConfig(context.Background())
	test.SetupLocalTable(ctx)
}

func TestDeviceAHNoDeviceUuidSDK(t *testing.T) {
	os.Setenv(common.SESSION_CACHE_TABLE, test.SdkSessionCacheTable)
	defer func() {
		os.Setenv(common.SESSION_CACHE_TABLE, test.PosconnectorSessionCacheTable)
	}()

	customerUuid := uuid.NewString()
	accessToken := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), &customerUuid)

	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + accessToken,
		},
	}

	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Nil(t, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Deny).String(), statement.Effect)
	}
}

func TestDeviceAHNoDeviceCacheSDK(t *testing.T) {
	os.Setenv(common.SESSION_CACHE_TABLE, test.SdkSessionCacheTable)
	defer func() {
		os.Setenv(common.SESSION_CACHE_TABLE, test.PosconnectorSessionCacheTable)
	}()

	customerUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	accessToken := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), &customerUuid)

	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + accessToken,
			"DeviceUuid":    deviceUuid,
		},
	}

	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Nil(t, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Deny).String(), statement.Effect)
	}
}

func TestDeviceAHInactiveDeviceCacheSDK(t *testing.T) {
	os.Setenv(common.SESSION_CACHE_TABLE, test.SdkSessionCacheTable)
	defer func() {
		os.Setenv(common.SESSION_CACHE_TABLE, test.PosconnectorSessionCacheTable)
	}()

	customerUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	accessToken := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), &customerUuid)

	deviceCache := session.DeviceCache{
		Id:          &deviceUuid,
		Type:        ptr.String(string(session.CacheTypeDeviceUuid)),
		EntityUuid:  ptr.String(uuid.NewString()),
		Model:       ptr.String(uuid.NewString()),
		Serial:      ptr.String(uuid.NewString()),
		ModelSerial: ptr.String(uuid.NewString()),
		Status:      ptr.String(string(session.DeviceStatusInactive)),
	}

	item, _ := attributevalue.MarshalMap(session.ToMap(deviceCache))
	dbClient.InsertItem(ctx, test.SdkSessionCacheTable, &item, nil)

	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + accessToken,
			"DeviceUuid":    deviceUuid,
		},
	}

	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Nil(t, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Deny).String(), statement.Effect)
	}
}

func TestDeviceAHValidExistingTokenSDK(t *testing.T) {
	os.Setenv(common.SESSION_CACHE_TABLE, test.SdkSessionCacheTable)
	defer func() {
		os.Setenv(common.SESSION_CACHE_TABLE, test.PosconnectorSessionCacheTable)
	}()
	dbClient := db.NewDynamoDb(ctx)

	entityUuid := uuid.NewString()
	customerUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	model := "POS_INTERFACE"
	serial := uuid.NewString()
	accessToken := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), &customerUuid)

	deviceCache := session.DeviceCache{
		Id:          &deviceUuid,
		Type:        ptr.String(string(session.CacheTypeDeviceUuid)),
		EntityUuid:  &entityUuid,
		Model:       &model,
		Serial:      &serial,
		ModelSerial: ptr.String(model + "." + serial),
		Status:      ptr.String(string(session.DeviceStatusActive)),
	}

	item, _ := attributevalue.MarshalMap(session.ToMap(deviceCache))
	dbClient.InsertItem(ctx, test.SdkSessionCacheTable, &item, nil)

	identityCache := session.IdentityCache{
		Id:           &accessToken,
		Type:         ptr.String(string(session.CacheTypeIdentitySub)),
		EntityUuid:   &entityUuid,
		Auth0Sub:     ptr.String(uuid.NewString()),
		CustomerUuid: &customerUuid,
		DeviceUuid:   &deviceUuid,
		TTL:          ptr.Int(int(time.Now().Unix() + 60 + 60)),
	}
	item, _ = attributevalue.MarshalMap(session.ToMap(identityCache))
	dbClient.InsertItem(ctx, test.SdkSessionCacheTable, &item, nil)

	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + accessToken,
			"DeviceUuid":    deviceUuid,
		},
	}
	pairingData := map[string]string{
		"id":         deviceUuid,
		"type":       fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, deviceUuid),
		"provider":   string(types.IMPOS),
		"status":     "PAIRED",
		"deviceType": string(types.DeviceTypePOS),
	}
	item, _ = attributevalue.MarshalMap(&pairingData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Equal(t, map[string]any{
		"entityUuid":    entityUuid,
		"deviceUuid":    deviceUuid,
		"status":        string(session.DeviceStatusActive),
		"siteUuid":      nil,
		"role":          nil,
		"provider":      pairingData["provider"],
		"customerUuid":  customerUuid,
		"deviceType":    string(types.DeviceTypePOS),
		"zellerTraceId": zellerTraceId,
	}, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Allow).String(), statement.Effect)
	}
}

func TestDeviceAHValidExistingTokenNoPairingSDK(t *testing.T) {
	os.Setenv(common.SESSION_CACHE_TABLE, test.SdkSessionCacheTable)
	defer func() {
		os.Setenv(common.SESSION_CACHE_TABLE, test.PosconnectorSessionCacheTable)
	}()
	dbClient := db.NewDynamoDb(ctx)

	entityUuid := uuid.NewString()
	customerUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	model := "POS_INTERFACE"
	serial := uuid.NewString()
	accessToken := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), &customerUuid)

	deviceCache := session.DeviceCache{
		Id:          &deviceUuid,
		Type:        ptr.String(string(session.CacheTypeDeviceUuid)),
		EntityUuid:  &entityUuid,
		Model:       &model,
		Serial:      &serial,
		ModelSerial: ptr.String(model + "." + serial),
		Status:      ptr.String(string(session.DeviceStatusActive)),
	}

	item, _ := attributevalue.MarshalMap(session.ToMap(deviceCache))
	dbClient.InsertItem(ctx, test.SdkSessionCacheTable, &item, nil)

	identityCache := session.IdentityCache{
		Id:           &accessToken,
		Type:         ptr.String(string(session.CacheTypeIdentitySub)),
		EntityUuid:   &entityUuid,
		Auth0Sub:     ptr.String(uuid.NewString()),
		CustomerUuid: &customerUuid,
		DeviceUuid:   &deviceUuid,
		TTL:          ptr.Int(int(time.Now().Unix() + 60 + 60)),
	}
	item, _ = attributevalue.MarshalMap(session.ToMap(identityCache))
	dbClient.InsertItem(ctx, test.SdkSessionCacheTable, &item, nil)

	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + accessToken,
			"DeviceUuid":    deviceUuid,
		},
	}

	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Nil(t, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Deny).String(), statement.Effect)
	}
}

func TestDeviceAHNewIdentityCacheNoPairingSDK(t *testing.T) {
	os.Setenv(common.SESSION_CACHE_TABLE, test.SdkSessionCacheTable)
	defer func() {
		os.Setenv(common.SESSION_CACHE_TABLE, test.PosconnectorSessionCacheTable)
	}()
	dbClient := db.NewDynamoDb(ctx)

	entityUuid := uuid.NewString()
	customerUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	model := "POS_INTERFACE"
	serial := uuid.NewString()
	accessToken := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), &customerUuid)

	deviceCache := session.DeviceCache{
		Id:          &deviceUuid,
		Type:        ptr.String(string(session.CacheTypeDeviceUuid)),
		EntityUuid:  &entityUuid,
		Model:       &model,
		Serial:      &serial,
		ModelSerial: ptr.String(model + "." + serial),
		Status:      ptr.String(string(session.DeviceStatusActive)),
	}

	item, _ := attributevalue.MarshalMap(session.ToMap(deviceCache))
	dbClient.InsertItem(ctx, test.SdkSessionCacheTable, &item, nil)

	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + accessToken,
			"DeviceUuid":    deviceUuid,
		},
	}

	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Nil(t, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Deny).String(), statement.Effect)
	}
}

func TestDeviceAHValidNewIdentityCacheSDK(t *testing.T) {
	os.Setenv(common.SESSION_CACHE_TABLE, test.SdkSessionCacheTable)
	defer func() {
		os.Setenv(common.SESSION_CACHE_TABLE, test.PosconnectorSessionCacheTable)
	}()
	dbClient := db.NewDynamoDb(ctx)

	entityUuid := uuid.NewString()
	customerUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	model := "POS_INTERFACE"
	serial := uuid.NewString()
	accessToken := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), &customerUuid)

	deviceCache := session.DeviceCache{
		Id:          &deviceUuid,
		Type:        ptr.String(string(session.CacheTypeDeviceUuid)),
		EntityUuid:  &entityUuid,
		Model:       &model,
		Serial:      &serial,
		ModelSerial: ptr.String(model + "." + serial),
		Status:      ptr.String(string(session.DeviceStatusActive)),
	}

	item, _ := attributevalue.MarshalMap(session.ToMap(deviceCache))
	dbClient.InsertItem(ctx, test.SdkSessionCacheTable, &item, nil)

	pairingData := map[string]string{
		"id":         deviceUuid,
		"type":       fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, deviceUuid),
		"provider":   string(types.IMPOS),
		"status":     "PAIRED",
		"deviceType": string(types.DeviceTypePOS),
	}
	item, _ = attributevalue.MarshalMap(&pairingData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + accessToken,
			"DeviceUuid":    deviceUuid,
		},
	}

	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Equal(t, map[string]any{
		"entityUuid":    entityUuid,
		"deviceUuid":    deviceUuid,
		"status":        string(session.DeviceStatusActive),
		"siteUuid":      nil,
		"role":          nil,
		"provider":      pairingData["provider"],
		"customerUuid":  customerUuid,
		"deviceType":    string(types.DeviceTypePOS),
		"zellerTraceId": zellerTraceId,
	}, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Allow).String(), statement.Effect)
	}
}
