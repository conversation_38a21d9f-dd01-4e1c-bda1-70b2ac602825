package handler

import (
	"context"
	"errors"
	"fmt"

	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/middleware"
)

type CronJobType string

const (
	PosConnectionStatus = "posConnectionStatus"
)

func (h *Handler) HandleCronJob(ctx context.Context, cronJobInput middleware.CronJobInput) error {
	cronJobType := cronJobInput.CronJobType
	logger.Info(ctx, "Cron Job Handler: "+cronJobType)
	switch cronJobType {
	case PosConnectionStatus:
		h.protocol.HandlePosConnectionStatusCron(ctx, h.mgrApiMap)
		return nil
	default:
		logger.Error(ctx, "this is invalid cron job type"+cronJobType)
		errMsg := fmt.Sprintf("Unknown cron job type %s", cronJobType)
		return errors.New(errMsg)
	}
}
