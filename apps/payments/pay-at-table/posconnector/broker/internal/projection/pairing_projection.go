package projection

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-xray-sdk-go/xray"
	"github.com/aws/smithy-go/ptr"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

type PairingProjection struct {
	dbClient      *db.DynamoDb
	connectionMgr connection.IfConnectionManager
	protocol      protocol.PosConnectorProtocolIf
	httpClient    http.Client
	mgrApiMap     map[string]common.ManagementApi
}

func NewPairingProjection(ctx context.Context) *PairingProjection {
	dbClient := db.NewDynamoDb(ctx)
	return &PairingProjection{
		dbClient:      dbClient,
		connectionMgr: connection.New(ctx, dbClient),
		protocol:      protocol.NewFromContext(ctx),
		httpClient:    *xray.Client(&http.Client{Timeout: 30 * time.Second}),
		mgrApiMap:     map[string]common.ManagementApi{},
	}
}

func (p *PairingProjection) MaterialiseDevicePairedEvent(ctx context.Context, eventDetail map[string]any) error {
	logger.Debug(ctx, fmt.Sprintf("posinterface:MaterialiseDevicePairedEvent -> eventDetail: %s", utils.BestEffortStringify(eventDetail, false)))

	payload := eventDetail["payload"].(map[string]any)
	requiredFields := []string{"posProvider"}
	if validatePayloadErr := ValidateRequiredFields(ctx, requiredFields, payload); validatePayloadErr != nil {
		return validatePayloadErr
	}

	switch payload["posProvider"].(string) {
	case string(types.HL), string(types.IMPOS), string(types.TEVALIS):
		return p.materialisePayAtTableProviderDevicePairedEvent(ctx, eventDetail)
	case string(types.ORACLE), string(types.SDK):
		return p.materialiseDirectProviderDevicePairedEvent(ctx, eventDetail)
	default:
		return fmt.Errorf("posProvider %s is not supported", payload["posProvider"].(string))
	}
}

func (p *PairingProjection) DeleteRecordByDeviceUnpairedEvent(ctx context.Context, eventDetail map[string]any) error {
	logger.Info(ctx, fmt.Sprintf("posinterface:DeleteRecordByDeviceUnpairedEvent -> eventDetail: %s", utils.BestEffortStringify(eventDetail, false)))

	aggregateId := eventDetail["aggregateId"].(string)
	payload := eventDetail["payload"].(map[string]any)
	requiredFields := []string{"deviceUuid", "timestamp"}
	if validatePayloadErr := ValidateRequiredFields(ctx, requiredFields, payload); validatePayloadErr != nil {
		return validatePayloadErr
	}
	deviceUuid := payload["deviceUuid"].(string)
	timestamp := payload["timestamp"].(string)

	itemType := fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_DEVICE, deviceUuid)

	deviceType, ok := payload["type"].(string)
	if ok && deviceType == string(types.DeviceTypePOS) {
		itemType = fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, deviceUuid)
	}

	output, err := p.dbClient.Query(common.PairingTableName()).Eq("id", aggregateId).Eq("type", itemType).Exec(ctx)

	if err != nil {
		logger.Error(ctx, err.Error())
	}

	if len(output.Data) == 0 {
		errorMessage := fmt.Sprintf("No record found for pairingUuid %s and itemType %s", aggregateId, itemType)
		logger.Error(ctx, errorMessage)
		return errors.New(errorMessage)
	}

	if output.Data[0]["timestamp"].(string) > timestamp {
		logger.Warn(ctx, fmt.Sprintf("Incoming unpair event for pairingUuid %s has an earlier timestamp than timestamp of existing record, will not delete record", aggregateId))
		return nil
	}

	key := struct {
		Id   string `dynamodbav:"id"`
		Type string `dynamodbav:"type"`
	}{Id: aggregateId, Type: itemType}
	deleteError := p.dbClient.DeleteItem(ctx, common.PairingTableName(), key)
	if deleteError != nil {
		logger.Error(ctx, fmt.Sprintf("Delete error: %#v", deleteError))
		return deleteError
	}

	output, _ = p.dbClient.Query(common.PairingTableName()).Index("deviceUuidGsi").Eq("deviceUuid", deviceUuid).Eq("type", types.CONNECTION_CORE).Where().Eq("status", connection.CONNECTED).Exec(ctx)
	for _, conn := range output.Data {
		connectionId := conn["id"].(string)
		connectionEndpoint := conn["connectionEndpoint"].(string)
		mgrApi := common.GetMgrApiFromMap(ctx, p.mgrApiMap, connectionEndpoint)
		_, err := mgrApi.DeleteConnection(ctx, &apigatewaymanagementapi.DeleteConnectionInput{ConnectionId: aws.String(connectionId)})
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("Failed to disconnect %s", err.Error()))
		}
	}

	return nil
}

func (p *PairingProjection) materialisePayAtTableProviderDevicePairedEvent(ctx context.Context, eventDetail map[string]any) error {
	aggregateId := eventDetail["aggregateId"].(string)
	payload := eventDetail["payload"].(map[string]any)

	if _, ok := payload["type"].(string); !ok {
		payload["type"] = string(types.DeviceTypeTerminal)
	}

	switch payload["type"].(string) {
	case string(types.DeviceTypeTerminal):
		return p.materialisePayAtTableTerminalPairedEvent(ctx, aggregateId, payload)
	case string(types.DeviceTypePOS):
		return p.materialisePayAtTablePosPairedEvent(ctx, aggregateId, payload)
	}

	return fmt.Errorf("deviceType %s not supported", payload["type"].(string))
}

func (p *PairingProjection) materialisePayAtTablePosPairedEvent(ctx context.Context, aggregateId string, payload map[string]any) error {
	requiredFields := []string{"posProvider", "deviceUuid", "venueId", "entityUuid", "timestamp"}
	if validatePayloadErr := ValidateRequiredFields(ctx, requiredFields, payload); validatePayloadErr != nil {
		return validatePayloadErr
	}
	posInterfaceDevicePairingModel := PosInterfacePairingModel{
		Id:         aggregateId,
		Provider:   payload["posProvider"].(string),
		VenueId:    payload["venueId"].(string),
		EntityUuid: payload["entityUuid"].(string),
		DeviceUuid: payload["deviceUuid"].(string),
		Timestamp:  payload["timestamp"].(string),
		DeviceType: payload["type"].(string),
		Status:     "ACTIVE",
	}

	if payload["pairedDeviceId"] != nil {
		posInterfaceDevicePairingModel.PairedDeviceId = payload["pairedDeviceId"].(string)
	}

	return p.insertDevicePairedRecord(ctx, posInterfaceDevicePairingModel)
}

func (p *PairingProjection) materialisePayAtTableTerminalPairedEvent(ctx context.Context, aggregateId string, payload map[string]any) error {
	logger.Debug(ctx, fmt.Sprintf("posinterface:materialisePayAtTableTerminalPairedEvent -> aggregateId: %s, payload: %s", aggregateId, utils.BestEffortStringify(payload, false)))

	requiredFields := []string{"posProvider", "siteUuid", "venueId", "deviceUuid", "entityUuid", "timestamp"}
	if validatePayloadErr := ValidateRequiredFields(ctx, requiredFields, payload); validatePayloadErr != nil {
		return validatePayloadErr
	}
	posInterfaceDevicePairingModel := PosInterfacePairingModel{
		Id:         aggregateId,
		Provider:   payload["posProvider"].(string),
		SiteUuid:   ptr.String(payload["siteUuid"].(string)),
		VenueId:    payload["venueId"].(string),
		EntityUuid: payload["entityUuid"].(string),
		DeviceUuid: payload["deviceUuid"].(string),
		Timestamp:  payload["timestamp"].(string),
		DeviceType: payload["type"].(string),
		Status:     "ACTIVE",
	}

	if payload["pairedDeviceId"] != nil {
		posInterfaceDevicePairingModel.PairedDeviceId = payload["pairedDeviceId"].(string)
	}

	if payload["stationId"] != nil {
		posInterfaceDevicePairingModel.StationId = ptr.String(payload["stationId"].(string))
	}

	if payload["locations"] != nil {
		locations := payload["locations"].([]any)
		var locationIds []string
		for _, location := range locations {
			location := location.(map[string]any)
			locationIds = append(locationIds, location["locationId"].(string))
		}
		posInterfaceDevicePairingModel.Locations = locationIds
	}

	return p.insertDevicePairedRecord(ctx, posInterfaceDevicePairingModel)
}

func (p *PairingProjection) materialiseDirectProviderDevicePairedEvent(ctx context.Context, eventDetail map[string]any) error {
	logger.Debug(ctx, fmt.Sprintf("posinterface:materialiseDirectProviderDevicePairedEvent -> eventDetail: %s", utils.BestEffortStringify(eventDetail, false)))

	aggregateId := eventDetail["aggregateId"].(string)
	payload := eventDetail["payload"].(map[string]any)
	requiredFields := []string{"posProvider", "deviceUuid", "entityUuid", "timestamp"}
	if validatePayloadErr := ValidateRequiredFields(ctx, requiredFields, payload); validatePayloadErr != nil {
		return validatePayloadErr
	}

	if _, ok := payload["type"].(string); !ok {
		payload["type"] = string(types.DeviceTypeTerminal)
	}

	posInterfaceDevicePairingModel := PosInterfacePairingModel{
		Id:         aggregateId,
		Provider:   payload["posProvider"].(string),
		DeviceUuid: payload["deviceUuid"].(string),
		EntityUuid: payload["entityUuid"].(string),
		Timestamp:  payload["timestamp"].(string),
		DeviceType: payload["type"].(string),
		Status:     "ACTIVE",
	}

	if payload["stationId"] != nil {
		posInterfaceDevicePairingModel.ClientId = payload["stationId"].(string)
	}

	if payload["pairedDeviceId"] != nil {
		posInterfaceDevicePairingModel.PairedDeviceId = payload["pairedDeviceId"].(string)
	}

	if payload["siteUuid"] != nil {
		posInterfaceDevicePairingModel.SiteUuid = ptr.String(payload["siteUuid"].(string))
	}

	return p.insertDevicePairedRecord(ctx, posInterfaceDevicePairingModel)
}

func (p *PairingProjection) insertDevicePairedRecord(ctx context.Context, posInterfaceDevicePairingModel PosInterfacePairingModel) error {

	recordType := string(types.POSINTERFACE_PAIR_DEVICE)

	if posInterfaceDevicePairingModel.DeviceType == string(types.DeviceTypePOS) {
		recordType = string(types.POSINTERFACE_PAIR_POS)
	}

	itemType := fmt.Sprintf("%s%s", recordType, posInterfaceDevicePairingModel.DeviceUuid)
	itemMap := posInterfaceDevicePairingItem{
		PosInterfacePairingModel: posInterfaceDevicePairingModel,
		Type:                     itemType,
	}

	item, _ := attributevalue.MarshalMap(itemMap)
	output, err := p.dbClient.InsertItem(ctx, common.PairingTableName(), &item, nil)

	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error while inserting item: %#v", err))
		return err
	}
	logger.Info(ctx, fmt.Sprintf("item inserted: %#v", output))
	return nil
}
