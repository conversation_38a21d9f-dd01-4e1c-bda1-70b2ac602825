package projection

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func TestMaterialiseImposTerminalPairedWithLocation(t *testing.T) {
	p := NewPairingProjection(ctx)
	pairingUuid := uuid.NewString()
	siteUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	stationId := uuid.NewString()

	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"siteUuid":    siteUuid,
			"venueId":     "mockVenueId",
			"deviceUuid":  deviceUuid,
			"stationId":   stationId,
			"entityUuid":  "mockEntityUuid",
			"posProvider": types.IMPOS,
			"timestamp":   "**********",
			"locations": []map[string]any{
				{
					"locationId":   uuid.NewString(),
					"locationName": uuid.NewString(),
				},
				{
					"locationId":   uuid.NewString(),
					"locationName": uuid.NewString(),
				},
			},
			"deviceType": types.DeviceTypeTerminal,
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	err := json.Unmarshal([]byte(detailMap), &detailData)
	fmt.Println("err", err)
	p.MaterialiseDevicePairedEvent(context.Background(), detailData)

	itemType := strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, "")
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)

	var locationIds []any
	var eventPayloadLocations []map[string]any = eventDetailMap["payload"].(map[string]any)["locations"].([]map[string]any)
	for _, location := range eventPayloadLocations {
		locationIds = append(locationIds, location["locationId"].(string))
	}

	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, pairingUuid, output.Data[0]["id"])
	assert.Equal(t, siteUuid, output.Data[0]["siteUuid"])
	assert.Equal(t, deviceUuid, output.Data[0]["deviceUuid"])
	assert.Equal(t, locationIds, output.Data[0]["locations"])
	assert.Equal(t, stationId, output.Data[0]["stationId"])
	assert.Equal(t, "ACTIVE", output.Data[0]["status"])
	assert.Equal(t, string(types.DeviceTypeTerminal), output.Data[0]["deviceType"])
	assert.Equal(t, strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, ""), output.Data[0]["type"])
}

func TestMaterialiseImposPosDevicePaired(t *testing.T) {
	p := NewPairingProjection(ctx)
	pairingUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	pairedDeviceId := uuid.NewString()

	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"venueId":        deviceUuid,
			"deviceUuid":     deviceUuid,
			"entityUuid":     entityUuid,
			"posProvider":    types.IMPOS,
			"timestamp":      "**********",
			"pairedDeviceId": pairedDeviceId,
			"type":           types.DeviceTypePOS,
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	err := json.Unmarshal([]byte(detailMap), &detailData)
	fmt.Println("err", err)
	p.MaterialiseDevicePairedEvent(context.Background(), detailData)

	itemType := strings.Join([]string{string(types.POSINTERFACE_PAIR_POS), deviceUuid}, "")
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)

	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, pairingUuid, output.Data[0]["id"])
	assert.Equal(t, deviceUuid, output.Data[0]["deviceUuid"])
	assert.Equal(t, deviceUuid, output.Data[0]["venueId"])
	assert.Equal(t, entityUuid, output.Data[0]["entityUuid"])
	assert.Equal(t, "ACTIVE", output.Data[0]["status"])
	assert.Equal(t, string(types.DeviceTypePOS), output.Data[0]["deviceType"])
	assert.Equal(t, pairedDeviceId, output.Data[0]["pairedDeviceId"])
	assert.Equal(t, strings.Join([]string{string(types.POSINTERFACE_PAIR_POS), deviceUuid}, ""), output.Data[0]["type"])
}

func TestMaterialiseImposPosDeviceUnpaired(t *testing.T) {
	p := NewPairingProjection(ctx)
	pairingUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	deviceUuid := uuid.NewString()

	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"venueId":     deviceUuid,
			"deviceUuid":  deviceUuid,
			"entityUuid":  entityUuid,
			"posProvider": types.IMPOS,
			"timestamp":   "**********",
			"type":        types.DeviceTypePOS,
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	json.Unmarshal([]byte(detailMap), &detailData)
	p.MaterialiseDevicePairedEvent(context.Background(), detailData)

	itemType := strings.Join([]string{string(types.POSINTERFACE_PAIR_POS), deviceUuid}, "")
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)

	assert.Equal(t, 1, len(output.Data))

	unpairEventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"deviceUuid":  deviceUuid,
			"posProvider": types.IMPOS,
			"timestamp":   "**********",
			"type":        types.DeviceTypePOS,
		},
	}
	unpairDetailMap, _ := json.Marshal(unpairEventDetailMap)
	unpairDetailData := map[string]any{}
	json.Unmarshal([]byte(unpairDetailMap), &unpairDetailData)
	p.DeleteRecordByDeviceUnpairedEvent(context.Background(), unpairDetailData)

	itemType = strings.Join([]string{string(types.POSINTERFACE_PAIR_POS), deviceUuid}, "")
	output, _ = dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)

	assert.Equal(t, 0, len(output.Data))
}

func TestMaterialiseImposTerminalDeviceUnpaired(t *testing.T) {
	p := NewPairingProjection(ctx)
	pairingUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	siteUuid := uuid.NewString()

	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"venueId":     deviceUuid,
			"siteUuid":    siteUuid,
			"deviceUuid":  deviceUuid,
			"entityUuid":  entityUuid,
			"posProvider": types.IMPOS,
			"timestamp":   "**********",
			"type":        types.DeviceTypeTerminal,
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	json.Unmarshal([]byte(detailMap), &detailData)
	p.MaterialiseDevicePairedEvent(context.Background(), detailData)

	itemType := strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, "")
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)

	assert.Equal(t, 1, len(output.Data))

	unpairEventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"deviceUuid":  deviceUuid,
			"posProvider": types.IMPOS,
			"timestamp":   "**********",
			"type":        types.DeviceTypeTerminal,
		},
	}
	unpairDetailMap, _ := json.Marshal(unpairEventDetailMap)
	unpairDetailData := map[string]any{}
	json.Unmarshal([]byte(unpairDetailMap), &unpairDetailData)
	p.DeleteRecordByDeviceUnpairedEvent(context.Background(), unpairDetailData)

	itemType = strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, "")
	output, _ = dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)

	assert.Equal(t, 0, len(output.Data))
}

func TestMaterialiseImposPosDevicePairedInvalidPayload(t *testing.T) {
	p := NewPairingProjection(ctx)
	pairingUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	deviceUuid := uuid.NewString()

	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"venueId": deviceUuid,
			// "deviceUuid":  deviceUuid,
			"entityUuid":  entityUuid,
			"posProvider": types.IMPOS,
			"timestamp":   "**********",
			"type":        types.DeviceTypePOS,
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	err := json.Unmarshal([]byte(detailMap), &detailData)
	fmt.Println("err", err)
	err = p.MaterialiseDevicePairedEvent(context.Background(), detailData)
	assert.NotNil(t, err)
}

func TestMaterialiseUnsupportedDeviceType(t *testing.T) {
	p := NewPairingProjection(ctx)
	pairingUuid := uuid.NewString()
	siteUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	stationId := uuid.NewString()

	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"siteUuid":    siteUuid,
			"venueId":     "mockVenueId",
			"deviceUuid":  deviceUuid,
			"stationId":   stationId,
			"entityUuid":  "mockEntityUuid",
			"posProvider": types.IMPOS,
			"timestamp":   "**********",
			"type":        "UNSUPPORTED",
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	err := json.Unmarshal([]byte(detailMap), &detailData)
	fmt.Println("err", err)
	err = p.MaterialiseDevicePairedEvent(context.Background(), detailData)
	assert.NotNil(t, err)
	assert.Equal(t, err.Error(), "deviceType UNSUPPORTED not supported")
}
