package projection

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func TestMaterialiseOracleDevicePaired(t *testing.T) {
	p := NewPairingProjection(ctx)
	pairingUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	stationId := uuid.NewString()

	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"deviceUuid":  deviceUuid,
			"stationId":   stationId,
			"entityUuid":  "mockEntityUuid",
			"posProvider": types.ORACLE,
			"timestamp":   "**********",
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	err := json.Unmarshal([]byte(detailMap), &detailData)
	fmt.Println("err", err)
	p.MaterialiseDevicePairedEvent(context.Background(), detailData)

	itemType := strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, "")
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)

	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, pairingUuid, output.Data[0]["id"])
	assert.Equal(t, deviceUuid, output.Data[0]["deviceUuid"])
	assert.Equal(t, stationId, output.Data[0]["clientId"])
	assert.Equal(t, "ACTIVE", output.Data[0]["status"])
	assert.Equal(t, string(types.DeviceTypeTerminal), output.Data[0]["deviceType"])
	assert.Equal(t, strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, ""), output.Data[0]["type"])
}

func TestFailedOracleDevicePairedWithRequiredFieldsNotProvided(t *testing.T) {
	p := NewPairingProjection(ctx)
	pairingUuid := uuid.NewString()
	deviceUuid := uuid.NewString()

	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"deviceUuid":  deviceUuid,
			"posProvider": types.ORACLE,
			"timestamp":   "**********",
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	err := json.Unmarshal([]byte(detailMap), &detailData)
	fmt.Println("err", err)
	err = p.MaterialiseDevicePairedEvent(context.Background(), detailData)
	assert.NotNil(t, err)

	itemType := strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, "")
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)

	assert.Equal(t, 0, len(output.Data))
}

func TestMaterialiseOracleDevicePairedWithSiteUuid(t *testing.T) {
	p := NewPairingProjection(ctx)
	pairingUuid := uuid.NewString()
	siteUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	stationId := uuid.NewString()

	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"deviceUuid":  deviceUuid,
			"siteUuid":    siteUuid,
			"stationId":   stationId,
			"entityUuid":  "mockEntityUuid",
			"posProvider": types.ORACLE,
			"timestamp":   "**********",
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	err := json.Unmarshal([]byte(detailMap), &detailData)
	fmt.Println("err", err)
	p.MaterialiseDevicePairedEvent(context.Background(), detailData)

	itemType := strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, "")
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)

	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, pairingUuid, output.Data[0]["id"])
	assert.Equal(t, siteUuid, output.Data[0]["siteUuid"])
	assert.Equal(t, deviceUuid, output.Data[0]["deviceUuid"])
	assert.Equal(t, stationId, output.Data[0]["clientId"])
	assert.Equal(t, "ACTIVE", output.Data[0]["status"])
}

func insertOracleDevicePairedItem(p *PairingProjection, pairingUuid string, deviceUuid string, timestamp string) {
	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"siteUuid":    uuid.NewString(),
			"stationId":   "mockClientId",
			"deviceUuid":  deviceUuid,
			"entityUuid":  "mockEntityUuid",
			"posProvider": types.ORACLE,
			"timestamp":   timestamp,
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	json.Unmarshal([]byte(detailMap), &detailData)
	p.MaterialiseDevicePairedEvent(context.Background(), detailData)
}

func TestOracleDeviceUnpairedEventShouldDeleteRecord(t *testing.T) {
	p := NewPairingProjection(ctx)
	pairingUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	itemType := strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, "")

	existingTimestamp := "**********"
	incomingTimestamp := "**********"
	insertOracleDevicePairedItem(p, pairingUuid, deviceUuid, existingTimestamp)
	outputBeforeDelete, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)
	assert.Equal(t, 1, len(outputBeforeDelete.Data))

	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"deviceUuid": deviceUuid,
			"timestamp":  incomingTimestamp,
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	json.Unmarshal([]byte(detailMap), &detailData)
	p.DeleteRecordByDeviceUnpairedEvent(context.Background(), detailData)

	outputAfterDelete, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)
	assert.Equal(t, 0, len(outputAfterDelete.Data))
}

func TestOracleDeviceUnpairedEventShouldDeleteRecordWithConnection(t *testing.T) {
	connId := uuid.NewString()
	// setup mock server
	done := make(chan any)
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		fmt.Println("receive delete connection request:" + req.URL.String())
		assert.Equal(t, fmt.Sprintf("/@connections/%s", connId), req.URL.String())
		res.WriteHeader(http.StatusOK)
		close(done)
	}))

	p := NewPairingProjection(ctx)
	pairingUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	itemType := strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, "")

	existingTimestamp := "**********"
	incomingTimestamp := "**********"
	insertOracleDevicePairedItem(p, pairingUuid, deviceUuid, existingTimestamp)
	outputBeforeDelete, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)
	assert.Equal(t, 1, len(outputBeforeDelete.Data))

	connModel := types.RequestAuthorizer{
		DeviceUuid: deviceUuid,
		EntityUuid: uuid.NewString(),
	}
	connMgr := connection.New(ctx, dbClient)
	connMgr.Connect(ctx, connId, testServer.URL, &connModel)

	item := map[string]any{
		"id":           uuid.NewString(),
		"type":         fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, uuid.NewString(), protocol.SUB_ORDERS_UPDATE),
		"status":       string(connection.CONNECTED),
		"deviceUuid":   deviceUuid,
		"connectionId": connId,
	}
	dbItem, _ := attributevalue.MarshalMap(item)
	_, _ = p.dbClient.InsertItem(ctx, common.PairingTableName(), &dbItem, nil)

	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"deviceUuid": deviceUuid,
			"timestamp":  incomingTimestamp,
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	json.Unmarshal([]byte(detailMap), &detailData)

	p.DeleteRecordByDeviceUnpairedEvent(context.Background(), detailData)

	outputAfterDelete, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)
	assert.Equal(t, 0, len(outputAfterDelete.Data))
	ticker := time.NewTicker(3 * time.Second)
	select {
	case <-done:
		fmt.Printf("Finish \n")
	case <-ticker.C:
		assert.Fail(t, "timeout")
	}
}

func TestFailOracleDeviceUnpairedEventWhenIncomingIsEarlierThanExisting(t *testing.T) {
	p := NewPairingProjection(ctx)
	pairingUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	itemType := strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, "")

	existingTimestamp := "**********"
	incomingTimestamp := "**********"
	insertOracleDevicePairedItem(p, pairingUuid, deviceUuid, existingTimestamp)
	outputBeforeDelete, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)
	assert.Equal(t, 1, len(outputBeforeDelete.Data))

	eventDetailMap := map[string]any{
		"aggregateId": pairingUuid,
		"payload": map[string]any{
			"deviceUuid": deviceUuid,
			"timestamp":  incomingTimestamp,
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	json.Unmarshal([]byte(detailMap), &detailData)
	p.DeleteRecordByDeviceUnpairedEvent(context.Background(), detailData)

	outputAfterDeleteFailed, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)
	assert.Equal(t, 1, len(outputAfterDeleteFailed.Data))
}
