package projection

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func TestMaterialiseSDKPOSDevicePaired(t *testing.T) {
	p := NewPairingProjection(ctx)
	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()

	eventDetailMap := map[string]any{
		"aggregateId": sdkDeviceUuid,
		"payload": map[string]any{
			"deviceUuid":     sdkDeviceUuid,
			"pairedDeviceId": terminalDeviceUuid,
			"entityUuid":     entityUuid,
			"posProvider":    types.SDK,
			"timestamp":      "**********",
			"type":           types.DeviceTypePOS,
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	err := json.Unmarshal([]byte(detailMap), &detailData)
	fmt.Println("err", err)
	p.MaterialiseDevicePairedEvent(context.Background(), detailData)

	posType := strings.Join([]string{string(types.POSINTERFACE_PAIR_POS), sdkDeviceUuid}, "")
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", sdkDeviceUuid).Eq("type", posType).Exec(ctx)

	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, sdkDeviceUuid, output.Data[0]["id"])
	assert.Equal(t, sdkDeviceUuid, output.Data[0]["deviceUuid"])
	assert.Equal(t, terminalDeviceUuid, output.Data[0]["pairedDeviceId"])
	assert.Equal(t, "ACTIVE", output.Data[0]["status"])
	assert.Equal(t, string(types.DeviceTypePOS), output.Data[0]["deviceType"])
	assert.Equal(t, posType, output.Data[0]["type"])
}

func TestMaterialiseSDKTerminalDevicePaired(t *testing.T) {
	p := NewPairingProjection(ctx)
	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()

	eventDetailMap := map[string]any{
		"aggregateId": terminalDeviceUuid,
		"payload": map[string]any{
			"deviceUuid":     terminalDeviceUuid,
			"pairedDeviceId": sdkDeviceUuid,
			"entityUuid":     entityUuid,
			"posProvider":    types.SDK,
			"timestamp":      "**********",
			"type":           types.DeviceTypeTerminal,
		},
	}
	detailMap, _ := json.Marshal(eventDetailMap)
	detailData := map[string]any{}
	err := json.Unmarshal([]byte(detailMap), &detailData)
	fmt.Println("err", err)
	p.MaterialiseDevicePairedEvent(context.Background(), detailData)

	posType := strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), terminalDeviceUuid}, "")
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", terminalDeviceUuid).Eq("type", posType).Exec(ctx)

	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, terminalDeviceUuid, output.Data[0]["id"])
	assert.Equal(t, terminalDeviceUuid, output.Data[0]["deviceUuid"])
	assert.Equal(t, sdkDeviceUuid, output.Data[0]["pairedDeviceId"])
	assert.Equal(t, "ACTIVE", output.Data[0]["status"])
	assert.Equal(t, string(types.DeviceTypeTerminal), output.Data[0]["deviceType"])
	assert.Equal(t, posType, output.Data[0]["type"])
}
