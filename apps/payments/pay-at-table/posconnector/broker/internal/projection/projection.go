package projection

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
)

type Projection struct {
	pairingProjection *PairingProjection
}

func New(ctx context.Context) *Projection {
	p := Projection{NewPairingProjection(ctx)}
	return &p
}

func (p *Projection) ProjectionHandler(ctx context.Context, event events.SQSEvent) (events.SQSEventResponse, error) {
	logger.Info(ctx, fmt.Sprintf("ProjectionHandler: project event -> %#v", event))

	dataList := []common.EventBridgeEventData{}
	for _, message := range event.Records {
		data := common.EventBridgeEventData{}
		err := json.Unmarshal([]byte(message.Body), &data)
		dataList = append(dataList, data)
		if err != nil {
			logger.Error(ctx, err)
			eventResponse := events.SQSEventResponse{
				BatchItemFailures: []events.SQSBatchItemFailure{
					{
						ItemIdentifier: message.MessageId,
					},
				},
			}
			return eventResponse, err
		}
	}

	errorEvents := []common.EventBridgeEventData{}

	for i, eventData := range dataList {
		logger.Debug(ctx, fmt.Sprintf("projecting data - %#v", eventData))
		detailType := eventData.DetailType
		aggregate, aggregateEvent := strings.Split(detailType, ".")[1], strings.Split(detailType, ".")[2]
		deconstructedUri := strings.Join([]string{aggregate, aggregateEvent}, ".")
		detail := eventData.Detail.(map[string]any)
		var err error

		requiredDetailFields := []string{"payload", "aggregateId"}
		validateDetailErr := ValidateRequiredFields(ctx, requiredDetailFields, detail)
		if validateDetailErr != nil {
			logEventError(ctx, validateDetailErr)
			errorEvents = append(errorEvents, eventData)
			continue
		}

		switch deconstructedUri {
		case "PosInterface.DevicePaired":
			materialiseError := p.pairingProjection.MaterialiseDevicePairedEvent(ctx, detail)
			err = materialiseError
		case "PosInterface.DeviceUnpaired":
			materailiseError := p.pairingProjection.DeleteRecordByDeviceUnpairedEvent(ctx, detail)
			err = materailiseError
		default:
			errorMsg := fmt.Sprintf("Invalid detail type: %s", detailType)
			logger.Error(ctx, errorMsg)
			err = errors.New(errorMsg)
		}

		if err != nil {
			logger.Warn(ctx, fmt.Sprintf("get error: %#v", err))
			logger.Warn(ctx, "Failed to project events, need to retry.")
			errorEvents = append(errorEvents, eventData)
			logger.Error(ctx, err.Error())
			continue
		}

		deleteMessage(ctx, event.Records[i], common.ProjectionSqsUrl())
	}
	return checkErrors(ctx, errorEvents)
}

func ValidateRequiredFields(ctx context.Context, requiredFields []string, payload map[string]any) error {
	for _, field := range requiredFields {
		_, ok := payload[field]
		if !ok {
			errorMsg := fmt.Sprintf("Invalid request, %s is missing", field)
			logger.Error(ctx, errorMsg)
			return errors.New(errorMsg)
		}
	}
	return nil
}

func logEventError(ctx context.Context, err error) {
	logger.Warn(ctx, fmt.Sprintf("get error: %#v", err))
	logger.Warn(ctx, "Failed to project events, need to retry.")
	logger.Error(ctx, err.Error())
}

type ISqsClient interface {
	DeleteMessage(ctx context.Context, params *sqs.DeleteMessageInput, optFns ...func(*sqs.Options)) (*sqs.DeleteMessageOutput, error)
}

type AwsSqs struct{}

func deleteMessage(ctx context.Context, message events.SQSMessage, sqsQueueUrl string) {
	var sqsClient ISqsClient
	ctxClient, ok := ctx.Value(AwsSqs{}).(ISqsClient)
	if ok {
		sqsClient = ctxClient
	} else {
		cfg := common.LoadAwsConfig(ctx)
		sqsClient = sqs.NewFromConfig(cfg)
	}
	params := sqs.DeleteMessageInput{
		QueueUrl:      &sqsQueueUrl,
		ReceiptHandle: &message.ReceiptHandle,
	}
	output, _ := sqsClient.DeleteMessage(ctx, &params)
	logger.Info(ctx, fmt.Sprintf("delete message from sqs %#v %#v", message, output))
}

func checkErrors(ctx context.Context, errorEvents []common.EventBridgeEventData) (events.SQSEventResponse, error) {
	marshaledErrorEvents, _ := json.Marshal(errorEvents)

	logger.Info(ctx, fmt.Sprintf("checkErrors -> %#v", string(marshaledErrorEvents)))
	if len(errorEvents) > 0 {
		return events.SQSEventResponse{BatchItemFailures: []events.SQSBatchItemFailure{}}, errors.New(strings.Join([]string{"process event", string(marshaledErrorEvents), "failed"}, " "))
	}
	return events.SQSEventResponse{BatchItemFailures: []events.SQSBatchItemFailure{}}, nil
}
