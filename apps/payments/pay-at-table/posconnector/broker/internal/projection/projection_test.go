package projection

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"testing"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/test"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

var ctx = context.Background()
var dbClient *db.DynamoDb
var mockSqs ISqsClient

func init() {
	test.SetTestEnv()
	ctx = test.LoadAwsMockConfig(context.Background())
	test.SetupLocalTable(ctx)
	dbClient = db.NewDynamoDb(ctx)
	mockSqs := new(test.MockSqs)
	mockSqs.On("DeleteMessage", mock.Anything, mock.Anything).Return(sqs.DeleteMessageOutput{}, nil)
	ctx = context.WithValue(ctx, AwsSqs{}, mockSqs)
}

func TestDevicePairedMaterialiseFunc(t *testing.T) {
	p := New(ctx)
	pairingUuid := uuid.NewString()
	siteUuid := uuid.NewString()
	deviceUuid := uuid.NewString()

	bodyMap := map[string]any{
		"detail-type": "hlpos.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId": pairingUuid,
			"payload": map[string]any{
				"siteUuid":    siteUuid,
				"venueId":     "mockVenueId",
				"deviceUuid":  deviceUuid,
				"entityUuid":  "mockEntityUuid",
				"posProvider": types.HL,
				"timestamp":   "**********",
				"locations": []map[string]any{
					{
						"locationId":   "mockLocationId",
						"locationName": "mockLocationName",
					},
					{
						"locationId":   "mockLocationId",
						"locationName": "mockLocationName",
					},
				},
			},
			"version":          "1",
			"createdTimestamp": "**********",
		},
	}
	body, _ := json.Marshal(bodyMap)

	sqsEvents := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				MessageId:     "mockMessageId",
				ReceiptHandle: "mockReceiptHandle",
				Body:          string(body),
			},
		},
	}
	p.ProjectionHandler(ctx, sqsEvents)

	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Begin("type", string(types.POSINTERFACE_PAIR_DEVICE)).Exec(ctx)
	assert.Equal(t, pairingUuid, output.Data[0]["id"])
	assert.Equal(t, siteUuid, output.Data[0]["siteUuid"])
	assert.Equal(t, deviceUuid, output.Data[0]["deviceUuid"])

	var locationIds []any
	var eventPayloadLocations []map[string]any = bodyMap["detail"].(map[string]any)["payload"].(map[string]any)["locations"].([]map[string]any)
	for _, location := range eventPayloadLocations {
		locationIds = append(locationIds, location["locationId"].(string))
	}
	assert.Equal(t, locationIds, output.Data[0]["locations"])
}

func projectDevicePairedItem(p *Projection, pairingUuid string, deviceUuid string, timestamp string) {
	bodyMap := map[string]any{
		"detail-type": "hlpos.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId": pairingUuid,
			"payload": map[string]any{
				"siteUuid":    uuid.NewString(),
				"venueId":     "mockVenueId",
				"deviceUuid":  deviceUuid,
				"entityUuid":  "mockEntityUuid",
				"posProvider": types.HL,
				"timestamp":   timestamp,
			},
			"version":          "1",
			"createdTimestamp": timestamp,
		},
	}
	body, _ := json.Marshal(bodyMap)

	sqsEvents := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				MessageId:     "mockMessageId",
				ReceiptHandle: "mockReceiptHandle",
				Body:          string(body),
			},
		},
	}
	p.ProjectionHandler(ctx, sqsEvents)
}

func TestDeviceUnpairedMaterialiseFunc(t *testing.T) {
	p := New(ctx)
	pairingUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	itemType := strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, "")
	existingTimestamp := "**********"
	incomingTimestamp := "**********"

	projectDevicePairedItem(p, pairingUuid, deviceUuid, existingTimestamp)
	outputBeforeDelete, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)
	assert.Equal(t, 1, len(outputBeforeDelete.Data))
	assert.Equal(t, pairingUuid, outputBeforeDelete.Data[0]["id"])
	assert.Equal(t, deviceUuid, outputBeforeDelete.Data[0]["deviceUuid"])

	bodyMap := map[string]any{
		"detail-type": "hlpos.PosInterface.DeviceUnpaired",
		"detail": map[string]any{
			"aggregateId": pairingUuid,
			"payload": map[string]any{
				"siteUuid":   uuid.NewString(),
				"venueId":    "mockVenueId",
				"deviceUuid": deviceUuid,
				"timestamp":  incomingTimestamp,
			},
			"version":          "1",
			"createdTimestamp": incomingTimestamp,
		},
	}
	body, _ := json.Marshal(bodyMap)

	sqsEvents := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				MessageId:     "mockMessageId",
				ReceiptHandle: "mockReceiptHandle",
				Body:          string(body),
			},
		},
	}
	p.ProjectionHandler(ctx, sqsEvents)
	outputAfterDelete, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)
	assert.Equal(t, 0, len(outputAfterDelete.Data))
}

func TestNoDetailTypeMapped(t *testing.T) {
	h := New(ctx)
	pairingUuid := uuid.NewString()
	siteUuid := uuid.NewString()
	deviceUuid := uuid.NewString()

	bodyMap := map[string]any{
		"detail-type": "hlpos.InvalidAggregate.InvalidAggregateDetail",
		"detail": map[string]any{
			"aggregateId": pairingUuid,
			"payload": map[string]any{
				"siteUuid":    siteUuid,
				"venueId":     "mockVenueId",
				"deviceUuid":  deviceUuid,
				"entityUuid":  "mockEntityUuid",
				"posProvider": types.HL,
				"timestamp":   "**********",
			},
			"version":          "1",
			"createdTimestamp": "**********",
		},
	}
	body, _ := json.Marshal(bodyMap)

	sqsEvents := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				MessageId:     "mockMessageId",
				ReceiptHandle: "mockReceiptHandle",
				Body:          string(body),
			},
		},
	}
	_, err := h.ProjectionHandler(ctx, sqsEvents)
	assert.NotNil(t, err)
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Begin("type", string(types.POSINTERFACE_PAIR_DEVICE)).Exec(ctx)
	assert.Equal(t, 0, len(output.Data))
}

func TestFailedDevicePairedEventWhileRequiredPayloadNotProvided(t *testing.T) {
	h := New(ctx)
	pairingUuid := uuid.NewString()
	deviceUuid := uuid.NewString()

	bodyMap := map[string]any{
		"detail-type": "hlpos.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId": pairingUuid,
			"payload": map[string]any{
				"venueId":     "mockVenueId",
				"deviceUuid":  deviceUuid,
				"entityUuid":  "mockEntityUuid",
				"posProvider": types.HL,
				"timestamp":   "**********",
			},
			"version":          "1",
			"createdTimestamp": "**********",
		},
	}
	body, _ := json.Marshal(bodyMap)

	sqsEvents := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				MessageId:     "mockMessageId",
				ReceiptHandle: "mockReceiptHandle",
				Body:          string(body),
			},
		},
	}

	_, err := h.ProjectionHandler(ctx, sqsEvents)
	assert.NotNil(t, err)
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Begin("type", string(types.POSINTERFACE_PAIR_DEVICE)).Exec(ctx)
	fmt.Println(output.Data)
	assert.Equal(t, 0, len(output.Data))
}

func TestFailedDevicePairedEventWhileRequiredPayloadProviderNotProvided(t *testing.T) {
	h := New(ctx)
	pairingUuid := uuid.NewString()
	deviceUuid := uuid.NewString()

	bodyMap := map[string]any{
		"detail-type": "hlpos.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId": pairingUuid,
			"payload": map[string]any{
				"deviceUuid": deviceUuid,
			},
			"version":          "1",
			"createdTimestamp": "**********",
		},
	}
	body, _ := json.Marshal(bodyMap)

	sqsEvents := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				MessageId:     "mockMessageId",
				ReceiptHandle: "mockReceiptHandle",
				Body:          string(body),
			},
		},
	}

	_, err := h.ProjectionHandler(ctx, sqsEvents)
	assert.NotNil(t, err)
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Begin("type", string(types.POSINTERFACE_PAIR_DEVICE)).Exec(ctx)
	fmt.Println(output.Data)
	assert.Equal(t, 0, len(output.Data))
}

func TestFailedDevicePairedEventWhilePayloadProviderNotSupported(t *testing.T) {
	h := New(ctx)
	pairingUuid := uuid.NewString()
	deviceUuid := uuid.NewString()

	bodyMap := map[string]any{
		"detail-type": "hlpos.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId": pairingUuid,
			"payload": map[string]any{
				"deviceUuid":  deviceUuid,
				"posProvider": "invalidProvider",
			},
			"version":          "1",
			"createdTimestamp": "**********",
		},
	}
	body, _ := json.Marshal(bodyMap)

	sqsEvents := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				MessageId:     "mockMessageId",
				ReceiptHandle: "mockReceiptHandle",
				Body:          string(body),
			},
		},
	}

	_, err := h.ProjectionHandler(ctx, sqsEvents)
	assert.NotNil(t, err)
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Begin("type", string(types.POSINTERFACE_PAIR_DEVICE)).Exec(ctx)
	fmt.Println(output.Data)
	assert.Equal(t, 0, len(output.Data))
}

func TestFailedEventWhileRequiredDetailFieldNotProvided(t *testing.T) {
	h := New(ctx)
	pairingUuid := uuid.NewString()

	bodyMap := map[string]any{
		"detail-type": "hlpos.PosInterface.DevicePaired",
		"detail":      map[string]any{},
	}
	body, _ := json.Marshal(bodyMap)

	sqsEvents := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				MessageId:     "mockMessageId",
				ReceiptHandle: "mockReceiptHandle",
				Body:          string(body),
			},
		},
	}

	_, err := h.ProjectionHandler(ctx, sqsEvents)
	assert.NotNil(t, err)
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Begin("type", string(types.POSINTERFACE_PAIR_DEVICE)).Exec(ctx)
	fmt.Println(output.Data)
	assert.Equal(t, 0, len(output.Data))
}

func TestContinueNextEventWhenFirstFailed(t *testing.T) {
	h := New(ctx)
	pairingUuid := uuid.NewString()
	siteUuid := uuid.NewString()
	deviceUuid := uuid.NewString()

	failedEventBodyMap := map[string]any{
		"detail-type": "hlpos.PosInterface.DevicePaired",
		"detail":      map[string]any{},
	}
	failedEventBody, _ := json.Marshal(failedEventBodyMap)

	successEventBodyMap := map[string]any{
		"detail-type": "hlpos.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId": pairingUuid,
			"payload": map[string]any{
				"siteUuid":    siteUuid,
				"venueId":     "mockVenueId",
				"deviceUuid":  deviceUuid,
				"entityUuid":  "mockEntityUuid",
				"posProvider": types.HL,
				"timestamp":   "**********",
			},
			"version":          "1",
			"createdTimestamp": "**********",
		},
	}
	successEventBody, _ := json.Marshal(successEventBodyMap)

	sqsEvents := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				MessageId:     "mockMessageId",
				ReceiptHandle: "mockReceiptHandle",
				Body:          string(failedEventBody),
			},
			{
				MessageId:     "mockMessageId2",
				ReceiptHandle: "mockReceiptHandle2",
				Body:          string(successEventBody),
			},
		},
	}

	_, err := h.ProjectionHandler(ctx, sqsEvents)
	assert.NotNil(t, err)
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Begin("type", string(types.POSINTERFACE_PAIR_DEVICE)).Exec(ctx)
	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, pairingUuid, output.Data[0]["id"])
	assert.Equal(t, siteUuid, output.Data[0]["siteUuid"])
	assert.Equal(t, deviceUuid, output.Data[0]["deviceUuid"])
}

func TestInvalidSQSEvent(t *testing.T) {
	h := New(ctx)

	e := []byte("invalid json")

	sqsEvents := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				MessageId:     "mockMessageId",
				ReceiptHandle: "mockReceiptHandle",
				Body:          string(e),
			},
		},
	}

	_, err := h.ProjectionHandler(ctx, sqsEvents)
	assert.NotNil(t, err)
}
