package lambdas

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/aws/aws-lambda-go/events"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/middleware"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/handler"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/projection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

type EVENT_TYPE string

var (
	CONNECT    EVENT_TYPE = "CONNECT"
	DISCONNECT EVENT_TYPE = "DISCONNECT"
	MESSAGE    EVENT_TYPE = "MESSAGE"
)

type Lambda struct {
	handler           *handler.Handler
	projectionHandler *projection.Projection
}

var maskedBearer = types.BEARER + common.GetMaskedString()
var maskedProtocolHeader = types.SEC_WEBSOCKET_PROTOCOL + " " + common.GetMaskedString()

func New(ctx context.Context) *Lambda {
	return &Lambda{handler.New(ctx), projection.New(ctx)}
}

func (l *Lambda) getLogSafeApiEvent(ctx context.Context, event events.APIGatewayWebsocketProxyRequest) (newEvent events.APIGatewayWebsocketProxyRequest) {
	defer func() {
		if err := recover(); err != nil {
			newEvent = event
		}
	}()

	var e events.APIGatewayWebsocketProxyRequest
	common.CopyStruct(event, &e)

	_, hasAuthHeader := e.Headers[types.AUTHORIZATION]
	if hasAuthHeader {
		e.Headers[types.AUTHORIZATION] = maskedBearer
	}
	_, hasMVAuthHeader := e.MultiValueHeaders[types.AUTHORIZATION]
	if hasMVAuthHeader {
		e.MultiValueHeaders[types.AUTHORIZATION][0] = maskedBearer
	}

	_, hasProtocolHeader := e.Headers[types.SEC_WEBSOCKET_PROTOCOL]
	if hasProtocolHeader {
		e.Headers[types.SEC_WEBSOCKET_PROTOCOL] = maskedProtocolHeader
	}

	_, hasMVProtocolHeader := e.MultiValueHeaders[types.SEC_WEBSOCKET_PROTOCOL]
	if hasMVProtocolHeader {
		e.MultiValueHeaders[types.SEC_WEBSOCKET_PROTOCOL][0] = maskedProtocolHeader
	}

	auth, err := types.NewRequestAuthorizerFromInterface(ctx, e.RequestContext.Authorizer)
	if err == nil {
		e.RequestContext.Authorizer = *auth
	}

	return e
}

// This function logs the event with basic masking for sensitive headers
func (l *Lambda) getLogSafeWsAuthorizerEvent(event events.APIGatewayCustomAuthorizerRequestTypeRequest) (newEvent events.APIGatewayCustomAuthorizerRequestTypeRequest) {
	var e events.APIGatewayCustomAuthorizerRequestTypeRequest
	common.CopyStruct(event, &e)

	_, hasAuthHeader := e.Headers[types.AUTHORIZATION]
	if hasAuthHeader {
		e.Headers[types.AUTHORIZATION] = maskedBearer
	}

	_, hasMVAuthHeader := e.MultiValueHeaders[types.AUTHORIZATION]
	if hasMVAuthHeader {
		e.MultiValueHeaders[types.AUTHORIZATION][0] = maskedBearer
	}

	_, hasProtocolHeader := e.Headers[types.SEC_WEBSOCKET_PROTOCOL]
	if hasProtocolHeader {
		e.Headers[types.SEC_WEBSOCKET_PROTOCOL] = maskedProtocolHeader
	}

	return e
}

func (l *Lambda) setApiEventContextMetadata(ctx context.Context, auth types.RequestAuthorizer) context.Context {
	ctx = logger.AddMetadata(ctx, "deviceUuid", auth.DeviceUuid)
	ctx = logger.AddMetadata(ctx, "entityUuid", auth.EntityUuid)
	ctx = logger.AddMetadata(ctx, "provider", auth.Provider)

	if auth.SiteUuid != "" {
		ctx = logger.AddMetadata(ctx, "siteUuid", auth.SiteUuid)
	}

	if auth.ZellerTraceId != "" {
		ctx = logger.AddMetadata(ctx, "zellerTraceId", auth.ZellerTraceId)
	}

	return ctx
}

func (l *Lambda) ApiEventHandler(ctx context.Context, event events.APIGatewayWebsocketProxyRequest) (events.APIGatewayProxyResponse, error) {
	logSafeEvent := l.getLogSafeApiEvent(ctx, event)

	auth := logSafeEvent.RequestContext.Authorizer.(types.RequestAuthorizer)
	ctx = l.setApiEventContextMetadata(ctx, auth)

	jsonE, _ := json.Marshal(logSafeEvent)
	logger.Info(ctx, "event:"+string(jsonE))

	e := event
	logger.Debug(ctx, "event type:"+e.RequestContext.EventType)
	var resp *events.APIGatewayProxyResponse
	var err error
	switch {
	case e.RequestContext.EventType == string(CONNECT):
		resp, err = l.handler.HandleConnection(ctx, e)
	case e.RequestContext.EventType == string(MESSAGE):
		l.handler.HandleMessage(ctx, e)
		resp = &events.APIGatewayProxyResponse{StatusCode: 200}
	case e.RequestContext.EventType == string(DISCONNECT):
		resp, err = l.handler.HandleDisconnection(ctx, e)
	default:
		logger.Error(ctx, fmt.Sprintf("Unknown connection type %s.", e.RequestContext.EventType))
		resp, err = &events.APIGatewayProxyResponse{Body: "", StatusCode: 400}, nil
	}
	return *resp, err
}

func (l *Lambda) ProjectionHandler(ctx context.Context, event events.SQSEvent) (events.SQSEventResponse, error) {
	return l.projectionHandler.ProjectionHandler(ctx, event)
}

func (l *Lambda) WarmupHandler(ctx context.Context) {
	l.handler.WarmupHandler(ctx)
}

func (l *Lambda) WsAuthorizerHandler(ctx context.Context, event events.APIGatewayCustomAuthorizerRequestTypeRequest) (events.APIGatewayCustomAuthorizerResponse, error) {
	logSafeEvent := l.getLogSafeWsAuthorizerEvent(event)
	jsonE, _ := json.Marshal(logSafeEvent)
	logger.Info(ctx, "event:"+string(jsonE))

	return l.handler.DeviceAuthorizerHandler(ctx, event)
}

func (l *Lambda) WebhookHandler(ctx context.Context, event events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	return l.handler.HandleWebhook(ctx, event)
}

func (l *Lambda) CronJobHandler(ctx context.Context, cronJobInput middleware.CronJobInput) (any, error) {
	return nil, l.handler.HandleCronJob(ctx, cronJobInput)
}
