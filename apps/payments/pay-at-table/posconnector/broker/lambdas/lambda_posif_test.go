// test protocol against mock pos interface server

package lambdas

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/session"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/auth"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/projection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/test"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

type TestData struct {
	connId        string
	ctx           context.Context
	lambda        *Lambda
	entityUuid    string
	deviceUuid    string
	siteUuid      string
	pairingUuid   string
	authorizer    types.RequestAuthorizer
	mockServer    *protocol.PosInterfaceMockServer
	managementApi *common.MockManagementApi
}

var testData TestData

func establishAuth(t *testing.T, ctx context.Context, lambda *Lambda, headers map[string]string) (events.APIGatewayCustomAuthorizerResponse, error) {
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Type:    "REQUEST",
		Headers: headers,
	}
	resp, err := lambda.WsAuthorizerHandler(ctx, event)

	assert.Nil(t, err, err)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Allow).String(), statement.Effect)
	}
	assert.NotNil(t, resp.Context)
	return resp, err
}

func establishConnection(t *testing.T, ctx context.Context, lambda *Lambda, connectionId string, authorizer types.RequestAuthorizer) {
	event := events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "CONNECT",
			ConnectionID: connectionId,
			Authorizer:   authorizer,
			DomainName:   test.WsRestApiDomain,
		},
	}
	resp, err := lambda.ApiEventHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
}

func establishPairing(t *testing.T, ctx context.Context, lambda *Lambda, pairingUuid string, payload map[string]any) {
	bodyMap := map[string]any{
		"detail-type": "posconnector.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId":      pairingUuid,
			"payload":          payload,
			"version":          "1",
			"createdTimestamp": "**********",
		},
	}

	body, _ := json.Marshal(bodyMap)
	bodyString := string(body)

	e := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				Body: bodyString,
			},
		},
	}

	_, err := lambda.ProjectionHandler(ctx, e)

	assert.Nil(t, err, err)
}

func insertDeviceCache(ctx context.Context, deviceUuid string, entityUuid string) {
	deviceCache := session.DeviceCache{
		Id:          &deviceUuid,
		Type:        ptr.String(string(session.CacheTypeDeviceUuid)),
		EntityUuid:  &entityUuid,
		Model:       ptr.String(uuid.NewString()),
		Serial:      ptr.String(uuid.NewString()),
		ModelSerial: ptr.String(uuid.NewString()),
		Status:      ptr.String(string(session.DeviceStatusActive)),
	}
	item, _ := attributevalue.MarshalMap(session.ToMap(deviceCache))
	dbClient.InsertItem(ctx, test.SdkSessionCacheTable, &item, nil)
}

func insertIdentitySubCache(ctx context.Context, accessToken string, deviceUuid string, entityUuid string, customerUuid string) {
	identityCache := session.IdentityCache{
		Id:           &accessToken,
		Type:         ptr.String(string(session.CacheTypeIdentitySub)),
		EntityUuid:   &entityUuid,
		Auth0Sub:     ptr.String(uuid.NewString()),
		CustomerUuid: &customerUuid,
		DeviceUuid:   &deviceUuid,
		TTL:          ptr.Int(int(time.Now().Unix() + 60 + 60)),
	}
	item, _ := attributevalue.MarshalMap(session.ToMap(identityCache))
	dbClient.InsertItem(ctx, test.SdkSessionCacheTable, &item, nil)
}

func beforEachBase() {
	testData = TestData{}
	testData.connId = uuid.NewString()
	testData.ctx = test.LoadAwsMockConfig(context.Background())
	testData.mockServer = protocol.NewMockPosInterfaceServer(testData.ctx)
	testData.managementApi = &common.MockManagementApi{}
	mockSqs := new(test.MockSqs)
	mockSqs.On("DeleteMessage", mock.Anything, mock.Anything).Return(sqs.DeleteMessageOutput{}, nil)
	testData.ctx = context.WithValue(testData.ctx, projection.AwsSqs{}, mockSqs)
	testData.ctx = context.WithValue(testData.ctx, common.ManagementApiConfig{
		Endpoint: test.WsRestApiEndpoint,
	}, testData.managementApi)

	test.SetupLocalTable(testData.ctx)
	test.SetTestEnv()
	testData.lambda = New(testData.ctx)
}

func beforeEach(t *testing.T) {
	beforEachBase()
	testData.pairingUuid = uuid.NewString()
	testData.entityUuid = uuid.NewString()
	testData.siteUuid = uuid.NewString()
	testData.deviceUuid = uuid.NewString()
	testData.authorizer = types.RequestAuthorizer{
		EntityUuid: testData.entityUuid,
		DeviceUuid: testData.deviceUuid,
		SiteUuid:   testData.siteUuid,
		Provider:   string(types.HL),
		Status:     "ACTIVE",
		DeviceType: string(types.DeviceTypeTerminal),
	}
	establishConnection(t, testData.ctx, testData.lambda, testData.connId, testData.authorizer)

	establishPairing(t, testData.ctx, testData.lambda, testData.pairingUuid, map[string]any{
		"siteUuid":    testData.siteUuid,
		"venueId":     "mockVenueId",
		"deviceUuid":  testData.deviceUuid,
		"pairingUuid": testData.pairingUuid,
		"entityUuid":  testData.entityUuid,
		"posProvider": string(types.HL),
		"type":        string(types.DeviceTypeTerminal),
		"timestamp":   "**********",
	})
}

func afterEach(t *testing.T) {
	resp, err := testData.lambda.handler.HandleDisconnection(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "DISCONNECT",
			ConnectionID: testData.connId,
			DomainName:   test.WsRestApiDomain,
		},
	})
	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.mockServer.Server.Close()
}

func TestGetOrders(t *testing.T) {
	beforeEach(t)
	defer afterEach(t)

	itemData := map[string]string{
		"id":       string(types.HL),
		"type":     fmt.Sprintf("%s%s", types.METADATA, types.HL),
		"endpoint": testData.mockServer.Server.URL,
	}
	item, _ := attributevalue.MarshalMap(itemData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	oid := uuid.NewString()
	tn := uuid.NewString()
	tName := uuid.NewString()
	ta := 10
	oa := 1
	lid := uuid.NewString()
	lname := uuid.NewString()
	testData.mockServer.Orders = []protocol.DeviceOrder{
		{
			OrderId:     &oid,
			TableNumber: &tn,
			TableName:   &tName,
			TotalAmount: &ta,
			OwedAmount:  &oa,
			Location: &protocol.Location{
				LocationId:   &lid,
				LocationName: &lname,
			},
		},
	}
	event := map[string]string{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.ActionType(protocol.GET_ORDERS)),
	}
	b, _ := json.Marshal(event)

	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: testData.connId,
			Authorizer:   testData.authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})
	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)
}

func TestGetOrder(t *testing.T) {
	beforeEach(t)
	defer afterEach(t)

	oid := uuid.NewString()
	tn := uuid.NewString()
	tName := uuid.NewString()
	ta := 10
	oa := 1
	lid := uuid.NewString()
	lname := uuid.NewString()
	testData.mockServer.Order = protocol.DeviceOrder{
		OrderId:     &oid,
		TableNumber: &tn,
		TableName:   &tName,
		TotalAmount: &ta,
		OwedAmount:  &oa,
		Location: &protocol.Location{
			LocationId:   &lid,
			LocationName: &lname,
		},
	}
	event := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.ActionType(protocol.GET_ORDER)),
		"data":      map[string]any{"orderId": uuid.NewString()},
	}
	b, _ := json.Marshal(event)

	testData.managementApi.On("GetConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.GetConnectionOutput{}, nil)
	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: testData.connId,
			Authorizer:   testData.authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})
	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)
}

func TestGetPosConnectionStatus(t *testing.T) {
	beforeEach(t)
	defer afterEach(t)

	posinterface := types.HL
	status := connection.CONNECTED
	testData.mockServer.PosConnectionStatus = protocol.PosConnectionStatus{
		PosInterface: (*string)(&posinterface),
		Status:       (*string)(&status),
	}
	event := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.ActionType(protocol.GET_POS_CONNECTION_STATUS)),
	}
	b, _ := json.Marshal(event)

	testData.managementApi.On("GetConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.GetConnectionOutput{}, nil)
	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: testData.connId,
			Authorizer:   testData.authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})
	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)
}

func TestUpdateOrderItem(t *testing.T) {
	beforeEach(t)
	defer afterEach(t)

	oid := uuid.NewString()
	tn := uuid.NewString()
	tName := uuid.NewString()
	ta := 10
	oa := 1
	lid := uuid.NewString()
	lname := uuid.NewString()
	testData.mockServer.UpdateOrderItem = protocol.DeviceOrder{
		OrderId:     &oid,
		TableNumber: &tn,
		TableName:   &tName,
		TotalAmount: &ta,
		OwedAmount:  &oa,
		Location: &protocol.Location{
			LocationId:   &lid,
			LocationName: &lname,
		},
	}
	event := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.ActionType(protocol.UPDATE_ORDER_ITEM)),
		"data": map[string]any{
			"orderId":    "100",
			"reference":  uuid.NewString(),
			"deviceTime": uuid.NewString(),
			"item": map[string]any{
				"itemId":      uuid.NewString(),
				"description": uuid.NewString(),
				"quantity":    1,
				"amount":      10,
			},
		},
	}
	b, _ := json.Marshal(event)

	testData.managementApi.On("GetConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.GetConnectionOutput{}, nil)
	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: testData.connId,
			Authorizer:   testData.authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})
	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)
}

func TestPayment(t *testing.T) {
	beforeEach(t)
	defer afterEach(t)

	itemData := map[string]string{
		"id":       string(types.HL),
		"type":     fmt.Sprintf("%s%s", types.METADATA, types.HL),
		"endpoint": testData.mockServer.Server.URL,
	}
	item, _ := attributevalue.MarshalMap(itemData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	event := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.ActionType(protocol.PAYMENT)),
		"data": map[string]any{
			"orderId":         "123",
			"locationId":      "123",
			"amount":          1000,
			"maskedPan":       "...1234",
			"merchantId":      "13123",
			"tid":             "123123",
			"rrn":             "4444",
			"stan":            "0033",
			"authCode":        "adssa12",
			"transactionTime": "1231312",
			"transactionUuid": "12313-12-313-213",
		},
	}
	b, _ := json.Marshal(event)

	testData.managementApi.On("GetConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.GetConnectionOutput{}, nil)
	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: testData.connId,
			Authorizer:   testData.authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})
	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)
}

func TestWSPurchaseSDKToTerminal(t *testing.T) {
	beforeEach(t)
	defer afterEach(t)

	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	connectionId := uuid.NewString()

	pairData := map[string]string{
		"id":             uuid.NewString(),
		"type":           fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, sdkDeviceUuid),
		"pairedDeviceId": terminalDeviceUuid,
		"deviceType":     string(types.DeviceTypePOS),
		"deviceUuid":     sdkDeviceUuid,
		"entityUuid":     entityUuid,
		"provider":       string(types.SDK),
		"status":         "ACTIVE",
		"createdTime":    time.Now().UTC().Format(time.RFC3339),
	}
	item, _ := attributevalue.MarshalMap(pairData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	sdkConnectionData := test.GetConnectionCoreMap(map[string]string{
		"id":               connectionId,
		"pairedDeviceUuid": terminalDeviceUuid,
		"deviceUuid":       sdkDeviceUuid,
		"entityUuid":       entityUuid,
		"provider":         string(types.SDK),
	})
	connectionItem, _ := attributevalue.MarshalMap(sdkConnectionData)
	dbClient.InsertItem(ctx, test.PairingTableName, &connectionItem, nil)

	terminalConnectionData := test.GetConnectionCoreMap(map[string]string{
		"pairedDeviceUuid": sdkDeviceUuid,
		"deviceType":       string(types.DeviceTypeTerminal),
		"deviceUuid":       terminalDeviceUuid,
		"entityUuid":       entityUuid,
		"provider":         string(types.SDK),
	})
	terminalConnectionItem, _ := attributevalue.MarshalMap(terminalConnectionData)
	dbClient.InsertItem(ctx, test.PairingTableName, &terminalConnectionItem, nil)

	authorizer := types.RequestAuthorizer{
		EntityUuid: entityUuid,
		DeviceUuid: sdkDeviceUuid,
		SiteUuid:   uuid.NewString(),
		Provider:   string(types.SDK),
		Status:     "ACTIVE",
		DeviceType: string(types.DeviceTypePOS),
	}

	event := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.ActionType(protocol.PURCHASE)),
		"data": map[string]any{
			"sessionUuid":         "7f590937-bea0-4817-987e-77043cf1e320",
			"transactionUuid":     "7f590937-bea0-4817-987e-77043cf1e320",
			"externalReference":   "************",
			"deviceUuid":          "72648fcb-ac21-4fa7-b8cb-ab61852d6c55",
			"amount":              3651,
			"caid":                "000000000038536",
			"timestamp":           "2025-01-21T11:51:49+11:00",
			"tipAmount":           0,
			"transactionCurrency": "AUD",
			"type":                "PURCHASE",
		},
	}
	b, _ := json.Marshal(event)

	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: connectionId,
			Authorizer:   authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})

	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)
}

func TestWSCancelSDKToTerminal(t *testing.T) {
	beforeEach(t)
	defer afterEach(t)

	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	connectionId := uuid.NewString()

	pairData := map[string]string{
		"id":             uuid.NewString(),
		"type":           fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, sdkDeviceUuid),
		"pairedDeviceId": terminalDeviceUuid,
		"deviceType":     string(types.DeviceTypePOS),
		"deviceUuid":     sdkDeviceUuid,
		"entityUuid":     entityUuid,
		"provider":       string(types.SDK),
		"status":         "ACTIVE",
		"createdTime":    time.Now().UTC().Format(time.RFC3339),
	}
	item, _ := attributevalue.MarshalMap(pairData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	sdkConnectionData := test.GetConnectionCoreMap(map[string]string{
		"id":               connectionId,
		"pairedDeviceUuid": terminalDeviceUuid,
		"deviceType":       string(types.DeviceTypePOS),
		"deviceUuid":       sdkDeviceUuid,
		"entityUuid":       entityUuid,
		"provider":         string(types.SDK),
	})
	connectionItem, _ := attributevalue.MarshalMap(sdkConnectionData)
	dbClient.InsertItem(ctx, test.PairingTableName, &connectionItem, nil)

	terminalConnectionData := test.GetConnectionCoreMap(map[string]string{
		"pairedDeviceUuid": sdkDeviceUuid,
		"deviceType":       string(types.DeviceTypeTerminal),
		"deviceUuid":       terminalDeviceUuid,
		"entityUuid":       entityUuid,
		"provider":         string(types.SDK),
	})
	terminalConnectionItem, _ := attributevalue.MarshalMap(terminalConnectionData)
	dbClient.InsertItem(ctx, test.PairingTableName, &terminalConnectionItem, nil)

	authorizer := types.RequestAuthorizer{
		EntityUuid: entityUuid,
		DeviceUuid: sdkDeviceUuid,
		SiteUuid:   uuid.NewString(),
		Provider:   string(types.SDK),
		Status:     "ACTIVE",
		DeviceType: string(types.DeviceTypePOS),
	}

	event := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.ActionType(protocol.CANCEL)),
		"data": map[string]any{
			"sessionUuid": "7f590937-bea0-4817-987e-77043cf1e320",
		},
	}
	b, _ := json.Marshal(event)

	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: connectionId,
			Authorizer:   authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})

	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)
}

func TestWSGetPairedDevicesSDK(t *testing.T) {
	beforeEach(t)
	defer afterEach(t)

	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	connectionId := uuid.NewString()

	sdkPairData := map[string]string{
		"id":             uuid.NewString(),
		"type":           fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, sdkDeviceUuid),
		"pairedDeviceId": terminalDeviceUuid,
		"deviceType":     string(types.DeviceTypePOS),
		"deviceUuid":     sdkDeviceUuid,
		"entityUuid":     entityUuid,
		"provider":       string(types.SDK),
		"status":         "ACTIVE",
		"createdTime":    time.Now().UTC().Format(time.RFC3339),
	}
	item, _ := attributevalue.MarshalMap(sdkPairData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	sdkConnectionData := test.GetConnectionCoreMap(map[string]string{
		"id":               connectionId,
		"pairedDeviceUuid": terminalDeviceUuid,
		"deviceType":       string(types.DeviceTypePOS),
		"deviceUuid":       sdkDeviceUuid,
		"entityUuid":       entityUuid,
		"provider":         string(types.SDK),
	})
	connectionItem, _ := attributevalue.MarshalMap(sdkConnectionData)
	dbClient.InsertItem(ctx, test.PairingTableName, &connectionItem, nil)

	terminalPairData := map[string]string{
		"id":             uuid.NewString(),
		"type":           fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_DEVICE, terminalDeviceUuid),
		"pairedDeviceId": sdkDeviceUuid,
		"deviceType":     string(types.DeviceTypeTerminal),
		"deviceUuid":     terminalDeviceUuid,
		"entityUuid":     entityUuid,
		"provider":       string(types.SDK),
		"status":         "ACTIVE",
		"createdTime":    time.Now().UTC().Format(time.RFC3339),
	}
	terminalPairItem, _ := attributevalue.MarshalMap(terminalPairData)
	dbClient.InsertItem(ctx, test.PairingTableName, &terminalPairItem, nil)

	terminalConnectionData := test.GetConnectionCoreMap(map[string]string{
		"pairedDeviceUuid": sdkDeviceUuid,
		"deviceType":       string(types.DeviceTypeTerminal),
		"deviceUuid":       terminalDeviceUuid,
		"entityUuid":       entityUuid,
		"provider":         string(types.SDK),
	})
	terminalConnectionItem, _ := attributevalue.MarshalMap(terminalConnectionData)
	dbClient.InsertItem(ctx, test.PairingTableName, &terminalConnectionItem, nil)

	authorizer := types.RequestAuthorizer{
		EntityUuid: entityUuid,
		DeviceUuid: sdkDeviceUuid,
		SiteUuid:   uuid.NewString(),
		Provider:   string(types.SDK),
		Status:     "ACTIVE",
		DeviceType: string(types.DeviceTypePOS),
	}

	event := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.ActionType(protocol.GET_PAIRED_DEVICES)),
		"data":      map[string]any{},
	}
	b, _ := json.Marshal(event)

	testData.managementApi.On("GetConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.GetConnectionOutput{}, nil)
	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: connectionId,
			Authorizer:   authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})

	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)
}

func TestWSGetPairedDevicesSDKSessionCache(t *testing.T) {
	beforEachBase()
	defer afterEach(t)

	os.Setenv(common.SESSION_CACHE_TABLE, test.SdkSessionCacheTable)
	defer func() {
		os.Setenv(common.SESSION_CACHE_TABLE, test.PosconnectorSessionCacheTable)
	}()

	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	customerUuid := uuid.NewString()
	connectionId := testData.connId

	accessToken := test.GenerateAccessToken(ptr.Duration(time.Hour*1), []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), &customerUuid)

	establishPairing(t, testData.ctx, testData.lambda, sdkDeviceUuid, map[string]any{
		"deviceUuid":     sdkDeviceUuid,
		"pairedDeviceId": terminalDeviceUuid,
		"pairingUuid":    sdkDeviceUuid,
		"entityUuid":     entityUuid,
		"posProvider":    string(types.SDK),
		"type":           string(types.DeviceTypePOS),
		"timestamp":      "**********",
	})

	insertDeviceCache(ctx, sdkDeviceUuid, entityUuid)
	insertIdentitySubCache(ctx, accessToken, sdkDeviceUuid, entityUuid, customerUuid)

	auth, err := establishAuth(t, ctx, testData.lambda, map[string]string{
		"Sec-WebSocket-Protocol": fmt.Sprintf("posconnector, Authorization|%s, DeviceUuid|%s", accessToken, sdkDeviceUuid),
	})
	if err != nil {
		log.Println(err.Error())
		t.FailNow()
	}
	requestAuthorizer, err := types.NewRequestAuthorizerFromInterface(ctx, auth.Context)
	if err != nil {
		log.Println(err.Error())
		t.Fail()
	}

	establishConnection(t, ctx, testData.lambda, connectionId, *requestAuthorizer)

	event := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.ActionType(protocol.GET_PAIRED_DEVICES)),
		"data":      map[string]any{},
	}
	b, _ := json.Marshal(event)

	testData.managementApi.On("GetConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.GetConnectionOutput{}, nil)
	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: connectionId,
			Authorizer:   requestAuthorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})

	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)
}

func TestWSPurchaseSDKSessionCache(t *testing.T) {
	beforEachBase()
	defer afterEach(t)

	os.Setenv(common.SESSION_CACHE_TABLE, test.SdkSessionCacheTable)
	defer func() {
		os.Setenv(common.SESSION_CACHE_TABLE, test.PosconnectorSessionCacheTable)
	}()

	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	customerUuid := uuid.NewString()
	connectionId := testData.connId

	accessToken := test.GenerateAccessToken(ptr.Duration(time.Hour*1), []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), &customerUuid)

	establishPairing(t, testData.ctx, testData.lambda, sdkDeviceUuid, map[string]any{
		"deviceUuid":     sdkDeviceUuid,
		"pairedDeviceId": terminalDeviceUuid,
		"pairingUuid":    sdkDeviceUuid,
		"entityUuid":     entityUuid,
		"posProvider":    string(types.SDK),
		"type":           string(types.DeviceTypePOS),
		"timestamp":      "**********",
	})

	insertDeviceCache(ctx, sdkDeviceUuid, entityUuid)
	insertIdentitySubCache(ctx, accessToken, sdkDeviceUuid, entityUuid, customerUuid)

	auth, err := establishAuth(t, ctx, testData.lambda, map[string]string{
		"Sec-WebSocket-Protocol": fmt.Sprintf("posconnector, Authorization|%s, DeviceUuid|%s", accessToken, sdkDeviceUuid),
	})
	if err != nil {
		log.Println(err.Error())
		t.FailNow()
	}
	requestAuthorizer, err := types.NewRequestAuthorizerFromInterface(ctx, auth.Context)
	if err != nil {
		log.Println(err.Error())
		t.Fail()
	}

	establishConnection(t, ctx, testData.lambda, connectionId, *requestAuthorizer)

	event := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.ActionType(protocol.PURCHASE)),
		"data": map[string]any{
			"sessionUuid": uuid.NewString(),
		},
	}
	b, _ := json.Marshal(event)

	testData.managementApi.On("GetConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.GetConnectionOutput{}, nil)
	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: connectionId,
			Authorizer:   requestAuthorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})

	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)
}

func TestWSPurchaseSDKNoSession(t *testing.T) {
	beforEachBase()
	defer afterEach(t)

	os.Setenv(common.SESSION_CACHE_TABLE, test.SdkSessionCacheTable)
	defer func() {
		os.Setenv(common.SESSION_CACHE_TABLE, test.PosconnectorSessionCacheTable)
	}()

	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	auth0Sub := uuid.NewString()
	customerUuid := uuid.NewString()
	connectionId := testData.connId

	accessToken := test.GenerateAccessToken(ptr.Duration(time.Hour*1), []string{common.SdkAuth0Audience()}, ptr.String(auth0Sub), &customerUuid)

	establishPairing(t, testData.ctx, testData.lambda, sdkDeviceUuid, map[string]any{
		"deviceUuid":     sdkDeviceUuid,
		"pairedDeviceId": terminalDeviceUuid,
		"pairingUuid":    sdkDeviceUuid,
		"entityUuid":     entityUuid,
		"posProvider":    string(types.SDK),
		"type":           string(types.DeviceTypePOS),
		"timestamp":      "**********",
	})

	insertDeviceCache(ctx, sdkDeviceUuid, entityUuid)

	auth, err := establishAuth(t, ctx, testData.lambda, map[string]string{
		"Sec-WebSocket-Protocol": fmt.Sprintf("posconnector, Authorization|%s, DeviceUuid|%s", accessToken, sdkDeviceUuid),
	})
	if err != nil {
		log.Println(err.Error())
		t.FailNow()
	}
	requestAuthorizer, err := types.NewRequestAuthorizerFromInterface(ctx, auth.Context)
	if err != nil {
		log.Println(err.Error())
		t.Fail()
	}

	establishConnection(t, ctx, testData.lambda, connectionId, *requestAuthorizer)

	event := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.ActionType(protocol.PURCHASE)),
		"data": map[string]any{
			"sessionUuid": uuid.NewString(),
		},
	}
	b, _ := json.Marshal(event)

	testData.managementApi.On("GetConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.GetConnectionOutput{}, nil)
	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: connectionId,
			Authorizer:   requestAuthorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})

	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)

	sessionOutput, _ := dbClient.GetItem(test.SdkSessionCacheTable).Eq("id", accessToken).Eq("type", session.CacheTypeIdentitySub).Exec(ctx)
	assert.NotNil(t, sessionOutput.Item)
	assert.Equal(t, string(session.CacheTypeIdentitySub), sessionOutput.Item["type"])
	assert.Equal(t, accessToken, sessionOutput.Item["id"])
	assert.Equal(t, auth0Sub, sessionOutput.Item["auth0sub"])
	assert.Equal(t, customerUuid, sessionOutput.Item["customerUuid"])
	assert.Equal(t, entityUuid, sessionOutput.Item["entityUuid"])
	assert.Equal(t, sdkDeviceUuid, sessionOutput.Item["deviceUuid"])
}

func TestWSTransactionEventTerminalToSdk(t *testing.T) {
	beforeEach(t)
	defer afterEach(t)

	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	connectionId := uuid.NewString()

	sdkConnectionData := test.GetConnectionCoreMap(map[string]string{
		"pairedDeviceUuid": terminalDeviceUuid,
		"deviceType":       string(types.DeviceTypePOS),
		"deviceUuid":       sdkDeviceUuid,
		"entityUuid":       entityUuid,
		"provider":         string(types.SDK),
		"status":           string(connection.CONNECTED),
	})
	connectionItem, _ := attributevalue.MarshalMap(sdkConnectionData)
	dbClient.InsertItem(ctx, test.PairingTableName, &connectionItem, nil)

	terminalPairData := map[string]string{
		"id":             uuid.NewString(),
		"type":           fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_DEVICE, sdkDeviceUuid),
		"pairedDeviceId": sdkDeviceUuid,
		"deviceType":     string(types.DeviceTypeTerminal),
		"deviceUuid":     terminalDeviceUuid,
		"entityUuid":     entityUuid,
		"provider":       string(types.SDK),
		"status":         "ACTIVE",
		"createdTime":    time.Now().UTC().Format(time.RFC3339),
	}
	item, _ := attributevalue.MarshalMap(terminalPairData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	terminalConnectionData := test.GetConnectionCoreMap(map[string]string{
		"id":               connectionId,
		"pairedDeviceUuid": sdkDeviceUuid,
		"deviceType":       string(types.DeviceTypeTerminal),
		"deviceUuid":       terminalDeviceUuid,
		"entityUuid":       entityUuid,
		"provider":         string(types.SDK),
	})
	terminalConnectionItem, _ := attributevalue.MarshalMap(terminalConnectionData)
	dbClient.InsertItem(ctx, test.PairingTableName, &terminalConnectionItem, nil)

	authorizer := types.RequestAuthorizer{
		EntityUuid: entityUuid,
		DeviceUuid: terminalDeviceUuid,
		SiteUuid:   uuid.NewString(),
		Provider:   string(types.SDK),
		Status:     "ACTIVE",
		DeviceType: string(types.DeviceTypeTerminal),
	}

	request := protocol.PosConnectorRequest{
		EventId:   uuid.NewString(),
		Timestamp: strconv.Itoa(int(time.Now().UnixMilli())),
		Action:    string(protocol.TRANSACTION_EVENT),
		Data: map[string]any{
			"sessionUuid":    uuid.NewString(),
			"event":          "AWAITING_TIP_ENTRY",
			"displayMessage": "Please enter tip amount",
		},
	}
	b, _ := json.Marshal(request)

	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: connectionId,
			Authorizer:   authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})

	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)

	postConnInput := testData.managementApi.Calls[0].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput)
	var published map[string]any
	json.Unmarshal(postConnInput.Data, &published)
	publishedData := published["data"].(map[string]any)

	publishedDataBytes, _ := json.Marshal(publishedData)
	requestDataBytes, _ := json.Marshal(request.Data)
	assert.Equal(t, string(requestDataBytes), string(publishedDataBytes))
	assert.Equal(t, request.Action, published["action"])
	assert.Equal(t, request.EventId, published["eventId"])
}

func TestWSSignatureVerificationTerminalToSdk(t *testing.T) {
	beforeEach(t)
	defer afterEach(t)

	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	connectionId := uuid.NewString()

	sdkConnectionData := test.GetConnectionCoreMap(map[string]string{
		"pairedDeviceUuid": terminalDeviceUuid,
		"deviceType":       string(types.DeviceTypePOS),
		"deviceUuid":       sdkDeviceUuid,
		"entityUuid":       entityUuid,
		"provider":         string(types.SDK),
	})
	connectionItem, _ := attributevalue.MarshalMap(sdkConnectionData)
	dbClient.InsertItem(ctx, test.PairingTableName, &connectionItem, nil)

	terminalPairData := map[string]string{
		"id":             uuid.NewString(),
		"type":           fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_DEVICE, sdkDeviceUuid),
		"pairedDeviceId": sdkDeviceUuid,
		"deviceType":     string(types.DeviceTypeTerminal),
		"deviceUuid":     terminalDeviceUuid,
		"entityUuid":     entityUuid,
		"provider":       string(types.SDK),
		"status":         "ACTIVE",
		"createdTime":    time.Now().UTC().Format(time.RFC3339),
	}
	item, _ := attributevalue.MarshalMap(terminalPairData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	terminalConnectionData := test.GetConnectionCoreMap(map[string]string{
		"id":               connectionId,
		"pairedDeviceUuid": sdkDeviceUuid,
		"deviceType":       string(types.DeviceTypeTerminal),
		"deviceUuid":       terminalDeviceUuid,
		"entityUuid":       entityUuid,
		"provider":         string(types.SDK),
	})
	terminalConnectionItem, _ := attributevalue.MarshalMap(terminalConnectionData)
	dbClient.InsertItem(ctx, test.PairingTableName, &terminalConnectionItem, nil)

	authorizer := types.RequestAuthorizer{
		EntityUuid: entityUuid,
		DeviceUuid: terminalDeviceUuid,
		SiteUuid:   uuid.NewString(),
		Provider:   string(types.SDK),
		Status:     "ACTIVE",
		DeviceType: string(types.DeviceTypeTerminal),
	}

	request := protocol.PosConnectorRequest{
		EventId:   uuid.NewString(),
		Timestamp: strconv.Itoa(int(time.Now().UnixMilli())),
		Action:    string(protocol.SIGNATURE_VERIFICATION),
		Data: map[string]any{
			"sessionUuid": uuid.NewString(),
			"state":       "SIGNATURE_SUBMITTED",
			"signature":   "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAAB4CAIAAAA48Cq8AAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAABCtJREFUeJzt3cFyozAQRVE8///PmQVVLsXCipD6Sd30PasspggFNy1CwPPvBxD4dwAChAUJwoIEYUGCsCBBWJAgLEgQFiQICxKEBQnCggRhQYKwJF6F3fuyB2HZ+4gpZ1uEZSxnRjXCslRWdT6WtHFn9iIsicxJnQjLzHtcUdVBWFao6gNhQYKwDDCual7Cek3YvufnF1RV8hLWDD+R4c1XWHdfXqu3sLgwxtU3XsIau53YiIwBtpeXsOZdRrYmL8ZV7Tlhldbk9ec2M4/MZ4Z1qvMSfRfFZqN7clgnUVv9m8pZ3vPDOn6PLtu5lTOaHinCOhm2lfniqVOisEqUoZYrLNvrLdbBhlxhHRY1MO16pAvrML3YwjcZwzLBOtiWNKwFQyv5OEwa1jJpB1vesMZOefI51C9vWG8DraSdQ/0ICxKpw2Lw6KQOS4dLMcKCBGFdsHriNPNSS1ifWMVMEBYkCAsSYcJSvFZfb6f9Amrn92UxPURhmb9u1Shgvc5L8sxX7keIiVVOEcVrEckLEJGEpfj4zXKDJhtnwZIKMLEabJ9bJzVDUcOaHFo836cWNawZk5891JMO123pwpqpilz6JQqrvAnSToSA5oUPa+Cm5czNT5OdySBMWPU565wrH3drmUZrBAhr+I5ondSaqmj3CBHW8butdl7f/p7IyV4sRlhHdSezTKfxx+nG5yub4wKrFCas485adrengUW28a1vbeqpIoV1qj8d+aci/e66jT9JvLB8Yh38QFif+u9iqPckNMKyxEL5Rli3Zw+f29aDsAxQWG0wLA//BdLGT2y//Mesg6WpiWX+2syAvadz+0+XW2ZL4euK1cYX4ErL1mBYnXcjw0XGcmbFYGLVN76/PfAUqLAe5i8OPYnqt8JGZM8r7GBBrKy43fBnYQNnxeGJfN5Py4yl97F6FspbqTlZgBqP9KS15wZpz8MIl79mmp+zma29mo+nNvZ/r/GDdYeLO+93n3t5XT3iN38Qx+bfxyW8kyHasCZKF2HVLn/THDhnt47U2FGeOfoP5jSsb8q8vsU3XOGT/HlwxvTvQLCwbrl7pAYO8dDZWWHH8f4lXlge1hoP++BcvLBEBn7KPQwGt6KGtfGkMq56BAuLkxpFsLBOHsYV62BbyLDMUYm5SGFtXwcZV/0ihWVue6kPFi+sXdOCcXVLmLCcTBeq6hQmrL2cZB1IjLAUy1B/KyyCA2KEtRFVjYkUFqc2kABhKa5v6kf82v+Mpu8KEJa5MpdGMVQ1I0xY6rNbDq3yCWOqGuM9LNt18LKY9xf1Q+tUNcx7WCeTE9wopt6+kwd844oR1t33Zxov1RzfP93K1TPj0cUIyxDRrOE9rOe9vpKE97AQFGFBgrAgQViQICxIEBYkCAsShAUJwoIEYUGCsCBBWJAgLEgQFiQICxL/Ac9YZ/Nrl/HmAAAAAElFTkSuQmCC",
		},
	}
	b, _ := json.Marshal(request)

	testData.managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp, err := testData.lambda.ApiEventHandler(testData.ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: connectionId,
			Authorizer:   authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})

	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	testData.managementApi.AssertExpectations(t)

	postConnInput := testData.managementApi.Calls[0].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput)
	var published map[string]any
	json.Unmarshal(postConnInput.Data, &published)
	publishedData := published["data"].(map[string]any)

	publishedDataBytes, _ := json.Marshal(publishedData)
	requestDataBytes, _ := json.Marshal(request.Data)
	assert.Equal(t, string(requestDataBytes), string(publishedDataBytes))
	assert.Equal(t, request.Action, published["action"])
	assert.Equal(t, request.EventId, published["eventId"])
}

func TestConnectionsWithDifferentConnectionEndpoint(t *testing.T) {
	ctx := context.Background()
	ctx = test.LoadAwsMockConfig(context.Background())
	test.SetupLocalTable(ctx)
	test.SetTestEnv()

	posDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	connectionId := uuid.NewString()
	apiEndpoint := "https://api.posconnector.test.com"
	sydEndpoint := "https://syd.posconnector.test.com"

	pairData := test.GetPairingMap(map[string]any{
		"type":       fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, posDeviceUuid),
		"deviceUuid": posDeviceUuid,
		"entityUuid": entityUuid,
		"provider":   string(types.IMPOS),
	})
	item, _ := attributevalue.MarshalMap(pairData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	posConnectionData := test.GetConnectionCoreMap(map[string]string{
		"id":         connectionId,
		"deviceType": string(types.DeviceTypePOS),
		"deviceUuid": posDeviceUuid,
		"entityUuid": entityUuid,
		"provider":   string(types.IMPOS),
	})
	connectionItem, _ := attributevalue.MarshalMap(posConnectionData)
	dbClient.InsertItem(ctx, test.PairingTableName, &connectionItem, nil)

	authorizer := types.RequestAuthorizer{
		EntityUuid: entityUuid,
		DeviceUuid: posDeviceUuid,
		SiteUuid:   uuid.NewString(),
		Provider:   string(types.IMPOS),
		Status:     "ACTIVE",
		DeviceType: string(types.DeviceTypePOS),
	}

	lambda := New(ctx)

	event := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.ActionType(protocol.SUB_ORDERS_UPDATE)),
		"data": map[string]any{
			"siteUuid": uuid.NewString(),
			"location": map[string]string{
				"locationId":   uuid.NewString(),
				"locationName": uuid.NewString(),
			},
			"orderId": uuid.NewString(),
		},
	}
	b, _ := json.Marshal(event)

	// Three subscription records with 2 different endpoints, only 2 management API clients will be created
	subscriptionRecords := []map[string]any{
		test.GetSubscriberMap(map[string]any{
			"type":               fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, uuid.NewString(), event["action"].(string)),
			"venueId":            posDeviceUuid,
			"entityUuid":         entityUuid,
			"provider":           string(types.IMPOS),
			"connectionEndpoint": apiEndpoint,
		}),
		test.GetSubscriberMap(map[string]any{
			"type":               fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, uuid.NewString(), event["action"].(string)),
			"venueId":            posDeviceUuid,
			"entityUuid":         entityUuid,
			"provider":           string(types.IMPOS),
			"connectionEndpoint": sydEndpoint,
		}),
		test.GetSubscriberMap(map[string]any{
			"type":               fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, uuid.NewString(), event["action"].(string)),
			"venueId":            posDeviceUuid,
			"entityUuid":         entityUuid,
			"provider":           string(types.IMPOS),
			"connectionEndpoint": sydEndpoint,
		}),
	}
	for _, record := range subscriptionRecords {
		subscriberItem, _ := attributevalue.MarshalMap(record)
		_, err := dbClient.InsertItem(ctx, test.PairingTableName, &subscriberItem, nil)
		if err != nil {
			log.Fatal("Failed to insert subscriber item:", err)
		}
	}
	// Mock the management API for different endpoints
	mockMgrApiApi := &common.MockManagementApi{}
	mockMgrApiSyd := &common.MockManagementApi{}
	mockMgrApiApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil).Times(1)
	mockMgrApiSyd.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil).Times(2)

	ctx = context.WithValue(ctx, common.ManagementApiConfig{
		Endpoint: apiEndpoint,
	}, mockMgrApiApi)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{
		Endpoint: sydEndpoint,
	}, mockMgrApiSyd)

	lambda.ApiEventHandler(ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: connectionId,
			Authorizer:   authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	})
	mockMgrApiApi.AssertExpectations(t)
	mockMgrApiSyd.AssertExpectations(t)
}
