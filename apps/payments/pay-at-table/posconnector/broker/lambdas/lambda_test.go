package lambdas

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/middleware"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/projection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/test"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

var ctx = context.Background()
var dbClient *db.DynamoDb
var l *Lambda
var authorizer types.RequestAuthorizer
var accessToken = test.GenerateAccessToken(ptr.Duration(time.Hour), nil, nil, nil)

func init() {
	ctx = test.LoadAwsMockConfig(context.Background())
	test.SetTestEnv()
	test.SetupLocalTable(ctx)
	dbClient = db.NewDynamoDb(ctx)
	authorizer = types.RequestAuthorizer{
		EntityUuid:   uuid.NewString(),
		DeviceUuid:   uuid.NewString(),
		SiteUuid:     uuid.NewString(),
		CustomerUuid: uuid.NewString(),
		Provider:     string(types.HL),
		Status:       "VALID",
	}
	mockSqs := new(test.MockSqs)
	mockSqs.On("DeleteMessage", mock.Anything, mock.Anything).Return(sqs.DeleteMessageOutput{}, nil)
	ctx = context.WithValue(ctx, projection.AwsSqs{}, mockSqs)
	mockMgrApi := &common.MockManagementApi{}
	ctx = context.WithValue(ctx, common.ManagementApiConfig{
		Endpoint: test.WsRestApiEndpoint,
	}, mockMgrApi)
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	mockMgrApi.On("GetConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.GetConnectionOutput{}, nil)
	l = New(ctx)
}

func TestConnectEvent(t *testing.T) {
	// pairing
	pairingUuid := uuid.NewString()
	pairingData := map[string]string{
		"siteUuid":    uuid.NewString(),
		"venueId":     uuid.NewString(),
		"deviceUuid":  authorizer.DeviceUuid,
		"pairingUuid": pairingUuid,
		"entityUuid":  uuid.NewString(),
		"posProvider": string(types.HL),
		"timestamp":   strconv.Itoa(int(time.Now().UnixMilli())),
	}
	bodyMap := map[string]any{
		"detail-type": "hlpos.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId":      pairingUuid,
			"payload":          pairingData,
			"version":          "1",
			"createdTimestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		},
	}

	body, _ := json.Marshal(bodyMap)
	bodyString := string(body)

	e := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				Body: bodyString,
			},
		},
	}
	_, err := l.ProjectionHandler(ctx, e)
	assert.Nil(t, err, err)
	// connect
	connId := uuid.NewString()
	event := events.APIGatewayWebsocketProxyRequest{
		Headers: map[string]string{
			"Authorization": fmt.Sprintf("%s%s", types.BEARER, accessToken),
		},
		MultiValueHeaders: map[string][]string{
			"Authorization": {
				fmt.Sprintf("%s%s", types.BEARER, accessToken),
			},
		},
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "CONNECT",
			ConnectionID: connId,
			Authorizer:   authorizer,
			DomainName:   test.WsRestApiDomain,
		},
	}
	resp, err := l.ApiEventHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	output, err := dbClient.Query(test.PairingTableName).Eq("id", connId).Eq("type", string(types.CONNECTION_CORE)).Exec(ctx)
	assert.Nil(t, err, err)
	assert.Equal(t, string(connection.CONNECTED), output.Data[0]["status"])
	assert.Equal(t, test.WsRestApiEndpoint, output.Data[0]["connectionEndpoint"])

	// subscribe
	subEvent := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.SUB_ORDERS_UPDATE),
	}
	b, _ := json.Marshal(subEvent)
	se := events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: connId,
			Authorizer:   authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	}
	r, err := l.ApiEventHandler(ctx, se)
	assert.Nil(t, err, err)
	assert.Equal(t, 200, r.StatusCode)
	o, err := dbClient.Query(test.PairingTableName).Index("connectionIdGsi").Eq("connectionId", connId).Begin("type", types.SUBSCRIBER).Exec(ctx)
	assert.Nil(t, err, err)
	assert.Equal(t, subEvent["eventId"].(string), o.Data[0]["id"].(string))
	assert.Equal(t, string(connection.CONNECTED), o.Data[0]["status"].(string))
	assert.Equal(t, connId, o.Data[0]["connectionId"].(string))
	assert.Equal(t, test.WsRestApiEndpoint, o.Data[0]["connectionEndpoint"].(string))
	assert.Nil(t, o.Data[0]["ttl"])

	// webhook
	apiKey := uuid.NewString()
	wsAuthData := map[string]string{
		"id":   string(types.HL),
		"type": fmt.Sprintf("%s%s", types.METADATA, apiKey),
	}
	item, _ := attributevalue.MarshalMap(wsAuthData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	wsEvent := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": uuid.NewString(),
		"action":    string(protocol.SUB_ORDERS_UPDATE),
		"data": map[string]any{
			"orderId":     uuid.NewString(),
			"tableNumber": uuid.NewString(),
			"tableName":   uuid.NewString(),
			"location": map[string]string{
				"locationId":   uuid.NewString(),
				"locationName": uuid.NewString(),
			},
			"totalAmount": 10,
			"owedAmount":  9,
			"siteUuid":    pairingData["siteUuid"],
		},
	}

	wsBody, _ := json.Marshal(wsEvent)
	resp, err = l.WebhookHandler(ctx, events.APIGatewayProxyRequest{
		Headers: map[string]string{"Authorization": apiKey},
		Body:    string(wsBody),
	})
	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)

	// unsubscribe
	unSubEvent := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.UNSUB_ORDERS_UPDATE),
	}
	b, _ = json.Marshal(unSubEvent)
	use := events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: connId,
			Authorizer:   authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: string(b),
	}
	resp, err = l.ApiEventHandler(ctx, use)
	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
	o, err = dbClient.Query(test.PairingTableName).Index("connectionIdGsi").Eq("connectionId", connId).Begin("type", types.SUBSCRIBER).Exec(ctx)
	assert.Nil(t, err, err)
	assert.Equal(t, subEvent["eventId"].(string), o.Data[0]["id"].(string))
	assert.Equal(t, string(connection.DISCONNECTED), o.Data[0]["status"].(string))
	assert.Equal(t, connId, o.Data[0]["connectionId"].(string))
	assert.NotNil(t, o.Data[0]["ttl"])

	// disconnect
	l.handler.HandleDisconnection(ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "DISCONNECT",
			ConnectionID: connId,
			DomainName:   test.WsRestApiDomain,
		}})
	output, err = dbClient.Query(test.PairingTableName).Eq("id", connId).Eq("type", string(types.CONNECTION_CORE)).Exec(ctx)
	assert.Nil(t, err, err)
	assert.Equal(t, string(connection.DISCONNECTED), output.Data[0]["status"])
}

func TestEventTypes(t *testing.T) {
	connId := uuid.NewString()
	testEvents := []map[string]any{
		{"eventType": "UNKNOWN", "statusCode": 400},
		{"eventType": "CONNECT", "statusCode": 200},
		{"eventType": "DISCONNECT", "statusCode": 200},
		{"eventType": "MESSAGE", "statusCode": 200},
	}
	for _, event := range testEvents {
		e := events.APIGatewayWebsocketProxyRequest{
			RequestContext: events.APIGatewayWebsocketProxyRequestContext{
				EventType:    event["eventType"].(string),
				ConnectionID: connId,
				Authorizer:   authorizer,
				DomainName:   test.WsRestApiDomain,
			},
		}
		resp, err := l.ApiEventHandler(ctx, e)
		assert.Nil(t, err, err)
		assert.Equal(t, event["statusCode"].(int), resp.StatusCode)
	}
}

func TestUnknownMessage(t *testing.T) {
	resp, err := l.ApiEventHandler(ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:  "unknown",
			Authorizer: authorizer,
			DomainName: test.WsRestApiDomain,
		},
	})
	assert.Nil(t, err, err)
	assert.Equal(t, 400, resp.StatusCode)
}

func TestInvalidMessageBody(t *testing.T) {
	e := events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: uuid.NewString(),
			Authorizer:   authorizer,
			DomainName:   test.WsRestApiDomain,
		},
		Body: "invalid json",
	}
	resp, err := l.ApiEventHandler(ctx, e)
	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
}

func TestLambdaProjectionHandler(t *testing.T) {
	pairingUuid := uuid.NewString()
	bodyMap := map[string]any{
		"detail-type": "hlpos.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId": pairingUuid,
			"payload": map[string]any{
				"siteUuid":    uuid.NewString(),
				"venueId":     "mockVenueId",
				"deviceUuid":  uuid.NewString(),
				"pairingUuid": "mockPairingUuid",
				"entityUuid":  "mockEntityUuid",
				"posProvider": types.HL,
				"timestamp":   "**********",
			},
			"version":          "1",
			"createdTimestamp": "**********",
		},
	}

	body, _ := json.Marshal(bodyMap)
	bodyString := string(body)

	e := events.SQSEvent{
		Records: []events.SQSMessage{
			{
				Body: bodyString,
			},
		},
	}
	resp, err := l.ProjectionHandler(ctx, e)
	assert.Nil(t, err, err)
	assert.Equal(t, events.SQSEventResponse{BatchItemFailures: []events.SQSBatchItemFailure{}}, resp)
	output, _ := dbClient.Query(common.PairingTableName()).Eq("id", pairingUuid).Begin("type", string(types.POSINTERFACE_PAIR_DEVICE)).Exec(ctx)
	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, pairingUuid, output.Data[0]["id"])
}

func TestWsAuthorizerHandler(t *testing.T) {
	_, err := l.WsAuthorizerHandler(ctx, events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization":          "eydadadadada",
			"Sec-WebSocket-Protocol": "eydadadadada",
		},
		MultiValueHeaders: map[string][]string{
			"Authorization": {"eydadadadada"},
		},
	})
	assert.NotNil(t, err)
}

func TestCronJobInvalidJobType(t *testing.T) {
	cronJobInput := middleware.CronJobInput{
		CronJobType: "Invalid Job Type",
	}
	_, err := l.CronJobHandler(ctx, cronJobInput)
	assert.NotNil(t, err)
}

func TestGetLogSafeApiEvent(t *testing.T) {
	event := events.APIGatewayWebsocketProxyRequest{
		Headers: map[string]string{
			"Authorization":          "eydadadadada",
			"Sec-WebSocket-Protocol": "eydadadadada",
		},
	}
	l.getLogSafeApiEvent(ctx, event)
	assert.Equal(t, "Bearer N/A", maskedBearer)
	assert.Equal(t, "Sec-WebSocket-Protocol N/A", maskedProtocolHeader)
}
