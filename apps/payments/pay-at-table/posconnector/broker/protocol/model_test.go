package protocol

import (
	"context"
	"testing"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestValidateRequestFailures(t *testing.T) {
	cases := []map[string]any{
		{
			"timestamp": uuid.NewString(),
			"action":    uuid.NewString(),
			"siteUuid":  uuid.NewString(),
		},
		{
			"eventId":   uuid.NewString(),
			"timestamp": uuid.NewString(),
			"action":    uuid.NewString(),
		},
		{
			"eventId":   uuid.NewString(),
			"timestamp": uuid.NewString(),
			"action":    uuid.NewString(),
		},
	}
	for _, c := range cases {
		r, e := NewPosConnectorRequest(context.Background(), &c)
		assert.Nil(t, e, e)
		assert.NotNil(t, r.validate(ctx))
	}
}

func TestNewOrder(t *testing.T) {
	cases := []map[string]any{
		{
			"orderId": uuid.NewString(),
		},
		{
			"siteUuid": uuid.NewString(),
		},
	}
	for _, c := range cases {
		o, e := NewOrder(context.Background(), &c)
		assert.NotNil(t, e, e)
		assert.Nil(t, o, o)
	}
}

func TestNewOrderWithItemsMissingFields(t *testing.T) {
	cases := []map[string]any{
		{
			"data": map[string]any{
				"orderId":  uuid.NewString(),
				"siteUuid": uuid.NewString(),
				"location": map[string]string{
					"locationId":   uuid.NewString(),
					"locationName": uuid.NewString(),
				},
				"items": []map[string]any{
					{
						"itemId":      uuid.NewString(),
						"totalAmount": 3,
						"amount":      1,
					},
				},
			},
			"missingField": "Quantity",
		},
		{
			"data": map[string]any{
				"orderId":  uuid.NewString(),
				"siteUuid": uuid.NewString(),
				"location": map[string]string{
					"locationId":   uuid.NewString(),
					"locationName": uuid.NewString(),
				},
				"items": []map[string]any{
					{
						"itemId":      uuid.NewString(),
						"totalAmount": 2,
						"quantity":    1,
					},
				},
			},
			"missingField": "Amount",
		},
	}
	for _, c := range cases {
		d := c["data"].(map[string]any)
		o, e := NewOrder(context.Background(), &d)
		assert.NotNil(t, e, e)
		assert.Nil(t, o, o)
		errs := e.(validator.ValidationErrors)
		assert.Equal(t, 1, len(errs))
		assert.Equal(t, errs[0].Field(), c["missingField"].(string))
	}
}

func TestNewOrderWithItem(t *testing.T) {
	d := map[string]any{
		"orderId":  uuid.NewString(),
		"siteUuid": uuid.NewString(),
		"location": map[string]string{
			"locationId":   uuid.NewString(),
			"locationName": uuid.NewString(),
		},
		"items": []map[string]any{
			{
				"itemId":      uuid.NewString(),
				"totalAmount": 10,
				"amount":      1,
				"quantity":    float64(10),
				"description": uuid.NewString(),
			},
		},
	}
	o, e := NewOrder(context.Background(), &d)
	assert.Nil(t, e, e)
	assert.Equal(t, d["orderId"].(string), *o.OrderId)
	assert.Equal(t, d["siteUuid"].(string), *o.SiteUuid)
	assert.Equal(t, d["location"].(map[string]string)["locationId"], *o.Location.LocationId)
	assert.Equal(t, d["location"].(map[string]string)["locationName"], *o.Location.LocationName)
	assert.Equal(t, d["items"].([]map[string]any)[0]["itemId"], *o.Items[0].ItemId)
	assert.Equal(t, d["items"].([]map[string]any)[0]["amount"], *o.Items[0].Amount)
	assert.Equal(t, d["items"].([]map[string]any)[0]["quantity"], *o.Items[0].Quantity)
	assert.Equal(t, d["items"].([]map[string]any)[0]["totalAmount"], *o.Items[0].TotalAmount)
	assert.Equal(t, d["items"].([]map[string]any)[0]["description"], *o.Items[0].Description)
}

func TestNewOrderWithRelatedItem(t *testing.T) {
	d := map[string]any{
		"orderId":  uuid.NewString(),
		"siteUuid": uuid.NewString(),
		"location": map[string]string{
			"locationId":   uuid.NewString(),
			"locationName": uuid.NewString(),
		},
		"items": []map[string]any{
			{
				"itemId":      uuid.NewString(),
				"totalAmount": 10,
				"amount":      1,
				"quantity":    float64(10),
				"description": uuid.NewString(),
				"relatedItems": []map[string]any{
					{
						"itemId":      uuid.NewString(),
						"totalAmount": 10,
						"amount":      1,
						"quantity":    float64(10),
						"description": uuid.NewString(),
					},
				},
			},
		},
	}
	o, e := NewOrder(context.Background(), &d)
	assert.Nil(t, e, e)
	assert.Equal(t, d["orderId"].(string), *o.OrderId)
	assert.Equal(t, d["siteUuid"].(string), *o.SiteUuid)
	assert.Equal(t, d["location"].(map[string]string)["locationId"], *o.Location.LocationId)
	assert.Equal(t, d["location"].(map[string]string)["locationName"], *o.Location.LocationName)
	assert.Equal(t, d["items"].([]map[string]any)[0]["itemId"], *o.Items[0].ItemId)
	assert.Equal(t, d["items"].([]map[string]any)[0]["amount"], *o.Items[0].Amount)
	assert.Equal(t, d["items"].([]map[string]any)[0]["quantity"], *o.Items[0].Quantity)
	assert.Equal(t, d["items"].([]map[string]any)[0]["totalAmount"], *o.Items[0].TotalAmount)
	assert.Equal(t, d["items"].([]map[string]any)[0]["description"], *o.Items[0].Description)
	relatedItems := d["items"].([]map[string]any)[0]["relatedItems"]
	assert.Equal(t, relatedItems.([]map[string]any)[0]["itemId"], *o.Items[0].RelatedItems[0].ItemId)
	assert.Equal(t, relatedItems.([]map[string]any)[0]["amount"], *o.Items[0].RelatedItems[0].Amount)
	assert.Equal(t, relatedItems.([]map[string]any)[0]["description"], *o.Items[0].RelatedItems[0].Description)
	assert.Equal(t, relatedItems.([]map[string]any)[0]["quantity"], *o.Items[0].RelatedItems[0].Quantity)
	assert.Equal(t, relatedItems.([]map[string]any)[0]["totalAmount"], *o.Items[0].RelatedItems[0].TotalAmount)
}

func TestNewOrderWithoutItem(t *testing.T) {
	d := map[string]any{
		"orderId":      uuid.NewString(),
		"siteUuid":     uuid.NewString(),
		"tableNumber":  uuid.NewString(),
		"tableName":    uuid.NewString(),
		"subTableName": uuid.NewString(),
		"totalAmount":  100,
		"owedAmount":   10,
		"location": map[string]string{
			"locationId":   uuid.NewString(),
			"locationName": uuid.NewString(),
		},
	}
	o, e := NewOrder(context.Background(), &d)
	assert.Nil(t, e, e)
	assert.Equal(t, d["orderId"].(string), *o.OrderId)
	assert.Equal(t, d["siteUuid"].(string), *o.SiteUuid)
	assert.Equal(t, d["tableNumber"].(string), *o.TableNumber)
	assert.Equal(t, d["tableName"].(string), *o.TableName)
	assert.Equal(t, d["subTableName"].(string), *o.SubTableName)
	assert.Equal(t, d["location"].(map[string]string)["locationId"], *o.Location.LocationId)
	assert.Equal(t, d["location"].(map[string]string)["locationName"], *o.Location.LocationName)
	assert.Equal(t, d["totalAmount"].(int), *o.TotalAmount)
	assert.Equal(t, d["owedAmount"].(int), *o.OwedAmount)
}

func TestNewPosConnectorRequestWithInvalidType(t *testing.T) {
	req, err := NewPosConnectorRequest(context.Background(), &map[string]any{"eventId": 1})
	assert.Nil(t, req, req)
	assert.NotNil(t, err)
}

func TestPosConnectorRequestByte(t *testing.T) {
	req, err := NewPosConnectorRequest(context.Background(), &map[string]any{"eventId": "1"})
	assert.Nil(t, err)
	assert.NotNil(t, req, req)
	assert.Equal(t, req.EventId, "1")

	bReq := req.Bytes()
	assert.NotNil(t, bReq)
	assert.Greater(t, len(bReq), 0)
}

func TestNewPayment(t *testing.T) {
	data := map[string]any{
		"orderId":         uuid.NewString(),
		"locationId":      uuid.NewString(),
		"amount":          100,
		"transactionTime": getServerTimestamp(),
		"tip":             10,
		"surcharge":       1,
		"serviceCharge":   3,
		"currencyCode":    "AUD",
		"maskedPan":       uuid.NewString(),
		"merchantId":      uuid.NewString(),
		"tid":             uuid.NewString(),
		"rrn":             uuid.NewString(),
		"stan":            uuid.NewString(),
		"authCode":        uuid.NewString(),
		"accountType":     "CREDIT",
		"transactionUuid": uuid.NewString(),
	}
	p, err := NewPayment(context.Background(), &data)
	assert.Nil(t, err, err)
	assert.NotNil(t, p)

	delete(data, "orderId")
	p, err = NewPayment(context.Background(), &data)
	assert.NotNil(t, err, err)
	assert.Nil(t, p)

	data["amount"] = "10"
	p, err = NewPayment(context.Background(), &data)
	assert.NotNil(t, err, err)
	assert.Nil(t, p)
}

func TestNewPaymentAck(t *testing.T) {
	d := map[string]any{
		"orderId":         uuid.NewString(),
		"locationId":      uuid.NewString(),
		"amount":          100,
		"transactionTime": getServerTimestamp(),
		"tip":             10,
		"surcharge":       1,
		"serviceCharge":   3,
		"currencyCode":    "AUD",
		"maskedPan":       uuid.NewString(),
		"merchantId":      uuid.NewString(),
		"tid":             uuid.NewString(),
		"rrn":             uuid.NewString(),
		"stan":            uuid.NewString(),
		"authCode":        uuid.NewString(),
		"accountType":     "CREDIT",
		"transactionUuid": uuid.NewString(),
	}
	ack := NewPaymentAck(context.Background(), &d)
	assert.Equal(t, d["orderId"].(string), *ack.OrderId)
	assert.Equal(t, "ACKNOWLEDGED", *ack.Status)
}

func TestNewPaymentAckFailed(t *testing.T) {
	defer func() {
		e := recover()
		assert.NotNil(t, e)
	}()
	d := map[string]any{
		"locationId": uuid.NewString(),
	}
	NewPaymentAck(context.Background(), &d)
	assert.Fail(t, "should have panic")
}

func createTransactionResponse(transactionType string) map[string]any {
	return map[string]any{
		"sessionUuid":       uuid.NewString(),
		"transactionUuid":   uuid.NewString(),
		"type":              transactionType,
		"status":            "APPROVED",
		"amount":            1000,
		"tipAmount":         0,
		"surchargeAmount":   0,
		"responseCode":      uuid.NewString(),
		"responseText":      uuid.NewString(),
		"rrn":               uuid.NewString(),
		"approvalCode":      uuid.NewString(),
		"scheme":            "VISA",
		"panMasked":         uuid.NewString(),
		"caid":              uuid.NewString(),
		"catid":             uuid.NewString(),
		"isoProcessingCode": uuid.NewString(),
		"cardholderUuid":    uuid.NewString(),
		"cardExpiryDate":    uuid.NewString(),
		"cardMedia":         "MANUAL",
		"cvm":               "NO_CVM",
		"receiptData": map[string]any{
			"cardType": uuid.NewString(),
			"aid":      uuid.NewString(),
		},
	}
}

func TestNewPurchaseBase(t *testing.T) {
	data := createTransactionResponse("PURCHASE")
	p, err := NewTransactionBase(context.Background(), &data)
	assert.Nil(t, err, err)
	assert.NotNil(t, p)

	// Validation failure
	data["type"] = 1
	p, err = NewTransactionBase(context.Background(), &data)
	assert.NotNil(t, err, err)
	assert.Nil(t, p)

	// Parsing failure
	data = map[string]any{
		"sessionUuid": true,
	}
	p, err = NewTransactionBase(context.Background(), &data)
	assert.NotNil(t, err, err)
	assert.Nil(t, p)
}

func TestNewTransactionResponse(t *testing.T) {
	data := createTransactionResponse("PURCHASE")
	p, err := NewTransactionResponse(context.Background(), &data)
	assert.Nil(t, err, err)
	assert.NotNil(t, p)

	data["tipAmount"] = "10"
	p, err = NewTransactionResponse(context.Background(), &data)
	assert.NotNil(t, err, err)
	assert.Nil(t, p)
}

func TestNewTransactionResponseNoResponseText(t *testing.T) {
	cases := []struct {
		status               string
		expectedResponseText string
	}{
		{
			status:               "FAILED",
			expectedResponseText: DefaultDeclinedResponseText,
		},
		{
			status:               "DECLINED",
			expectedResponseText: DefaultDeclinedResponseText,
		},
		{
			status:               "SUCCESS",
			expectedResponseText: DefaultApprovedResponseText,
		},
		{
			status:               "APPROVED",
			expectedResponseText: DefaultApprovedResponseText,
		},
		{
			status:               "UNKNOWN",
			expectedResponseText: DefaultUnknownResponseText,
		},
	}
	for _, testCase := range cases {
		t.Run(testCase.status, func(t *testing.T) {
			data := createTransactionResponse("PURCHASE")
			data["responseText"] = ""
			data["status"] = testCase.status
			p, err := NewTransactionResponse(context.Background(), &data)
			assert.Nil(t, err, err)
			assert.NotNil(t, p)
			assert.Equal(t, p.ResponseText, testCase.expectedResponseText)
		})
	}
}

func TestNewPurchaseLifecycleEvent(t *testing.T) {
	data := map[string]any{
		"sessionUuid":     uuid.NewString(),
		"transactionUuid": uuid.NewString(),
		"type":            "PURCHASE",
		"status":          "PROCESSING",
		"terminalEvent":   uuid.NewString(),
	}
	p, err := NewTransactionLifecycleEvent(context.Background(), &data)
	assert.Nil(t, err, err)
	assert.NotNil(t, p)

	// Validation failure
	data["type"] = 1
	p, err = NewTransactionLifecycleEvent(context.Background(), &data)
	assert.NotNil(t, err, err)
	assert.Nil(t, p)

	// Parsing failure
	data = map[string]any{
		"sessionUuid": true,
	}
	p, err = NewTransactionLifecycleEvent(context.Background(), &data)
	assert.NotNil(t, err, err)
	assert.Nil(t, p)
}

func TestNewUpdateOrderItem(t *testing.T) {
	d := map[string]any{
		"orderId":    "100",
		"reference":  uuid.NewString(),
		"deviceTime": getServerTimestamp(),
		"item": map[string]any{
			"itemId":      uuid.NewString(),
			"description": uuid.NewString(),
			"quantity":    1,
			"amount":      10,
		},
	}
	uoi, _ := NewUpdateOrderItem(context.Background(), &d)
	assert.Equal(t, d["orderId"].(string), *uoi.OrderId)
	assert.Equal(t, d["reference"].(string), *uoi.Reference)
	assert.Equal(t, d["deviceTime"].(string), *uoi.DeviceTime)
	assert.Equal(t, d["item"].(map[string]any)["itemId"], *uoi.Item.ItemId)
	assert.Equal(t, d["item"].(map[string]any)["description"], *uoi.Item.Description)
	assert.Equal(t, d["item"].(map[string]any)["quantity"], *uoi.Item.Quantity)
	assert.Equal(t, d["item"].(map[string]any)["amount"], *uoi.Item.Amount)

	delete(d, "orderId")
	p, err := NewUpdateOrderItem(context.Background(), &d)
	assert.NotNil(t, err, err)
	assert.Nil(t, p)

	d["amount"] = "10"
	p, err = NewUpdateOrderItem(context.Background(), &d)
	assert.NotNil(t, err, err)
	assert.Nil(t, p)
}

func TestNewUpdateOrderItemError(t *testing.T) {
	d := map[string]any{
		"orderId": 100,
	}
	_, err := NewUpdateOrderItem(context.Background(), &d)
	assert.NotNil(t, err)
}

func TestNewPurchaseRefundRequest(t *testing.T) {
	data := map[string]any{
		"sessionUuid":         uuid.NewString(),
		"deviceUuid":          uuid.NewString(),
		"transactionUuid":     uuid.NewString(),
		"type":                uuid.NewString(),
		"amount":              1000,
		"tipAmount":           1000,
		"transactionCurrency": uuid.NewString(),
		"timestamp":           uuid.NewString(),
	}
	pr, err := NewPurchaseRequest(context.Background(), &data)
	assert.Nil(t, pr)
	assert.NotNil(t, err)

	data["externalReference"] = uuid.NewString()
	pr, err = NewPurchaseRequest(context.Background(), &data)
	assert.Nil(t, err, err)
	assert.NotNil(t, pr)
	data["originalTransactionUuid"] = uuid.NewString()
	data["originalIsoProcessingCode"] = uuid.NewString()
	rr, err := NewRefundRequest(context.Background(), &data)

	assert.Nil(t, err, err)
	assert.NotNil(t, rr)
}
