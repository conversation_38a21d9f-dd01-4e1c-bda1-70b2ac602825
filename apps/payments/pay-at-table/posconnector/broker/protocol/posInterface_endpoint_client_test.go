package protocol

import (
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func TestPosInterfaceGetOrder(t *testing.T) {
	oid := uuid.NewString()
	tn := uuid.NewString()
	order := DeviceOrder{
		OrderId:     &oid,
		TableNumber: &tn,
	}
	serverResp := PosInterfaceResponse{
		Action:    string(GET_ORDER),
		Timestamp: uuid.NewString(),
		EventId:   uuid.NewString(),
		Data:      order,
	}
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		b, _ := json.Marshal(serverResp)
		res.Write(b)
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	req := &PosConnectorRequest{
		Data: map[string]any{"orderId": uuid.NewString()},
	}
	resp := posIf.GetOrder(ctx, req)
	assert.Equal(t, order, *resp)
}

func TestPosInterfaceCheckOrderBalances(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.WriteHeader(http.StatusOK)
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	req := &PosConnectorRequest{
		DeviceUuid: uuid.NewString(),
		SiteUuid:   uuid.NewString(),
		Data:       map[string]any{"orderId": uuid.NewString()},
	}
	posIf.CheckOrdersBalances(ctx, req)
}

func TestPosInterfaceGetOrderWithDecimalItems(t *testing.T) {
	oid := uuid.NewString()
	tn := uuid.NewString()
	order := DeviceOrder{
		OrderId:     &oid,
		TableNumber: &tn,
		Items: []Item{
			{
				ItemId:      ptr.String("1234"),
				Amount:      ptr.Int(1990),
				TotalAmount: ptr.Int(995),
				Quantity:    ptr.Float64(0.25),
			},
		},
	}
	serverResp := PosInterfaceResponse{
		Action:    string(GET_ORDER),
		Timestamp: uuid.NewString(),
		EventId:   uuid.NewString(),
		Data:      order,
	}
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		b, _ := json.Marshal(serverResp)
		res.Write(b)
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	req := &PosConnectorRequest{
		Data: map[string]any{"orderId": uuid.NewString()},
	}
	resp := posIf.GetOrder(ctx, req)
	assert.Equal(t, order, *resp)
	assert.Equal(t, *resp.Items[0].Quantity, 0.25)
}

func TestGetOrderWithoutOrderId(t *testing.T) {
	defer func() {
		err := recover()
		assert.NotNil(t, err, err)
	}()
	posIf := NewPosInterfaceEndpointClient(ctx, "")
	assert.NotNil(t, posIf)
	req := &PosConnectorRequest{}
	posIf.GetOrder(ctx, req)
	assert.Fail(t, "should have panicked")
}

func TestGetOrderWithEmptyOrderId(t *testing.T) {
	posIf := NewPosInterfaceEndpointClient(ctx, "")
	assert.NotNil(t, posIf)
	req := &PosConnectorRequest{Data: map[string]any{"orderId": ""}}
	order := posIf.GetOrder(ctx, req)
	assert.Nil(t, order)
}

func TestGetOrderWithInvalidResponse(t *testing.T) {
	defer func() {
		err := recover()
		assert.NotNil(t, err, err)
	}()
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.Write([]byte(""))
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	req := &PosConnectorRequest{
		Data: map[string]any{"orderId": uuid.NewString()},
	}
	posIf.GetOrder(ctx, req)
	assert.Fail(t, "should have panic")
}

func TestGetOrderWithInvalidResponseValueType(t *testing.T) {
	defer func() {
		err := recover()
		assert.NotNil(t, err, err)
	}()
	order := map[string]any{
		"orderId": 100,
	}
	serverResp := PosInterfaceResponse{
		Action:    string(GET_ORDER),
		Timestamp: uuid.NewString(),
		EventId:   uuid.NewString(),
		Data:      order,
	}
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		b, _ := json.Marshal(serverResp)
		res.Write(b)
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	req := &PosConnectorRequest{
		Data: map[string]any{"orderId": uuid.NewString()},
	}
	posIf.GetOrder(ctx, req)
	assert.Fail(t, "should have panic")
}

func TestGetOrderWithErrorResponse(t *testing.T) {
	defer func() {
		err := recover()
		assert.NotNil(t, err, err)
	}()
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.WriteHeader(http.StatusInternalServerError)
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	req := &PosConnectorRequest{
		Data: map[string]any{"orderId": uuid.NewString()},
	}
	posIf.GetOrder(ctx, req)
	assert.Fail(t, "should have panic")
}

func TestGetOrderWithHTTPErrorResponse(t *testing.T) {
	defer func() {
		err := recover()
		assert.NotNil(t, err, err)
	}()
	malformedUrl := "http://malformed`Url"
	posIf := NewPosInterfaceEndpointClient(ctx, malformedUrl)
	req := &PosConnectorRequest{
		Data: map[string]any{"orderId": uuid.NewString()},
	}
	posIf.GetOrder(ctx, req)
	assert.Fail(t, "should have panic")
}

func TestPosInterfaceGetOrders(t *testing.T) {
	oid := uuid.NewString()
	tn := uuid.NewString()
	order := DeviceOrder{
		OrderId:     &oid,
		TableNumber: &tn,
	}
	serverResp := PosInterfaceResponse{
		Action:    string(GET_ORDERS),
		Timestamp: uuid.NewString(),
		EventId:   uuid.NewString(),
		Data:      []DeviceOrder{order},
	}

	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		b, _ := json.Marshal(serverResp)
		res.Write(b)
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)

	resp := posIf.GetOrders(ctx, &PosConnectorRequest{})
	assert.Equal(t, []DeviceOrder{order}, resp)
}

func TestGetOrdersWithInvalidResponse(t *testing.T) {
	defer func() {
		err := recover()
		assert.NotNil(t, err, err)
	}()
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.Write([]byte(""))
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	req := &PosConnectorRequest{}
	posIf.GetOrders(ctx, req)
	assert.Fail(t, "should have panic")
}

func TestGetOrdersWithErrorResponse(t *testing.T) {
	defer func() {
		err := recover()
		assert.NotNil(t, err, err)
	}()
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.WriteHeader(http.StatusInternalServerError)
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	req := &PosConnectorRequest{}
	posIf.GetOrders(ctx, req)
	assert.Fail(t, "should have panic")
}

func TestGetOrdersWithHTTPErrorResponse(t *testing.T) {
	defer func() {
		err := recover()
		assert.NotNil(t, err, err)
	}()
	malformedUrl := "http://malformed`Url"
	posIf := NewPosInterfaceEndpointClient(ctx, malformedUrl)
	req := &PosConnectorRequest{}
	posIf.GetOrders(ctx, req)
	assert.Fail(t, "should have panic")
}

func TestPosInterfaceGetPosConnectionStatus(t *testing.T) {
	posInterface := string(types.HL)
	status := string(connection.CONNECTED)
	posConnectionStatus := PosConnectionStatus{
		PosInterface: &posInterface,
		Status:       &status,
	}
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		b, _ := json.Marshal(events.APIGatewayProxyResponse{StatusCode: 200})
		res.Write(b)
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)

	resp := posIf.GetPosConnectionStatus(ctx, &PosConnectorRequest{Provider: string(types.HL)})
	assert.Equal(t, posConnectionStatus, *resp)
}

func TestGetPosConnectionStatusWithErrorResponse(t *testing.T) {
	defer func() {
		err := recover()
		assert.Nil(t, err, err)
	}()
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.WriteHeader(http.StatusInternalServerError)
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	req := &PosConnectorRequest{
		Provider: string(types.HL),
	}
	result := posIf.GetPosConnectionStatus(ctx, req)
	disconnected := string(connection.DISCONNECTED)
	assert.Equal(t, &PosConnectionStatus{PosInterface: (*string)(&types.HL), Status: &disconnected}, result)
}

func TestGetPosConnectionStatusWithHTTPErrorResponse(t *testing.T) {
	defer func() {
		err := recover()
		logger.Error(ctx, err)
		assert.Nil(t, err, err)
	}()
	malformedUrl := "http://malformed`Url"
	posIf := NewPosInterfaceEndpointClient(ctx, malformedUrl)
	req := &PosConnectorRequest{
		Provider: string(types.HL),
	}
	result := posIf.GetPosConnectionStatus(ctx, req)
	disconnected := string(connection.DISCONNECTED)
	assert.Equal(t, &PosConnectionStatus{PosInterface: (*string)(&types.HL), Status: &disconnected}, result)
}

func TestBuildPosConnectorErrorResponse(t *testing.T) {
	posIf := PosInterfaceEndpointClient{}
	cases := []map[string]any{
		{
			"response": http.Response{
				StatusCode: 200,
				Body:       io.NopCloser(strings.NewReader("")),
			},
			"errorResponse": nil,
		},
		{
			"response": http.Response{
				StatusCode: 400,
				Body:       io.NopCloser(strings.NewReader("")),
			},
			"errorResponse": &PosConnectorError{
				Type: INVALID_REQUEST,
			},
		},
		{
			"response": http.Response{
				StatusCode: 500,
				Body:       io.NopCloser(strings.NewReader("")),
			},
			"errorResponse": &PosConnectorError{
				Type: SERVER_ERROR,
			},
		},
		{
			"response": http.Response{
				StatusCode: 408,
				Body:       io.NopCloser(strings.NewReader("")),
			},
			"errorResponse": &PosConnectorError{
				Type: REQUEST_TIMEOUT,
			},
		},
	}
	for i, c := range cases {
		resp := c["response"].(http.Response)
		r := posIf.buildPosConnectorErrorResponse(ctx, &resp)
		if i == 0 {
			assert.Nil(t, r, r)
		} else {
			assert.Equal(t, c["errorResponse"].(*PosConnectorError), r)
		}
	}
}

func TestPaymentWrongInput(t *testing.T) {
	defer func() {
		recover()
	}()
	posIf := PosInterfaceEndpointClient{}
	posIf.Payment(ctx, &PosConnectorRequest{})
	assert.Fail(t, "should have panic")
}

func TestPosInterfacePayment(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.Write([]byte(""))
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	resp, err := posIf.Payment(ctx, newPaymentRequest())
	assert.Nil(t, err, err)
	assert.NotNil(t, resp)
}

func TestPosInterfacePaymentTimeout(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.WriteHeader(http.StatusBadGateway)
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	resp, err := posIf.Payment(ctx, newPaymentRequest())
	assert.NotNil(t, err, err)
	assert.NotNil(t, resp)
	assert.Equal(t, err, &PosConnectorError{
		Type:    SERVER_ERROR,
		Message: "",
	})
	assert.Equal(t, resp, &PaymentResponse{Status: ptr.String(string(FAIL))})
}

func newPaymentRequest() *PosConnectorRequest {
	return &PosConnectorRequest{
		Data: map[string]any{
			"orderId":         uuid.NewString(),
			"locationId":      uuid.NewString(),
			"amount":          10,
			"transactionTime": uuid.NewString(),
			"transactionUuid": uuid.NewString(),
		},
	}
}
func TestPosInterfacePurchase(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.Write([]byte(""))
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	err := posIf.Purchase(ctx, newPurchaseResponse(), true)
	assert.Nil(t, err, err)
}

func TestPosInterfacePurchaseResponseText(t *testing.T) {
	cases := []struct {
		status               string
		expectedResponseText string
	}{
		{
			status:               "FAILED",
			expectedResponseText: DefaultDeclinedResponseText,
		},
		{
			status:               "DECLINED",
			expectedResponseText: DefaultDeclinedResponseText,
		},
		{
			status:               "SUCCESS",
			expectedResponseText: DefaultApprovedResponseText,
		},
		{
			status:               "APPROVED",
			expectedResponseText: DefaultApprovedResponseText,
		},
		{
			status:               "UNKNOWN",
			expectedResponseText: DefaultUnknownResponseText,
		},
	}
	for _, testCase := range cases {
		t.Run(testCase.status, func(t *testing.T) {
			resChan := make(chan string, 1)
			testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
				bytes, _ := io.ReadAll(req.Body)
				defer req.Body.Close()
				var response map[string]any
				json.Unmarshal(bytes, &response)

				resChan <- response["data"].(map[string]any)["responseText"].(string)
				close(resChan)
				res.Write([]byte(""))
			}))
			defer testServer.Close()
			posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
			pr := newPurchaseResponse()
			pr.Data.(map[string]any)["status"] = testCase.status
			pr.Data.(map[string]any)["responseText"] = ""
			err := posIf.Purchase(ctx, pr, true)
			assert.Nil(t, err, err)
			responseText := <-resChan
			assert.Equal(t, responseText, testCase.expectedResponseText)
		})
	}
}

func TestPosInterfacePurchaseLifecycleEvent(t *testing.T) {
	receivedBody := make(chan string, 1)
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		body, _ := io.ReadAll(req.Body)
		receivedBody <- string(body)
		res.Write([]byte(""))
	}))
	defer testServer.Close()

	deviceUuid := uuid.NewString()
	testCases := map[string]map[string]any{
		"STATE": {
			"sessionUuid": uuid.NewString(),
			"state":       string(ACKNOWLEDGED),
		},
		"STATUS": {
			"sessionUuid":     uuid.NewString(),
			"status":          string(ACKNOWLEDGED),
			"transactionUuid": uuid.NewString(),
			"type":            string(types.PURCHASE),
		},
	}

	for stateStatus, data := range testCases {
		t.Run(stateStatus, func(t *testing.T) {
			posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
			req := &PosConnectorRequest{
				EventId:    uuid.NewString(),
				DeviceUuid: deviceUuid,
				Action:     string(PURCHASE),
				Timestamp:  time.Now().Format(time.RFC3339),
				Data:       data,
			}
			err := posIf.Purchase(ctx, req, false)
			assert.Nil(t, err, err)

			postedBody := <-receivedBody

			postedBodyMap := map[string]any{}
			json.Unmarshal([]byte(postedBody), &postedBodyMap)

			assert.Equal(t, req.EventId, postedBodyMap["eventId"])
			assert.Equal(t, req.Action, postedBodyMap["action"])
			assert.Equal(t, req.Timestamp, postedBodyMap["timestamp"])
			assert.Equal(t, data["sessionUuid"], postedBodyMap["data"].(map[string]any)["sessionUuid"])
			assert.Equal(t, deviceUuid, postedBodyMap["data"].(map[string]any)["deviceUuid"])
			if stateStatus == "STATUS" {
				assert.Equal(t, data["transactionUuid"], postedBodyMap["data"].(map[string]any)["transactionUuid"])
				assert.Equal(t, data["type"], postedBodyMap["data"].(map[string]any)["type"])
				assert.Equal(t, data["status"], postedBodyMap["data"].(map[string]any)["status"])
			} else {
				assert.Equal(t, data["state"], postedBodyMap["data"].(map[string]any)["state"])
			}
		})
	}
}

func TestPurchaseResponseWrongInput(t *testing.T) {
	defer func() {
		recover()
	}()
	posIf := PosInterfaceEndpointClient{}
	posIf.Purchase(ctx, &PosConnectorRequest{}, true)
	assert.Fail(t, "should have panic")
}

func TestPosInterfacePurchaseError(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		panic("Panic")
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	err := posIf.Purchase(ctx, newPurchaseResponse(), true)
	assert.NotNil(t, err, err)
	assert.Equal(t, SERVER_ERROR, err.Type)
}

func TestPosInterfacePaymentError(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		panic("Panic")
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	resp, err := posIf.Payment(ctx, newPaymentRequest())
	assert.NotNil(t, resp)
	assert.NotNil(t, err, err)
	assert.Equal(t, SERVER_ERROR, err.Type)
}

func TestPurchaseLifecycleEventWrongInput(t *testing.T) {
	defer func() {
		recover()
	}()
	posIf := PosInterfaceEndpointClient{}
	posIf.Purchase(ctx, &PosConnectorRequest{
		Data: map[string]any{
			"sessionUuid":     uuid.NewString(),
			"transactionUuid": uuid.NewString(),
			"status":          "APPROVED",
			"type":            "PURCHASE",
			"terminalEvent":   false,
		},
	}, false)
	assert.Fail(t, "should have panic")
}

func TestPosInterfaceRefund(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.Write([]byte(""))
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	err := posIf.Refund(ctx, newPurchaseResponse(), true)
	assert.Nil(t, err, err)
}

func TestPosInterfaceRefundError(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		panic("Panic")
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	err := posIf.Refund(ctx, newPurchaseResponse(), true)
	assert.NotNil(t, err, err)
	assert.Equal(t, SERVER_ERROR, err.Type)
}

func TestRefundWrongInput(t *testing.T) {
	defer func() {
		recover()
	}()
	posIf := PosInterfaceEndpointClient{}
	posIf.Refund(ctx, &PosConnectorRequest{}, true)
	assert.Fail(t, "should have panic")
}

func TestPosInterfaceRefundLifecycleEvent(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.Write([]byte(""))
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	err := posIf.Refund(ctx, newPurchaseResponse(), false)
	assert.Nil(t, err, err)
}

func TestRefundLifecycleEventWrongInput(t *testing.T) {
	defer func() {
		recover()
	}()
	posIf := PosInterfaceEndpointClient{}
	posIf.Refund(ctx, &PosConnectorRequest{
		Data: map[string]any{
			"sessionUuid":     uuid.NewString(),
			"transactionUuid": uuid.NewString(),
			"status":          "APPROVED",
			"type":            "REFUND",
			"terminalEvent":   false,
		},
	}, false)
	assert.Fail(t, "should have panic")
}

func TestPosInterfaceReversal(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.Write([]byte(""))
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	err := posIf.Reversal(ctx, newPurchaseResponse(), true)
	assert.Nil(t, err, err)
}

func TestPosInterfaceReversalError(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		panic("Panic")
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	err := posIf.Reversal(ctx, newPurchaseResponse(), true)
	assert.NotNil(t, err, err)
	assert.Equal(t, SERVER_ERROR, err.Type)
}

func TestReversalWrongInput(t *testing.T) {
	defer func() {
		recover()
	}()
	posIf := PosInterfaceEndpointClient{}
	posIf.Reversal(ctx, &PosConnectorRequest{}, true)
	assert.Fail(t, "should have panic")
}

func TestPosInterfaceReversalLifecycleEvent(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.Write([]byte(""))
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	err := posIf.Reversal(ctx, newPurchaseResponse(), false)
	assert.Nil(t, err, err)
}

func TestReversalLifecycleEventWrongInput(t *testing.T) {
	defer func() {
		recover()
	}()
	posIf := PosInterfaceEndpointClient{}
	posIf.Reversal(ctx, &PosConnectorRequest{
		Data: map[string]any{
			"sessionUuid":     uuid.NewString(),
			"transactionUuid": uuid.NewString(),
			"status":          "APPROVED",
			"type":            "REVERSAL",
			"terminalEvent":   false,
		},
	}, false)
	assert.Fail(t, "should have panic")
}

func newPurchaseResponse() *PosConnectorRequest {
	return &PosConnectorRequest{
		DeviceUuid: uuid.NewString(),
		Data: map[string]any{
			"sessionUuid":       uuid.NewString(),
			"transactionUuid":   uuid.NewString(),
			"status":            "APPROVED",
			"type":              "PURCHASE",
			"amount":            10,
			"responseCode":      uuid.NewString(),
			"responseText":      uuid.NewString(),
			"rrn":               "",
			"scheme":            "VISA",
			"panMasked":         uuid.NewString(),
			"caid":              uuid.NewString(),
			"catid":             uuid.NewString(),
			"isoProcessingCode": uuid.NewString(),
			"cardholderUuid":    uuid.NewString(),
			"cardExpiryDate":    uuid.NewString(),
			"cardMedia":         "MANUAL",
			"cvm":               "NO_CVM",
			"cardType":          uuid.NewString(),
			"aid":               uuid.NewString(),
			"receiptData": map[string]any{
				"cardType": uuid.NewString(),
				"aid":      uuid.NewString(),
			},
		},
	}
}

func TestPosInterfaceUpdateOrderItem(t *testing.T) {
	oid := uuid.NewString()
	tn := uuid.NewString()
	order := DeviceOrder{
		OrderId:     &oid,
		TableNumber: &tn,
	}
	serverResp := PosInterfaceResponse{
		Action:    string(UPDATE_ORDER_ITEM),
		Timestamp: uuid.NewString(),
		EventId:   uuid.NewString(),
		Data:      order,
	}
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		b, _ := json.Marshal(serverResp)
		res.Write(b)
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	req := newUpdateOrderItemRequest()
	resp := posIf.UpdateOrderItem(ctx, req)
	assert.Equal(t, order, *resp)
}

func TestPosInterfaceUpdateOrderItemErrorResponse(t *testing.T) {
	defer func() {
		err := recover()
		assert.NotNil(t, err, err)
	}()
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.WriteHeader(http.StatusInternalServerError)
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	req := newUpdateOrderItemRequest()
	posIf.UpdateOrderItem(ctx, req)
	assert.Fail(t, "should have panic")
}

func TestPosInterfaceUpdateOrderItemInvalidResponse(t *testing.T) {
	defer func() {
		err := recover()
		assert.NotNil(t, err, err)
	}()
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		res.Write([]byte(""))
	}))
	defer testServer.Close()
	posIf := NewPosInterfaceEndpointClient(ctx, testServer.URL)
	req := newUpdateOrderItemRequest()
	posIf.UpdateOrderItem(ctx, req)
	assert.Fail(t, "should have panic")
}

func newUpdateOrderItemRequest() *PosConnectorRequest {
	return &PosConnectorRequest{
		Data: map[string]any{
			"orderId":    "100",
			"reference":  uuid.NewString(),
			"deviceTime": uuid.NewString(),
			"item": map[string]any{
				"itemId":      uuid.NewString(),
				"description": uuid.NewString(),
				"amount":      10,
				"quantity":    1,
			},
		},
	}
}

func TestPosInterfaceUpdateOrderItemNoOrderId(t *testing.T) {
	posIf := NewPosInterfaceEndpointClient(ctx, "")
	assert.NotNil(t, posIf)
	req := &PosConnectorRequest{Data: map[string]any{"orderId": ""}}
	res := posIf.UpdateOrderItem(ctx, req)
	assert.Nil(t, res)
}

func TestPosInterfaceEndpointSubOrdersUpdate(t *testing.T) {
	posIf := NewPosInterfaceEndpointClient(ctx, "")
	assert.NotNil(t, posIf)
	res := posIf.SubscribeOrdersUpdate(ctx, &PosConnectorRequest{})
	assert.Nil(t, res)
}

func TestPosInterfaceEndpointGetPairedDevices(t *testing.T) {
	posIf := NewPosInterfaceEndpointClient(ctx, "")
	assert.NotNil(t, posIf)
	res := posIf.GetPairedDevices(ctx, &PosConnectorRequest{})
	assert.Nil(t, res)
}

func TestPosInterfaceEndpointCancel(t *testing.T) {
	posIf := NewPosInterfaceEndpointClient(ctx, "")
	assert.NotNil(t, posIf)
	res := posIf.Cancel(ctx, &PosConnectorRequest{})
	assert.Nil(t, res)
}

func TestPosInterfaceEndpointTransactionEvent(t *testing.T) {
	posIf := NewPosInterfaceEndpointClient(ctx, "")
	assert.NotNil(t, posIf)
	res := posIf.TransactionEvent(ctx, &PosConnectorRequest{})
	assert.Nil(t, res)
}

func TestPosInterfaceEndpointSignatureVerification(t *testing.T) {
	posIf := NewPosInterfaceEndpointClient(ctx, "")
	assert.NotNil(t, posIf)
	res := posIf.SignatureVerification(ctx, &PosConnectorRequest{})
	assert.Nil(t, res)
}
