// posinterface is used to handle all the logic about connecting to posinterface component
package protocol

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/aws/aws-xray-sdk-go/xray"
	"github.com/aws/smithy-go/ptr"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
)

type PosInterfaceEndpointClient struct {
	endpoint   string
	httpClient http.Client
	mgrApiMap  map[string]common.ManagementApi
}

func NewPosInterfaceEndpointClient(ctx context.Context, endpoint string) PosInterfaceClient {
	client := *xray.Client(&http.Client{Timeout: 30 * time.Second})
	mgrApiMap := map[string]common.ManagementApi{}
	return &PosInterfaceEndpointClient{endpoint, client, mgrApiMap}
}

const ENDPOINT_FORMAT_3 = "%s/%s/%s"
const ENDPOINT_FORMAT_4 = "%s/%s/%s/%s"
const SEND_LOG_FORMAT = "send %s to oraclepos engine for transactionUuid: %s sessionUuid: %s connectionId: %s deviceUuid: %s data: %s"

func (p *PosInterfaceEndpointClient) Purchase(ctx context.Context, request *PosConnectorRequest, isPurchaseResponse bool) *PosConnectorError {
	// 1. Extract and process purchase data
	requestData := request.Data.(map[string]any)
	purchaseBase, err := NewTransactionBase(ctx, &requestData)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}

	// 2. Create appropriate transaction data based on isPurchaseResponse flag
	var purchaseData any
	if isPurchaseResponse {
		purchaseData, err = NewTransactionResponse(ctx, &requestData)
	} else {
		purchaseData, err = NewTransactionLifecycleEvent(ctx, &requestData)
	}

	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}

	// 3. Add session UUID to context for tracking
	ctx = context.WithValue(ctx, logger.AggregateId{}, purchaseBase.SessionUuid)

	// 4. Create HTTP payload
	bodyBytes := p.createTransactionResponsePayload(purchaseData, PURCHASE, request.DeviceUuid, request.EventId, request.Timestamp)

	// 5. Log the purchase attempt
	logger.Info(ctx, fmt.Sprintf(SEND_LOG_FORMAT, PURCHASE, purchaseBase.TransactionUuid, purchaseBase.SessionUuid, request.ConnectionId, request.DeviceUuid, string(bodyBytes)))

	// 6. Send HTTP POST request to POS interface
	resp, err := p.postHttpRequest(ctx, fmt.Sprintf("%s/purchase/%s", p.endpoint, request.DeviceUuid), bodyBytes)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to send purchase to posinterface %s", err.Error()))
		return &PosConnectorError{
			Type:    SERVER_ERROR,
			Message: err.Error(),
		}
	}
	defer resp.Body.Close()

	// 7. Process and return any errors
	return p.buildPosConnectorErrorResponse(ctx, resp)
}

func (p *PosInterfaceEndpointClient) Refund(ctx context.Context, request *PosConnectorRequest, isRefundResponse bool) *PosConnectorError {
	logger.Info(ctx, fmt.Sprintf("refund event from device %s", request.DeviceUuid))

	var purchaseData any
	var err error

	requestData := request.Data.(map[string]any)
	purchaseBase, err := NewTransactionBase(ctx, &requestData)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}

	if isRefundResponse {
		purchaseData, err = NewTransactionResponse(ctx, &requestData)
	} else {
		purchaseData, err = NewTransactionLifecycleEvent(ctx, &requestData)
	}

	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}

	ctx = context.WithValue(ctx, logger.AggregateId{}, purchaseBase.SessionUuid)

	bodyBytes := p.createTransactionResponsePayload(purchaseData, REFUND, request.DeviceUuid, request.EventId, request.Timestamp)

	logger.Info(ctx, fmt.Sprintf(SEND_LOG_FORMAT, REFUND, purchaseBase.TransactionUuid, purchaseBase.SessionUuid, request.ConnectionId, request.DeviceUuid, string(bodyBytes)))

	resp, err := p.postHttpRequest(ctx, fmt.Sprintf("%s/refund/%s", p.endpoint, request.DeviceUuid), bodyBytes)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to send refund to posinterface %s", err.Error()))
		return &PosConnectorError{
			Type:    SERVER_ERROR,
			Message: err.Error(),
		}
	}
	defer resp.Body.Close()
	return p.buildPosConnectorErrorResponse(ctx, resp)
}

func (p *PosInterfaceEndpointClient) Reversal(ctx context.Context, request *PosConnectorRequest, isReversalResponse bool) *PosConnectorError {
	logger.Info(ctx, fmt.Sprintf("reversal event from device %s", request.DeviceUuid))

	var reversalData any
	var err error

	requestData := request.Data.(map[string]any)
	purchaseBase, err := NewTransactionBase(ctx, &requestData)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}

	if isReversalResponse {
		reversalData, err = NewTransactionResponse(ctx, &requestData)
	} else {
		reversalData, err = NewTransactionLifecycleEvent(ctx, &requestData)
	}

	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}

	ctx = context.WithValue(ctx, logger.AggregateId{}, purchaseBase.SessionUuid)

	bodyBytes := p.createTransactionResponsePayload(reversalData, REVERSAL, request.DeviceUuid, request.EventId, request.Timestamp)

	logger.Info(ctx, fmt.Sprintf(SEND_LOG_FORMAT, REVERSAL, purchaseBase.TransactionUuid, purchaseBase.SessionUuid, request.ConnectionId, request.DeviceUuid, string(bodyBytes)))

	resp, err := p.postHttpRequest(ctx, fmt.Sprintf("%s/reversal/%s", p.endpoint, request.DeviceUuid), bodyBytes)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to send reversal to posinterface %s", err.Error()))
		return &PosConnectorError{
			Type:    SERVER_ERROR,
			Message: err.Error(),
		}
	}
	defer resp.Body.Close()
	return p.buildPosConnectorErrorResponse(ctx, resp)
}

func (p *PosInterfaceEndpointClient) CheckOrdersBalances(ctx context.Context, request *PosConnectorRequest) {
	logger.Info(ctx, fmt.Sprintf("check orders balances from posinterface %s from device %s for site %s", p.endpoint, request.DeviceUuid, request.SiteUuid))
	url := fmt.Sprintf(ENDPOINT_FORMAT_3, p.endpoint, "checkOrdersBalances", request.SiteUuid)
	logger.Debug(ctx, fmt.Sprintf("call posinterface CheckOrdersBalances on %s", url))
	r, _ := http.NewRequest("GET", url, nil)
	resp, err := p.httpClient.Do(r.WithContext(ctx))
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to check orders balances from pos interface: %s", err.Error()))
	}

	defer resp.Body.Close()

	body := p.readHttpResponse(ctx, resp)
	logger.Debug(ctx, fmt.Sprintf("checkOrdersBalances response %d %s", resp.StatusCode, string(body)))
	if resp.StatusCode > 299 {
		logger.Error(ctx, fmt.Sprintf("Failed to call checkOrdersBalances %d %s", resp.StatusCode, (body)))
		panic(errors.New(string(body)))
	}
}

func (p *PosInterfaceEndpointClient) handleDeviceOrderPosInterfaceResponse(ctx context.Context, resp *http.Response) *DeviceOrder {
	body := p.readHttpResponse(ctx, resp)
	if resp.StatusCode > 299 {
		errMsg := fmt.Sprintf("Receive error response %d, %s", resp.StatusCode, string(body))
		logger.Error(ctx, errMsg)
		panic(errors.New(errMsg))
	}

	piResp := PosInterfaceResponse{}
	err := json.Unmarshal(body, &piResp)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}

	b, _ := json.Marshal(piResp.Data)
	var order DeviceOrder
	err = json.Unmarshal(b, &order)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}
	return &order
}

func (p *PosInterfaceEndpointClient) UpdateOrderItem(ctx context.Context, request *PosConnectorRequest) *DeviceOrder {
	requestData := request.Data.(map[string]any)
	orderId, ok := requestData["orderId"].(string)
	if !ok || len(orderId) == 0 {
		warnMsg := "order id is required"
		logger.Warn(ctx, warnMsg)
		return nil
	}

	updateOrderItem, err := NewUpdateOrderItem(ctx, &requestData)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}

	ctx = context.WithValue(ctx, logger.AggregateId{}, orderId)

	j, _ := json.Marshal(updateOrderItem)
	logger.Info(ctx, fmt.Sprintf("send update order to HL %s", string(j)))

	requestBody := map[string]any{
		"eventId":   request.EventId,
		"timestamp": request.Timestamp,
		"action":    string(UPDATE_ORDER_ITEM),
		"data":      updateOrderItem,
	}
	bodyBytes, _ := json.Marshal(requestBody)

	logger.Info(ctx, fmt.Sprintf("send updateOrderItem to posinterface %s, %s", p.endpoint, string(bodyBytes)))

	r, _ := http.NewRequest("POST", fmt.Sprintf(ENDPOINT_FORMAT_3, p.endpoint, UPDATE_ORDER_ITEM, request.SiteUuid), bytes.NewBuffer(bodyBytes))
	resp, err := p.httpClient.Do(r.WithContext(ctx))
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to update order item from pos interface: %s", err.Error()))
		panic(err)
	}

	defer resp.Body.Close()

	return p.handleDeviceOrderPosInterfaceResponse(ctx, resp)
}

func (p *PosInterfaceEndpointClient) GetOrder(ctx context.Context, request *PosConnectorRequest) *DeviceOrder {
	requestData := request.Data.(map[string]any)
	orderId, ok := requestData["orderId"].(string)
	if !ok || len(orderId) == 0 {
		warnMsg := "order id is required"
		logger.Warn(ctx, warnMsg)
		return nil
	}
	ctx = logger.AddMetadata(ctx, "orderId", orderId)
	ctx = context.WithValue(ctx, logger.AggregateId{}, orderId)
	logger.Info(ctx, fmt.Sprintf("query order id %s for site %s", orderId, request.SiteUuid))
	r, _ := http.NewRequest("GET", fmt.Sprintf(ENDPOINT_FORMAT_4, p.endpoint, "order", request.SiteUuid, orderId), nil)
	resp, err := p.httpClient.Do(r.WithContext(ctx))
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to query order %s from pos interface: %s", orderId, err.Error()))
		panic(err)
	}
	defer resp.Body.Close()

	return p.handleDeviceOrderPosInterfaceResponse(ctx, resp)
}

func (p *PosInterfaceEndpointClient) getOrders(ctx context.Context, request *PosConnectorRequest) []DeviceOrder {
	logger.Info(ctx, fmt.Sprintf("query orders from posinterface %s from device %s for site %s", p.endpoint, request.DeviceUuid, request.SiteUuid))
	r, _ := http.NewRequest("GET", fmt.Sprintf(ENDPOINT_FORMAT_3, p.endpoint, "orders", request.SiteUuid), nil)
	resp, err := p.httpClient.Do(r.WithContext(ctx))
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to query orders from pos interface: %s", err.Error()))
		panic(err)
	}
	defer resp.Body.Close()
	body := p.readHttpResponse(ctx, resp)
	logger.Info(ctx, fmt.Sprintf("get orders response %s", string(body)))
	if resp.StatusCode > 299 {
		logger.Error(ctx, fmt.Sprintf("Failed to query orders %d %s", resp.StatusCode, (body)))
		panic(errors.New(string(body)))
	}

	piResp := PosInterfaceResponse{}
	err = json.Unmarshal(body, &piResp)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}

	var orders []DeviceOrder
	b, _ := json.Marshal(piResp.Data)
	_ = json.Unmarshal(b, &orders)
	return orders
}

func (p *PosInterfaceEndpointClient) GetOrders(ctx context.Context, request *PosConnectorRequest) []DeviceOrder {
	orders := p.getOrders(ctx, request)

	resp := PosConnectorResponse{
		EventId:   request.EventId,
		Action:    request.Action,
		Timestamp: getServerTimestamp(),
		Data:      orders,
		Error:     nil,
	}
	mgrApi := common.GetMgrApiFromMap(ctx, p.mgrApiMap, request.ConnectionEndpoint)
	PostToConnection(ctx, mgrApi, request.ConnectionId, resp.Bytes(), "site", request.SiteUuid)
	if len(orders) > 0 {
		p.CheckOrdersBalances(ctx, request)
	}

	return orders
}

func (p *PosInterfaceEndpointClient) GetPosConnectionStatus(ctx context.Context, request *PosConnectorRequest) *PosConnectionStatus {
	disconnectedStatus := string(connection.DISCONNECTED)
	connectedStatus := string(connection.CONNECTED)
	logger.Info(ctx, fmt.Sprintf("check posinterface status %s", p.endpoint))
	r, err := http.NewRequest("GET", fmt.Sprintf("%s/%s", p.endpoint, "health"), nil)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed build connection status request: %s", err.Error()))
		return &PosConnectionStatus{
			PosInterface: (*string)(&request.Provider),
			Status:       &disconnectedStatus,
		}
	}
	resp, err := p.httpClient.Do(r.WithContext(ctx))
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to get pos connection status from pos interface: %s", err.Error()))
		return &PosConnectionStatus{
			PosInterface: (*string)(&request.Provider),
			Status:       &disconnectedStatus,
		}
	}
	defer resp.Body.Close()

	logger.Info(ctx, fmt.Sprintf("endpoint %s resp status code %d", p.endpoint, resp.StatusCode))

	if resp.StatusCode != 200 {
		return &PosConnectionStatus{
			PosInterface: (*string)(&request.Provider),
			Status:       &disconnectedStatus,
		}
	}

	return &PosConnectionStatus{
		PosInterface: (*string)(&request.Provider),
		Status:       &connectedStatus,
	}
}

func (p *PosInterfaceEndpointClient) Payment(ctx context.Context, request *PosConnectorRequest) (*PaymentResponse, *PosConnectorError) {
	logger.Info(ctx, fmt.Sprintf("payment from device %s for site %s", request.DeviceUuid, request.SiteUuid))

	requestData := request.Data.(map[string]any)
	SendPaymentAck(ctx, request, common.GetMgrApiFromMap(ctx, p.mgrApiMap, request.ConnectionEndpoint))

	payment, err := NewPayment(ctx, &requestData)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}
	ctx = logger.AddMetadata(ctx, "orderId", *payment.OrderId)
	ctx = logger.AddMetadata(ctx, "transactionUuid", *payment.TransactionUuid)
	ctx = context.WithValue(ctx, logger.AggregateId{}, *payment.OrderId)
	j, _ := json.Marshal(payment)
	logger.Info(ctx, fmt.Sprintf("send payment to HL for transactionUuid %s payment %s over connection %s of device %s", *payment.TransactionUuid, (j), request.ConnectionId, request.DeviceUuid))
	var data map[string]any
	_ = json.Unmarshal(j, &data)
	body := map[string]any{
		"eventId":   request.EventId,
		"timestamp": request.Timestamp,
		"action":    string(PAYMENT),
		"data":      data,
	}
	bodyBytes, _ := json.Marshal(body)

	logger.Info(ctx, fmt.Sprintf("send payment to posinterface %s, %s", p.endpoint, string(bodyBytes)))
	r, _ := http.NewRequest("POST", fmt.Sprintf("%s/payment/%s", p.endpoint, request.SiteUuid), bytes.NewBuffer(bodyBytes))
	resp, err := p.httpClient.Do(r.WithContext(ctx))
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to send payment to posinterface %s", err.Error()))
		return &PaymentResponse{Status: ptr.String(string(FAIL))}, &PosConnectorError{
			Type:    SERVER_ERROR,
			Message: err.Error(),
		}
	}
	defer resp.Body.Close()

	paymentErr := p.buildPosConnectorErrorResponse(ctx, resp)
	if paymentErr == nil {
		logger.Info(ctx, "payment success")
		return &PaymentResponse{Status: ptr.String(string(SUCCESS))}, nil
	} else {
		// If payment failed with status code 408 we should not log error.
		if paymentErr.Type != REQUEST_TIMEOUT {
			logger.Error(ctx, "payment failed "+paymentErr.Error())
		}
		return &PaymentResponse{Status: ptr.String(string(FAIL))}, paymentErr
	}
}

func (p *PosInterfaceEndpointClient) SubscribeOrdersUpdate(ctx context.Context, request *PosConnectorRequest) *SubscriptionResponse {
	// Not applicable for endpoint client
	return nil
}

func (p *PosInterfaceEndpointClient) createTransactionResponsePayload(payload any, action ActionType, deviceUuid, eventId, timestamp string) []byte {
	j, _ := json.Marshal(payload)
	var data map[string]any
	_ = json.Unmarshal(j, &data)
	data["deviceUuid"] = deviceUuid

	body := map[string]any{
		"eventId":   eventId,
		"timestamp": timestamp,
		"action":    string(action),
		"data":      data,
	}
	bodyBytes, _ := json.Marshal(body)

	return bodyBytes
}

func (p *PosInterfaceEndpointClient) readHttpResponse(ctx context.Context, resp *http.Response) []byte {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error(ctx, "Failed to load data from posinterface http connection:"+err.Error())
		panic(err)
	}
	return body
}

func (p *PosInterfaceEndpointClient) postHttpRequest(ctx context.Context, url string, body []byte) (*http.Response, error) {
	r, _ := http.NewRequest("POST", url, bytes.NewBuffer(body))
	return p.httpClient.Do(r.WithContext(ctx))
}

func (p *PosInterfaceEndpointClient) buildPosConnectorErrorResponse(ctx context.Context, resp *http.Response) *PosConnectorError {
	respBody := string(p.readHttpResponse(ctx, resp))
	if resp.StatusCode == http.StatusRequestTimeout {
		return &PosConnectorError{
			Type:    REQUEST_TIMEOUT,
			Message: respBody,
		}
	} else if resp.StatusCode > 299 && resp.StatusCode < 500 {
		logger.Info(ctx, fmt.Sprintf("http response: status code %d, body %s", resp.StatusCode, respBody))
		return &PosConnectorError{
			Type:    INVALID_REQUEST,
			Message: respBody,
		}
	} else if resp.StatusCode >= 500 {
		return &PosConnectorError{
			Type:    SERVER_ERROR,
			Message: respBody,
		}
	}
	return nil
}

func (p *PosInterfaceEndpointClient) GetPairedDevices(ctx context.Context, request *PosConnectorRequest) *PairedDevicesResponse {
	// Not applicable for endpoint client
	return nil
}

func (p *PosInterfaceEndpointClient) Cancel(ctx context.Context, request *PosConnectorRequest) *PosConnectorError {
	// Not applicable for endpoint client
	return nil
}

func (p *PosInterfaceEndpointClient) TransactionEvent(ctx context.Context, request *PosConnectorRequest) *PosConnectorError {
	// Not applicable for endpoint client
	return nil
}

func (p *PosInterfaceEndpointClient) SignatureVerification(ctx context.Context, request *PosConnectorRequest) *PosConnectorError {
	// Not applicable for endpoint client
	return nil
}
