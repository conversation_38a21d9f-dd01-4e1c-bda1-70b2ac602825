package protocol

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"

	"github.com/google/uuid"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
)

type PosInterfaceMockServer struct {
	UpdateOrderItem     DeviceOrder
	Order               DeviceOrder
	Orders              []DeviceOrder
	PosConnectionStatus PosConnectionStatus
	Server              *httptest.Server
}

func NewMockPosInterfaceServer(ctx context.Context) *PosInterfaceMockServer {
	server := PosInterfaceMockServer{}
	testServer := httptest.NewServer(http.HandlerFunc(server.handlerFunc))
	server.Server = testServer
	return &server
}

func (p *PosInterfaceMockServer) handlerFunc(res http.ResponseWriter, req *http.Request) {
	logger.Info(context.Background(), "get orders request:"+req.URL.Path)
	if strings.HasPrefix(req.URL.Path, "/orders") {
		response := PosConnectorResponse{
			EventId:   uuid.NewString(),
			Timestamp: getServerTimestamp(),
			Data: map[string]any{
				"data": p.Orders,
			},
		}
		b, _ := json.Marshal(response)
		res.Write(b)
	} else if strings.HasPrefix(req.URL.Path, "/checkOrdersBalance") {
		res.WriteHeader(http.StatusOK)
	} else if strings.HasPrefix(req.URL.Path, "/payment") {
		res.WriteHeader(http.StatusOK)
	} else {
		b, _ := json.Marshal(p.Order)
		res.Write(b)
	}
}
