package protocol

import (
	"context"
	"fmt"
	"sync"

	dynamodbTypes "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

type PosInterfaceWebsocketClient struct {
	dbClient  *db.DynamoDb
	connMgr   connection.IfConnectionManager
	mgrApiMap map[string]common.ManagementApi
}

type PairedDeviceConnection struct {
	ID            string `json:"id"`
	ConnectedTime string `json:"connectedTime"`
	DeviceType    string `json:"deviceType"`
	DeviceUuid    string `json:"deviceUuid"`
	Provider      string `json:"provider"`
	Status        string `json:"status"`
}

type PairedDevicesResponse struct {
	PairedDevices    []string                 `json:"pairedDevices"`
	ConnectedDevices []PairedDeviceConnection `json:"connectedDevices"`
}

func NewPosInterfaceWebsocketClient(ctx context.Context) PosInterfaceClient {
	mgrApiMap := map[string]common.ManagementApi{}
	dbClient := db.NewDynamoDb(ctx)
	return &PosInterfaceWebsocketClient{dbClient: dbClient, connMgr: connection.New(ctx, dbClient), mgrApiMap: mgrApiMap}
}

func (p *PosInterfaceWebsocketClient) GetOrder(ctx context.Context, request *PosConnectorRequest) *DeviceOrder {
	requestData := request.Data.(map[string]any)
	orderId, ok := requestData["orderId"].(string)
	if !ok || len(orderId) == 0 {
		warnMsg := "order id is required"
		logger.Warn(ctx, warnMsg)
		return nil
	}
	ctx = logger.AddMetadata(ctx, "orderId", orderId)
	ctx = context.WithValue(ctx, logger.AggregateId{}, orderId)

	logger.Debug(ctx, utils.BestEffortStringify(request, true))

	connectionIds := p.getRecipientsConnections(ctx, request)

	p.postToConnections(ctx, connectionIds, request)

	return nil
}

func (p *PosInterfaceWebsocketClient) GetOrders(ctx context.Context, request *PosConnectorRequest) []DeviceOrder {
	connectionIds := p.getRecipientsConnections(ctx, request)

	if request.DeviceType == string(types.DeviceTypeTerminal) && request.Locations != nil {
		request.Data = map[string]any{
			"locations": request.Locations,
		}
	}

	p.postToConnections(ctx, connectionIds, request)

	return nil
}

func (p *PosInterfaceWebsocketClient) SubscribeOrdersUpdate(ctx context.Context, request *PosConnectorRequest) *SubscriptionResponse {
	subscribers := p.getSubscriptionRecords(ctx, request, SUB_ORDERS_UPDATE)

	var wg sync.WaitGroup
	for _, data := range subscribers {
		connectionId, hasId := data["connectionId"].(string)
		connectionEndpoint, hasEndpoint := data["connectionEndpoint"].(string)
		if !hasId || !hasEndpoint {
			logger.Error(ctx, fmt.Sprintf("Cant find connection id or endpoint for the subscriber %s", data["deviceUuid"]))
			continue
		}
		wg.Add(1)
		mgrApi := common.GetMgrApiFromMap(ctx, p.mgrApiMap, connectionEndpoint)
		go func(connectionId string, eventId string, deviceUuid string, mgrApi common.ManagementApi) {
			defer wg.Done()
			resp := PosConnectorResponse{
				EventId:   eventId,
				Action:    request.Action,
				Timestamp: request.Timestamp,
				Data:      request.Data,
			}
			PostToConnection(ctx, mgrApi, connectionId, resp.Bytes(), "site", request.SiteUuid)
			logger.Info(ctx, fmt.Sprintf("publish to site %s device %s, connection %s: %+v", request.SiteUuid, deviceUuid, connectionId, string(resp.Bytes())))
		}(connectionId, data["id"].(string), data["deviceUuid"].(string), mgrApi)
	}
	wg.Wait()
	logger.Info(ctx, "Finish pushing")

	return nil
}

func (p *PosInterfaceWebsocketClient) GetPosConnectionStatus(ctx context.Context, request *PosConnectorRequest) *PosConnectionStatus {
	// to do
	return nil
}

func (p *PosInterfaceWebsocketClient) Payment(ctx context.Context, request *PosConnectorRequest) (*PaymentResponse, *PosConnectorError) {
	if request.DeviceType == string(types.DeviceTypeTerminal) {
		SendPaymentAck(ctx, request, common.GetMgrApiFromMap(ctx, p.mgrApiMap, request.ConnectionEndpoint))
	}

	connectionIds := p.getRecipientsConnections(ctx, request)

	p.postToConnections(ctx, connectionIds, request)

	return nil, nil
}

func (p *PosInterfaceWebsocketClient) getPosRequestResponseStruct(request *PosConnectorRequest) PosConnectorByter {
	if request.DeviceType == string(types.DeviceTypeTerminal) {
		resp := PosConnectorRequest{
			EventId:           request.EventId,
			Action:            request.Action,
			Timestamp:         request.Timestamp,
			Data:              request.Data,
			PaymentDeviceUuid: request.DeviceUuid,
			PairedDeviceId:    request.DeviceUuid,
		}
		return resp
	} else {
		resp := PosConnectorRequest{
			EventId:   request.EventId,
			Action:    request.Action,
			Timestamp: request.Timestamp,
			Data:      request.Data,
		}
		return resp
	}
}

func (p *PosInterfaceWebsocketClient) getRecipientsConnections(ctx context.Context, request *PosConnectorRequest) []connection.ConnectionModel {
	connections := []connection.ConnectionModel{}
	if request.DeviceType == string(types.DeviceTypeTerminal) {
		// look for pos
		return p.getPosConnection(ctx, request)
	} else if request.DeviceType == string(types.DeviceTypePOS) {
		// look for terminals
		return p.getTerminalConnections(ctx, request)
	}

	logger.Error(ctx, "unknown request device type")
	return connections
}

func (p *PosInterfaceWebsocketClient) getSubscriptionRecords(ctx context.Context, request *PosConnectorRequest, subType ActionType) []map[string]any {
	var lastEvaluatedKey map[string]dynamodbTypes.AttributeValue
	subscribers := []map[string]any{}
	for {
		dbQuery := p.dbClient.Query(common.PairingTableName()).Index("statusGsi").
			Eq("status", string(connection.CONNECTED)).
			Begin("type", string(types.SUBSCRIBER)).
			Where().Eq("entityUuid", request.EntityUuid).
			AndWhere().Eq("provider", request.Provider).
			AndWhere().Eq("venueId", request.DeviceUuid).
			AndWhere().Eq("action", string(subType))

		if lastEvaluatedKey != nil {
			dbQuery = dbQuery.StartKey(&lastEvaluatedKey)
		}

		requestData := request.Data.(map[string]any)
		location, hasLocation := requestData["location"].(map[string]any)

		if hasLocation {
			logger.Info(ctx, fmt.Sprintf("compare location id %s", location["locationId"]))
			dbQuery = dbQuery.
				AndWhere().NotExist("locations").
				Or().Contains("locations", location["locationId"]).
				Or().Eq("locations", nil)
		}

		output, err := dbQuery.Exec(ctx)
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error getting terminal subscription records for entityUuid %s venueId %s", request.EntityUuid, request.DeviceUuid))
			return nil
		}

		lastEvaluatedKey = output.Raw.LastEvaluatedKey
		subscribers = append(subscribers, output.Data...)
		if lastEvaluatedKey == nil {
			break
		}
	}

	if len(subscribers) == 0 {
		logger.Warn(ctx, fmt.Sprintf("terminal subscription records not found for entityUuid %s venueId %s", request.EntityUuid, request.DeviceUuid))
		return nil
	}

	return subscribers
}

func (p *PosInterfaceWebsocketClient) getPosConnection(ctx context.Context, request *PosConnectorRequest) []connection.ConnectionModel {
	connections := []connection.ConnectionModel{}

	pairedDeviceId := request.PairedDeviceId
	if pairedDeviceId == "" {
		pairedDeviceId = request.PosDeviceUuid
	}

	output, err := p.dbClient.Query(common.PairingTableName()).
		Index("deviceUuidGsi").
		Eq("deviceUuid", pairedDeviceId).
		Begin("type", types.CONNECTION_CORE).
		Where().Eq("entityUuid", request.EntityUuid).
		AndWhere().Eq("provider", request.Provider).
		AndWhere().Eq("status", connection.CONNECTED).
		Exec(ctx)

	if err != nil {
		logger.Error(ctx, "error getting pos connection record for entityUuid "+request.EntityUuid)
		return connections
	}
	if len(output.Data) == 0 {
		logger.Warn(ctx, "pos connection record not found for entityUuid "+request.EntityUuid)
		return connections
	}
	connections = append(connections, p.connMgr.MapConnectionItemToConnectionModel(output.Data[0]))
	return connections
}

func (p *PosInterfaceWebsocketClient) getTerminalConnections(ctx context.Context, request *PosConnectorRequest) []connection.ConnectionModel {
	connections := []connection.ConnectionModel{}

	if request.PaymentDeviceUuid != "" || request.PairedDeviceId != "" {
		pairedDeviceId := request.PairedDeviceId
		if pairedDeviceId == "" {
			pairedDeviceId = request.PaymentDeviceUuid
		}

		deviceConnections, err := p.connMgr.GetConnectedConnectionsByDevice(ctx, pairedDeviceId, request.Provider)

		if err != nil || len(deviceConnections) == 0 {
			logger.Warn(ctx, "terminal connection record not found for deviceUuid "+pairedDeviceId)
			return connections
		}

		connections = append(connections, *deviceConnections[0])
		return connections
	}

	terminalPairedToDevice, err := p.dbClient.Query(common.PairingTableName()).
		Index("entityGsi").
		Eq("entityUuid", request.EntityUuid).
		Begin("type", types.POSINTERFACE_PAIR).
		Where().Eq("pairedDeviceId", request.DeviceUuid).
		AndWhere().Eq("provider", request.Provider).
		AndWhere().Eq("status", "ACTIVE").
		Attributes("deviceUuid").
		Exec(ctx)
	if err != nil {
		logger.Error(ctx, "error getting paired device records for deviceUuid "+request.DeviceUuid)
		return connections
	}
	if len(terminalPairedToDevice.Data) == 0 {
		logger.Warn(ctx, "terminal pairing records not found for deviceUuid "+request.DeviceUuid)
		return connections
	}
	terminalUuids := []string{}
	for _, terminalPairing := range terminalPairedToDevice.Data {
		terminalUuids = append(terminalUuids, terminalPairing["deviceUuid"].(string))
	}

	output, err := p.dbClient.Query(common.PairingTableName()).Index("statusGsi").
		Eq("status", "CONNECTED").
		Begin("type", types.CONNECTION_CORE).
		Where().Eq("entityUuid", request.EntityUuid).
		AndWhere().Eq("provider", request.Provider).
		AndWhere().In("deviceUuid", terminalUuids).
		Exec(ctx)
	if err != nil {
		logger.Error(ctx, "error getting terminal connection records for entityUuid "+request.EntityUuid)
		return connections
	}
	if len(output.Data) == 0 {
		logger.Warn(ctx, "terminal connection records not found for entityUuid "+request.EntityUuid)
		return connections
	}

	for _, connectionRecord := range output.Data {
		connections = append(connections, p.connMgr.MapConnectionItemToConnectionModel(connectionRecord))
	}

	return connections
}

func (p *PosInterfaceWebsocketClient) postToConnections(ctx context.Context, connections []connection.ConnectionModel, request *PosConnectorRequest) {
	var wg sync.WaitGroup
	for _, connection := range connections {
		wg.Add(1)
		mgrApi := common.GetMgrApiFromMap(ctx, p.mgrApiMap, connection.ConnectionEndpoint)
		go func(connectionId string, mgrApi common.ManagementApi) {
			defer wg.Done()
			resp := p.getPosRequestResponseStruct(request)
			PostToConnection(ctx, mgrApi, connectionId, resp.Bytes(), "", "")
		}(connection.Id, mgrApi)
	}
	wg.Wait()
}

func (p *PosInterfaceWebsocketClient) Purchase(ctx context.Context, request *PosConnectorRequest, isPurchaseResponse bool) *PosConnectorError {
	return p.handleTransactionRequest(ctx, request)
}

func (p *PosInterfaceWebsocketClient) Refund(ctx context.Context, request *PosConnectorRequest, isRefundResponse bool) *PosConnectorError {
	return p.handleTransactionRequest(ctx, request)
}

func (p *PosInterfaceWebsocketClient) Cancel(ctx context.Context, request *PosConnectorRequest) *PosConnectorError {
	return p.handleTransactionRequest(ctx, request)
}

func (p *PosInterfaceWebsocketClient) TransactionEvent(ctx context.Context, request *PosConnectorRequest) *PosConnectorError {
	return p.handleTransactionRequest(ctx, request)
}

func (p *PosInterfaceWebsocketClient) SignatureVerification(ctx context.Context, request *PosConnectorRequest) *PosConnectorError {
	return p.handleTransactionRequest(ctx, request)
}

func (p *PosInterfaceWebsocketClient) handleTransactionRequest(ctx context.Context, request *PosConnectorRequest) *PosConnectorError {
	action := request.Action
	// 1. Get target connections based on device type
	connectionIds := p.getRecipientsConnections(ctx, request)

	if len(connectionIds) == 0 {
		message := fmt.Sprintf("no connection found for %s", action)
		logger.Error(ctx, message)
		panic(PosConnectorError{
			Type:    FORBIDDEN,
			Message: "no connections found for paired devices",
		})
	}

	// 2. Extract and process request data
	requestData := request.Data.(map[string]any)
	purchaseBase, err := NewTransactionBase(ctx, &requestData)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}

	// 3. Add session UUID to context for tracking
	ctx = context.WithValue(ctx, logger.AggregateId{}, purchaseBase.SessionUuid)

	// 4. Log purchase information
	logger.Info(ctx, fmt.Sprintf("%s event %s from device %s, sessionUuid: %s", action,
		request.EventId, request.DeviceUuid, purchaseBase.SessionUuid))

	// 5. Forward request data to recipients via websocket
	p.postToConnections(ctx, connectionIds, request)

	return nil
}

func (p *PosInterfaceWebsocketClient) Reversal(ctx context.Context, request *PosConnectorRequest, isReversalResponse bool) *PosConnectorError {
	// Not applicable for websocket client
	return nil
}

func (p *PosInterfaceWebsocketClient) CheckOrdersBalances(ctx context.Context, request *PosConnectorRequest) {
	// Not applicable for websocket client
}

func (p *PosInterfaceWebsocketClient) UpdateOrderItem(ctx context.Context, request *PosConnectorRequest) *DeviceOrder {
	// Not applicable for websocket client
	return nil
}

func (p *PosInterfaceWebsocketClient) GetPairedDevices(ctx context.Context, request *PosConnectorRequest) *PairedDevicesResponse {
	if request == nil {
		return &PairedDevicesResponse{
			PairedDevices:    []string{},
			ConnectedDevices: []PairedDeviceConnection{},
		}
	}

	if request.Provider != string(types.IMPOS) && request.Provider != string(types.SDK) {
		logger.Error(ctx, "Unsupported provider for GetPairedDevices: "+request.Provider)
		return nil
	}

	response := &PairedDevicesResponse{
		PairedDevices:    []string{},
		ConnectedDevices: []PairedDeviceConnection{},
	}

	switch types.DeviceType(request.DeviceType) {
	case types.DeviceTypePOS:
		p.getPosPairedDevices(ctx, request, response)
	case types.DeviceTypeTerminal:
		p.getTerminalPairedDevices(ctx, request, response)
	default:
		logger.Error(ctx, "Unsupported device type for GetPairedDevices: "+request.DeviceType)
	}

	return response
}

func (p *PosInterfaceWebsocketClient) getPosPairedDevices(ctx context.Context, request *PosConnectorRequest, response *PairedDevicesResponse) {
	if request.DeviceUuid == "" {
		logger.Error(ctx, "DeviceUuid is required for POS device pairing query")
		return
	}

	output, err := p.dbClient.Query(common.PairingTableName()).
		Index("entityGsi").
		Eq("entityUuid", request.EntityUuid).
		Begin("type", string(types.POSINTERFACE_PAIR_DEVICE)).
		Where().Eq("provider", request.Provider).
		AndWhere().Eq("status", "ACTIVE").
		AndWhere().Eq("pairedDeviceId", request.DeviceUuid).
		Or().Eq("venueId", request.DeviceUuid).
		Exec(ctx)

	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Error querying POS pairings: %v", err))
		return
	}

	if len(output.Data) == 0 {
		logger.Info(ctx, fmt.Sprintf("No active pairings found for POS device: %s", request.DeviceUuid))
		return
	}

	seenDevices := make(map[string]bool)
	for _, pairing := range output.Data {
		terminalUuid, ok := pairing["deviceUuid"].(string)
		if !ok || terminalUuid == "" {
			logger.Error(ctx, fmt.Sprintf("Invalid or empty deviceUuid in pairing record for device: %s", request.DeviceUuid))
			continue
		}

		// Skip if we've already processed this device
		if seenDevices[terminalUuid] {
			continue
		}
		seenDevices[terminalUuid] = true

		response.PairedDevices = append(response.PairedDevices, terminalUuid)

		response.ConnectedDevices = append(response.ConnectedDevices, p.getPairedDeviceConnections(ctx, terminalUuid, request.Provider)...)
	}
}

func (p *PosInterfaceWebsocketClient) getTerminalPairedDevices(ctx context.Context, request *PosConnectorRequest, response *PairedDevicesResponse) {
	pairedDeviceId := request.VenueId
	if pairedDeviceId == "" {
		pairedDeviceId = request.PairedDeviceId
	}

	if pairedDeviceId == "" {
		logger.Error(ctx, "Either VenueId or PairedDeviceId is required for terminal device pairing query")
		return
	}
	output, err := p.dbClient.Query(common.PairingTableName()).
		Index("deviceUuidGsi").
		Eq("deviceUuid", pairedDeviceId).
		Begin("type", string(types.POSINTERFACE_PAIR)).
		Where().Eq("provider", request.Provider).
		AndWhere().Eq("status", "ACTIVE").
		AndWhere().Eq("entityUuid", request.EntityUuid).
		Exec(ctx)

	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Error querying terminal pairings: %v", err))
		return
	}

	if len(output.Data) == 0 {
		logger.Info(ctx, fmt.Sprintf("No active pairings found for terminal device: %s", request.DeviceUuid))
		return
	}

	// Get the first active pairing
	pairing := output.Data[0]
	posDeviceUuid, ok := pairing["deviceUuid"].(string)
	if !ok || posDeviceUuid == "" {
		logger.Error(ctx, fmt.Sprintf("Invalid or empty deviceUuid in POS pairing record for terminal: %s", request.DeviceUuid))
		return
	}

	response.PairedDevices = append(response.PairedDevices, posDeviceUuid)

	response.ConnectedDevices = p.getPairedDeviceConnections(ctx, posDeviceUuid, request.Provider)
}

func (p *PosInterfaceWebsocketClient) getPairedDeviceConnections(ctx context.Context, deviceUuid string, provider string) []PairedDeviceConnection {
	pairedDeviceConnections := []PairedDeviceConnection{}

	conns, err := p.connMgr.GetConnectedConnectionsByDevice(ctx, deviceUuid, provider)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Error getting connections for device %s: %v", deviceUuid, err))
		return pairedDeviceConnections
	}

	for _, conn := range conns {
		pairedDeviceConnection := PairedDeviceConnection{
			ID:            conn.Id,
			ConnectedTime: conn.CreatedTime,
			DeviceType:    string(conn.DeviceType),
			DeviceUuid:    conn.DeviceUuid,
			Provider:      conn.Provider,
			Status:        string(conn.Status),
		}
		pairedDeviceConnections = append(pairedDeviceConnections, pairedDeviceConnection)
	}

	return pairedDeviceConnections
}
