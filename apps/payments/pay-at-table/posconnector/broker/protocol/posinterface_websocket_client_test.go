// posinterface is used to handle all the logic about connecting to posinterface component
package protocol

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	coreTypes "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/types"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/test"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

type MockConnectionManager struct {
	mock.Mock
}

func (m *MockConnectionManager) Connect(ctx context.Context, connectionId string, endpoint string, authorizer *types.RequestAuthorizer) error {
	return nil
}

func (m *MockConnectionManager) Disconnect(ctx context.Context, connectionId string) (*connection.ConnectionModel, error) {
	return nil, nil
}

func (m *MockConnectionManager) GetConnection(ctx context.Context, connectionId string) (*connection.ConnectionModel, error) {
	return nil, nil
}

func (m *MockConnectionManager) GetConnectedConnectionsByDevice(ctx context.Context, deviceUuid string, provider string) ([]*connection.ConnectionModel, error) {
	args := m.Called(ctx, deviceUuid, provider)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*connection.ConnectionModel), args.Error(1)
}

func (m *MockConnectionManager) GetConnectionWithConsistentRead(ctx context.Context, connectionId string) (*connection.ConnectionModel, error) {
	return nil, nil
}

func (m *MockConnectionManager) MapConnectionItemToConnectionModel(connectionItem map[string]any) connection.ConnectionModel {
	return connection.ConnectionModel{}
}

type PairingInput struct {
	DeviceUuid     *string
	EntityUuid     string
	DeviceType     types.DeviceType
	Provider       types.PosProvider
	PairedDeviceId *string
}

func insertPairingRecord(overrides PairingInput) map[string]any {
	if overrides.DeviceUuid == nil {
		overrides.DeviceUuid = ptr.String(uuid.NewString())
	}
	recordType := string(types.POSINTERFACE_PAIR_DEVICE)
	if overrides.DeviceType == types.DeviceTypePOS {
		recordType = string(types.POSINTERFACE_PAIR_POS)
	}

	timestamp := time.Now().Format(time.RFC3339)
	posConnectionRecord := map[string]any{
		"id":             overrides.DeviceUuid,
		"deviceUuid":     overrides.DeviceUuid,
		"type":           recordType,
		"entityUuid":     overrides.EntityUuid,
		"status":         "ACTIVE",
		"deviceType":     string(overrides.DeviceType),
		"provider":       string(overrides.Provider),
		"pairedDeviceId": overrides.PairedDeviceId,
		"timestamp":      timestamp,
	}
	item, _ := attributevalue.MarshalMap(&posConnectionRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	return posConnectionRecord
}

func insertConnectionRecord(entityUuid string, deviceType types.DeviceType, provider types.PosProvider, deviceUuid *string) map[string]string {
	timestamp := time.Now().Format(time.RFC3339)
	if deviceUuid == nil {
		deviceUuid = ptr.String(uuid.NewString())
	}
	posConnectionRecord := map[string]string{
		"id":            uuid.NewString(),
		"deviceUuid":    *deviceUuid,
		"type":          string(types.CONNECTION_CORE),
		"entityUuid":    entityUuid,
		"status":        "CONNECTED",
		"deviceType":    string(deviceType),
		"provider":      string(provider),
		"timestamp":     timestamp,
		"createdTime":   timestamp,
		"connectedTime": timestamp,
		"updatedTime":   timestamp,
	}
	item, _ := attributevalue.MarshalMap(&posConnectionRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	return posConnectionRecord
}

func insertSubRecord(entityUuid string, venueId string, locations []string) map[string]any {
	eventId := uuid.NewString()
	subConnectionRecord := test.GetSubscriberMap(map[string]any{
		"type":          fmt.Sprintf("%s%s%s", string(types.SUBSCRIBER), uuid.NewString(), string(SUB_ORDERS_UPDATE)),
		"deviceType":    string(types.DeviceTypeTerminal),
		"entityUuid":    entityUuid,
		"eventId":       eventId,
		"action":        string(SUB_ORDERS_UPDATE),
		"provider":      string(types.IMPOS),
		"posDeviceUuid": venueId,
		"venueId":       venueId,
	})
	if len(locations) > 0 {
		subConnectionRecord["locations"] = locations
	}
	item, _ := attributevalue.MarshalMap(&subConnectionRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	return subConnectionRecord
}

func TestWSGetOrderTerminalToPos(t *testing.T) {
	entityUuid := uuid.NewString()
	pos := insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.IMPOS, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		EventId:       uuid.NewString(),
		Action:        string(GET_ORDER),
		DeviceType:    string(types.DeviceTypeTerminal),
		EntityUuid:    entityUuid,
		DeviceUuid:    uuid.NewString(),
		Provider:      string(types.IMPOS),
		Data:          map[string]any{"orderId": uuid.NewString()},
		PosDeviceUuid: pos["deviceUuid"],
	}
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	posIfWs.GetOrder(ctx, &req)
	mockMgrApi.AssertExpectations(t)
}

func TestWSGetOrderTerminalToPosNoPosConnection(t *testing.T) {
	entityUuid := uuid.NewString()
	mockMgrApi := &common.MockManagementApi{}
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		EventId:       uuid.NewString(),
		Action:        string(GET_ORDER),
		DeviceType:    string(types.DeviceTypeTerminal),
		EntityUuid:    entityUuid,
		DeviceUuid:    uuid.NewString(),
		PosDeviceUuid: uuid.NewString(),
		Provider:      string(types.IMPOS),
		Data:          map[string]any{"orderId": uuid.NewString()},
	}
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	posIfWs.GetOrder(ctx, &req)
	mockMgrApi.AssertNotCalled(t, "PostToConnection", mock.Anything)
}

func TestWSGetOrderTerminalToPosMissingField(t *testing.T) {
	entityUuid := uuid.NewString()
	mockMgrApi := &common.MockManagementApi{}
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		EventId:    uuid.NewString(),
		Action:     string(GET_ORDER),
		DeviceType: string(types.DeviceTypeTerminal),
		EntityUuid: entityUuid,
		DeviceUuid: uuid.NewString(),
		// Missing PosDeviceUuid
		Provider: string(types.IMPOS),
		Data:     map[string]any{"orderId": uuid.NewString()},
	}
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	posIfWs.GetOrder(ctx, &req)
	mockMgrApi.AssertNotCalled(t, "PostToConnection", mock.Anything)
}

func TestWSGetOrderTerminalToPosNoOrderId(t *testing.T) {
	mockMgrApi := &common.MockManagementApi{}
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		EventId:    uuid.NewString(),
		Action:     string(GET_ORDER),
		DeviceType: string(types.DeviceTypeTerminal),
		EntityUuid: uuid.NewString(),
		Provider:   string(types.IMPOS),
		Data:       map[string]any{"wrongKey": uuid.NewString()},
	}
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	posIfWs.GetOrder(ctx, &req)
	mockMgrApi.AssertNotCalled(t, "PostToConnection", mock.Anything)
}

func TestWSGetOrderPosToPaymentDevice(t *testing.T) {
	entityUuid := uuid.NewString()
	connectionRecord := insertConnectionRecord(entityUuid, types.DeviceTypeTerminal, types.IMPOS, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		EventId:           uuid.NewString(),
		Action:            string(GET_ORDER),
		DeviceType:        string(types.DeviceTypePOS),
		EntityUuid:        entityUuid,
		DeviceUuid:        uuid.NewString(),
		Provider:          string(types.IMPOS),
		Data:              map[string]any{"orderId": uuid.NewString()},
		PaymentDeviceUuid: connectionRecord["deviceUuid"],
	}
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	posIfWs.GetOrder(ctx, &req)
	mockMgrApi.AssertExpectations(t)
}

func TestWSGetOrderPosToPaymentDevices(t *testing.T) {
	entityUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	d1 := insertPairingRecord(PairingInput{EntityUuid: entityUuid, Provider: types.IMPOS, PairedDeviceId: &deviceUuid})
	d2 := insertPairingRecord(PairingInput{EntityUuid: entityUuid, Provider: types.IMPOS, PairedDeviceId: &deviceUuid})
	d3 := insertPairingRecord(PairingInput{EntityUuid: entityUuid, Provider: types.IMPOS, PairedDeviceId: &deviceUuid})
	insertConnectionRecord(entityUuid, types.DeviceTypeTerminal, types.IMPOS, d1["deviceUuid"].(*string))
	insertConnectionRecord(entityUuid, types.DeviceTypeTerminal, types.IMPOS, d2["deviceUuid"].(*string))
	insertConnectionRecord(entityUuid, types.DeviceTypeTerminal, types.IMPOS, d3["deviceUuid"].(*string))

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		EventId:    uuid.NewString(),
		Action:     string(GET_ORDER),
		DeviceType: string(types.DeviceTypePOS),
		EntityUuid: entityUuid,
		DeviceUuid: deviceUuid,
		Provider:   string(types.IMPOS),
		Data:       map[string]any{"orderId": uuid.NewString()},
	}
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	posIfWs.GetOrder(ctx, &req)
	mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 3)
	mockMgrApi.AssertExpectations(t)
}

func TestWSGetOrderPosToPaymentDevicesNotFound(t *testing.T) {
	entityUuid := uuid.NewString()

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		EventId:    uuid.NewString(),
		Action:     string(GET_ORDER),
		DeviceType: string(types.DeviceTypePOS),
		EntityUuid: entityUuid,
		DeviceUuid: uuid.NewString(),
		Provider:   string(types.IMPOS),
		Data:       map[string]any{"orderId": uuid.NewString()},
	}
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	posIfWs.GetOrder(ctx, &req)
	mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 0)
}

func TestWSGetOrdersTerminalToPos(t *testing.T) {
	entityUuid := uuid.NewString()
	pos := insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.IMPOS, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		EventId:       uuid.NewString(),
		Action:        string(GET_ORDERS),
		DeviceType:    string(types.DeviceTypeTerminal),
		EntityUuid:    entityUuid,
		DeviceUuid:    uuid.NewString(),
		Provider:      string(types.IMPOS),
		PosDeviceUuid: pos["deviceUuid"],
	}
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	res := posIfWs.GetOrders(ctx, &req)
	assert.Nil(t, res)
	mockMgrApi.AssertExpectations(t)
}

func TestWSGetOrdersTerminalToPosWithLocations(t *testing.T) {
	entityUuid := uuid.NewString()
	pos := insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.IMPOS, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		EventId:       uuid.NewString(),
		Action:        string(GET_ORDERS),
		DeviceType:    string(types.DeviceTypeTerminal),
		EntityUuid:    entityUuid,
		DeviceUuid:    uuid.NewString(),
		Provider:      string(types.IMPOS),
		PosDeviceUuid: pos["deviceUuid"],
		Locations:     []string{"1", "2"},
	}
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	res := posIfWs.GetOrders(ctx, &req)
	assert.Nil(t, res)

	var mockRequest map[string]any
	mockArgumentRequest := mockMgrApi.Calls[0].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput).Data

	json.Unmarshal(mockArgumentRequest, &mockRequest)

	assert.Equal(t, map[string]any{
		"locations": []any{"1", "2"},
	}, mockRequest["data"])
	mockMgrApi.AssertExpectations(t)
}

func TestWSPurchaseTerminalToPos(t *testing.T) {
	// Setup test context and mock data
	entityUuid := uuid.NewString()
	pos := insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.IMPOS, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	// Create test request with required purchase data
	req := PosConnectorRequest{
		EventId:       uuid.NewString(),
		Action:        string(PURCHASE),
		DeviceType:    string(types.DeviceTypeTerminal),
		EntityUuid:    entityUuid,
		DeviceUuid:    uuid.NewString(),
		Provider:      string(types.IMPOS),
		PosDeviceUuid: pos["deviceUuid"],
		Data: map[string]any{
			"sessionUuid":     uuid.NewString(),
			"transactionUuid": uuid.NewString(),
			"amount":          1000,
			"currencyCode":    "AUD",
			"orderId":         "123",
			"type":            "PURCHASE",
			"status":          "SUCCESS",
			"transactionTime": "2024-10-30T15:49:11.961+11:00[Australia/Sydney]",
			"maskedPan":       "123456xxxxxx5555",
			"merchantId":      "123456",
			"tid":             "456",
			"rrn":             uuid.NewString(),
			"stan":            "********",
			"authCode":        uuid.NewString(),
			"accountType":     "CREDIT",
			"responseCode":    "00",
			"caid":            "********",
			"catid":           "********",
		},
	}

	// Execute purchase
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	err := posIfWs.Purchase(ctx, &req, true)

	// Verify results
	assert.Nil(t, err)
	mockMgrApi.AssertExpectations(t)
	mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 1)
}

func TestWSRefundTerminalToPos(t *testing.T) {
	// Setup test context
	entityUuid := uuid.NewString()
	pos := insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.IMPOS, nil)

	testCases := []struct {
		name        string
		requestData map[string]any
		expectPanic bool
	}{
		{
			name: "valid refund request",
			requestData: map[string]any{
				"sessionUuid":     uuid.NewString(),
				"transactionUuid": uuid.NewString(),
				"amount":          float64(1000),
				"currencyCode":    "AUD",
				"type":            string(REFUND),
			},
			expectPanic: false,
		},
		{
			name: "refund with additional fields",
			requestData: map[string]any{
				"sessionUuid":     uuid.NewString(),
				"transactionUuid": uuid.NewString(),
				"amount":          float64(1500),
				"currencyCode":    "USD",
				"type":            string(REFUND),
				"reason":          "customer_request",
				"metadata": map[string]any{
					"originalTransactionId": uuid.NewString(),
					"refundReason":          "product_return",
				},
			},
			expectPanic: false,
		},
		{
			name: "refund with zero amount",
			requestData: map[string]any{
				"sessionUuid":     uuid.NewString(),
				"transactionUuid": uuid.NewString(),
				"amount":          float64(0),
				"currencyCode":    "AUD",
				"type":            string(REFUND),
			},
			expectPanic: false,
		},
		{
			name: "missing required field - sessionUuid",
			requestData: map[string]any{
				"transactionUuid": uuid.NewString(),
				"currencyCode":    "AUD",
				"type":            string(REFUND),
			},
			expectPanic: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			mockMgrApi := &common.MockManagementApi{}
			mockMgrApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
			ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

			req := PosConnectorRequest{
				EventId:       uuid.NewString(),
				Action:        string(REFUND),
				DeviceType:    string(types.DeviceTypeTerminal),
				EntityUuid:    entityUuid,
				DeviceUuid:    uuid.NewString(),
				Provider:      string(types.IMPOS),
				PosDeviceUuid: pos["deviceUuid"],
				Data:          tc.requestData,
			}

			posIfWs := NewPosInterfaceWebsocketClient(ctx)

			if tc.expectPanic {
				assert.Panics(t, func() {
					posIfWs.Refund(ctx, &req, false)
				})
				return
			}

			err := posIfWs.Refund(ctx, &req, false)
			assert.Nil(t, err)
			mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 1)

			// Verify the message structure
			var sentPayload map[string]any
			data := mockMgrApi.Calls[len(mockMgrApi.Calls)-1].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput).Data
			json.Unmarshal(data, &sentPayload)

			assert.Equal(t, string(REFUND), sentPayload["action"])
			assert.Equal(t, tc.requestData["sessionUuid"], sentPayload["data"].(map[string]any)["sessionUuid"])
			assert.Equal(t, tc.requestData["transactionUuid"], sentPayload["data"].(map[string]any)["transactionUuid"])
			assert.Equal(t, tc.requestData["amount"], sentPayload["data"].(map[string]any)["amount"])
			assert.Equal(t, tc.requestData["currencyCode"], sentPayload["data"].(map[string]any)["currencyCode"])
		})
	}
}

func TestWSRefundPosToTerminal(t *testing.T) {
	entityUuid := uuid.NewString()
	terminal := insertConnectionRecord(entityUuid, types.DeviceTypeTerminal, types.IMPOS, nil)

	testCases := []struct {
		name        string
		requestData map[string]any
	}{
		{
			name: "successful refund response",
			requestData: map[string]any{
				"sessionUuid":     uuid.NewString(),
				"transactionUuid": uuid.NewString(),
				"status":          "SUCCESS",
				"responseCode":    "00",
				"type":            string(REFUND),
				"amount":          float64(1000),
			},
		},
		{
			name: "failed refund response",
			requestData: map[string]any{
				"sessionUuid":     uuid.NewString(),
				"transactionUuid": uuid.NewString(),
				"status":          "FAILED",
				"responseCode":    "51",
				"type":            string(REFUND),
				"amount":          float64(1000),
				"errorMessage":    "Insufficient funds",
			},
		},
		{
			name: "processing refund status",
			requestData: map[string]any{
				"sessionUuid":     uuid.NewString(),
				"transactionUuid": uuid.NewString(),
				"status":          "PROCESSING",
				"type":            string(REFUND),
				"amount":          float64(1000),
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			mockMgrApi := &common.MockManagementApi{}
			mockMgrApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
			ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

			req := PosConnectorRequest{
				EventId:           uuid.NewString(),
				Action:            string(REFUND),
				DeviceType:        string(types.DeviceTypePOS),
				EntityUuid:        entityUuid,
				DeviceUuid:        uuid.NewString(),
				Provider:          string(types.IMPOS),
				PaymentDeviceUuid: terminal["deviceUuid"],
				Data:              tc.requestData,
			}

			posIfWs := NewPosInterfaceWebsocketClient(ctx)
			err := posIfWs.Refund(ctx, &req, false)

			assert.Nil(t, err)
			mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 1)

			// Verify the message structure
			var sentPayload map[string]any
			data := mockMgrApi.Calls[len(mockMgrApi.Calls)-1].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput).Data
			json.Unmarshal(data, &sentPayload)

			assert.Equal(t, string(REFUND), sentPayload["action"])
			assert.Equal(t, tc.requestData["sessionUuid"], sentPayload["data"].(map[string]any)["sessionUuid"])
			assert.Equal(t, tc.requestData["status"], sentPayload["data"].(map[string]any)["status"])
			assert.Equal(t, tc.requestData["type"], sentPayload["data"].(map[string]any)["type"])
		})
	}
}

func TestWSRefundValidation(t *testing.T) {
	entityUuid := uuid.NewString()
	pos := insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.IMPOS, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	testCases := []struct {
		name        string
		request     *PosConnectorRequest
		expectPanic bool
	}{
		{
			name:        "nil request",
			request:     nil,
			expectPanic: true,
		},
		{
			name: "nil data",
			request: &PosConnectorRequest{
				EventId:    uuid.NewString(),
				Action:     string(REFUND),
				DeviceType: string(types.DeviceTypeTerminal),
				Data:       nil,
			},
			expectPanic: true,
		},
		{
			name: "missing device uuid",
			request: &PosConnectorRequest{
				EventId:    uuid.NewString(),
				Action:     string(REFUND),
				DeviceType: string(types.DeviceTypeTerminal),
				EntityUuid: entityUuid,
				Provider:   string(types.IMPOS),
				Data: map[string]any{
					"sessionUuid":     uuid.NewString(),
					"transactionUuid": uuid.NewString(),
					"type":            string(REFUND),
				},
			},
			expectPanic: true,
		},
		{
			name: "invalid device type",
			request: &PosConnectorRequest{
				EventId:       uuid.NewString(),
				Action:        string(REFUND),
				DeviceType:    "INVALID_DEVICE",
				EntityUuid:    entityUuid,
				DeviceUuid:    uuid.NewString(),
				Provider:      string(types.IMPOS),
				PosDeviceUuid: pos["deviceUuid"],
				Data: map[string]any{
					"sessionUuid":     uuid.NewString(),
					"transactionUuid": uuid.NewString(),
					"type":            string(REFUND),
				},
			},
			expectPanic: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			posIfWs := NewPosInterfaceWebsocketClient(ctx)
			if tc.expectPanic {
				assert.Panics(t, func() {
					posIfWs.Refund(ctx, tc.request, false)
				})
				return
			}

			err := posIfWs.Refund(ctx, tc.request, false)
			assert.Nil(t, err)
		})
	}
}

func TestWSReversalTerminalToPos(t *testing.T) {
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	res := posIfWs.Reversal(ctx, &PosConnectorRequest{}, true)
	assert.Nil(t, res)
}

func TestWSCheckOrdersBalancesTerminalToPos(t *testing.T) {
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	posIfWs.CheckOrdersBalances(ctx, &PosConnectorRequest{})
}

func TestWSUpdateOrderItemTerminalToPos(t *testing.T) {
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	posIfWs.UpdateOrderItem(ctx, &PosConnectorRequest{})
}

func TestWSGetPosConnectionStatusTerminalToPos(t *testing.T) {
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	res := posIfWs.GetPosConnectionStatus(ctx, &PosConnectorRequest{})
	assert.Nil(t, res)
}

func TestWSPaymentTerminalToPos(t *testing.T) {
	entityUuid := uuid.NewString()
	pos := insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.IMPOS, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		EventId:    uuid.NewString(),
		Action:     string(PAYMENT),
		DeviceType: string(types.DeviceTypeTerminal),
		EntityUuid: entityUuid,
		DeviceUuid: uuid.NewString(),
		Provider:   string(types.IMPOS),
		Data: map[string]any{
			"orderId":         "194",
			"amount":          990,
			"transactionTime": "2024-10-30T15:49:11.961+11:00[Australia/Sydney]",
			"tip":             0,
			"surcharge":       0,
			"serviceCharge":   0,
			"currencyCode":    "AUD",
			"maskedPan":       "123456xxxxxx5555",
			"merchantId":      "123456",
			"tid":             "456",
			"rrn":             uuid.NewString(),
			"stan":            "********",
			"authCode":        uuid.NewString(),
			"accountType":     "CREDIT",
			"transactionUuid": uuid.NewString(),
		},
		PosDeviceUuid: pos["deviceUuid"],
	}

	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	resp, res := posIfWs.Payment(ctx, &req)
	assert.Nil(t, resp)
	assert.Nil(t, res)

	mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 2) // Acknowledgement and post to POS
}

func TestWSPaymentPosToTerminal(t *testing.T) {
	entityUuid := uuid.NewString()
	terminal := insertConnectionRecord(entityUuid, types.DeviceTypeTerminal, types.IMPOS, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		Action:     string(PAYMENT),
		DeviceType: string(types.DeviceTypePOS),
		EntityUuid: entityUuid,
		DeviceUuid: uuid.NewString(),
		Provider:   string(types.IMPOS),
		Data: map[string]any{
			"status": "SUCCESS",
		},
		PaymentDeviceUuid: terminal["deviceUuid"],
	}

	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	resp, res := posIfWs.Payment(ctx, &req)
	assert.Nil(t, resp)
	assert.Nil(t, res)

	mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 1)
}

func TestWSSubOrdersUpdatePosToTerminal(t *testing.T) {
	entityUuid := uuid.NewString()
	venueId := uuid.NewString() // posDeviceUuid
	subRecord := insertSubRecord(entityUuid, venueId, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{
		Endpoint: test.WsRestApiEndpoint,
	}, mockMgrApi)

	req := PosConnectorRequest{
		Action:     string(SUB_ORDERS_UPDATE),
		DeviceType: string(types.DeviceTypePOS),
		EntityUuid: entityUuid,
		DeviceUuid: venueId,
		Provider:   string(types.IMPOS),
		Data: map[string]any{
			"items": []map[string]any{
				{
					"amount":       650,
					"description":  "Great Nothern Zero",
					"itemId":       "**********",
					"quantity":     3,
					"relatedItems": []any{},
					"totalAmount":  1950,
				},
				{
					"amount":       900,
					"description":  "Corona",
					"itemId":       "**********",
					"quantity":     1,
					"relatedItems": []any{},
					"totalAmount":  900,
				},
			},
			"location": map[string]any{
				"locationId":   "3",
				"locationName": "Outside",
			},
			"orderId":     "201",
			"owedAmount":  3135,
			"siteUuid":    nil,
			"status":      "OPEN",
			"tableName":   "71",
			"tableNumber": "71",
			"totalAmount": 3135,
		},
		PaymentDeviceUuid: subRecord["deviceUuid"].(string),
	}

	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	res := posIfWs.SubscribeOrdersUpdate(ctx, &req)
	assert.Nil(t, res)

	mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 1)
}

func TestWSSubOrdersUpdateLocationFilterPosToTerminal(t *testing.T) {
	entityUuid := uuid.NewString()
	venueId := uuid.NewString() // posDeviceUuid

	subLocation := "5"
	orderLocation := "3"

	subRecord := insertSubRecord(entityUuid, venueId, []string{subLocation})

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		Action:     string(SUB_ORDERS_UPDATE),
		DeviceType: string(types.DeviceTypePOS),
		EntityUuid: entityUuid,
		DeviceUuid: venueId,
		Provider:   string(types.IMPOS),
		Data: map[string]any{
			"items": []map[string]any{
				{
					"amount":       650,
					"description":  "Great Nothern Zero",
					"itemId":       "**********",
					"quantity":     3,
					"relatedItems": []any{},
					"totalAmount":  1950,
				},
				{
					"amount":       900,
					"description":  "Corona",
					"itemId":       "**********",
					"quantity":     1,
					"relatedItems": []any{},
					"totalAmount":  900,
				},
			},
			"location": map[string]any{
				"locationId":   orderLocation,
				"locationName": "Cafe",
			},
			"orderId":     "201",
			"owedAmount":  3135,
			"siteUuid":    nil,
			"status":      "OPEN",
			"tableName":   "71",
			"tableNumber": "71",
			"totalAmount": 3135,
		},
		PaymentDeviceUuid: subRecord["deviceUuid"].(string),
	}

	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	res := posIfWs.SubscribeOrdersUpdate(ctx, &req)
	assert.Nil(t, res)

	mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 0)
}

func TestWSSubOrdersUpdateNoConnection(t *testing.T) {
	entityUuid := uuid.NewString()
	venueId := uuid.NewString() // posDeviceUuid
	eventId := uuid.NewString() // eventId

	subRecord := map[string]any{
		"id":   eventId,
		"type": fmt.Sprintf("%s%s%s", string(types.SUBSCRIBER), uuid.NewString(), string(SUB_ORDERS_UPDATE)),
		// "connectionId":  uuid.NewString(),
		"deviceType":    string(types.DeviceTypeTerminal),
		"deviceUuid":    uuid.NewString(),
		"entityUuid":    entityUuid,
		"eventId":       eventId,
		"action":        string(SUB_ORDERS_UPDATE),
		"status":        "CONNECTED",
		"provider":      string(types.IMPOS),
		"posDeviceUuid": venueId,
		"venueId":       venueId,
	}
	item, _ := attributevalue.MarshalMap(&subRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	req := PosConnectorRequest{
		Action:            string(SUB_ORDERS_UPDATE),
		DeviceType:        string(types.DeviceTypePOS),
		EntityUuid:        entityUuid,
		DeviceUuid:        venueId,
		Provider:          string(types.IMPOS),
		Data:              map[string]any{},
		PaymentDeviceUuid: subRecord["deviceUuid"].(string),
	}

	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	res := posIfWs.SubscribeOrdersUpdate(ctx, &req)
	assert.Nil(t, res)

	mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 0)
}

func TestWSUnknownDeviceType(t *testing.T) {
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	res := posIfWs.GetOrder(ctx, &PosConnectorRequest{
		Action:     string(GET_ORDER),
		DeviceType: string("UNKNOWN"),
		EntityUuid: uuid.NewString(),
		DeviceUuid: uuid.NewString(),
		Provider:   string(types.IMPOS),
		Data:       map[string]any{"orderId": uuid.NewString()},
	})
	assert.Nil(t, res)
}

func TestWSPurchaseRequest(t *testing.T) {
	entityUuid := uuid.NewString()
	terminalConnection := insertConnectionRecord(entityUuid, types.DeviceTypeTerminal, types.SDK, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	sessionUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	req := PosConnectorRequest{
		EventId:        uuid.NewString(),
		Action:         string(PURCHASE),
		Timestamp:      fmt.Sprintf("%d", time.Now().UnixMilli()),
		DeviceType:     string(types.DeviceTypePOS),
		EntityUuid:     entityUuid,
		DeviceUuid:     deviceUuid,
		Provider:       string(types.SDK),
		PairedDeviceId: terminalConnection["deviceUuid"],
		Data: map[string]any{
			"amount":              1673,
			"caid":                "000000000038529",
			"deviceUuid":          terminalConnection["deviceUuid"],
			"externalReference":   "096907187377",
			"sessionUuid":         sessionUuid,
			"timestamp":           "2025-01-21T15:02:57+11:00",
			"tipAmount":           0,
			"transactionCurrency": "AUD",
			"transactionUuid":     sessionUuid,
			"type":                string(types.PURCHASE),
		},
	}

	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	err := posIfWs.Purchase(ctx, &req, true)
	assert.Nil(t, err)

	// Verify the response text was set correctly
	if len(mockMgrApi.Calls) > 0 {
		postConnInput := mockMgrApi.Calls[0].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput)
		var published map[string]any
		json.Unmarshal(postConnInput.Data, &published)
		publishedData := published["data"].(map[string]any)

		publishedDataBytes, _ := json.Marshal(publishedData)
		requestDataBytes, _ := json.Marshal(req.Data)
		assert.Equal(t, string(requestDataBytes), string(publishedDataBytes))
		assert.Equal(t, req.Action, published["action"])
		assert.Equal(t, req.EventId, published["eventId"])
	}
}

func TestWSCancelRequest(t *testing.T) {
	entityUuid := uuid.NewString()
	terminalConnection := insertConnectionRecord(entityUuid, types.DeviceTypeTerminal, types.SDK, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	sessionUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	req := PosConnectorRequest{
		EventId:        uuid.NewString(),
		Action:         string(CANCEL),
		Timestamp:      fmt.Sprintf("%d", time.Now().UnixMilli()),
		DeviceType:     string(types.DeviceTypePOS),
		EntityUuid:     entityUuid,
		DeviceUuid:     deviceUuid,
		Provider:       string(types.SDK),
		PairedDeviceId: terminalConnection["deviceUuid"],
		Data: map[string]any{
			"sessionUuid": sessionUuid,
		},
	}

	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	err := posIfWs.Purchase(ctx, &req, true)
	assert.Nil(t, err)

	// Verify the response text was set correctly
	if len(mockMgrApi.Calls) > 0 {
		postConnInput := mockMgrApi.Calls[0].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput)
		var published map[string]any
		json.Unmarshal(postConnInput.Data, &published)
		publishedData := published["data"].(map[string]any)

		publishedDataBytes, _ := json.Marshal(publishedData)
		requestDataBytes, _ := json.Marshal(req.Data)
		assert.Equal(t, string(requestDataBytes), string(publishedDataBytes))
		assert.Equal(t, req.Action, published["action"])
		assert.Equal(t, req.EventId, published["eventId"])
	}
}

func TestWSPurchaseResponse(t *testing.T) {
	entityUuid := uuid.NewString()
	pos := insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.SDK, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	sessionUuid := uuid.NewString()
	deviceUuid := uuid.NewString()
	req := PosConnectorRequest{
		EventId:       uuid.NewString(),
		Action:        string(PURCHASE),
		Timestamp:     fmt.Sprintf("%d", time.Now().UnixMilli()),
		DeviceType:    string(types.DeviceTypeTerminal),
		EntityUuid:    entityUuid,
		DeviceUuid:    deviceUuid,
		Provider:      string(types.SDK),
		PosDeviceUuid: pos["deviceUuid"],
		Data: map[string]any{
			"amount":            1000,
			"approvalCode":      uuid.NewString(),
			"caid":              uuid.NewString(),
			"cardExpiryDate":    "2808",
			"cardholderUuid":    uuid.NewString(),
			"cardMedia":         "PICC",
			"catid":             "001123",
			"cvm":               "NO_CVM",
			"deviceUuid":        deviceUuid,
			"isoProcessingCode": "003000",
			"panMasked":         "...5555",
			"receiptData": map[string]any{
				"aid":      "A0000000031010",
				"cardType": "VISA CREDIT",
			},
			"responseText":    "APPROVED",
			"responseCode":    "00",
			"rrn":             uuid.NewString(),
			"scheme":          "VISA",
			"sessionUuid":     sessionUuid,
			"status":          "APPROVED",
			"transactionUuid": sessionUuid,
			"type":            "PURCHASE",
		},
	}

	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	err := posIfWs.Purchase(ctx, &req, true)
	assert.Nil(t, err)

	// Verify the response text was set correctly
	if len(mockMgrApi.Calls) > 0 {
		postConnInput := mockMgrApi.Calls[0].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput)
		var published map[string]any
		json.Unmarshal(postConnInput.Data, &published)
		publishedData := published["data"].(map[string]any)

		publishedDataBytes, _ := json.Marshal(publishedData)
		requestDataBytes, _ := json.Marshal(req.Data)
		assert.Equal(t, string(requestDataBytes), string(publishedDataBytes))
		assert.Equal(t, req.Action, published["action"])
		assert.Equal(t, req.EventId, published["eventId"])
	}
}

func TestWSPurchaseLifecycleEvent(t *testing.T) {
	// Setup test context and mock data
	entityUuid := uuid.NewString()
	pos := insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.IMPOS, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	// Create test request with required purchase data
	req := PosConnectorRequest{
		EventId:       uuid.NewString(),
		Action:        string(PURCHASE),
		DeviceType:    string(types.DeviceTypeTerminal),
		EntityUuid:    entityUuid,
		DeviceUuid:    uuid.NewString(),
		Provider:      string(types.IMPOS),
		PosDeviceUuid: pos["deviceUuid"],
		Data: map[string]any{
			"sessionUuid":     uuid.NewString(),
			"transactionUuid": uuid.NewString(),
			"status":          "PROCESSING",
			"type":            "PURCHASE",
		},
	}

	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	err := posIfWs.Purchase(ctx, &req, false)

	// Verify results
	assert.Nil(t, err)
	if len(mockMgrApi.Calls) > 0 {
		postConnInput := mockMgrApi.Calls[0].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput)
		var published map[string]any
		json.Unmarshal(postConnInput.Data, &published)
		publishedData := published["data"].(map[string]any)

		publishedDataBytes, _ := json.Marshal(publishedData)
		requestDataBytes, _ := json.Marshal(req.Data)
		assert.Equal(t, string(requestDataBytes), string(publishedDataBytes))
		assert.Equal(t, req.Action, published["action"])
		assert.Equal(t, req.EventId, published["eventId"])
	}
}

func TestWSPurchaseWrongInput(t *testing.T) {
	defer func() {
		err := recover()
		assert.NotNil(t, err)
	}()
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	posIfWs.Purchase(ctx, &PosConnectorRequest{}, true)
	assert.Fail(t, "should have panicked")
}

func TestWSPurchaseLifecycleEventWrongInput(t *testing.T) {
	defer func() {
		err := recover()
		assert.NotNil(t, err)
	}()
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	posIfWs.Purchase(ctx, &PosConnectorRequest{
		Data: map[string]any{
			"sessionUuid":     uuid.NewString(),
			"transactionUuid": uuid.NewString(),
			"status":          "APPROVED",
			"type":            "PURCHASE",
			"terminalEvent":   false,
		},
	}, false)
	assert.Fail(t, "should have panicked")
}

func TestWSGetTerminalPairedDevices(t *testing.T) {
	t.Run("should return paired POS device with connection", func(t *testing.T) {
		entityUuid := uuid.NewString()
		terminalUuid := uuid.NewString()
		posUuid := uuid.NewString()

		// Create pairing record
		pairingRecord := map[string]any{
			"id":         uuid.NewString(),
			"type":       string(types.POSINTERFACE_PAIR),
			"deviceUuid": posUuid,
			"deviceType": string(types.DeviceTypeTerminal),
			"venueId":    terminalUuid,
			"entityUuid": entityUuid,
			"provider":   string(types.IMPOS),
			"status":     "ACTIVE",
			"timestamp":  time.Now().Format(time.RFC3339),
		}

		item, err := attributevalue.MarshalMap(pairingRecord)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		// Create active POS connection
		timestamp := time.Now().Format(time.RFC3339)
		posConn := map[string]string{
			"id":            uuid.NewString(),
			"deviceUuid":    posUuid,
			"type":          string(types.CONNECTION_CORE),
			"entityUuid":    entityUuid,
			"status":        "CONNECTED",
			"deviceType":    string(types.DeviceTypePOS),
			"provider":      string(types.IMPOS),
			"timestamp":     timestamp,
			"createdTime":   timestamp,
			"connectedTime": timestamp,
			"updatedTime":   timestamp,
		}
		item, err = attributevalue.MarshalMap(posConn)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypeTerminal),
			EntityUuid: entityUuid,
			DeviceUuid: terminalUuid,
			Provider:   string(types.IMPOS),
			VenueId:    posUuid,
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Len(t, resp.PairedDevices, 1)
		assert.Equal(t, posUuid, resp.PairedDevices[0])
		assert.Len(t, resp.ConnectedDevices, 1)
		assert.Equal(t, posUuid, resp.ConnectedDevices[0].DeviceUuid)
	})

	t.Run("should return empty when no active pairings", func(t *testing.T) {
		entityUuid := uuid.NewString()
		terminalUuid := uuid.NewString()

		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypeTerminal),
			EntityUuid: entityUuid,
			DeviceUuid: terminalUuid,
			Provider:   string(types.IMPOS),
			VenueId:    uuid.NewString(), // Non-existent venue
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Empty(t, resp.PairedDevices)
		assert.Empty(t, resp.ConnectedDevices)
	})

	t.Run("should handle both venueId and pairedDeviceId", func(t *testing.T) {
		entityUuid := uuid.NewString()
		terminalUuid := uuid.NewString()
		posUuid := uuid.NewString()

		// Create pairing with pairedDeviceId
		pairingRecord := map[string]any{
			"id":         uuid.NewString(),
			"type":       string(types.POSINTERFACE_PAIR),
			"deviceUuid": posUuid,
			"deviceType": string(types.DeviceTypeTerminal),
			"venueId":    terminalUuid,
			"entityUuid": entityUuid,
			"provider":   string(types.IMPOS),
			"status":     "ACTIVE",
			"timestamp":  time.Now().Format(time.RFC3339),
		}
		item, _ := attributevalue.MarshalMap(pairingRecord)
		dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

		req := PosConnectorRequest{
			DeviceType:     string(types.DeviceTypeTerminal),
			EntityUuid:     entityUuid,
			DeviceUuid:     terminalUuid,
			Provider:       string(types.IMPOS),
			PairedDeviceId: posUuid,
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Len(t, resp.PairedDevices, 1)
		assert.Equal(t, posUuid, resp.PairedDevices[0])
	})
}

func TestWSGetPosPairedDevices(t *testing.T) {
	t.Run("should return paired terminal device with connection", func(t *testing.T) {
		entityUuid := uuid.NewString()
		posUuid := uuid.NewString()
		terminalUuid := uuid.NewString()

		// Create pairing record
		pairingRecord := map[string]any{
			"id":         uuid.NewString(),
			"type":       string(types.POSINTERFACE_PAIR_DEVICE),
			"deviceUuid": terminalUuid,
			"deviceType": string(types.DeviceTypePOS),
			"venueId":    posUuid,
			"entityUuid": entityUuid,
			"provider":   string(types.IMPOS),
			"status":     "ACTIVE",
			"timestamp":  time.Now().Format(time.RFC3339),
		}

		item, err := attributevalue.MarshalMap(pairingRecord)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		// Create active terminal connection
		timestamp := time.Now().Format(time.RFC3339)
		terminalConn := map[string]string{
			"id":            uuid.NewString(),
			"deviceUuid":    terminalUuid,
			"type":          string(types.CONNECTION_CORE),
			"entityUuid":    entityUuid,
			"status":        "CONNECTED",
			"deviceType":    string(types.DeviceTypeTerminal),
			"provider":      string(types.IMPOS),
			"timestamp":     timestamp,
			"createdTime":   timestamp,
			"connectedTime": timestamp,
			"updatedTime":   timestamp,
		}
		item, err = attributevalue.MarshalMap(terminalConn)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypePOS),
			EntityUuid: entityUuid,
			DeviceUuid: posUuid,
			Provider:   string(types.IMPOS),
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Len(t, resp.PairedDevices, 1)
		assert.Equal(t, terminalUuid, resp.PairedDevices[0])
		assert.Len(t, resp.ConnectedDevices, 1)
		assert.Equal(t, terminalUuid, resp.ConnectedDevices[0].DeviceUuid)
	})

	t.Run("should return empty when no active pairings", func(t *testing.T) {
		entityUuid := uuid.NewString()
		posUuid := uuid.NewString()

		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypePOS),
			EntityUuid: entityUuid,
			DeviceUuid: posUuid,
			Provider:   string(types.IMPOS),
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Empty(t, resp.PairedDevices)
		assert.Empty(t, resp.ConnectedDevices)
	})

	t.Run("should handle disconnected terminal device", func(t *testing.T) {
		entityUuid := uuid.NewString()
		posUuid := uuid.NewString()
		terminalUuid := uuid.NewString()

		// Create pairing record
		pairingRecord := map[string]any{
			"id":         uuid.NewString(),
			"type":       string(types.POSINTERFACE_PAIR_DEVICE),
			"deviceUuid": terminalUuid,
			"deviceType": string(types.DeviceTypePOS),
			"venueId":    posUuid,
			"entityUuid": entityUuid,
			"provider":   string(types.IMPOS),
			"status":     "ACTIVE",
			"timestamp":  time.Now().Format(time.RFC3339),
		}
		item, err := attributevalue.MarshalMap(pairingRecord)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		// No active connections created

		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypePOS),
			EntityUuid: entityUuid,
			DeviceUuid: posUuid,
			Provider:   string(types.IMPOS),
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Len(t, resp.PairedDevices, 1)
		assert.Equal(t, terminalUuid, resp.PairedDevices[0])
		assert.Empty(t, resp.ConnectedDevices) // No connections
	})

	t.Run("should handle multiple paired devices", func(t *testing.T) {
		entityUuid := uuid.NewString()
		posUuid := uuid.NewString()
		terminal1Uuid := uuid.NewString()
		terminal2Uuid := uuid.NewString()

		// Create first pairing record
		pairingRecord1 := map[string]any{
			"id":         uuid.NewString(),
			"type":       string(types.POSINTERFACE_PAIR_DEVICE),
			"deviceUuid": terminal1Uuid,
			"deviceType": string(types.DeviceTypePOS),
			"venueId":    posUuid,
			"entityUuid": entityUuid,
			"provider":   string(types.IMPOS),
			"status":     "ACTIVE",
			"timestamp":  time.Now().Format(time.RFC3339),
		}
		item, err := attributevalue.MarshalMap(pairingRecord1)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		// Create second pairing record
		pairingRecord2 := map[string]any{
			"id":         uuid.NewString(),
			"type":       string(types.POSINTERFACE_PAIR_DEVICE),
			"deviceUuid": terminal2Uuid,
			"deviceType": string(types.DeviceTypePOS),
			"venueId":    posUuid,
			"entityUuid": entityUuid,
			"provider":   string(types.IMPOS),
			"status":     "ACTIVE",
			"timestamp":  time.Now().Format(time.RFC3339),
		}
		item, err = attributevalue.MarshalMap(pairingRecord2)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		// Create active connections for both terminals
		timestamp := time.Now().Format(time.RFC3339)
		for _, terminalUuid := range []string{terminal1Uuid, terminal2Uuid} {
			terminalConn := map[string]string{
				"id":            uuid.NewString(),
				"deviceUuid":    terminalUuid,
				"type":          string(types.CONNECTION_CORE),
				"entityUuid":    entityUuid,
				"status":        "CONNECTED",
				"deviceType":    string(types.DeviceTypeTerminal),
				"provider":      string(types.IMPOS),
				"timestamp":     timestamp,
				"createdTime":   timestamp,
				"connectedTime": timestamp,
				"updatedTime":   timestamp,
			}
			item, err = attributevalue.MarshalMap(terminalConn)
			assert.NoError(t, err)
			_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
			assert.NoError(t, err)
		}

		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypePOS),
			EntityUuid: entityUuid,
			DeviceUuid: posUuid,
			Provider:   string(types.IMPOS),
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Len(t, resp.PairedDevices, 2)
		assert.Contains(t, resp.PairedDevices, terminal1Uuid)
		assert.Contains(t, resp.PairedDevices, terminal2Uuid)
		assert.Len(t, resp.ConnectedDevices, 2)
		deviceUuids := []string{resp.ConnectedDevices[0].DeviceUuid, resp.ConnectedDevices[1].DeviceUuid}
		assert.Contains(t, deviceUuids, terminal1Uuid)
		assert.Contains(t, deviceUuids, terminal2Uuid)
	})
}

func TestWSPurchaseSDKMultiplePairedDevices(t *testing.T) {
	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	entityUuid := uuid.NewString()
	sdkDeviceUuid := uuid.NewString()
	terminalUuid1 := uuid.NewString()
	terminalUuid2 := uuid.NewString()
	terminalUuid3 := uuid.NewString()

	// Create pairing record for T1
	pairingRecordT1 := map[string]any{
		"id":             uuid.NewString(),
		"type":           string(types.POSINTERFACE_PAIR_DEVICE),
		"deviceUuid":     terminalUuid1,
		"deviceType":     string(types.DeviceTypeTerminal),
		"pairedDeviceId": sdkDeviceUuid,
		"entityUuid":     entityUuid,
		"provider":       string(types.SDK),
		"status":         "ACTIVE",
		"timestamp":      time.Now().Format(time.RFC3339),
	}
	item, err := attributevalue.MarshalMap(pairingRecordT1)
	assert.NoError(t, err)
	_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	assert.NoError(t, err)
	// Create pairing record for T2
	pairingRecordT2 := map[string]any{
		"id":             uuid.NewString(),
		"type":           string(types.POSINTERFACE_PAIR_DEVICE),
		"deviceUuid":     terminalUuid2,
		"deviceType":     string(types.DeviceTypeTerminal),
		"pairedDeviceId": sdkDeviceUuid,
		"entityUuid":     entityUuid,
		"provider":       string(types.SDK),
		"status":         "ACTIVE",
		"timestamp":      time.Now().Format(time.RFC3339),
	}
	item, err = attributevalue.MarshalMap(pairingRecordT2)
	assert.NoError(t, err)
	_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	assert.NoError(t, err)
	// Create pairing record for T3 with different pairedDeviceId
	pairingRecordT3 := map[string]any{
		"id":             uuid.NewString(),
		"type":           string(types.POSINTERFACE_PAIR_DEVICE),
		"deviceUuid":     terminalUuid2,
		"deviceType":     string(types.DeviceTypeTerminal),
		"pairedDeviceId": uuid.NewString(),
		"entityUuid":     entityUuid,
		"provider":       string(types.SDK),
		"status":         "ACTIVE",
		"timestamp":      time.Now().Format(time.RFC3339),
	}
	item, err = attributevalue.MarshalMap(pairingRecordT3)
	assert.NoError(t, err)
	_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	assert.NoError(t, err)

	// Create active connections for both terminals
	timestamp := time.Now().Format(time.RFC3339)
	terminalConnectionIds := map[string]string{
		terminalUuid1: uuid.NewString(),
		terminalUuid2: uuid.NewString(),
		terminalUuid3: uuid.NewString(),
	}
	for terminalUuid, terminalConnectionId := range terminalConnectionIds {
		terminalConn := map[string]string{
			"id":            terminalConnectionId,
			"deviceUuid":    terminalUuid,
			"type":          string(types.CONNECTION_CORE),
			"entityUuid":    entityUuid,
			"status":        "CONNECTED",
			"deviceType":    string(types.DeviceTypeTerminal),
			"provider":      string(types.SDK),
			"timestamp":     timestamp,
			"createdTime":   timestamp,
			"connectedTime": timestamp,
			"updatedTime":   timestamp,
		}
		item, err = attributevalue.MarshalMap(terminalConn)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)
	}

	req := PosConnectorRequest{
		Action:     "purchase",
		DeviceType: string(types.DeviceTypePOS),
		EntityUuid: entityUuid,
		DeviceUuid: sdkDeviceUuid,
		Provider:   string(types.SDK),
		Data: map[string]any{
			"amount":              1673,
			"caid":                "000000000038529",
			"externalReference":   "096907187377",
			"sessionUuid":         uuid.NewString(),
			"timestamp":           "2025-01-21T15:02:57+11:00",
			"tipAmount":           0,
			"transactionCurrency": "AUD",
			"transactionUuid":     uuid.NewString(),
			"type":                string(types.PURCHASE),
		},
	}

	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	posIfWs.Purchase(ctx, &req, false)

	mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 2)
	connectionPublishedTo := []string{
		*mockMgrApi.Calls[0].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput).ConnectionId,
		*mockMgrApi.Calls[1].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput).ConnectionId,
	}
	assert.ElementsMatch(t, []string{terminalConnectionIds[terminalUuid1], terminalConnectionIds[terminalUuid2]}, connectionPublishedTo)
}

func TestWSPurchaseSDKMultiplePairedDevicesError(t *testing.T) {
	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	entityUuid := uuid.NewString()
	sdkDeviceUuid := uuid.NewString()

	req := PosConnectorRequest{
		Action:     "purchase",
		DeviceType: string(types.DeviceTypePOS),
		EntityUuid: entityUuid,
		DeviceUuid: sdkDeviceUuid,
		Provider:   string(types.SDK),
		Data: map[string]any{
			"amount":              1673,
			"caid":                "000000000038529",
			"externalReference":   "096907187377",
			"sessionUuid":         uuid.NewString(),
			"timestamp":           "2025-01-21T15:02:57+11:00",
			"tipAmount":           0,
			"transactionCurrency": "AUD",
			"transactionUuid":     uuid.NewString(),
			"type":                string(types.PURCHASE),
		},
	}

	cfg := ctx.Value(coreTypes.AwsDynamodbConfig{}).(aws.Config)
	cfg.HTTPClient = &http.Client{
		Timeout: 1 * time.Nanosecond,
	}

	ctx := context.WithValue(ctx, coreTypes.AwsDynamodbConfig{}, cfg)
	dynamo := db.NewDynamoDbWithOptions(ctx, &db.DbOptions{
		MaximumRetry: 2,
		Timeout:      10,
	})

	posIfWs := NewPosInterfaceWebsocketClient(ctx).(*PosInterfaceWebsocketClient)
	posIfWs.dbClient = dynamo
	assert.Panics(t, func() {
		posIfWs.Purchase(ctx, &req, false)
	})
	mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 0)
}

func TestWSGetPairedDevicesSDKProvider(t *testing.T) {
	t.Run("should handle SDK provider for POS device", func(t *testing.T) {
		entityUuid := uuid.NewString()
		sdkDeviceUuid := uuid.NewString()
		terminalUuid := uuid.NewString()

		// Create pairing record for SDK provider
		pairingRecord := map[string]any{
			"id":             uuid.NewString(),
			"type":           string(types.POSINTERFACE_PAIR_DEVICE),
			"deviceUuid":     terminalUuid,
			"deviceType":     string(types.DeviceTypeTerminal),
			"pairedDeviceId": sdkDeviceUuid,
			"entityUuid":     entityUuid,
			"provider":       string(types.SDK),
			"status":         "ACTIVE",
			"timestamp":      time.Now().Format(time.RFC3339),
		}

		item, err := attributevalue.MarshalMap(pairingRecord)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypePOS),
			EntityUuid: entityUuid,
			DeviceUuid: sdkDeviceUuid,
			Provider:   string(types.SDK),
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Len(t, resp.PairedDevices, 1)
		assert.Equal(t, terminalUuid, resp.PairedDevices[0])
	})

	t.Run("should handle SDK provider for terminal device", func(t *testing.T) {
		entityUuid := uuid.NewString()
		terminalUuid := uuid.NewString()
		sdkDeviceUuid := uuid.NewString()

		pairingRecord := map[string]any{
			"id":             uuid.NewString(),
			"type":           string(types.POSINTERFACE_PAIR_POS),
			"deviceUuid":     sdkDeviceUuid,
			"deviceType":     string(types.DeviceTypePOS),
			"pairedDeviceId": terminalUuid,
			"entityUuid":     entityUuid,
			"provider":       string(types.SDK),
			"status":         "ACTIVE",
			"timestamp":      time.Now().Format(time.RFC3339),
		}

		item, err := attributevalue.MarshalMap(pairingRecord)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		req := PosConnectorRequest{
			DeviceType:     string(types.DeviceTypeTerminal),
			EntityUuid:     entityUuid,
			DeviceUuid:     terminalUuid,
			Provider:       string(types.SDK),
			PairedDeviceId: sdkDeviceUuid,
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Len(t, resp.PairedDevices, 1)
		assert.Equal(t, sdkDeviceUuid, resp.PairedDevices[0])
	})
}

func TestWSGetPairedDevicesInvalidData(t *testing.T) {
	t.Run("should handle invalid deviceUuid in pairing record", func(t *testing.T) {
		entityUuid := uuid.NewString()
		posUuid := uuid.NewString()

		// Create pairing record with missing deviceUuid
		pairingRecord := map[string]any{
			"id":         uuid.NewString(),
			"type":       string(types.POSINTERFACE_PAIR_DEVICE),
			"deviceType": string(types.DeviceTypePOS),
			"venueId":    posUuid,
			"entityUuid": entityUuid,
			"provider":   string(types.IMPOS),
			"status":     "ACTIVE",
			"timestamp":  time.Now().Format(time.RFC3339),
		}

		item, err := attributevalue.MarshalMap(pairingRecord)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypePOS),
			EntityUuid: entityUuid,
			DeviceUuid: posUuid,
			Provider:   string(types.IMPOS),
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Empty(t, resp.PairedDevices)
		assert.Empty(t, resp.ConnectedDevices)
	})

	t.Run("should handle empty deviceUuid for POS device", func(t *testing.T) {
		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypePOS),
			EntityUuid: uuid.NewString(),
			Provider:   string(types.IMPOS),
			DeviceUuid: "", // Empty device UUID
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Empty(t, resp.PairedDevices)
		assert.Empty(t, resp.ConnectedDevices)
	})
}

func TestWSGetPairedDevicesMultipleConnections(t *testing.T) {
	t.Run("should handle multiple connections for same device", func(t *testing.T) {
		entityUuid := uuid.NewString()
		posUuid := uuid.NewString()
		terminalUuid := uuid.NewString()

		// Create pairing record
		pairingRecord := map[string]any{
			"id":         uuid.NewString(),
			"type":       string(types.POSINTERFACE_PAIR_DEVICE),
			"deviceUuid": terminalUuid,
			"deviceType": string(types.DeviceTypePOS),
			"venueId":    posUuid,
			"entityUuid": entityUuid,
			"provider":   string(types.IMPOS),
			"status":     "ACTIVE",
			"timestamp":  time.Now().Format(time.RFC3339),
		}

		item, err := attributevalue.MarshalMap(pairingRecord)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		// Create multiple active connections for the same terminal
		timestamp := time.Now().Format(time.RFC3339)
		for i := 0; i < 3; i++ {
			terminalConn := map[string]string{
				"id":            uuid.NewString(),
				"deviceUuid":    terminalUuid,
				"type":          string(types.CONNECTION_CORE),
				"entityUuid":    entityUuid,
				"status":        "CONNECTED",
				"deviceType":    string(types.DeviceTypeTerminal),
				"provider":      string(types.IMPOS),
				"timestamp":     timestamp,
				"createdTime":   timestamp,
				"connectedTime": timestamp,
				"updatedTime":   timestamp,
			}
			item, err = attributevalue.MarshalMap(terminalConn)
			assert.NoError(t, err)
			_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
			assert.NoError(t, err)
		}

		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypePOS),
			EntityUuid: entityUuid,
			DeviceUuid: posUuid,
			Provider:   string(types.IMPOS),
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Len(t, resp.PairedDevices, 1)
		assert.Equal(t, terminalUuid, resp.PairedDevices[0])
		assert.Len(t, resp.ConnectedDevices, 3) // Should show all active connections
		for _, conn := range resp.ConnectedDevices {
			assert.Equal(t, terminalUuid, conn.DeviceUuid)
			assert.Equal(t, string(types.DeviceTypeTerminal), conn.DeviceType)
			assert.Equal(t, string(types.IMPOS), conn.Provider)
			assert.Equal(t, "CONNECTED", conn.Status)
		}
	})
}

func TestWSGetTerminalPairedDevicesEdgeCases(t *testing.T) {
	t.Run("should handle missing both venueId and pairedDeviceId", func(t *testing.T) {
		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypeTerminal),
			EntityUuid: uuid.NewString(),
			DeviceUuid: uuid.NewString(),
			Provider:   string(types.IMPOS),
			// Both VenueId and PairedDeviceId are missing
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Empty(t, resp.PairedDevices)
		assert.Empty(t, resp.ConnectedDevices)
	})

	t.Run("should handle invalid pairing data for terminal", func(t *testing.T) {
		entityUuid := uuid.NewString()
		terminalUuid := uuid.NewString()
		posUuid := uuid.NewString()

		// Create pairing record with missing required fields
		pairingRecord := map[string]any{
			"id":   uuid.NewString(),
			"type": string(types.POSINTERFACE_PAIR),
			// Missing deviceUuid field
			"deviceType": string(types.DeviceTypeTerminal),
			"venueId":    terminalUuid,
			"entityUuid": entityUuid,
			"provider":   string(types.IMPOS),
			"status":     "ACTIVE",
			"timestamp":  time.Now().Format(time.RFC3339),
		}

		item, err := attributevalue.MarshalMap(pairingRecord)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypeTerminal),
			EntityUuid: entityUuid,
			DeviceUuid: terminalUuid,
			Provider:   string(types.IMPOS),
			VenueId:    posUuid,
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Empty(t, resp.PairedDevices)
		assert.Empty(t, resp.ConnectedDevices)
	})
}

func TestWSGetPairedDevicesBasicValidation(t *testing.T) {
	t.Run("should handle nil request", func(t *testing.T) {
		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, nil)

		assert.NotNil(t, resp)
		assert.Empty(t, resp.PairedDevices)
		assert.Empty(t, resp.ConnectedDevices)
		assert.IsType(t, &PairedDevicesResponse{}, resp)
	})

	t.Run("should return nil for unsupported provider", func(t *testing.T) {
		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypePOS),
			EntityUuid: uuid.NewString(),
			DeviceUuid: uuid.NewString(),
			Provider:   "UNSUPPORTED_PROVIDER",
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.Nil(t, resp)
	})

	t.Run("should return nil for empty provider", func(t *testing.T) {
		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypePOS),
			EntityUuid: uuid.NewString(),
			DeviceUuid: uuid.NewString(),
			Provider:   "",
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.Nil(t, resp)
	})

	t.Run("should handle unsupported device type", func(t *testing.T) {
		req := PosConnectorRequest{
			DeviceType: "UNSUPPORTED_DEVICE_TYPE",
			EntityUuid: uuid.NewString(),
			DeviceUuid: uuid.NewString(),
			Provider:   string(types.IMPOS),
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Empty(t, resp.PairedDevices)
		assert.Empty(t, resp.ConnectedDevices)
	})

	t.Run("should handle empty device type", func(t *testing.T) {
		req := PosConnectorRequest{
			DeviceType: "",
			EntityUuid: uuid.NewString(),
			DeviceUuid: uuid.NewString(),
			Provider:   string(types.IMPOS),
		}

		posIfWs := NewPosInterfaceWebsocketClient(ctx)
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Empty(t, resp.PairedDevices)
		assert.Empty(t, resp.ConnectedDevices)
	})
}

func TestWSGetPairedDeviceConnectionsError(t *testing.T) {
	t.Run("should handle connection manager error", func(t *testing.T) {
		entityUuid := uuid.NewString()
		posUuid := uuid.NewString()
		terminalUuid := uuid.NewString()

		// Create pairing record
		pairingRecord := map[string]any{
			"id":         uuid.NewString(),
			"type":       string(types.POSINTERFACE_PAIR_DEVICE),
			"deviceUuid": terminalUuid,
			"deviceType": string(types.DeviceTypePOS),
			"venueId":    posUuid,
			"entityUuid": entityUuid,
			"provider":   string(types.IMPOS),
			"status":     "ACTIVE",
			"timestamp":  time.Now().Format(time.RFC3339),
		}

		item, err := attributevalue.MarshalMap(pairingRecord)
		assert.NoError(t, err)
		_, err = dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
		assert.NoError(t, err)

		// Mock connection manager to return error
		mockConnMgr := &MockConnectionManager{}
		mockConnMgr.On("GetConnectedConnectionsByDevice", mock.Anything, terminalUuid, string(types.IMPOS)).Return(nil, fmt.Errorf("mock connection error"))

		req := PosConnectorRequest{
			DeviceType: string(types.DeviceTypePOS),
			EntityUuid: entityUuid,
			DeviceUuid: posUuid,
			Provider:   string(types.IMPOS),
		}

		posIfWs := &PosInterfaceWebsocketClient{
			dbClient: dbClient,
			connMgr:  mockConnMgr,
		}
		resp := posIfWs.GetPairedDevices(ctx, &req)

		assert.NotNil(t, resp)
		assert.Len(t, resp.PairedDevices, 1) // Should still return paired device
		assert.Equal(t, terminalUuid, resp.PairedDevices[0])
		assert.Empty(t, resp.ConnectedDevices) // But no connections due to error
		mockConnMgr.AssertExpectations(t)
	})
}

func TestWSRefundResponseForwarding(t *testing.T) {
	// Setup test context
	entityUuid := uuid.NewString()

	// Create connected terminal and POS devices
	terminalConn := insertConnectionRecord(entityUuid, types.DeviceTypeTerminal, types.IMPOS, nil)
	posConn := insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.IMPOS, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	// Create refund response from POS
	sessionUuid := uuid.NewString()
	req := PosConnectorRequest{
		EventId:        uuid.NewString(),
		Action:         string(REFUND),
		DeviceType:     string(types.DeviceTypePOS),
		EntityUuid:     entityUuid,
		DeviceUuid:     posConn["deviceUuid"],
		Provider:       string(types.IMPOS),
		PairedDeviceId: terminalConn["deviceUuid"],
		Data: map[string]any{
			"sessionUuid":     sessionUuid,
			"transactionUuid": uuid.NewString(),
			"status":          "SUCCESS",
			"responseCode":    "00",
			"type":            string(REFUND),
		},
	}

	// Execute refund response handling
	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	err := posIfWs.Refund(ctx, &req, true)

	// Verify response is routed to terminal
	assert.Nil(t, err)
	mockMgrApi.AssertNumberOfCalls(t, "PostToConnection", 1)
}

func TestWSRefundMessageStructure(t *testing.T) {
	entityUuid := uuid.NewString()
	pos := insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.IMPOS, nil)

	mockMgrApi := &common.MockManagementApi{}
	mockMgrApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

	// Create test request
	sessionUuid := uuid.NewString()
	req := PosConnectorRequest{
		EventId:       uuid.NewString(),
		Action:        string(REFUND),
		DeviceType:    string(types.DeviceTypeTerminal),
		EntityUuid:    entityUuid,
		DeviceUuid:    uuid.NewString(),
		Provider:      string(types.IMPOS),
		PosDeviceUuid: pos["deviceUuid"],
		Data: map[string]any{
			"sessionUuid":     sessionUuid,
			"transactionUuid": uuid.NewString(),
			"amount":          2500,
			"currencyCode":    "USD",
			"reason":          "customer_return",
			"type":            string(REFUND),
		},
	}

	posIfWs := NewPosInterfaceWebsocketClient(ctx)
	err := posIfWs.Refund(ctx, &req, false)

	// Capture and unmarshal sent message
	assert.Nil(t, err)
	assert.Equal(t, 1, len(mockMgrApi.Calls))

	var sentPayload map[string]any
	data := mockMgrApi.Calls[0].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput).Data
	json.Unmarshal(data, &sentPayload)

	// Validate message structure
	assert.Contains(t, sentPayload, "action")
	assert.Equal(t, string(REFUND), sentPayload["action"])
	assert.Contains(t, sentPayload["data"], "sessionUuid")
	assert.Equal(t, sessionUuid, sentPayload["data"].(map[string]any)["sessionUuid"])
	assert.Contains(t, sentPayload["data"], "amount")
	assert.Equal(t, float64(2500), sentPayload["data"].(map[string]any)["amount"])
	assert.Contains(t, sentPayload["data"], "currencyCode")
	assert.Equal(t, "USD", sentPayload["data"].(map[string]any)["currencyCode"])
}

func TestWSTransactionEvent(t *testing.T) {
	entityUuid := uuid.NewString()
	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	sessionUuid := uuid.NewString()

	insertConnectionRecord(entityUuid, types.DeviceTypeTerminal, types.SDK, &terminalDeviceUuid)
	insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.SDK, &sdkDeviceUuid)

	testCases := map[string]PosConnectorRequest{
		"TERMINAL_TO_SDK": {
			EventId:        uuid.NewString(),
			Action:         string(TRANSACTION_EVENT),
			Timestamp:      fmt.Sprintf("%d", time.Now().UnixMilli()),
			DeviceType:     string(types.DeviceTypeTerminal),
			EntityUuid:     entityUuid,
			DeviceUuid:     terminalDeviceUuid,
			Provider:       string(types.SDK),
			PairedDeviceId: sdkDeviceUuid,
			Data: map[string]any{
				"sessionUuid":    sessionUuid,
				"event":          "AWITING_TIP_ENTRY",
				"displayMessage": "$50 selected",
			},
		},
		"SDK_TO_TERMINAL": {
			EventId:        uuid.NewString(),
			Action:         string(TRANSACTION_EVENT),
			Timestamp:      fmt.Sprintf("%d", time.Now().UnixMilli()),
			DeviceType:     string(types.DeviceTypePOS),
			EntityUuid:     entityUuid,
			DeviceUuid:     sdkDeviceUuid,
			Provider:       string(types.SDK),
			PairedDeviceId: terminalDeviceUuid,
			Data: map[string]any{
				"sessionUuid": sessionUuid,
				"event":       "PROCEED",
			},
		},
	}

	for name, request := range testCases {
		t.Run(name, func(t *testing.T) {
			mockMgrApi := &common.MockManagementApi{}
			mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
			ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

			posIfWs := NewPosInterfaceWebsocketClient(ctx)
			err := posIfWs.TransactionEvent(ctx, &request)
			assert.Nil(t, err)

			// Verify the response text was set correctly
			if len(mockMgrApi.Calls) > 0 {
				postConnInput := mockMgrApi.Calls[0].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput)
				var published map[string]any
				json.Unmarshal(postConnInput.Data, &published)
				publishedData := published["data"].(map[string]any)

				publishedDataBytes, _ := json.Marshal(publishedData)
				requestDataBytes, _ := json.Marshal(request.Data)
				assert.Equal(t, string(requestDataBytes), string(publishedDataBytes))
				assert.Equal(t, request.Action, published["action"])
				assert.Equal(t, request.EventId, published["eventId"])
			}
		})
	}
}

func TestWSSignatureVerification(t *testing.T) {
	entityUuid := uuid.NewString()
	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	sessionUuid := uuid.NewString()

	insertConnectionRecord(entityUuid, types.DeviceTypeTerminal, types.SDK, &terminalDeviceUuid)
	insertConnectionRecord(entityUuid, types.DeviceTypePOS, types.SDK, &sdkDeviceUuid)

	testCases := map[string]PosConnectorRequest{
		"TERMINAL_TO_SDK": {
			EventId:        uuid.NewString(),
			Action:         string(SIGNATURE_VERIFICATION),
			Timestamp:      fmt.Sprintf("%d", time.Now().UnixMilli()),
			DeviceType:     string(types.DeviceTypeTerminal),
			EntityUuid:     entityUuid,
			DeviceUuid:     terminalDeviceUuid,
			Provider:       string(types.SDK),
			PairedDeviceId: sdkDeviceUuid,
			Data: map[string]any{
				"sessionUuid": sessionUuid,
				"state":       "SIGNATURE_SUBMITTED",
				"signature":   "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAAB4CAIAAAA48Cq8AAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAABCtJREFUeJzt3cFyozAQRVE8///PmQVVLsXCipD6Sd30PasspggFNy1CwPPvBxD4dwAChAUJwoIEYUGCsCBBWJAgLEgQFiQICxKEBQnCggRhQYKwJF6F3fuyB2HZ+4gpZ1uEZSxnRjXCslRWdT6WtHFn9iIsicxJnQjLzHtcUdVBWFao6gNhQYKwDDCual7Cek3YvufnF1RV8hLWDD+R4c1XWHdfXqu3sLgwxtU3XsIau53YiIwBtpeXsOZdRrYmL8ZV7Tlhldbk9ec2M4/MZ4Z1qvMSfRfFZqN7clgnUVv9m8pZ3vPDOn6PLtu5lTOaHinCOhm2lfniqVOisEqUoZYrLNvrLdbBhlxhHRY1MO16pAvrML3YwjcZwzLBOtiWNKwFQyv5OEwa1jJpB1vesMZOefI51C9vWG8DraSdQ/0ICxKpw2Lw6KQOS4dLMcKCBGFdsHriNPNSS1ifWMVMEBYkCAsSYcJSvFZfb6f9Amrn92UxPURhmb9u1Shgvc5L8sxX7keIiVVOEcVrEckLEJGEpfj4zXKDJhtnwZIKMLEabJ9bJzVDUcOaHFo836cWNawZk5891JMO123pwpqpilz6JQqrvAnSToSA5oUPa+Cm5czNT5OdySBMWPU565wrH3drmUZrBAhr+I5ondSaqmj3CBHW8butdl7f/p7IyV4sRlhHdSezTKfxx+nG5yub4wKrFCas485adrengUW28a1vbeqpIoV1qj8d+aci/e66jT9JvLB8Yh38QFif+u9iqPckNMKyxEL5Rli3Zw+f29aDsAxQWG0wLA//BdLGT2y//Mesg6WpiWX+2syAvadz+0+XW2ZL4euK1cYX4ErL1mBYnXcjw0XGcmbFYGLVN76/PfAUqLAe5i8OPYnqt8JGZM8r7GBBrKy43fBnYQNnxeGJfN5Py4yl97F6FspbqTlZgBqP9KS15wZpz8MIl79mmp+zma29mo+nNvZ/r/GDdYeLO+93n3t5XT3iN38Qx+bfxyW8kyHasCZKF2HVLn/THDhnt47U2FGeOfoP5jSsb8q8vsU3XOGT/HlwxvTvQLCwbrl7pAYO8dDZWWHH8f4lXlge1hoP++BcvLBEBn7KPQwGt6KGtfGkMq56BAuLkxpFsLBOHsYV62BbyLDMUYm5SGFtXwcZV/0ihWVue6kPFi+sXdOCcXVLmLCcTBeq6hQmrL2cZB1IjLAUy1B/KyyCA2KEtRFVjYkUFqc2kABhKa5v6kf82v+Mpu8KEJa5MpdGMVQ1I0xY6rNbDq3yCWOqGuM9LNt18LKY9xf1Q+tUNcx7WCeTE9wopt6+kwd844oR1t33Zxov1RzfP93K1TPj0cUIyxDRrOE9rOe9vpKE97AQFGFBgrAgQViQICxIEBYkCAsShAUJwoIEYUGCsCBBWJAgLEgQFiQICxL/Ac9YZ/Nrl/HmAAAAAElFTkSuQmCC",
			},
		},
		"SDK_TO_TERMINAL": {
			EventId:        uuid.NewString(),
			Action:         string(TRANSACTION_EVENT),
			Timestamp:      fmt.Sprintf("%d", time.Now().UnixMilli()),
			DeviceType:     string(types.DeviceTypePOS),
			EntityUuid:     entityUuid,
			DeviceUuid:     sdkDeviceUuid,
			Provider:       string(types.SDK),
			PairedDeviceId: terminalDeviceUuid,
			Data: map[string]any{
				"sessionUuid": sessionUuid,
				"isValid":     true,
			},
		},
	}

	for name, request := range testCases {
		t.Run(name, func(t *testing.T) {
			mockMgrApi := &common.MockManagementApi{}
			mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
			ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mockMgrApi)

			posIfWs := NewPosInterfaceWebsocketClient(ctx)
			err := posIfWs.SignatureVerification(ctx, &request)
			assert.Nil(t, err)

			// Verify the response text was set correctly
			if len(mockMgrApi.Calls) > 0 {
				postConnInput := mockMgrApi.Calls[0].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput)
				var published map[string]any
				json.Unmarshal(postConnInput.Data, &published)
				publishedData := published["data"].(map[string]any)

				publishedDataBytes, _ := json.Marshal(publishedData)
				requestDataBytes, _ := json.Marshal(request.Data)
				assert.Equal(t, string(requestDataBytes), string(publishedDataBytes))
				assert.Equal(t, request.Action, published["action"])
				assert.Equal(t, request.EventId, published["eventId"])
			}
		})
	}
}
