package protocol

import (
	"context"
	"fmt"
	"runtime/debug"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	dynamodbTypes "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

type eventItem struct {
	PosConnectorRequest
	Id          string `dynamodbav:"id"`
	Ttl         *int64 `dynamodbav:"ttl,omitempty" json:"omitempty"`
	Type        string `dynamodbav:"type"`
	Status      string `dynamodbav:"status"`
	CreatedTime string `dynamodbav:"createdTime"`
}

type PosConnectorProtocolIf interface {
	HandleAction(ctx context.Context, data *map[string]any, mgrApiMap map[string]common.ManagementApi) *PosConnectorResponse
	HandleWebhook(ctx context.Context, event *events.APIGatewayProxyRequest, mgrApi map[string]common.ManagementApi) *PosConnectorResponse
	HandleDisconnect(ctx context.Context, conn *PosConnectorDisconnectRequest) *PosConnectorResponse
	HandlePosConnectionStatusCron(ctx context.Context, mgrApiMap map[string]common.ManagementApi)
	UnsubscribeConnection(ctx context.Context, connId string)
}

type PosInterfaceClient interface {
	GetOrder(ctx context.Context, request *PosConnectorRequest) *DeviceOrder
	GetOrders(ctx context.Context, request *PosConnectorRequest) []DeviceOrder
	SubscribeOrdersUpdate(ctx context.Context, request *PosConnectorRequest) *SubscriptionResponse
	CheckOrdersBalances(ctx context.Context, request *PosConnectorRequest)
	GetPosConnectionStatus(ctx context.Context, request *PosConnectorRequest) *PosConnectionStatus
	Payment(ctx context.Context, request *PosConnectorRequest) (*PaymentResponse, *PosConnectorError)
	UpdateOrderItem(ctx context.Context, request *PosConnectorRequest) *DeviceOrder
	Purchase(ctx context.Context, request *PosConnectorRequest, isPurchaseResponse bool) *PosConnectorError
	Refund(ctx context.Context, request *PosConnectorRequest, isRefundResponse bool) *PosConnectorError
	Reversal(ctx context.Context, request *PosConnectorRequest, isReversalResponse bool) *PosConnectorError
	Cancel(ctx context.Context, request *PosConnectorRequest) *PosConnectorError
	GetPairedDevices(ctx context.Context, request *PosConnectorRequest) *PairedDevicesResponse
	TransactionEvent(ctx context.Context, request *PosConnectorRequest) *PosConnectorError
	SignatureVerification(ctx context.Context, request *PosConnectorRequest) *PosConnectorError
}

type PosConnectorProtocol struct {
	dbClient  *db.DynamoDb
	tableName string
	connMgr   connection.IfConnectionManager
}

var subscriberDbFormat = "%s%s.%s"

func NewFromContext(ctx context.Context) PosConnectorProtocolIf {
	dbClient := db.NewDynamoDb(ctx)
	return &PosConnectorProtocol{dbClient: dbClient, tableName: common.PairingTableName(), connMgr: connection.New(ctx, dbClient)}
}

func (p *PosConnectorProtocol) handleSubOrdersUpdate(ctx context.Context, request *PosConnectorRequest) *SubscriptionResponse {
	if request.DeviceType == string(types.DeviceTypePOS) {
		return p.buildPosInterface(ctx, request.Provider, request.DeviceType).SubscribeOrdersUpdate(ctx, request)
	}
	p.subscribe(ctx, request)
	return &SubscriptionResponse{Status: string(connection.CONNECTED)}
}

func (p *PosConnectorProtocol) handleSubPosConnectionStatus(ctx context.Context, request *PosConnectorRequest) *SubscriptionResponse {
	p.subscribe(ctx, request)
	return &SubscriptionResponse{Status: string(connection.CONNECTED)}
}

func (p *PosConnectorProtocol) handleUnsubOrdersUpdate(ctx context.Context, request *PosConnectorRequest) *SubscriptionResponse {
	p.unsubscribeAction(ctx, request.SiteUuid, string(SUB_ORDERS_UPDATE))
	return &SubscriptionResponse{Status: string(connection.DISCONNECTED)}
}

func (p *PosConnectorProtocol) handleUnsubPosConnectionStatus(ctx context.Context, request *PosConnectorRequest) *SubscriptionResponse {
	p.unsubscribeAction(ctx, request.SiteUuid, string(SUB_POS_CONNECTION_STATUS))
	return &SubscriptionResponse{Status: string(connection.DISCONNECTED)}
}

func (p *PosConnectorProtocol) handleGetOrder(ctx context.Context, request *PosConnectorRequest) *DeviceOrder {
	return p.buildPosInterface(ctx, request.Provider, request.DeviceType).GetOrder(ctx, request)
}

func (p *PosConnectorProtocol) handleUpdateOrderItem(ctx context.Context, request *PosConnectorRequest) *DeviceOrder {
	return p.buildPosInterface(ctx, request.Provider, request.DeviceType).UpdateOrderItem(ctx, request)
}

func (p *PosConnectorProtocol) handleGetOrders(ctx context.Context, request *PosConnectorRequest) {
	p.buildPosInterface(ctx, request.Provider, request.DeviceType).GetOrders(ctx, request)
}

func (p *PosConnectorProtocol) handleGetPosConnectionStatus(ctx context.Context, request *PosConnectorRequest) *PosConnectionStatus {
	return p.buildPosInterface(ctx, request.Provider, request.DeviceType).GetPosConnectionStatus(ctx, request)
}

func (p *PosConnectorProtocol) handlePing() map[string]string {
	return map[string]string{"response": "pong"}
}

func (p *PosConnectorProtocol) handlePayment(ctx context.Context, request *PosConnectorRequest) (*PaymentResponse, *PosConnectorError) {
	return p.buildPosInterface(ctx, request.Provider, request.DeviceType).Payment(ctx, request)
}

func (p *PosConnectorProtocol) handlePurchase(ctx context.Context, request *PosConnectorRequest, eventId string, action string, mgrApiMap map[string]common.ManagementApi) (any, *PosConnectorError) {
	requestData := request.Data.(map[string]any)
	_, isPurchaseResponse := requestData["responseCode"]

	ctx, purchaseBase, err := p.newTransactionBase(ctx, request)

	if err != nil {
		logger.Error(ctx, err.Error())
		return nil, err
	}

	mgrApi := common.GetMgrApiFromMap(ctx, mgrApiMap, request.ConnectionEndpoint)

	if isPurchaseResponse {
		purchaseAck := TransactionResponseStatus{SessionUuid: purchaseBase.SessionUuid, Status: ACKNOWLEDGED, Type: types.TransactionType(purchaseBase.Type)}
		logger.Info(ctx, fmt.Sprintf("Send purchase ack eventId: %s, sessionUuid: %s", request.EventId, purchaseAck.SessionUuid))
		resp := PosConnectorResponse{
			EventId:   eventId,
			Action:    action,
			Timestamp: getServerTimestamp(),
			Data:      purchaseAck,
		}
		PostToConnection(ctx, mgrApi, request.ConnectionId, resp.Bytes(), "device", request.DeviceUuid)
	}
	return p.purchase(ctx, request, isPurchaseResponse, purchaseBase.SessionUuid)
}

func (p *PosConnectorProtocol) handleCancel(ctx context.Context, request *PosConnectorRequest) (any, *PosConnectorError) {
	posconnectorError := p.buildPosInterface(ctx, request.Provider, request.DeviceType).Cancel(ctx, request)
	return nil, posconnectorError
}

func (p *PosConnectorProtocol) handleRefund(ctx context.Context, request *PosConnectorRequest, eventId string, action string, mgrApiMap map[string]common.ManagementApi) (any, *PosConnectorError) {
	requestData := request.Data.(map[string]any)
	_, isRefundResponse := requestData["responseCode"]

	ctx, purchaseBase, err := p.newTransactionBase(ctx, request)

	if err != nil {
		logger.Error(ctx, err.Error())
		return nil, err
	}

	if isRefundResponse {
		refundAck := TransactionResponseStatus{SessionUuid: purchaseBase.SessionUuid, Status: ACKNOWLEDGED, Type: types.TransactionType(purchaseBase.Type)}
		logger.Info(ctx, fmt.Sprintf("Send refund ack eventId: %s, sessionUuid: %s", request.EventId, refundAck.SessionUuid))
		resp := PosConnectorResponse{
			EventId:   eventId,
			Action:    action,
			Timestamp: getServerTimestamp(),
			Data:      refundAck,
		}
		mgrApi := common.GetMgrApiFromMap(ctx, mgrApiMap, request.ConnectionEndpoint)
		PostToConnection(ctx, mgrApi, request.ConnectionId, resp.Bytes(), "device", request.DeviceUuid)
	}
	return p.refund(ctx, request, isRefundResponse, purchaseBase.SessionUuid)
}

func (p *PosConnectorProtocol) handleReversal(ctx context.Context, request *PosConnectorRequest, eventId string, action string, mgrApiMap map[string]common.ManagementApi) (any, *PosConnectorError) {
	requestData := request.Data.(map[string]any)
	_, isReversalResponse := requestData["responseCode"]

	ctx, purchaseBase, err := p.newTransactionBase(ctx, request)

	if err != nil {
		logger.Error(ctx, err.Error())
		return nil, err
	}

	ctx = context.WithValue(ctx, logger.AggregateId{}, purchaseBase.SessionUuid)

	if isReversalResponse {
		reversalAck := TransactionResponseStatus{SessionUuid: purchaseBase.SessionUuid, Status: ACKNOWLEDGED, Type: types.TransactionType(purchaseBase.Type)}
		logger.Info(ctx, fmt.Sprintf("Send reversal ack eventId: %s, sessionUuid: %s", request.EventId, reversalAck.SessionUuid))
		resp := PosConnectorResponse{
			EventId:   eventId,
			Action:    action,
			Timestamp: getServerTimestamp(),
			Data:      reversalAck,
		}
		mgrApi := common.GetMgrApiFromMap(ctx, mgrApiMap, request.ConnectionEndpoint)
		PostToConnection(ctx, mgrApi, request.ConnectionId, resp.Bytes(), "device", request.DeviceUuid)
	}
	return p.reversal(ctx, request, isReversalResponse, purchaseBase.SessionUuid)
}

func (p *PosConnectorProtocol) handleGetPairedDevices(ctx context.Context, request *PosConnectorRequest) *PairedDevicesResponse {
	return p.buildPosInterface(ctx, request.Provider, request.DeviceType).GetPairedDevices(ctx, request)
}

func (p *PosConnectorProtocol) handleTransactionEvent(ctx context.Context, request *PosConnectorRequest) (any, *PosConnectorError) {
	ctx, _, err := p.newTransactionBase(ctx, request)

	if err != nil {
		logger.Error(ctx, err.Error())
		return nil, err
	}

	return nil, p.buildPosInterface(ctx, request.Provider, request.DeviceType).TransactionEvent(ctx, request)
}

func (p *PosConnectorProtocol) handleSignatureVerification(ctx context.Context, request *PosConnectorRequest) (any, *PosConnectorError) {
	ctx, _, err := p.newTransactionBase(ctx, request)

	if err != nil {
		logger.Error(ctx, err.Error())
		return nil, err
	}

	return nil, p.buildPosInterface(ctx, request.Provider, request.DeviceType).SignatureVerification(ctx, request)
}

func (p *PosConnectorProtocol) HandleAction(ctx context.Context, data *map[string]any, mgrApiMap map[string]common.ManagementApi) (resp *PosConnectorResponse) {
	eventId, _ := (*data)["eventId"].(string)
	action, _ := (*data)["action"].(string)
	defer func(ctx context.Context, eventId string, action string) {
		if err := recover(); err != nil {
			logger.Error(ctx, fmt.Sprintf("stacktrace from panic: %s\n", string(debug.Stack())))
			err := p.recoverFromPanic(err)
			if err != nil {
				resp = &PosConnectorResponse{
					Action:    action,
					EventId:   eventId,
					Error:     err,
					Timestamp: getServerTimestamp(),
					Data:      struct{}{},
				}
			}
		}
	}(ctx, eventId, action)
	request := p.buildRequest(ctx, data)
	var errorResponse *PosConnectorError = nil
	var posData any = nil
	switch {
	case request.Action == string(SUB_ORDERS_UPDATE):
		posData = p.handleSubOrdersUpdate(ctx, request)
	case request.Action == string(SUB_POS_CONNECTION_STATUS):
		posData = p.handleSubPosConnectionStatus(ctx, request)
	case request.Action == string(UNSUB_ORDERS_UPDATE):
		p.handleUnsubOrdersUpdate(ctx, request)
	case request.Action == string(UNSUB_POS_CONNECTION_STATUS):
		posData = p.handleUnsubPosConnectionStatus(ctx, request)
	case request.Action == string(GET_ORDER):
		posData = p.handleGetOrder(ctx, request)
	case request.Action == string(UPDATE_ORDER_ITEM):
		posData = p.handleUpdateOrderItem(ctx, request)
	case request.Action == string(GET_ORDERS):
		p.handleGetOrders(ctx, request)
		return nil
	case request.Action == string(GET_POS_CONNECTION_STATUS):
		posData = p.handleGetPosConnectionStatus(ctx, request)
	case request.Action == string(PING):
		posData = p.handlePing()
	case request.Action == string(PAYMENT):
		posData, errorResponse = p.handlePayment(ctx, request)
	case request.Action == string(PURCHASE):
		posData, errorResponse = p.handlePurchase(ctx, request, eventId, action, mgrApiMap)
	case request.Action == string(CANCEL):
		posData, errorResponse = p.handleCancel(ctx, request)
	case request.Action == string(REFUND):
		posData, errorResponse = p.handleRefund(ctx, request, eventId, action, mgrApiMap)
	case request.Action == string(REVERSAL):
		posData, errorResponse = p.handleReversal(ctx, request, eventId, action, mgrApiMap)
	case request.Action == string(GET_PAIRED_DEVICES):
		posData = p.handleGetPairedDevices(ctx, request)
	case request.Action == string(TRANSACTION_EVENT):
		posData, errorResponse = p.handleTransactionEvent(ctx, request)
	case request.Action == string(SIGNATURE_VERIFICATION):
		posData, errorResponse = p.handleSignatureVerification(ctx, request)
	default:
		logger.Error(ctx, "this is invalid action "+request.Action)
		errMsg := fmt.Sprintf("Unknown action %s", request.Action)
		panic(PosConnectorError{
			Type:    INVALID_REQUEST,
			Message: errMsg,
		})
	}
	timestamp := getServerTimestamp()
	return &PosConnectorResponse{
		Timestamp: timestamp,
		EventId:   request.EventId,
		Action:    request.Action,
		Data:      posData,
		Error:     errorResponse,
	}
}

func (p *PosConnectorProtocol) HandleDisconnect(ctx context.Context, conn *PosConnectorDisconnectRequest) (resp *PosConnectorResponse) {
	defer func(ctx context.Context) {
		if err := recover(); err != nil {
			resp = &PosConnectorResponse{
				Error: &PosConnectorError{
					Type: SERVER_ERROR,
				},
			}
		}
	}(ctx)
	p.UnsubscribeConnection(ctx, conn.ConnectionId)
	timestamp := getServerTimestamp()
	return &PosConnectorResponse{
		EventId:   conn.EventId,
		Action:    conn.Action,
		Timestamp: timestamp,
	}
}

func (p *PosConnectorProtocol) recoverFromPanic(err any) (resp *PosConnectorError) {
	if posErr, ok := err.(PosConnectorError); ok {
		return &posErr
	} else if e, ok := err.(error); ok {
		return &PosConnectorError{
			Type:    SERVER_ERROR,
			Message: e.Error(),
		}
	} else {
		return &PosConnectorError{
			Type:    SERVER_ERROR,
			Message: "Unknown error",
		}
	}
}

func (p *PosConnectorProtocol) purchase(ctx context.Context, request *PosConnectorRequest, isPurchaseResponse bool, sessionUuid string) (any, *PosConnectorError) {
	// Build the pos interface according to the device type/sdk
	err := p.buildPosInterface(ctx, request.Provider, request.DeviceType).Purchase(ctx, request, isPurchaseResponse)
	if isPurchaseResponse {
		if err == nil {
			logger.Debug(ctx, "purchase success")
			return TransactionResponseStatus{SessionUuid: sessionUuid, Status: SUCCESS, Type: types.PURCHASE}, nil
		} else {
			logger.Error(ctx, "purchase failed "+err.Error())
			return TransactionResponseStatus{SessionUuid: sessionUuid, Status: FAIL, Type: types.PURCHASE}, err
		}
	}

	return nil, err
}

func (p *PosConnectorProtocol) refund(ctx context.Context, request *PosConnectorRequest, isRefundResponse bool, sessionUuid string) (any, *PosConnectorError) {
	err := p.buildPosInterface(ctx, request.Provider, request.DeviceType).Refund(ctx, request, isRefundResponse)
	if isRefundResponse {
		if err == nil {
			logger.Info(ctx, "refund success")
			return TransactionResponseStatus{SessionUuid: sessionUuid, Status: SUCCESS, Type: types.REFUND}, nil
		} else {
			logger.Error(ctx, "refund failed "+err.Error())
			return TransactionResponseStatus{SessionUuid: sessionUuid, Status: FAIL, Type: types.REFUND}, err
		}
	}

	return nil, err
}

func (p *PosConnectorProtocol) reversal(ctx context.Context, request *PosConnectorRequest, isReversalResponse bool, sessionUuid string) (any, *PosConnectorError) {
	err := p.buildPosInterface(ctx, request.Provider, request.DeviceType).Reversal(ctx, request, isReversalResponse)
	if isReversalResponse {
		if err == nil {
			logger.Info(ctx, "reversal success")
			return TransactionResponseStatus{SessionUuid: sessionUuid, Status: SUCCESS, Type: types.REVERSAL}, nil
		} else {
			logger.Error(ctx, "reversal failed "+err.Error())
			return TransactionResponseStatus{SessionUuid: sessionUuid, Status: FAIL, Type: types.REVERSAL}, err
		}
	}

	return nil, err
}

// save the subscribed event on database
func (p *PosConnectorProtocol) subscribe(ctx context.Context, request *PosConnectorRequest) {
	_, err := p.connMgr.GetConnection(ctx, request.ConnectionId)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}
	item := eventItem{
		PosConnectorRequest: *request,
		Id:                  request.EventId,
		Type:                fmt.Sprintf(subscriberDbFormat, types.SUBSCRIBER, request.SiteUuid, request.Action),
		Status:              string(connection.CONNECTED),
		CreatedTime:         time.Now().Format(time.RFC3339),
	}
	dbItem, _ := attributevalue.MarshalMap(item)
	_, err = p.dbClient.InsertItemConditionallyByIdAndType(ctx, p.tableName, &dbItem)
	if err != nil {
		if strings.Contains(err.Error(), string(db.CONDITIONAL_CHECK_FAILED)) {
			errMessage := "subscription id already exists"
			logger.Error(ctx, errMessage)
			panic(PosConnectorError{
				Type:    INVALID_REQUEST,
				Message: errMessage,
			})
		}
		logger.Error(ctx, err.Error())
		panic(err)
	}
}

// unsubscribe all data based on the connection id
func (p *PosConnectorProtocol) UnsubscribeConnection(ctx context.Context, connId string) {
	var startKey map[string]dynamodbTypes.AttributeValue
	for {
		query := p.dbClient.Query(p.tableName).Index("connectionIdGsi").Eq("connectionId", connId).Begin("type", types.SUBSCRIBER)
		if startKey != nil {
			query = query.StartKey(&startKey)
		}
		output, err := query.Exec(ctx)
		if err != nil {
			logger.Error(ctx, err.Error())
			panic(PosConnectorError{
				Type:    INVALID_REQUEST,
				Message: err.Error(),
			})
		}
		startKey = output.Raw.LastEvaluatedKey
		p.unsubscribe(ctx, output.Data)
		if startKey == nil {
			break
		}
	}
}

func (p *PosConnectorProtocol) unsubscribeAction(ctx context.Context, siteUuid string, action string) {
	output, _ := p.dbClient.Query(p.tableName).Index("typeGsi").Eq("type", fmt.Sprintf(subscriberDbFormat, types.SUBSCRIBER, siteUuid, action)).Exec(ctx)
	p.unsubscribe(ctx, output.Data)
}

func (p *PosConnectorProtocol) unsubscribe(ctx context.Context, subscribers []map[string]any) {
	for _, subscriber := range subscribers {
		id := subscriber["id"].(string)
		logger.Info(ctx, "unsubscriber "+id)
		key, _ := attributevalue.MarshalMap(map[string]any{
			"id":   subscriber["id"].(string),
			"type": subscriber["type"].(string),
		})
		err := p.dbClient.UpdateItem(ctx, p.tableName, key, map[string]any{"ttl": common.GetTtl(common.TtlInHours()), "status": string(connection.DISCONNECTED)}, nil)
		if err != nil {
			logger.Error(ctx, "Failed to unsubscriber "+id)
			logger.Error(ctx, err)
		}
	}
}

/*
Sets pairing data on the PosConnectorRequest struct
*/
func (p *PosConnectorProtocol) getPairingDataAndEnrichPCReq(ctx context.Context, pcReq *PosConnectorRequest) {
	// check pairing data
	dbClient := db.NewDynamoDb(ctx)
	output, _ := dbClient.Query(common.PairingTableName()).Index("deviceUuidGsi").
		Eq("deviceUuid", pcReq.DeviceUuid).
		Begin("type", types.POSINTERFACE_PAIR).
		AndWhere().Eq("provider", pcReq.Provider).
		AndWhere().Eq("status", "ACTIVE").
		Exec(ctx)

	if len(output.Data) == 0 {
		errMsg := fmt.Sprintf("Device %s not paired", pcReq.DeviceUuid)
		logger.Error(ctx, errMsg)
		panic(PosConnectorError{
			Type:    FORBIDDEN,
			Message: errMsg,
		})
	}

	pairingRecord := output.Data[0]

	pcReq.DeviceType = string(types.DeviceTypeTerminal)

	if strings.Contains(pairingRecord["type"].(string), string(types.POSINTERFACE_PAIR_POS)) {
		pcReq.DeviceType = string(types.DeviceTypePOS)
	}

	if pcReq.DeviceType == string(types.DeviceTypeTerminal) {
		pcReq.PosDeviceUuid, _ = pairingRecord["venueId"].(string)
		pcReq.VenueId, _ = pairingRecord["venueId"].(string)
		pcReq.PairedDeviceId, _ = pairingRecord["pairedDeviceId"].(string)
	}

	if pairedDeviceId, ok := pairingRecord["pairedDeviceId"].(string); ok {
		pcReq.PairedDeviceId = pairedDeviceId
	}

	if ls, ok := pairingRecord["locations"].([]any); ok {
		locs := make([]string, len(ls))
		for i, v := range ls {
			locs[i] = v.(string)
		}
		if len(locs) > 0 {
			pcReq.Locations = locs
		}
	}

	pcReq.SiteUuid, _ = pairingRecord["siteUuid"].(string)
	if v, ok := pairingRecord["customerUuid"]; ok {
		pcReq.CustomerUuid = v.(string)
	}
}

func (p *PosConnectorProtocol) buildRequest(ctx context.Context, request *map[string]any) *PosConnectorRequest {
	pcReq, err := NewPosConnectorRequest(ctx, request)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to create pos connector request %s", err.Error()))
		panic(PosConnectorError{
			Type:    INVALID_REQUEST,
			Message: err.Error(),
		})
	}

	connectionRecord, err := p.connMgr.GetConnectionWithConsistentRead(ctx, pcReq.ConnectionId)

	if err != nil {
		logger.Error(ctx, err.Error())
		panic(PosConnectorError{
			Type:    SERVER_ERROR,
			Message: "failed to find connection:" + err.Error(),
		})
	}

	if connectionRecord == nil {
		errorMessage := "connection not found " + pcReq.ConnectionId
		logger.Error(ctx, errorMessage)
		panic(PosConnectorError{
			Type:    INVALID_REQUEST,
			Message: errorMessage,
		})
	}

	if connectionRecord.Status != string(connection.CONNECTED) {
		errorMessage := fmt.Sprintf("connection not found %s status %s", pcReq.ConnectionId, connectionRecord.Status)
		logger.Warn(ctx, errorMessage)
		panic(PosConnectorError{
			Type:    INVALID_REQUEST,
			Message: errorMessage,
		})
	}

	pcReq.EntityUuid = connectionRecord.EntityUuid
	pcReq.DeviceUuid = connectionRecord.DeviceUuid
	pcReq.Provider = connectionRecord.Provider

	p.getPairingDataAndEnrichPCReq(ctx, pcReq)

	if v, ok := (*request)["data"]; ok {
		if data, ok := v.(map[string]any); ok {
			pcReq.Data = data
		}
	}

	if err = pcReq.validate(ctx); err != nil {
		panic(PosConnectorError{
			Type:    INVALID_REQUEST,
			Message: err.Error(),
		})
	}
	return pcReq
}

func (p *PosConnectorProtocol) getPosInterfaceEndpoint(ctx context.Context, provider types.PosProvider) string {
	dbType := fmt.Sprintf("%s%s", types.METADATA, provider)
	o, _ := p.dbClient.Query(p.tableName).Eq("id", provider).Begin("type", dbType).Exec(ctx)
	err := fmt.Errorf("cant find provider metadata %s", provider)
	if len(o.Data) == 0 {
		logger.Error(ctx, err.Error())
		panic(err)
	}
	err = fmt.Errorf("cant find provider metadata endpoint %s", provider)
	endpoint, ok := o.Data[0]["endpoint"].(string)
	if !ok {
		logger.Error(ctx, err.Error())
		panic(err)
	}
	logger.Debug(ctx, fmt.Sprintf("get pos interface endpoint %s: %s", provider, endpoint))
	return endpoint
}

func (p *PosConnectorProtocol) buildPosInterface(ctx context.Context, provider string, deviceType string) PosInterfaceClient {
	client, found := ctx.Value(common.PosInterfaceClient{}).(PosInterfaceClient)

	if found {
		return client
	}

	posProvider := types.PosProvider(provider)
	posDeviceType := types.DeviceType(deviceType)

	switch {
	case posProvider == types.HL && posDeviceType == types.DeviceTypeTerminal,
		posProvider == types.ORACLE && posDeviceType == types.DeviceTypeTerminal,
		posProvider == types.TEVALIS && posDeviceType == types.DeviceTypeTerminal:
		client = NewPosInterfaceEndpointClient(ctx, p.getPosInterfaceEndpoint(ctx, posProvider))
	case posProvider == types.IMPOS, posProvider == types.SDK:
		client = NewPosInterfaceWebsocketClient(ctx)
	default:
		panic("Unknown provider and device type")
	}

	return client
}

func (p *PosConnectorProtocol) newTransactionBase(ctx context.Context, request *PosConnectorRequest) (context.Context, *TransactionBase, *PosConnectorError) {
	requestData := request.Data.(map[string]any)
	transactionBase, err := NewTransactionBase(ctx, &requestData)
	if err != nil {
		logger.Error(ctx, err.Error())
		return ctx, nil, &PosConnectorError{
			Type:    INVALID_REQUEST,
			Message: err.Error(),
		}
	}

	ctx = context.WithValue(ctx, logger.AggregateId{}, transactionBase.SessionUuid)

	return ctx, transactionBase, nil
}
