package protocol

import (
	"context"
	"fmt"
	"runtime/debug"

	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	awsTypes "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func (p *PosConnectorProtocol) HandlePosConnectionStatusCron(ctx context.Context, mgrApiMap map[string]common.ManagementApi) {
	posConnectionStatus := p.getPosConnectionStatus(ctx, (*string)(&types.HL))
	logger.Info(ctx, "Pos Connection Status: "+*posConnectionStatus.Status)
	var lastEvaluatedKey map[string]awsTypes.AttributeValue

	for ok := true; ok; ok = (lastEvaluatedKey != nil) {
		dbQuery := p.dbClient.Query(common.PairingTableName()).Index("actionGsi").
			Eq("action", string(SUB_POS_CONNECTION_STATUS)).
			Begin("type", string(types.SUBSCRIBER)).
			Where().Eq("status", string(connection.CONNECTED))

		if lastEvaluatedKey != nil {
			dbQuery.StartKey(&lastEvaluatedKey)
		}

		output, _ := dbQuery.Exec(ctx)
		lastEvaluatedKey = output.Raw.LastEvaluatedKey
		logger.Info(ctx, fmt.Sprintf("found subscribers count %d", len(output.Data)))
		timestamp := getServerTimestamp()

		for _, data := range output.Data {
			logger.Info(ctx, fmt.Sprintf("publish to %v site %s device %s", data, data["siteUuid"], data["deviceUuid"]))
			connectionId, ok := data["connectionId"].(string)
			if !ok {
				logger.Error(ctx, fmt.Sprintf("Cant find connection for the subscriber %s", data["deviceUuid"]))
				continue
			}
			connectionEndpoint, ok := data["connectionEndpoint"].(string)
			if !ok {
				logger.Warn(ctx, fmt.Sprintf("Cant find connection endpoint for the subscriber %s", data["deviceUuid"]))
			}
			mgrApi := common.GetMgrApiFromMap(ctx, mgrApiMap, connectionEndpoint)
			resp := PosConnectorResponse{
				EventId:   data["id"].(string),
				Action:    string(SUB_POS_CONNECTION_STATUS),
				Timestamp: timestamp,
				Data:      posConnectionStatus,
			}
			o, err := mgrApi.PostToConnection(ctx, &apigatewaymanagementapi.PostToConnectionInput{
				ConnectionId: &connectionId,
				Data:         resp.Bytes(),
			})
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("failed to publish to connection %s, site %s, device %s, error: %s", connectionId, data["siteUuid"], data["deviceUuid"], err.Error()))
				continue
			}
			logger.Info(ctx, fmt.Sprintf("published to %v", *o))
		}
	}
}

func (p *PosConnectorProtocol) getPosConnectionStatus(ctx context.Context, provider *string) (resp *PosConnectionStatus) {
	defer func(ctx context.Context) {
		if err := recover(); err != nil {
			logger.Error(ctx, fmt.Sprintf("stacktrace from panic: \n%s", string(debug.Stack())))
			logger.Info(ctx, fmt.Sprintf("error occurred, returning pos connection status: DISCONNECTED for provider: %s", *provider))

			disconnectedStatus := string(connection.DISCONNECTED)
			resp = &PosConnectionStatus{
				PosInterface: provider,
				Status:       &disconnectedStatus,
			}
		}
	}(ctx)

	return p.buildPosInterface(ctx, *provider, string(types.DeviceTypeTerminal)).GetPosConnectionStatus(ctx, &PosConnectorRequest{Provider: *provider})
}
