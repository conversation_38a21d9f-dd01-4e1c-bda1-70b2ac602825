package protocol

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/test"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func TestHandlePosConnectionStatusCron(t *testing.T) {
	mockPosInterface := &MockPosInterfaceClient{}
	managementApi := &common.MockManagementApi{}
	mockMgrApiMap := map[string]common.ManagementApi{
		test.WsRestApiEndpoint: managementApi,
	}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mockPosInterface)
	p := PosConnectorProtocol{
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	status := "CONNECTED"
	posInterface := string(types.HL)
	posConnectionStatus := PosConnectionStatus{Status: &status, PosInterface: &posInterface}
	mockPosInterface.On("GetPosConnectionStatus").Return(&posConnectionStatus)
	managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)

	siteUuid := uuid.NewString()
	data := getSubscriberMap(map[string]any{
		"id":       uuid.NewString(),
		"type":     fmt.Sprintf(subscriberDbFormat, types.SUBSCRIBER, siteUuid, SUB_POS_CONNECTION_STATUS),
		"action":   string(SUB_POS_CONNECTION_STATUS),
		"siteUuid": siteUuid,
		"provider": string(types.HL),
	})
	item, _ := attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, common.PairingTableName(), &item, nil)

	p.HandlePosConnectionStatusCron(ctx, mockMgrApiMap)
	mockPosInterface.AssertExpectations(t)
	managementApi.AssertExpectations(t)
}

func TestHandlePosConnectionStatusCronWithoutConnectionId(t *testing.T) {
	mockPosInterface := &MockPosInterfaceClient{}
	managementApi := &common.MockManagementApi{}
	mockMgrApiMap := map[string]common.ManagementApi{
		test.WsRestApiEndpoint: managementApi,
	}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mockPosInterface)
	p := PosConnectorProtocol{
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	status := "CONNECTED"
	posInterface := string(types.HL)
	posConnectionStatus := PosConnectionStatus{Status: &status, PosInterface: &posInterface}
	mockPosInterface.On("GetPosConnectionStatus").Return(&posConnectionStatus)
	managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)

	siteUuid := uuid.NewString()
	data := map[string]string{
		"id":         uuid.NewString(),
		"type":       fmt.Sprintf(subscriberDbFormat, types.SUBSCRIBER, siteUuid, SUB_POS_CONNECTION_STATUS),
		"action":     string(SUB_POS_CONNECTION_STATUS),
		"status":     "CONNECTED",
		"siteUuid":   siteUuid,
		"provider":   string(types.HL),
		"deviceUuid": uuid.NewString(),
	}
	item, _ := attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, common.PairingTableName(), &item, nil)

	p.HandlePosConnectionStatusCron(ctx, mockMgrApiMap)
	mockPosInterface.AssertExpectations(t)
}

func TestFailPostToConnectionOnPosConnStatusCron(t *testing.T) {
	mockPosInterface := &MockPosInterfaceClient{}
	managementApi := &common.MockManagementApi{}
	mockMgrApiMap := map[string]common.ManagementApi{
		test.WsRestApiEndpoint: managementApi,
	}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mockPosInterface)
	p := PosConnectorProtocol{
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	status := "CONNECTED"
	posInterface := string(types.HL)
	posConnectionStatus := PosConnectionStatus{Status: &status, PosInterface: &posInterface}
	mockPosInterface.On("GetPosConnectionStatus").Return(&posConnectionStatus)
	managementApi.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, errors.New("error out post to connection"))

	siteUuid := uuid.NewString()
	data := getSubscriberMap(map[string]any{
		"type":     fmt.Sprintf(subscriberDbFormat, types.SUBSCRIBER, siteUuid, SUB_POS_CONNECTION_STATUS),
		"action":   string(SUB_POS_CONNECTION_STATUS),
		"siteUuid": siteUuid,
		"provider": string(types.HL),
	})
	item, _ := attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, common.PairingTableName(), &item, nil)

	p.HandlePosConnectionStatusCron(ctx, mockMgrApiMap)
	mockPosInterface.AssertExpectations(t)
	managementApi.AssertExpectations(t)
}

func TestHandlePanicForGetPosConnectionStatusInCronjob(t *testing.T) {
	mockPosInterface := &MockPosInterfaceClient{}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mockPosInterface)
	p := PosConnectorProtocol{
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}

	mockPosInterface.On("GetPosConnectionStatus").Panic("intended panic")

	provider := string(types.HL)
	resp := p.getPosConnectionStatus(ctx, &provider)
	assert.Equal(t, string(connection.DISCONNECTED), *resp.Status)
	assert.Equal(t, provider, *resp.PosInterface)
}
