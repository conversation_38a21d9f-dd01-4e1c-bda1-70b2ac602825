package protocol

import (
	"context"

	"github.com/aws/aws-lambda-go/events"
	"github.com/stretchr/testify/mock"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
)

type MockPosInterface struct {
	mock.Mock
}

func (m *MockPosInterface) HandleAction(ctx context.Context, data *map[string]any, mgrApiMap map[string]common.ManagementApi) *PosConnectorResponse {
	args := m.Called(ctx, data)
	return args.Get(0).(*PosConnectorResponse)
}

func (m *MockPosInterface) HandleWebhook(ctx context.Context, event *events.APIGatewayProxyRequest, mgrApiMap map[string]common.ManagementApi) *PosConnectorResponse {
	args := m.Called(ctx, event, mgrApiMap)
	first := args.Get(0)
	if first != nil {
		return first.(*PosConnectorResponse)
	}
	return nil
}

func (m *MockPosInterface) HandleDisconnect(ctx context.Context, conn *PosConnectorDisconnectRequest) *PosConnectorResponse {
	args := m.Called(ctx, conn)
	return args.Get(0).(*PosConnectorResponse)
}

func (m *MockPosInterface) HandlePosConnectionStatusCron(ctx context.Context, mgrApiMap map[string]common.ManagementApi) {
	m.Called(ctx)
}

func (m *MockPosInterface) UnsubscribeConnection(ctx context.Context, connId string) {
	m.Called(ctx, connId)
}
