package protocol

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"
	"strings"
	"testing"
	"time"

	"slices"

	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/test"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

var dbClient *db.DynamoDb
var ctx context.Context
var mgrApiMap = map[string]common.ManagementApi{}

func createPurchaseResponse(transType string) TransactionResponse {
	return TransactionResponse{
		TransactionBase: TransactionBase{
			SessionUuid:     uuid.NewString(),
			TransactionUuid: uuid.NewString(),
			Type:            transType,
			Status:          "APPROVED",
		},
		Amount:            10000,
		ResponseCode:      uuid.NewString(),
		ResponseText:      uuid.NewString(),
		Rrn:               uuid.NewString(),
		ApprovalCode:      uuid.NewString(),
		Scheme:            "MC",
		PanMasked:         uuid.NewString(),
		Caid:              uuid.NewString(),
		Catid:             uuid.NewString(),
		IsoProcessingCode: uuid.NewString(),
		CardholderUuid:    uuid.NewString(),
		CardExpiryDate:    uuid.NewString(),
		CardMedia:         "MANUAL",
		Cvm:               "NO_CVM",
	}
}

func CreateLifecycleEventTestData(action string, event string) *map[string]any {
	var testRequest = map[string]any{
		"eventId":      uuid.NewString(),
		"timestamp":    uuid.NewString(),
		"siteUuid":     uuid.NewString(),
		"entityUuid":   uuid.NewString(),
		"deviceUuid":   uuid.NewString(),
		"connectionId": uuid.NewString(),
		"action":       action,
		"data": TransactionLifecycleEvent{
			TransactionBase: TransactionBase{
				SessionUuid:     uuid.NewString(),
				TransactionUuid: uuid.NewString(),
				Type:            string(strings.ToUpper(action)),
				Status:          "PROCESSING",
			},
			TerminalEvent: event,
		},
	}
	var dbRecordTestData = map[string]string{
		"id":          testRequest["connectionId"].(string),
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  testRequest["deviceUuid"].(string),
		"entityUuid":  testRequest["entityUuid"].(string),
		"siteUuid":    testRequest["siteUuid"].(string),
		"accessToken": uuid.NewString(),
		"status":      string(connection.CONNECTED),
		"provider":    string(types.HL),
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}
	dbItem, _ := attributevalue.MarshalMap(dbRecordTestData)
	dbClient.InsertItem(ctx, test.PairingTableName, &dbItem, nil)
	return &testRequest
}

func CreateTestData(action string) *map[string]any {
	var protocolRequestTestData = map[string]any{
		"eventId":      uuid.NewString(),
		"timestamp":    uuid.NewString(),
		"siteUuid":     uuid.NewString(),
		"entityUuid":   uuid.NewString(),
		"deviceUuid":   uuid.NewString(),
		"connectionId": uuid.NewString(),
		"action":       action,
	}
	if action == string(PAYMENT) {
		protocolRequestTestData["data"] = Payment{
			OrderId:         ptr.String(uuid.NewString()),
			Amount:          ptr.Int(10),
			TransactionTime: ptr.String(uuid.NewString()),
			TransactionUuid: ptr.String(uuid.NewString()),
		}
	}

	if slices.Contains([]string{string(PURCHASE), string(REFUND), string(REVERSAL)}, action) {
		protocolRequestTestData["data"] = createPurchaseResponse(strings.ToUpper(action))
	}

	if action == string(UPDATE_ORDER_ITEM) {
		protocolRequestTestData["data"] = UpdateOrderItem{
			OrderId:    ptr.String(uuid.NewString()),
			Reference:  ptr.String(uuid.NewString()),
			DeviceTime: ptr.String(uuid.NewString()),
			Item: UpdateItem{
				ItemId:      ptr.String("123"),
				Description: ptr.String(uuid.NewString()),
				Quantity:    ptr.Int(1),
				Amount:      ptr.Int(10),
			},
		}
	}

	if action == string(GET_PAIRED_DEVICES) {
		protocolRequestTestData["data"] = PairedDevicesResponse{
			PairedDevices:    []string{},
			ConnectedDevices: []PairedDeviceConnection{},
		}
	}

	var dbRecordTestData = map[string]string{
		"id":          protocolRequestTestData["connectionId"].(string),
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  protocolRequestTestData["deviceUuid"].(string),
		"entityUuid":  protocolRequestTestData["entityUuid"].(string),
		"siteUuid":    protocolRequestTestData["siteUuid"].(string),
		"accessToken": uuid.NewString(),
		"status":      string(connection.CONNECTED),
		"provider":    string(types.HL),
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}
	dbItem, _ := attributevalue.MarshalMap(dbRecordTestData)
	dbClient.InsertItem(ctx, test.PairingTableName, &dbItem, nil)
	return &protocolRequestTestData
}

func setPairingData(ctx context.Context, t *testing.T, pairingUuid string, siteUuid string, deviceUuid string, locations []string, dbType types.DbItemType) {
	data := map[string]any{
		"id":          pairingUuid,
		"type":        fmt.Sprintf("%s%s", dbType, deviceUuid),
		"siteUuid":    siteUuid,
		"deviceUuid":  deviceUuid,
		"provider":    string(types.HL),
		"locations":   locations,
		"venueId":     uuid.NewString(),
		"status":      "ACTIVE",
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().Format(time.RFC3339),
	}
	item, _ := attributevalue.MarshalMap(data)
	_, err := dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	assert.Nil(t, err, err)
}

type MockPosInterfaceClient struct {
	mock.Mock
}

func (m *MockPosInterfaceClient) GetOrder(ctx context.Context, request *PosConnectorRequest) *DeviceOrder {
	m.Called()
	return nil
}

func (m *MockPosInterfaceClient) GetOrders(ctx context.Context, request *PosConnectorRequest) []DeviceOrder {
	args := m.Called()
	if args.Get(0) != nil {
		return args.Get(0).([]DeviceOrder)
	}
	return nil
}

func (m *MockPosInterfaceClient) CheckOrdersBalances(ctx context.Context, request *PosConnectorRequest) {
	m.Called()
}

func (m *MockPosInterfaceClient) GetPosConnectionStatus(ctx context.Context, request *PosConnectorRequest) *PosConnectionStatus {
	args := m.Called()
	return args.Get(0).(*PosConnectionStatus)
}

func (m *MockPosInterfaceClient) Payment(ctx context.Context, request *PosConnectorRequest) (*PaymentResponse, *PosConnectorError) {
	args := m.Called(ctx, request)
	if args.Get(1) != nil {
		return &PaymentResponse{Status: ptr.String(string(FAIL))}, args.Get(1).(*PosConnectorError)
	}
	if args.Get(0) != nil {
		return args.Get(0).(*PaymentResponse), nil
	}
	return &PaymentResponse{Status: ptr.String(string(SUCCESS))}, nil
}

func (m *MockPosInterfaceClient) Purchase(ctx context.Context, request *PosConnectorRequest, isPurchaseResponse bool) *PosConnectorError {
	args := m.Called(ctx, request)
	if args.Get(0) != nil {
		return args.Get(0).(*PosConnectorError)
	}
	return nil
}

func (m *MockPosInterfaceClient) Refund(ctx context.Context, request *PosConnectorRequest, isTransactionResponse bool) *PosConnectorError {
	args := m.Called(ctx, request)
	if args.Get(0) != nil {
		return args.Get(0).(*PosConnectorError)
	}
	return nil
}

func (m *MockPosInterfaceClient) Reversal(ctx context.Context, request *PosConnectorRequest, isTransactionResponse bool) *PosConnectorError {
	args := m.Called(ctx, request)
	if args.Get(0) != nil {
		return args.Get(0).(*PosConnectorError)
	}
	return nil
}

func (m *MockPosInterfaceClient) Cancel(ctx context.Context, request *PosConnectorRequest) *PosConnectorError {
	args := m.Called(ctx, request)
	if args.Get(0) != nil {
		return args.Get(0).(*PosConnectorError)
	}
	return nil
}

func (m *MockPosInterfaceClient) TransactionEvent(ctx context.Context, request *PosConnectorRequest) *PosConnectorError {
	args := m.Called(ctx, request)
	if args.Get(0) != nil {
		return args.Get(0).(*PosConnectorError)
	}
	return nil
}

func (m *MockPosInterfaceClient) SignatureVerification(ctx context.Context, request *PosConnectorRequest) *PosConnectorError {
	args := m.Called(ctx, request)
	if args.Get(0) != nil {
		return args.Get(0).(*PosConnectorError)
	}
	return nil
}

func (m *MockPosInterfaceClient) GetPairedDevices(ctx context.Context, request *PosConnectorRequest) *PairedDevicesResponse {
	args := m.Called(ctx, request)
	if args.Get(0) != nil {
		return args.Get(0).(*PairedDevicesResponse)
	}
	return nil
}

func (m *MockPosInterfaceClient) UpdateOrderItem(ctx context.Context, request *PosConnectorRequest) *DeviceOrder {
	args := m.Called(ctx, request)
	if args.Get(0) != nil {
		return args.Get(0).(*DeviceOrder)
	}
	return nil
}

func (m *MockPosInterfaceClient) SubscribeOrdersUpdate(ctx context.Context, request *PosConnectorRequest) *SubscriptionResponse {
	args := m.Called(ctx, request)
	if args.Get(0) != nil {
		return args.Get(0).(*SubscriptionResponse)
	}
	return nil
}

func init() {
	ctx = test.LoadAwsMockConfig(context.Background())
	test.SetTestEnv()
	test.SetupLocalTable(ctx)
	dbClient = db.NewDynamoDb(ctx)
	mgrApi := &common.MockManagementApi{}
	ctx = context.WithValue(ctx, common.ManagementApiConfig{}, mgrApi)
	mgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	mgrApi.On("GetConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.GetConnectionOutput{}, nil)
	mgrApi.On("GetPairedDevices", ctx, mock.Anything).Return(&PairedDevicesResponse{
		PairedDevices:    []string{},
		ConnectedDevices: []PairedDeviceConnection{},
	}, nil)
}

func TestBuildRequestDeviceType(t *testing.T) {
	mock := &MockPosInterfaceClient{}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mock)
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	testRequest := CreateTestData(string(PING))
	setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)
	req := p.buildRequest(ctx, testRequest)
	assert.Equal(t, string(types.DeviceTypeTerminal), req.DeviceType)

	testRequest = CreateTestData(string(PING))
	setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_POS)
	req = p.buildRequest(ctx, testRequest)
	assert.Equal(t, string(types.DeviceTypePOS), req.DeviceType)
}

func TestBuildRequestCrossProviderPairing(t *testing.T) {
	mock := &MockPosInterfaceClient{}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mock)
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	// HL CONNECTION
	testRequest := CreateTestData(string(PING))

	deviceUuid := (*testRequest)["deviceUuid"].(string)
	// ORACLE PAIRING
	data := map[string]any{
		"id":         uuid.NewString(),
		"type":       fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_DEVICE, deviceUuid),
		"siteUuid":   (*testRequest)["siteUuid"].(string),
		"deviceUuid": deviceUuid,
		"provider":   string(types.ORACLE),
		"locations":  []string{},
		"venueId":    uuid.NewString(),
		"status":     "ACTIVE",
	}
	item, _ := attributevalue.MarshalMap(data)
	_, err := dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	if err != nil {
		log.Println(err.Error())
	}
	assert.PanicsWithValue(t, PosConnectorError{
		Type:    FORBIDDEN,
		Message: fmt.Sprintf("Device %s not paired", deviceUuid),
	}, func() {
		p.buildRequest(ctx, testRequest)
	})
}

func TestPing(t *testing.T) {
	mock := &MockPosInterfaceClient{}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mock)
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	testRequest := CreateTestData(string(PING))
	setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)
	resp := p.HandleAction(ctx, testRequest, mgrApiMap)
	assert.Nil(t, resp.Error, resp.Error)
	assert.Equal(t, resp.EventId, (*testRequest)["eventId"])
	assert.Equal(t, resp.Action, string(PING))
	assert.Equal(t, resp.Data, map[string]string{"response": "pong"})
}

func TestPayment(t *testing.T) {
	mockPi := &MockPosInterfaceClient{}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mockPi)
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	testRequest := CreateTestData(string(PAYMENT))
	mockPi.On("Payment", mock.Anything, mock.Anything).Return(&PaymentResponse{Status: ptr.String(string(SUCCESS))}, nil).Once()
	setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)
	resp := p.HandleAction(ctx, testRequest, mgrApiMap)
	assert.Equal(t, resp.EventId, (*testRequest)["eventId"])
	assert.Equal(t, resp.Action, string(PAYMENT))
	assert.Equal(t, &PaymentResponse{Status: ptr.String(string(SUCCESS))}, resp.Data.(*PaymentResponse))

	mockPi.On("Payment", mock.Anything, mock.Anything).Return(&PaymentResponse{Status: ptr.String(string(FAIL))}, &PosConnectorError{Type: SERVER_ERROR, Message: "Some Error"}).Once()
	resp = p.HandleAction(ctx, testRequest, mgrApiMap)
	assert.Equal(t, resp.EventId, (*testRequest)["eventId"])
	assert.Equal(t, resp.Action, string(PAYMENT))
	assert.Equal(t, &PaymentResponse{Status: ptr.String(string(FAIL))}, resp.Data.(*PaymentResponse))
	assert.Equal(t, resp.Error.Type, SERVER_ERROR)
	assert.Equal(t, resp.Error.Message, "Some Error")
}

func TestTransactionLifecycleEvent(t *testing.T) {
	testCases := []types.TransactionType{
		types.PURCHASE,
		types.REVERSAL,
		types.REFUND,
	}

	for _, transactionType := range testCases {
		actionType := strings.ToLower(string(transactionType))
		methodType := cases.Title(language.English, cases.NoLower).String(actionType)

		t.Run(string(transactionType), func(t *testing.T) {
			mockPi := &MockPosInterfaceClient{}
			ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mockPi)
			p := PosConnectorProtocol{
				connMgr:   connection.New(ctx, dbClient),
				dbClient:  dbClient,
				tableName: test.PairingTableName,
			}
			testRequest := CreateLifecycleEventTestData(actionType, "TERMINAL_EVENT")
			mockPi.On(methodType, mock.Anything, mock.Anything).Return(nil).Once()
			setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)
			resp := p.HandleAction(ctx, testRequest, mgrApiMap)
			assert.Equal(t, resp.EventId, (*testRequest)["eventId"])
			assert.Equal(t, resp.Action, actionType)
			assert.Equal(t, nil, resp.Data)

			mockPi.On(methodType, mock.Anything, mock.Anything).Return(&PosConnectorError{Type: SERVER_ERROR, Message: "Some Error"}).Once()
			resp = p.HandleAction(ctx, testRequest, mgrApiMap)
			assert.Equal(t, resp.EventId, (*testRequest)["eventId"])
			assert.Equal(t, resp.Action, actionType)
			assert.Equal(t, resp.Data, nil)
			assert.Equal(t, resp.Error.Type, SERVER_ERROR)
			assert.Equal(t, resp.Error.Message, "Some Error")
		})
	}
}

func TestTransactionResponses(t *testing.T) {
	testCases := []types.TransactionType{
		types.PURCHASE,
		types.REVERSAL,
		types.REFUND,
	}

	for _, transactionType := range testCases {

		t.Run(string(transactionType), func(t *testing.T) {
			actionType := strings.ToLower(string(transactionType))
			methodType := cases.Title(language.English, cases.NoLower).String(actionType)

			mockPi := &MockPosInterfaceClient{}
			ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mockPi)
			p := PosConnectorProtocol{
				connMgr:   connection.New(ctx, dbClient),
				dbClient:  dbClient,
				tableName: test.PairingTableName,
			}
			testRequest := CreateTestData(actionType)
			mockPi.On(methodType, mock.Anything, mock.Anything).Return(nil).Once()
			setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)
			resp := p.HandleAction(ctx, testRequest, mgrApiMap)
			assert.Equal(t, resp.EventId, (*testRequest)["eventId"])
			assert.Equal(t, resp.Action, actionType)
			sessionUuid := (*testRequest)["data"].(TransactionResponse).TransactionBase.SessionUuid
			assert.Equal(t, resp.Data, TransactionResponseStatus{Status: SUCCESS, SessionUuid: sessionUuid, Type: transactionType})

			mockPi.On(methodType, mock.Anything, mock.Anything).Return(&PosConnectorError{Type: SERVER_ERROR, Message: "Some Error"}).Once()
			resp = p.HandleAction(ctx, testRequest, mgrApiMap)
			assert.Equal(t, resp.EventId, (*testRequest)["eventId"])
			assert.Equal(t, resp.Action, actionType)
			assert.Equal(t, resp.Data, TransactionResponseStatus{Status: FAIL, SessionUuid: sessionUuid, Type: transactionType})
			assert.Equal(t, resp.Error.Type, SERVER_ERROR)
			assert.Equal(t, resp.Error.Message, "Some Error")
		})
	}
}

func TestTransactionMissingSessionUuid(t *testing.T) {
	testCases := []string{string(PURCHASE), string(REVERSAL), string(REFUND), string(TRANSACTION_EVENT), string(SIGNATURE_VERIFICATION)}

	for _, testCase := range testCases {
		t.Run(testCase, func(t *testing.T) {
			mockPi := &MockPosInterfaceClient{}
			ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mockPi)
			p := PosConnectorProtocol{
				connMgr:   connection.New(ctx, dbClient),
				dbClient:  dbClient,
				tableName: test.PairingTableName,
			}
			testRequest := CreateTestData(testCase)
			(*testRequest)["data"] = map[string]any{}

			setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)

			resp := p.HandleAction(ctx, testRequest, mgrApiMap)

			assert.Equal(t, resp.EventId, (*testRequest)["eventId"])
			assert.Equal(t, resp.Action, testCase)
			assert.Equal(t, resp.Error.Type, INVALID_REQUEST)
			assert.Contains(t, resp.Error.Message, "Error:Field validation for 'SessionUuid'")
		})
	}
}

func TestGetOrder(t *testing.T) {
	mock := &MockPosInterfaceClient{}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mock)
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	testRequest := CreateTestData(string(GET_ORDER))
	setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)
	mock.On("GetOrder").Return()
	p.HandleAction(ctx, testRequest, mgrApiMap)
	mock.AssertExpectations(t)
}

func TestUpdateOrderItem(t *testing.T) {
	mockPi := &MockPosInterfaceClient{}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mockPi)
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	testRequest := CreateTestData(string(UPDATE_ORDER_ITEM))
	setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)
	mockRes := DeviceOrder{
		OrderId: ptr.String("123"),
	}
	mockPi.On("UpdateOrderItem", mock.Anything, mock.Anything).Return(&mockRes).Once()
	resp := p.HandleAction(ctx, testRequest, mgrApiMap)
	assert.Equal(t, resp.EventId, (*testRequest)["eventId"])
	assert.Equal(t, resp.Action, string(UPDATE_ORDER_ITEM))
	assert.Equal(t, &mockRes, resp.Data)
}

func TestGetOrders(t *testing.T) {
	mockPi := &MockPosInterfaceClient{}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mockPi)
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	testRequest := CreateTestData(string(GET_ORDERS))
	setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)
	mockPi.On("GetOrders").Return([]DeviceOrder{
		{
			OrderId: ptr.String("123"),
		},
	})
	p.HandleAction(ctx, testRequest, mgrApiMap)
	mockPi.AssertExpectations(t)
}

func TestGetOrdersEmptyResponse(t *testing.T) {
	mockPi := &MockPosInterfaceClient{}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mockPi)
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	testRequest := CreateTestData(string(GET_ORDERS))
	setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)
	mockPi.On("GetOrders").Return([]DeviceOrder{})
	p.HandleAction(ctx, testRequest, mgrApiMap)
	mockPi.AssertNotCalled(t, "CheckOrdersBalances")
	mockPi.AssertExpectations(t)
}

func TestActionNoPairing(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	testRequest := CreateTestData(string(GET_ORDERS))
	p.HandleAction(ctx, testRequest, mgrApiMap)
}

func TestGetPosConnectionStatus(t *testing.T) {
	mock := &MockPosInterfaceClient{}
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, mock)
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	testRequest := CreateTestData(string(GET_POS_CONNECTION_STATUS))
	setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)
	mock.On("GetPosConnectionStatus").Return()
	p.HandleAction(ctx, testRequest, mgrApiMap)
	mock.AssertExpectations(t)
}

func TestUnknownAction(t *testing.T) {
	testRequest := CreateTestData("unknown")
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	setPairingData(ctx, t, uuid.NewString(), (*testRequest)["siteUuid"].(string), (*testRequest)["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)
	resp := p.HandleAction(ctx, testRequest, mgrApiMap)
	assert.NotNil(t, resp.Error, resp.Error)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)
}

func TestSubscribeOrdersUpdate(t *testing.T) {
	request := map[string]any{
		"eventId":      uuid.NewString(),
		"timestamp":    uuid.NewString(),
		"siteUuid":     uuid.NewString(),
		"entityUuid":   uuid.NewString(),
		"deviceUuid":   uuid.NewString(),
		"connectionId": uuid.NewString(),
		"action":       string(SUB_ORDERS_UPDATE),
	}
	dbRecord := map[string]string{
		"id":          request["connectionId"].(string),
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  request["deviceUuid"].(string),
		"entityUuid":  request["entityUuid"].(string),
		"siteUuid":    request["siteUuid"].(string),
		"accessToken": uuid.NewString(),
		"status":      string(connection.CONNECTED),
		"provider":    string(types.HL),
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}
	locationIds := []string{uuid.NewString()}
	attributes, _ := attributevalue.MarshalMap(dbRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	setPairingData(ctx, t, uuid.NewString(), request["siteUuid"].(string), request["deviceUuid"].(string), locationIds, types.POSINTERFACE_PAIR_DEVICE)
	p := NewFromContext(ctx)
	resp := p.HandleAction(ctx, &request, mgrApiMap)
	assert.Nil(t, resp.Error, resp.Error)
	output, _ := dbClient.Query(test.PairingTableName).Eq("id", request["eventId"]).Exec(ctx)
	assert.Equal(t, int32(1), output.Raw.Count)
	assert.Equal(t, request["siteUuid"], output.Data[0]["siteUuid"])
	assert.Equal(t, request["eventId"], output.Data[0]["id"])
	assert.Equal(t, request["action"], output.Data[0]["action"])
	assert.Equal(t, request["deviceUuid"], output.Data[0]["deviceUuid"])
	assert.Equal(t, request["timestamp"], output.Data[0]["timestamp"])
	assert.Equal(t, request["connectionId"], output.Data[0]["connectionId"])
	assert.Equal(t, dbRecord["provider"], output.Data[0]["provider"])
	assert.Equal(t, string(connection.CONNECTED), output.Data[0]["status"])
	assert.Equal(t, fmt.Sprintf("subscriber.%s.%s", request["siteUuid"], request["action"]), output.Data[0]["type"])
	l := output.Data[0]["locations"].([]any)
	assert.Equal(t, locationIds[0], l[0].(string))
	assert.Nil(t, output.Data[0]["ttl"])
	assert.NotNil(t, output.Data[0]["createdTime"])

	unsubReq := map[string]any{
		"eventId":      uuid.NewString(),
		"timestamp":    uuid.NewString(),
		"action":       string(UNSUB_ORDERS_UPDATE),
		"connectionId": request["connectionId"].(string),
		"data": map[string]string{
			"siteUuid":   request["siteUuid"].(string),
			"entityUuid": request["entityUuid"].(string),
			"deviceUuid": request["deviceUuid"].(string),
		},
	}
	resp = p.HandleAction(ctx, &unsubReq, mgrApiMap)
	assert.Nil(t, resp.Error, resp.Error)
	assert.Equal(t, unsubReq["eventId"], resp.EventId)
	assert.Equal(t, unsubReq["action"], resp.Action)
	output, _ = dbClient.Query(test.PairingTableName).Index("typeGsi").Eq("type", fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, request["siteUuid"].(string), request["action"].(string))).Exec(ctx)
	assert.Equal(t, string(connection.DISCONNECTED), output.Data[0]["status"])
}

type ConnMgrMock struct {
	mock.Mock
}

func (cm *ConnMgrMock) Connect(ctx context.Context, connectId string, endpoint string, authorizer *types.RequestAuthorizer) error {
	return nil
}
func (cm *ConnMgrMock) Disconnect(ctx context.Context, connectId string) (*connection.ConnectionModel, error) {
	return nil, nil
}
func (cm *ConnMgrMock) GetConnection(ctx context.Context, connectId string) (*connection.ConnectionModel, error) {
	args := cm.Called(mock.Anything, connectId)
	e := args.Get(1)
	if e != nil {
		return args.Get(0).(*connection.ConnectionModel), e.(error)
	}
	return args.Get(0).(*connection.ConnectionModel), nil
}
func (cm *ConnMgrMock) GetConnectedConnectionsByDevice(ctx context.Context, deviceUuid string, provider string) ([]*connection.ConnectionModel, error) {
	return nil, nil
}
func (cm *ConnMgrMock) GetConnectionWithConsistentRead(ctx context.Context, connectId string) (*connection.ConnectionModel, error) {
	return nil, nil
}
func (cm *ConnMgrMock) MapConnectionItemToConnectionModel(connectionItem map[string]any) connection.ConnectionModel {
	return connection.ConnectionModel{}
}

func TestSubscribeOrdersUpdateNoConnection(t *testing.T) {
	connectionId := uuid.NewString()
	request := map[string]any{
		"eventId":      uuid.NewString(),
		"timestamp":    uuid.NewString(),
		"siteUuid":     uuid.NewString(),
		"entityUuid":   uuid.NewString(),
		"deviceUuid":   uuid.NewString(),
		"connectionId": connectionId,
		"action":       string(SUB_ORDERS_UPDATE),
	}
	dbRecord := map[string]string{
		"id":          connectionId,
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  request["deviceUuid"].(string),
		"entityUuid":  request["entityUuid"].(string),
		"siteUuid":    request["siteUuid"].(string),
		"accessToken": uuid.NewString(),
		"status":      string(connection.CONNECTED),
		"provider":    string(types.HL),
	}
	attributes, _ := attributevalue.MarshalMap(dbRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	mockConnMgr := ConnMgrMock{}
	locationIds := []string{uuid.NewString()}
	setPairingData(ctx, t, uuid.NewString(), request["siteUuid"].(string), request["deviceUuid"].(string), locationIds, types.POSINTERFACE_PAIR_DEVICE)
	p := PosConnectorProtocol{dbClient: dbClient, tableName: test.PairingTableName, connMgr: &mockConnMgr}
	mockConnMgr.On("GetConnection", mock.Anything, connectionId).Return(&connection.ConnectionModel{}, fmt.Errorf("connection not found %s", connectionId))
	resp := p.HandleAction(ctx, &request, mgrApiMap)
	assert.NotNil(t, resp.Error)
	assert.Equal(t, resp.Error.Message, fmt.Sprintf("connection not found %s", request["connectionId"]))

}

func TestSubscribeOrdersWithoutLocationUpdate(t *testing.T) {
	request := map[string]any{
		"eventId":      uuid.NewString(),
		"timestamp":    uuid.NewString(),
		"siteUuid":     uuid.NewString(),
		"entityUuid":   uuid.NewString(),
		"deviceUuid":   uuid.NewString(),
		"connectionId": uuid.NewString(),
		"action":       string(SUB_ORDERS_UPDATE),
	}
	dbRecord := map[string]string{
		"id":          request["connectionId"].(string),
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  request["deviceUuid"].(string),
		"entityUuid":  request["entityUuid"].(string),
		"siteUuid":    request["siteUuid"].(string),
		"accessToken": uuid.NewString(),
		"status":      string(connection.CONNECTED),
		"provider":    string(types.HL),
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}
	attributes, _ := attributevalue.MarshalMap(dbRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	setPairingData(ctx, t, uuid.NewString(), request["siteUuid"].(string), request["deviceUuid"].(string), nil, types.POSINTERFACE_PAIR_DEVICE)
	p := NewFromContext(ctx)
	resp := p.HandleAction(ctx, &request, mgrApiMap)
	assert.Nil(t, resp.Error, resp.Error)
	output, _ := dbClient.Query(test.PairingTableName).Eq("id", request["eventId"]).Exec(ctx)
	assert.Equal(t, int32(1), output.Raw.Count)
	assert.Equal(t, request["siteUuid"], output.Data[0]["siteUuid"])
	assert.Equal(t, request["eventId"], output.Data[0]["id"])
	assert.Equal(t, request["action"], output.Data[0]["action"])
	assert.Equal(t, request["deviceUuid"], output.Data[0]["deviceUuid"])
	assert.Equal(t, request["timestamp"], output.Data[0]["timestamp"])
	assert.Equal(t, request["connectionId"], output.Data[0]["connectionId"])
	assert.Equal(t, dbRecord["provider"], output.Data[0]["provider"])
	assert.Equal(t, string(connection.CONNECTED), output.Data[0]["status"])
	assert.Equal(t, fmt.Sprintf("subscriber.%s.%s", request["siteUuid"], request["action"]), output.Data[0]["type"])
	assert.Nil(t, output.Data[0]["locations"])
	assert.Nil(t, output.Data[0]["ttl"])
}

func TestSubscribePosConnectionStatus(t *testing.T) {
	request := map[string]any{
		"eventId":      uuid.NewString(),
		"timestamp":    uuid.NewString(),
		"siteUuid":     uuid.NewString(),
		"entityUuid":   uuid.NewString(),
		"deviceUuid":   uuid.NewString(),
		"connectionId": uuid.NewString(),
		"action":       string(SUB_POS_CONNECTION_STATUS),
	}
	dbRecord := map[string]string{
		"id":          request["connectionId"].(string),
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  request["deviceUuid"].(string),
		"entityUuid":  request["entityUuid"].(string),
		"siteUuid":    request["siteUuid"].(string),
		"accessToken": uuid.NewString(),
		"status":      string(connection.CONNECTED),
		"provider":    string(types.HL),
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}
	attributes, _ := attributevalue.MarshalMap(dbRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	setPairingData(ctx, t, uuid.NewString(), request["siteUuid"].(string), request["deviceUuid"].(string), []string{}, types.POSINTERFACE_PAIR_DEVICE)
	p := NewFromContext(ctx)
	resp := p.HandleAction(ctx, &request, mgrApiMap)
	assert.Nil(t, resp.Error, resp.Error)
	output, _ := dbClient.Query(test.PairingTableName).Eq("id", request["eventId"]).Exec(ctx)
	assert.Equal(t, int32(1), output.Raw.Count)
	assert.Equal(t, request["siteUuid"], output.Data[0]["siteUuid"])
	assert.Equal(t, request["eventId"], output.Data[0]["id"])
	assert.Equal(t, request["action"], output.Data[0]["action"])
	assert.Equal(t, request["deviceUuid"], output.Data[0]["deviceUuid"])
	assert.Equal(t, request["timestamp"], output.Data[0]["timestamp"])
	assert.Equal(t, request["connectionId"], output.Data[0]["connectionId"])
	assert.Equal(t, string(connection.CONNECTED), output.Data[0]["status"])
	assert.Equal(t, fmt.Sprintf("subscriber.%s.%s", request["siteUuid"], request["action"]), output.Data[0]["type"])
	assert.Nil(t, output.Data[0]["ttl"])

	unsubReq := map[string]any{
		"eventId":      uuid.NewString(),
		"timestamp":    uuid.NewString(),
		"action":       string(UNSUB_POS_CONNECTION_STATUS),
		"connectionId": request["connectionId"].(string),
		"data": map[string]string{
			"siteUuid":   request["siteUuid"].(string),
			"entityUuid": request["entityUuid"].(string),
			"deviceUuid": request["deviceUuid"].(string),
		},
	}
	resp = p.HandleAction(ctx, &unsubReq, mgrApiMap)
	assert.Nil(t, resp.Error, resp.Error)
	assert.Equal(t, unsubReq["eventId"], resp.EventId)
	assert.Equal(t, unsubReq["action"], resp.Action)
	output, _ = dbClient.Query(test.PairingTableName).Index("typeGsi").Eq("type", fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, request["siteUuid"].(string), request["action"].(string))).Exec(ctx)
	assert.Equal(t, string(connection.DISCONNECTED), output.Data[0]["status"])
	assert.NotNil(t, output.Data[0]["ttl"])
}

func TestUnsubscribe(t *testing.T) {
	subscriber1 := PosConnectorRequest{
		EventId:      uuid.NewString(),
		Timestamp:    uuid.NewString(),
		SiteUuid:     uuid.NewString(),
		EntityUuid:   uuid.NewString(),
		DeviceUuid:   uuid.NewString(),
		Action:       string(SUB_ORDERS_UPDATE),
		ConnectionId: uuid.NewString(),
	}
	subscriber2 := PosConnectorRequest{
		EventId:      uuid.NewString(),
		Timestamp:    uuid.NewString(),
		SiteUuid:     subscriber1.SiteUuid,
		EntityUuid:   subscriber1.EntityUuid,
		DeviceUuid:   uuid.NewString(),
		Action:       string(SUB_ORDERS_UPDATE),
		ConnectionId: subscriber1.ConnectionId,
	}
	subscriber3 := PosConnectorRequest{
		EventId:      uuid.NewString(),
		Timestamp:    uuid.NewString(),
		SiteUuid:     subscriber1.SiteUuid,
		EntityUuid:   uuid.NewString(),
		DeviceUuid:   uuid.NewString(),
		Action:       string(SUB_ORDERS_UPDATE),
		ConnectionId: uuid.NewString(),
	}

	p := PosConnectorProtocol{dbClient: dbClient, tableName: test.PairingTableName, connMgr: connection.New(ctx, dbClient)}
	for _, sub := range []PosConnectorRequest{subscriber1, subscriber2, subscriber3} {
		item := eventItem{
			PosConnectorRequest: sub,
			Id:                  sub.EventId,
			Type:                fmt.Sprintf(subscriberDbFormat, types.SUBSCRIBER, sub.SiteUuid, sub.Action),
			Status:              string(connection.CONNECTED),
			CreatedTime:         time.Now().Format(time.RFC3339),
		}
		dbItem, _ := attributevalue.MarshalMap(item)
		dbClient.InsertItem(ctx, test.PairingTableName, &dbItem, nil)
	}

	p.UnsubscribeConnection(ctx, subscriber1.ConnectionId)
	output, _ := dbClient.Query(test.PairingTableName).Eq("id", subscriber1.EventId).Exec(ctx)
	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, string(connection.DISCONNECTED), output.Data[0]["status"])
	assert.NotNil(t, output.Data[0]["ttl"])
	output, _ = dbClient.Query(test.PairingTableName).Eq("id", subscriber2.EventId).Exec(ctx)
	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, string(connection.DISCONNECTED), output.Data[0]["status"])
	assert.NotNil(t, output.Data[0]["ttl"])
	output, _ = dbClient.Query(test.PairingTableName).Eq("id", subscriber3.EventId).Exec(ctx)
	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, subscriber3.EventId, output.Data[0]["id"])
	assert.Equal(t, string(connection.CONNECTED), output.Data[0]["status"])
	assert.Nil(t, output.Data[0]["ttl"])
}

func TestHandleActionMissingRequiredFields(t *testing.T) {
	p := NewFromContext(ctx)
	resp := p.HandleAction(ctx, &map[string]any{
		"eventId":      uuid.NewString(),
		"timestamp":    uuid.NewString(),
		"connectionId": uuid.NewString(),
	}, mgrApiMap)
	assert.NotNil(t, resp.Error)

	resp = p.HandleAction(ctx, &map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": uuid.NewString(),
		"action":    uuid.NewString(),
	}, mgrApiMap)
	assert.NotNil(t, resp.Error)

	resp = p.HandleAction(ctx, &map[string]any{
		"connectionId": uuid.NewString(),
		"timestamp":    uuid.NewString(),
		"action":       uuid.NewString(),
	}, mgrApiMap)
	assert.NotNil(t, resp.Error)

	resp = p.HandleAction(ctx, &map[string]any{
		"connectionId": uuid.NewString(),
		"eventId":      uuid.NewString(),
		"action":       uuid.NewString(),
	}, mgrApiMap)
	assert.NotNil(t, resp.Error)
}

func TestRequestWithoutConnectionDbRecord(t *testing.T) {
	p := NewFromContext(ctx)
	resp := p.HandleAction(ctx, &map[string]any{
		"connectionId": uuid.NewString(),
		"eventId":      uuid.NewString(),
		"action":       uuid.NewString(),
		"timestamp":    uuid.NewString(),
	}, mgrApiMap)
	assert.NotNil(t, resp.Error)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)
}

func TestBuildRequestConnectionDbRecordMissingFields(t *testing.T) {
	data := map[string]any{
		"connectionId": uuid.NewString(),
		"eventId":      uuid.NewString(),
		"action":       uuid.NewString(),
		"timestamp":    uuid.NewString(),
	}
	// missing entity uuid
	dbRecord := map[string]string{
		"id":          data["connectionId"].(string),
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  uuid.NewString(),
		"accessToken": uuid.NewString(),
		"status":      string(connection.CONNECTED),
		"provider":    string(types.HL),
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}

	attributes, _ := attributevalue.MarshalMap(dbRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)

	pairing := map[string]string{
		"id":          dbRecord["deviceUuid"],
		"type":        string(types.POSINTERFACE_PAIR_DEVICE),
		"deviceUuid":  dbRecord["deviceUuid"],
		"status":      "ACTIVE",
		"provider":    string(types.HL),
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}

	pairingAttr, _ := attributevalue.MarshalMap(pairing)
	dbClient.InsertItem(ctx, test.PairingTableName, &pairingAttr, nil)
	p := NewFromContext(ctx)
	resp := p.HandleAction(ctx, &data, mgrApiMap)
	assert.NotNil(t, resp.Error, resp.Error)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)

	key := struct {
		Id   string `dynamodbav:"id"`
		Type string `dynamodbav:"type"`
	}{Id: dbRecord["id"], Type: dbRecord["type"]}
	dbClient.DeleteItem(ctx, test.PairingTableName, key)
	// missing device uuid
	dbRecord = map[string]string{
		"id":          data["connectionId"].(string),
		"type":        string(types.CONNECTION_CORE),
		"entityUuid":  uuid.NewString(),
		"accessToken": uuid.NewString(),
		"status":      string(connection.CONNECTED),
		"provider":    string(types.HL),
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}
	attributes, _ = attributevalue.MarshalMap(dbRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	resp = p.HandleAction(ctx, &data, mgrApiMap)
	assert.NotNil(t, resp.Error, resp.Error)
	assert.Equal(t, SERVER_ERROR, resp.Error.Type)

	// wrong event id type
	resp = p.HandleAction(ctx, &map[string]any{
		"connectionId": uuid.NewString(),
		"eventId":      1,
		"action":       uuid.NewString(),
		"timestamp":    uuid.NewString(),
	}, mgrApiMap)
	assert.NotNil(t, resp.Error, resp.Error)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)
}

func TestBuildRequestForDisconnectedConnection(t *testing.T) {
	connectionId := uuid.NewString()
	data := map[string]any{
		"connectionId": connectionId,
		"eventId":      uuid.NewString(),
		"action":       uuid.NewString(),
		"timestamp":    uuid.NewString(),
	}
	connectionRecord := map[string]string{
		"id":          connectionId,
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  uuid.NewString(),
		"entityUuid":  uuid.NewString(),
		"status":      string(connection.DISCONNECTED),
		"provider":    string(types.HL),
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}
	attributes, _ := attributevalue.MarshalMap(connectionRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	subRecord := map[string]string{
		"id":           data["eventId"].(string),
		"type":         fmt.Sprintf("%s%s%s", string(types.SUBSCRIBER), uuid.NewString(), data["action"].(string)),
		"deviceUuid":   uuid.NewString(),
		"entityUuid":   uuid.NewString(),
		"accessToken":  uuid.NewString(),
		"status":       string(connection.CONNECTED),
		"connectionId": connectionId,
	}
	attributes, _ = attributevalue.MarshalMap(subRecord)
	_, err := dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	assert.Nil(t, err, err)

	p := NewFromContext(ctx)
	resp := p.HandleDisconnect(ctx, &PosConnectorDisconnectRequest{
		ConnectionId: connectionId,
	})
	assert.Nil(t, resp.Error, resp.Error)
	output, err := dbClient.Query(test.PairingTableName).Eq("id", subRecord["id"]).Eq("type", subRecord["type"]).Exec(ctx)
	assert.Nil(t, err, err)
	assert.Equal(t, string(connection.DISCONNECTED), output.Data[0]["status"])

	resp = p.HandleAction(ctx, &data, mgrApiMap)
	assert.NotNil(t, resp.Error, resp.Error)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)
	assert.Equal(t, fmt.Sprintf("connection not found %s status %s", connectionId, string(connection.DISCONNECTED)), resp.Error.Message)
}

func TestPurchaseNoRecepients(t *testing.T) {
	ctx = context.WithValue(ctx, common.PosInterfaceClient{}, nil)
	connectionId := uuid.NewString()
	entityUuid := uuid.NewString()
	terminalUuid := uuid.NewString()
	sdkDeviceUuid := uuid.NewString()
	data := map[string]any{
		"connectionId": connectionId,
		"eventId":      uuid.NewString(),
		"action":       string(PURCHASE),
		"timestamp":    uuid.NewString(),
		"data": map[string]any{
			"amount":              1673,
			"caid":                "000000000038529",
			"externalReference":   "096907187377",
			"sessionUuid":         uuid.NewString(),
			"timestamp":           "2025-01-21T15:02:57+11:00",
			"tipAmount":           0,
			"transactionCurrency": "AUD",
			"transactionUuid":     uuid.NewString(),
			"type":                string(types.PURCHASE),
		},
	}
	sdkConnectionRecord := map[string]string{
		"id":          connectionId,
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  sdkDeviceUuid,
		"entityUuid":  entityUuid,
		"status":      string(connection.CONNECTED),
		"provider":    string(types.SDK),
		"deviceType":  string(types.DeviceTypePOS),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}
	attributes, _ := attributevalue.MarshalMap(sdkConnectionRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)

	sdkPairingRecord := map[string]string{
		"id":             uuid.NewString(),
		"type":           string(types.POSINTERFACE_PAIR_POS) + sdkDeviceUuid,
		"deviceUuid":     sdkDeviceUuid,
		"entityUuid":     entityUuid,
		"status":         "ACTIVE",
		"pairedDeviceId": terminalUuid,
		"provider":       string(types.SDK),
		"deviceType":     string(types.DeviceTypePOS),
		"createdTime":    time.Now().UTC().Format(time.RFC3339),
	}
	attributes, _ = attributevalue.MarshalMap(sdkPairingRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)

	terminalPairingRecord := map[string]string{
		"id":             uuid.NewString(),
		"type":           string(types.POSINTERFACE_PAIR_DEVICE) + terminalUuid,
		"deviceUuid":     terminalUuid,
		"entityUuid":     entityUuid,
		"status":         "ACTIVE",
		"pairedDeviceId": sdkDeviceUuid,
		"provider":       string(types.SDK),
		"deviceType":     string(types.DeviceTypeTerminal),
		"createdTime":    time.Now().UTC().Format(time.RFC3339),
	}
	attributes, _ = attributevalue.MarshalMap(terminalPairingRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)

	p := NewFromContext(ctx)

	resp := p.HandleAction(ctx, &data, mgrApiMap)
	assert.NotNil(t, resp.Error, resp.Error)
	assert.Equal(t, FORBIDDEN, resp.Error.Type)
	assert.Equal(t, "no connections found for paired devices", resp.Error.Message)
}

func TestHandleDisconnectFailed(t *testing.T) {
	p := NewFromContext(ctx)
	resp := p.HandleDisconnect(ctx, &PosConnectorDisconnectRequest{
		ConnectionId: "",
	})
	assert.NotNil(t, resp.Error, resp.Error)
	assert.Equal(t, SERVER_ERROR, resp.Error.Type)
}

func TestRequestActionSuccess(t *testing.T) {
	data := map[string]any{
		"connectionId": uuid.NewString(),
		"eventId":      uuid.NewString(),
		"action":       string(SUB_ORDERS_UPDATE),
		"timestamp":    uuid.NewString(),
	}
	dbRecord := map[string]string{
		"id":          data["connectionId"].(string),
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  uuid.NewString(),
		"entityUuid":  uuid.NewString(),
		"siteUuid":    uuid.NewString(),
		"accessToken": uuid.NewString(),
		"status":      string(connection.CONNECTED),
		"provider":    string(types.HL),
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}
	attributes, _ := attributevalue.MarshalMap(dbRecord)
	_, err := dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	assert.Nil(t, err, err)
	setPairingData(ctx, t, uuid.NewString(), dbRecord["siteUuid"], dbRecord["deviceUuid"], []string{}, types.POSINTERFACE_PAIR_DEVICE)
	p := NewFromContext(ctx)
	resp := p.HandleAction(ctx, &data, mgrApiMap)
	assert.NotNil(t, resp, resp)
	assert.Nil(t, resp.Error, resp.Error)

	dbRecord["customerUuid"] = uuid.NewString()
	data["eventId"] = uuid.NewString()
	attributes, _ = attributevalue.MarshalMap(dbRecord)
	_, err = dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	assert.Nil(t, err, err)
	resp = p.HandleAction(ctx, &data, mgrApiMap)
	assert.Nil(t, resp.Error, resp.Error)
}

func TestSubscriptionErrorOnDuplicatedEventIdTypePair(t *testing.T) {
	data := map[string]any{
		"connectionId": uuid.NewString(),
		"eventId":      uuid.NewString(),
		"action":       string(SUB_ORDERS_UPDATE),
		"timestamp":    uuid.NewString(),
	}
	dbRecord := map[string]string{
		"id":          data["connectionId"].(string),
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  uuid.NewString(),
		"entityUuid":  uuid.NewString(),
		"siteUuid":    uuid.NewString(),
		"accessToken": uuid.NewString(),
		"status":      string(connection.CONNECTED),
		"provider":    string(types.HL),
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}
	attributes, _ := attributevalue.MarshalMap(dbRecord)
	_, err := dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	assert.Nil(t, err, err)
	setPairingData(ctx, t, uuid.NewString(), dbRecord["siteUuid"], dbRecord["deviceUuid"], []string{}, types.POSINTERFACE_PAIR_DEVICE)
	p := NewFromContext(ctx)
	resp := p.HandleAction(ctx, &data, mgrApiMap)
	assert.NotNil(t, resp, resp)
	assert.Nil(t, resp.Error, resp.Error)

	resp = p.HandleAction(ctx, &data, mgrApiMap)
	assert.NotNil(t, resp.Error, resp.Error)
	assert.Equal(t, &PosConnectorError{Type: INVALID_REQUEST, Message: "subscription id already exists"}, resp.Error)
}

func TestRecoverFromPanicUnknownError(t *testing.T) {
	p := PosConnectorProtocol{dbClient: dbClient, tableName: test.PairingTableName}
	var resp *PosConnectorError

	testRecover := func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Error(ctx, fmt.Sprintf("receive error %s", reflect.TypeOf(err)))
				resp = p.recoverFromPanic(err)
			}
		}()
		// unknown error
		panic("unknow error")
	}
	testRecover()
	assert.NotNil(t, resp, resp)
	assert.Equal(t, SERVER_ERROR, resp.Type)
	assert.Equal(t, "Unknown error", resp.Message)
}

func TestRecoverFromPanicRegularError(t *testing.T) {
	p := PosConnectorProtocol{dbClient: dbClient, tableName: test.PairingTableName}
	var resp *PosConnectorError

	testRecover := func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Error(ctx, fmt.Sprintf("receive error %s", reflect.TypeOf(err)))
				resp = p.recoverFromPanic(err)
			}
		}()
		// unknown error
		panic(errors.New("regular error"))
	}
	testRecover()
	assert.NotNil(t, resp, resp)
	assert.Equal(t, SERVER_ERROR, resp.Type)
	assert.Equal(t, "regular error", resp.Message)
}

func TestGetPosInterfaceEndpointNoMetaData(t *testing.T) {
	defer func() {
		recover()
	}()
	p := PosConnectorProtocol{dbClient: dbClient, tableName: test.PairingTableName}
	p.getPosInterfaceEndpoint(ctx, "HL")
	assert.Fail(t, "should have panic")
}

func TestGetPosInterfaceEndpointNoProvider(t *testing.T) {
	defer func() { recover() }()
	p := PosConnectorProtocol{dbClient: dbClient, tableName: test.PairingTableName}
	d := map[string]string{
		"id":   "TEST",
		"type": string(types.METADATA),
	}
	item, _ := attributevalue.MarshalMap(d)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	p.getPosInterfaceEndpoint(ctx, "TEST")
	assert.Fail(t, "should have panic")
}

func TestGetPosInterfaceEndpointNoEndpoint(t *testing.T) {
	defer func() { recover() }()
	p := PosConnectorProtocol{dbClient: dbClient, tableName: test.PairingTableName}
	d := map[string]string{
		"id":   string(types.HL),
		"type": fmt.Sprintf("%s%s", types.METADATA, types.HL),
	}
	item, _ := attributevalue.MarshalMap(d)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	p.getPosInterfaceEndpoint(ctx, types.HL)
	assert.Fail(t, "should have panic")
}

func TestGetPosInterfaceEndpoint(t *testing.T) {
	p := PosConnectorProtocol{dbClient: dbClient, tableName: test.PairingTableName}
	d := map[string]string{
		"id":       string(types.HL),
		"type":     fmt.Sprintf("%s%s", types.METADATA, types.HL),
		"endpoint": "mockendpoint",
	}
	item, _ := attributevalue.MarshalMap(d)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	e := p.getPosInterfaceEndpoint(ctx, types.HL)
	assert.Equal(t, d["endpoint"], e)
}
