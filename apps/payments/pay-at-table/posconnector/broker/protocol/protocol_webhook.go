package protocol

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"

	"slices"

	"github.com/aws/aws-lambda-go/events"
	dynamodbTypes "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func (p *PosConnectorProtocol) validateRequestSiteUuid(ctx context.Context, request *PosConnectorRequest) {
	if request.SiteUuid == "" {
		err := fmt.Errorf("siteUuid is required")
		logger.Error(ctx, err.Error())
		panic(PosConnectorError{Type: INVALID_REQUEST, Message: err.Error()})
	}
}

func (p *PosConnectorProtocol) validateRequestDeviceUuid(ctx context.Context, request *PosConnectorRequest) {
	if request.DeviceUuid == "" {
		err := fmt.Errorf("deviceUuid is required")
		logger.Error(ctx, err.Error())
		panic(PosConnectorError{Type: INVALID_REQUEST, Message: err.Error()})
	}
}

func (p *PosConnectorProtocol) handlePurchaseWebhook(ctx context.Context, request *PosConnectorRequest, mgrApiMap map[string]common.ManagementApi) {
	p.validateRequestDeviceUuid(ctx, request)
	var pr *PurchaseRequest
	requestData := request.Data.(map[string]any)
	pr, err := NewPurchaseRequest(ctx, &requestData)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(PosConnectorError{Type: INVALID_REQUEST, Message: err.Error()})
	}
	ctx = context.WithValue(ctx, logger.AggregateId{}, *pr.SessionUuid)
	ctx = logger.AddMetadata(ctx, "transactionType", strings.ToUpper(string(PURCHASE)))
	ctx = logger.AddMetadata(ctx, "transactionUuid", *pr.SessionUuid)
	p.publishTransactionRequest(ctx, request, pr, mgrApiMap)
}

func (p *PosConnectorProtocol) handleRefundWebhook(ctx context.Context, request *PosConnectorRequest, mgrApiMap map[string]common.ManagementApi) {
	p.validateRequestDeviceUuid(ctx, request)
	requestData := request.Data.(map[string]any)
	pr, err := NewRefundRequest(ctx, &requestData)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(PosConnectorError{Type: INVALID_REQUEST, Message: err.Error()})
	}
	ctx = context.WithValue(ctx, logger.AggregateId{}, *pr.SessionUuid)
	ctx = logger.AddMetadata(ctx, "transactionType", strings.ToUpper(string(REFUND)))
	ctx = logger.AddMetadata(ctx, "sessionUuid", *pr.SessionUuid)
	ctx = logger.AddMetadata(ctx, "transactionUuid", *pr.TransactionUuid)

	p.publishTransactionRequest(ctx, request, pr, mgrApiMap)
}

func (p *PosConnectorProtocol) handleReversalWebhook(ctx context.Context, request *PosConnectorRequest, mgrApiMap map[string]common.ManagementApi) {
	p.validateRequestDeviceUuid(ctx, request)
	var pr *ReversalRequest
	requestData := request.Data.(map[string]any)
	pr, err := NewReversalRequest(ctx, &requestData)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(PosConnectorError{Type: INVALID_REQUEST, Message: err.Error()})
	}
	ctx = context.WithValue(ctx, logger.AggregateId{}, *pr.SessionUuid)
	ctx = logger.AddMetadata(ctx, "transactionType", strings.ToUpper(string(REVERSAL)))
	ctx = logger.AddMetadata(ctx, "transactionUuid", *pr.SessionUuid)
	p.publishTransactionRequest(ctx, request, pr, mgrApiMap)
}

func (p *PosConnectorProtocol) setWebhookContextMetadata(ctx context.Context, provider *string, request *PosConnectorRequest) context.Context {
	ctx = logger.AddMetadata(ctx, "provider", provider)
	if request.SiteUuid != "" {
		ctx = logger.AddMetadata(ctx, "siteUuid", request.SiteUuid)
	}
	if request.DeviceUuid != "" {
		ctx = logger.AddMetadata(ctx, "deviceUuid", request.DeviceUuid)
	}
	return ctx
}

func (p *PosConnectorProtocol) HandleWebhook(ctx context.Context, event *events.APIGatewayProxyRequest, mgrApiMap map[string]common.ManagementApi) (resp *PosConnectorResponse) {
	defer func(ctx context.Context) {
		if err := recover(); err != nil {
			err := p.recoverFromPanic(err)
			if err.Type != INVALID_REQUEST {
				logger.Error(ctx, err)
			}
			if err != nil {
				resp = &PosConnectorResponse{
					Error: err,
				}
			}
		}
	}(ctx)
	provider, request := p.authorizeWebhookEvent(ctx, event)

	ctx = p.setWebhookContextMetadata(ctx, provider, request)

	logger.Info(ctx, fmt.Sprintf("get provider from webhook event %s, site %s", *provider, request.SiteUuid))

	s, _ := json.Marshal(request)
	logger.Info(ctx, fmt.Sprintf("parsed event body %s", string(s)))

	var err error
	switch {
	case request.Action == string(PURCHASE):
		p.handlePurchaseWebhook(ctx, request, mgrApiMap)
	case request.Action == string(REFUND):
		p.handleRefundWebhook(ctx, request, mgrApiMap)
	case request.Action == string(REVERSAL):
		p.handleReversalWebhook(ctx, request, mgrApiMap)
	case request.Action == string(SUB_ORDERS_UPDATE):
		p.validateRequestSiteUuid(ctx, request)
		var o *Order
		requestData := request.Data.(map[string]any)
		o, err = NewOrder(ctx, &requestData)
		if err != nil {
			logger.Error(ctx, err.Error())
			panic(PosConnectorError{Type: INVALID_REQUEST, Message: err.Error()})
		}
		ctx = logger.AddMetadata(ctx, "orderId", *o.OrderId)
		ctx = context.WithValue(ctx, logger.AggregateId{}, *o.OrderId)
		p.publishSubOrdersUpdate(ctx, request, o, mgrApiMap)
	default:
		errMsg := fmt.Sprintf("receive unknown action %s", request.Action)
		logger.Error(ctx, errMsg)
		panic(PosConnectorError{Type: INVALID_REQUEST, Message: errMsg})
	}
	return &PosConnectorResponse{
		EventId:   request.EventId,
		Action:    request.Action,
		Timestamp: getServerTimestamp(),
	}
}

func (p *PosConnectorProtocol) authorizeWebhookEvent(ctx context.Context, event *events.APIGatewayProxyRequest) (*string, *PosConnectorRequest) {
	apiKey, ok := common.GetHeaderValue(event.Headers, types.AUTHORIZATION)
	if !ok {
		errMsg := "Invalid api key"
		logger.Error(ctx, errMsg)
		panic(PosConnectorError{
			Type:    FORBIDDEN,
			Message: errMsg,
		})
	}
	logger.Debug(ctx, fmt.Sprintf("get api key %s", apiKey))
	dbClient := db.NewDynamoDb(ctx)
	output, _ := dbClient.Query(common.PairingTableName()).Index("typeGsi").Eq("type", fmt.Sprintf("%s%s", string(types.METADATA), apiKey)).Exec(ctx)
	if len(output.Data) == 0 {
		errMsg := "Not found provider from pairing table"
		logger.Error(ctx, errMsg)
		panic(PosConnectorError{
			Type:    FORBIDDEN,
			Message: errMsg,
		})
	}
	provider := output.Data[0]["id"].(string)
	var reqMap map[string]any
	err := json.Unmarshal([]byte(event.Body), &reqMap)
	if err != nil {
		errMsg := fmt.Sprintf("Cant parse request %s, %s", event.Body, err.Error())
		logger.Error(ctx, errMsg)
		panic(PosConnectorError{
			Type:    INVALID_REQUEST,
			Message: errMsg,
		})
	}
	request, err := NewPosConnectorRequest(ctx, &reqMap)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("invalid request %s", err.Error()))
		panic(PosConnectorError{
			Type:    INVALID_REQUEST,
			Message: err.Error(),
		})
	}
	requestData := request.Data.(map[string]any)
	request.SiteUuid, _ = requestData["siteUuid"].(string)
	request.DeviceUuid, _ = requestData["deviceUuid"].(string)
	request.Provider = provider
	if err := request.validate(ctx); err != nil {
		logger.Error(ctx, err.Error())
		panic(PosConnectorError{
			Type:    INVALID_REQUEST,
			Message: err.Error(),
		})
	}
	return &provider, request
}

func (p *PosConnectorProtocol) publishSubOrdersUpdate(ctx context.Context, request *PosConnectorRequest, o *Order, mgrApiMap map[string]common.ManagementApi) {
	var lastKey map[string]dynamodbTypes.AttributeValue
	subscribers := []map[string]any{}
	for {
		dbClient := db.NewDynamoDb(ctx)
		dbQuery := dbClient.Query(common.PairingTableName()).Index("typeGsi").
			Eq("type", fmt.Sprintf(subscriberDbFormat, types.SUBSCRIBER, request.SiteUuid, request.Action)).
			Where().Eq("status", string(connection.CONNECTED)).AndWhere().Eq("provider", request.Provider)
		if lastKey != nil {
			dbQuery = dbQuery.StartKey(&lastKey)
		}
		if o.Location != nil {
			logger.Info(ctx, fmt.Sprintf("compare location id %s", *o.Location.LocationId))
			dbQuery = dbQuery.
				AndWhere().NotExist("locations").
				Or().Contains("locations", o.Location.LocationId).
				Or().Eq("locations", nil)
		}
		output, _ := dbQuery.Exec(ctx)
		lastKey = output.Raw.LastEvaluatedKey
		subscribers = append(subscribers, output.Data...)
		if lastKey == nil {
			break
		}
	}
	if len(subscribers) > 0 {
		ctx = logger.AddMetadata(ctx, "entityUuid", subscribers[0]["entityUuid"])
	}
	logger.Info(ctx, fmt.Sprintf("found subscribers count %d", len(subscribers)))

	var rd map[string]any
	b, _ := json.Marshal(o)
	_ = json.Unmarshal(b, &rd)
	delete(rd, "siteUuid")

	subscribers = p.getUniqueSubscriberPerConnection(subscribers)
	logger.Info(ctx, fmt.Sprintf("found unique subscribers count %d", len(subscribers)))
	var wg sync.WaitGroup
	for _, data := range subscribers {
		connectionId, ok := data["connectionId"].(string)
		if !ok {
			logger.Error(ctx, fmt.Sprintf("Cant find connection for the subscriber %s", data["deviceUuid"]))
			continue
		}
		connectionEndpoint, ok := data["connectionEndpoint"].(string)
		if !ok {
			logger.Warn(ctx, fmt.Sprintf("Cant find connection endpoint for the subscriber %s", data["deviceUuid"]))
		}
		mgrApi := common.GetMgrApiFromMap(ctx, mgrApiMap, connectionEndpoint)
		wg.Add(1)
		go func(connId string, eventId string, action string, timestamp string, deviceUuid string) {
			defer wg.Done()
			resp := PosConnectorResponse{
				EventId:   eventId,
				Action:    action,
				Timestamp: timestamp,
				Data:      rd,
			}
			PostToConnection(ctx, mgrApi, connId, resp.Bytes(), "site", request.SiteUuid)
			logger.Info(ctx, fmt.Sprintf("publish to site %s device %s, connection %s: %+v", request.SiteUuid, deviceUuid, connId, string(resp.Bytes())))
		}(connectionId, data["id"].(string), request.Action, request.Timestamp, data["deviceUuid"].(string))
	}
	wg.Wait()
	logger.Info(ctx, "Finish pushing")
}

func (p *PosConnectorProtocol) getDeviceConnectionIdAndEndpoint(ctx context.Context, deviceUuid string, provider string) (string, string) {
	connections, err := p.connMgr.GetConnectedConnectionsByDevice(ctx, deviceUuid, provider)

	if err != nil {
		panic(err)
	}

	if connections == nil {
		errorMessage := fmt.Sprintf("connection not found for device %s", deviceUuid)
		logger.Warn(ctx, errorMessage)
		panic(PosConnectorError{Type: INVALID_REQUEST, Message: errorMessage})
	}

	if len(connections) > 1 {
		logger.Warn(ctx, "Multiple active connections found for device "+deviceUuid+" provider "+provider)
	}

	return connections[0].Id, connections[0].ConnectionEndpoint
}

func (p *PosConnectorProtocol) publishTransactionRequest(ctx context.Context, request *PosConnectorRequest, transactionRequest any, mgrApiMap map[string]common.ManagementApi) {
	deviceUuid := request.DeviceUuid

	connectionId, connectionEndpoint := p.getDeviceConnectionIdAndEndpoint(ctx, deviceUuid, request.Provider)
	ctx = logger.AddMetadata(ctx, "connectionId", connectionId)

	mgrApi := common.GetMgrApiFromMap(ctx, mgrApiMap, connectionEndpoint)

	var rd map[string]any
	b, _ := json.Marshal(transactionRequest)
	_ = json.Unmarshal(b, &rd)

	resp := PosConnectorResponse{
		EventId:   request.EventId,
		Action:    request.Action,
		Timestamp: request.Timestamp,
		Data:      rd,
	}
	PostToConnection(ctx, mgrApi, connectionId, resp.Bytes(), "device", deviceUuid)
	logger.Info(ctx, fmt.Sprintf("publish %T to device %s, connection %s: %+v", transactionRequest, deviceUuid, connectionId, string(resp.Bytes())))
}

func (p *PosConnectorProtocol) getUniqueSubscriberPerConnection(subscribers []map[string]any) []map[string]any {
	uniqueSubs := []map[string]any{}
	connections := []string{}
	for _, sub := range subscribers {
		connId := sub["connectionId"].(string)
		if !slices.Contains(connections, connId) {
			connections = append(connections, connId)
			uniqueSubs = append(uniqueSubs, sub)
		}
	}
	return uniqueSubs
}
