package protocol

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"testing"
	"time"

	"maps"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/test"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func TestAuthorizeWebhookEventApiKey(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}

	r := func() (req *PosConnectorError) {
		defer func() {
			if err := recover(); err != nil {
				r := err.(PosConnectorError)
				req = &r
			}
		}()
		p.authorizeWebhookEvent(ctx, &events.APIGatewayProxyRequest{})
		assert.Fail(t, "should have panic")
		return nil
	}()
	assert.NotNil(t, r, r)
	assert.Equal(t, FORBIDDEN, r.Type)
	assert.Equal(t, "Invalid api key", r.Message)

	r = func() (req *PosConnectorError) {
		defer func() {
			if err := recover(); err != nil {
				r := err.(PosConnectorError)
				req = &r
			}
		}()
		p.authorizeWebhookEvent(ctx, &events.APIGatewayProxyRequest{
			Headers: map[string]string{},
		})
		assert.Fail(t, "should have panic")
		return nil
	}()
	assert.NotNil(t, r, r)
	assert.Equal(t, FORBIDDEN, r.Type)
	assert.Equal(t, "Invalid api key", r.Message)

	// invalid eventId type
	r = func() (req *PosConnectorError) {
		defer func() {
			if err := recover(); err != nil {
				r := err.(PosConnectorError)
				req = &r
			}
		}()
		apiKey := setApiKey(t, types.HL)
		body := map[string]any{"eventId": 1}
		b, _ := json.Marshal(body)
		p.authorizeWebhookEvent(ctx, &events.APIGatewayProxyRequest{
			Headers: map[string]string{
				"Authorization": apiKey,
			},
			Body: string(b),
		})
		assert.Fail(t, "should have panic")
		return nil
	}()
	assert.NotNil(t, r, r)
	assert.Equal(t, INVALID_REQUEST, r.Type)
}

func TestAuthorizeWebhookEventNoMetaData(t *testing.T) {
	r := func() (req *PosConnectorError) {
		defer func() {
			if err := recover(); err != nil {
				r := err.(PosConnectorError)
				req = &r
			}
		}()
		p := PosConnectorProtocol{
			dbClient:  dbClient,
			tableName: test.PairingTableName,
		}
		p.authorizeWebhookEvent(ctx, &events.APIGatewayProxyRequest{
			Headers: map[string]string{
				"Authorization": uuid.NewString(),
			},
		})
		assert.Fail(t, "should have panic")
		return nil
	}()
	assert.NotNil(t, r, r)
	assert.Equal(t, FORBIDDEN, r.Type)
	assert.Equal(t, "Not found provider from pairing table", r.Message)
}

func TestAuthorizeWebhookEventInvalidBody(t *testing.T) {
	r := func() (req *PosConnectorError) {
		defer func() {
			if err := recover(); err != nil {
				r := err.(PosConnectorError)
				req = &r
			}
		}()
		p := PosConnectorProtocol{
			dbClient:  dbClient,
			tableName: test.PairingTableName,
		}
		apiKey := setApiKey(t, types.HL)
		p.authorizeWebhookEvent(ctx, &events.APIGatewayProxyRequest{
			Headers: map[string]string{
				"Authorization": apiKey,
			},
		})
		assert.Fail(t, "should have panic")
		return nil
	}()
	assert.NotNil(t, r, r)
	assert.Equal(t, INVALID_REQUEST, r.Type)
	assert.True(t, strings.HasPrefix(r.Message, "Cant parse request"))
}

func TestAuthorizeWebhookEventInvalidPurchaseRequest(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.ORACLE)
	deviceUuid := uuid.NewString()
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(PURCHASE),
		"data": map[string]any{
			"deviceUuid":  deviceUuid,
			"sessionUuid": uuid.NewString(),
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	data := map[string]string{
		"id":         deviceUuid,
		"type":       fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_DEVICE, deviceUuid),
		"deviceUuid": deviceUuid,
		"status":     string(connection.CONNECTED),
		"provider":   string(types.ORACLE),
	}
	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	delete(body["data"].(map[string]any), "siteUuid")
	resp := p.HandleWebhook(ctx, apiRequest, mgrApiMap)

	assert.NotNil(t, resp)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)
	assert.True(t, strings.Contains(resp.Error.Message, "Error:Field validation"))
}

func TestAuthorizeWebhookEventInvalidRequest(t *testing.T) {
	cases := []ActionType{
		PURCHASE,
		REFUND,
		REVERSAL,
	}

	for _, testCase := range cases {
		t.Run(string(testCase), func(t *testing.T) {
			p := PosConnectorProtocol{
				connMgr:   connection.New(ctx, dbClient),
				dbClient:  dbClient,
				tableName: test.PairingTableName,
			}
			apiKey := setApiKey(t, types.ORACLE)
			deviceUuid := uuid.NewString()
			body := map[string]any{
				"eventId":   uuid.NewString(),
				"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
				"action":    string(testCase),
				"data": map[string]any{
					"deviceUuid":  deviceUuid,
					"sessionUuid": uuid.NewString(),
				},
			}
			b, _ := json.Marshal(body)
			apiRequest := &events.APIGatewayProxyRequest{
				Headers: map[string]string{
					"Authorization": apiKey,
				},
				Body: string(b),
			}
			data := map[string]string{
				"id":         deviceUuid,
				"type":       fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_DEVICE, deviceUuid),
				"deviceUuid": deviceUuid,
				"status":     string(connection.CONNECTED),
				"provider":   string(types.ORACLE),
			}
			item, _ := attributevalue.MarshalMap(data)
			dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
			delete(body["data"].(map[string]any), "siteUuid")
			resp := p.HandleWebhook(ctx, apiRequest, mgrApiMap)

			assert.NotNil(t, resp)
			assert.Equal(t, INVALID_REQUEST, resp.Error.Type)
			assert.True(t, strings.Contains(resp.Error.Message, "Error:Field validation"))
		})
	}
}

func TestAuthorizeWebhookEventInvalidOrder(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.HL)
	siteUuid := uuid.NewString()
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(SUB_ORDERS_UPDATE),
		"data": map[string]any{
			"siteUuid": "siteUuid",
			"location": map[string]string{
				"locationId":   uuid.NewString(),
				"locationName": uuid.NewString(),
			},
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	connectionId := uuid.NewString()
	data := map[string]string{
		"id":           body["eventId"].(string),
		"type":         fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, siteUuid, body["action"].(string)),
		"connectionId": connectionId,
		"deviceUuid":   uuid.NewString(),
		"status":       string(connection.CONNECTED),
		"provider":     string(types.HL),
	}
	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	resp := p.HandleWebhook(ctx, apiRequest, mgrApiMap)

	assert.NotNil(t, resp)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)
	assert.True(t, strings.Contains(resp.Error.Message, "Error:Field validation"))
}

func TestAuthorizeWebhookEventWithoutSiteuuid(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.HL)
	siteUuid := uuid.NewString()
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(SUB_ORDERS_UPDATE),
		"data": map[string]any{
			"noSiteUuid": "siteUuid",
			"orderId":    uuid.NewString(),
			"location": map[string]string{
				"locationId":   uuid.NewString(),
				"locationName": uuid.NewString(),
			},
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	connectionId := uuid.NewString()
	data := map[string]string{
		"id":           body["eventId"].(string),
		"type":         fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, siteUuid, body["action"].(string)),
		"connectionId": connectionId,
		"deviceUuid":   uuid.NewString(),
		"status":       string(connection.CONNECTED),
		"provider":     string(types.HL),
	}
	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	delete(body["data"].(map[string]any), "siteUuid")
	resp := p.HandleWebhook(ctx, apiRequest, mgrApiMap)

	assert.NotNil(t, resp)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)
	assert.True(t, strings.Contains(resp.Error.Message, "siteUuid is required"))
}

func TestAuthorizeWebhookEventWithoutDeviceUuid(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.ORACLE)
	siteUuid := uuid.NewString()
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(PURCHASE),
		"data": map[string]any{
			"noDeviceUuid": "siteUuid",
			"sessionUuid":  uuid.NewString(),
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	connectionId := uuid.NewString()
	data := map[string]string{
		"id":           body["eventId"].(string),
		"type":         fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, siteUuid, body["action"].(string)),
		"connectionId": connectionId,
		"deviceUuid":   uuid.NewString(),
		"status":       string(connection.CONNECTED),
		"provider":     string(types.ORACLE),
	}
	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	delete(body["data"].(map[string]any), "siteUuid")
	resp := p.HandleWebhook(ctx, apiRequest, mgrApiMap)

	assert.NotNil(t, resp)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)
	assert.True(t, strings.Contains(resp.Error.Message, "deviceUuid is required"))
}

func TestAuthorizeWebhookEventWithoutEventId(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.ORACLE)
	siteUuid := uuid.NewString()
	body := map[string]any{
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(PURCHASE),
		"data": map[string]any{
			"noDeviceUuid": "siteUuid",
			"sessionUuid":  uuid.NewString(),
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	connectionId := uuid.NewString()
	data := map[string]string{
		"id":           uuid.NewString(),
		"type":         fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, siteUuid, body["action"].(string)),
		"connectionId": connectionId,
		"deviceUuid":   uuid.NewString(),
		"status":       string(connection.CONNECTED),
		"provider":     string(types.ORACLE),
	}
	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	delete(body["data"].(map[string]any), "siteUuid")
	resp := p.HandleWebhook(ctx, apiRequest, mgrApiMap)

	assert.NotNil(t, resp)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)
	assert.True(t, strings.Contains(resp.Error.Message, "Error:Field validation"))
}

func TestAuthorizeWebhookEvent(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.HL)
	siteUuid := uuid.NewString()
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(SUB_ORDERS_UPDATE),
		"data": map[string]any{
			"siteUuid": siteUuid,
			"orderId":  uuid.NewString(),
			"location": map[string]string{
				"locationId":   uuid.NewString(),
				"locationName": uuid.NewString(),
			},
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	connectionId := uuid.NewString()
	data := getSubscriberMap(map[string]any{
		"id":           body["eventId"].(string),
		"type":         fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, siteUuid, body["action"].(string)),
		"connectionId": connectionId,
		"status":       string(connection.CONNECTED),
		"provider":     string(types.HL),
	})

	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	m := common.MockManagementApi{}
	mockMgrApiMap := map[string]common.ManagementApi{
		test.WsRestApiEndpoint: &m,
	}
	delete(body["data"].(map[string]any), "siteUuid")
	presp := PosConnectorResponse{
		EventId:   body["eventId"].(string),
		Action:    body["action"].(string),
		Timestamp: body["timestamp"].(string),
		Data:      body["data"],
	}
	m.On("PostToConnection", ctx, &apigatewaymanagementapi.PostToConnectionInput{ConnectionId: &connectionId, Data: presp.Bytes()}).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp := p.HandleWebhook(ctx, apiRequest, mockMgrApiMap)
	assert.Nil(t, resp.Error)
	m.AssertExpectations(t)
}

func TestHandleWebhookPurchaseEventOraclePos(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.ORACLE)
	deviceUuid := uuid.NewString()
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(PURCHASE),
		"data": map[string]any{
			"sessionUuid":         uuid.NewString(),
			"deviceUuid":          deviceUuid,
			"externalReference":   uuid.NewString(),
			"transactionUuid":     uuid.NewString(),
			"type":                "PURCHASE",
			"amount":              1500,
			"transactionCurrency": "AUD",
			"timestamp":           "2023-11-28T11:14:18+11:00",
			"caid":                "************",
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	connectionId := uuid.NewString()
	data := getConnectionCoreMap(map[string]string{
		"id":           connectionId,
		"connectionId": connectionId,
		"deviceUuid":   deviceUuid,
		"provider":     string(types.ORACLE),
	})
	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	m := common.MockManagementApi{}
	mockMgrApiMap := map[string]common.ManagementApi{
		test.WsRestApiEndpoint: &m,
	}
	presp := PosConnectorResponse{
		EventId:   body["eventId"].(string),
		Action:    body["action"].(string),
		Timestamp: body["timestamp"].(string),
		Data:      body["data"],
	}
	m.On("PostToConnection", ctx, &apigatewaymanagementapi.PostToConnectionInput{ConnectionId: &connectionId, Data: presp.Bytes()}).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp := p.HandleWebhook(ctx, apiRequest, mockMgrApiMap)
	assert.Nil(t, resp.Error)
	m.AssertExpectations(t)
}

func TestHandleWebhookPurchaseEventOraclePosNoConnection(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.ORACLE)
	deviceUuid := uuid.NewString()
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(PURCHASE),
		"data": map[string]any{
			"sessionUuid":         uuid.NewString(),
			"deviceUuid":          deviceUuid,
			"externalReference":   uuid.NewString(),
			"transactionUuid":     uuid.NewString(),
			"type":                "PURCHASE",
			"amount":              1500,
			"transactionCurrency": "AUD",
			"timestamp":           "2023-11-28T11:14:18+11:00",
			"caid":                "************",
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}

	resp := p.HandleWebhook(ctx, apiRequest, mgrApiMap)
	assert.NotNil(t, resp.Error)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)
	assert.Equal(t, fmt.Sprintf("connection not found for device %s", deviceUuid), resp.Error.Message)
}

func TestHandleWebhookRefundEventsOraclePos(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.ORACLE)
	deviceUuid := uuid.NewString()
	connectionId := uuid.NewString()
	data := getConnectionCoreMap(map[string]string{
		"id":         connectionId,
		"deviceUuid": deviceUuid,
		"provider":   string(types.ORACLE),
	})
	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	testCases := []struct {
		refundType  string
		webhookData map[string]any
	}{
		{
			refundType: "VOID",
			webhookData: map[string]any{
				"sessionUuid":               uuid.NewString(),
				"deviceUuid":                deviceUuid,
				"externalReference":         uuid.NewString(),
				"transactionUuid":           uuid.NewString(),
				"type":                      "REFUND",
				"amount":                    1500,
				"transactionCurrency":       "AUD",
				"timestamp":                 "2023-11-28T11:14:18+11:00",
				"caid":                      "************",
				"originalTransactionUuid":   uuid.NewString(),
				"originalIsoProcessingCode": uuid.NewString(),
				"isCardPresent":             false,
			},
		},
		{
			refundType: "REFUND_LINKED",
			webhookData: map[string]any{
				"amount":                    1500,
				"caid":                      uuid.NewString(),
				"deviceUuid":                deviceUuid,
				"externalReference":         uuid.NewString(),
				"isCardPresent":             true,
				"originalIsoProcessingCode": uuid.NewString(),
				"originalTransactionUuid":   uuid.NewString(),
				"sessionUuid":               uuid.NewString(),
				"timestamp":                 "2023-11-28T11:14:18+11:00",
				"transactionCurrency":       "AUD",
				"transactionUuid":           uuid.NewString(),
				"type":                      string(types.REFUND),
			},
		},
		{
			refundType: "REFUND_UNLINKED",
			webhookData: map[string]any{
				"amount":              1500,
				"caid":                uuid.NewString(),
				"deviceUuid":          deviceUuid,
				"externalReference":   uuid.NewString(),
				"isCardPresent":       true,
				"sessionUuid":         uuid.NewString(),
				"timestamp":           "2021-09-01T12:00:00",
				"transactionCurrency": "AUD",
				"transactionUuid":     uuid.NewString(),
				"type":                string(types.REFUND),
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.refundType, func(t *testing.T) {
			body := map[string]any{
				"eventId":   uuid.NewString(),
				"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
				"action":    string(REFUND),
				"data":      testCase.webhookData,
				"error":     nil,
			}
			b, _ := json.Marshal(body)
			apiRequest := &events.APIGatewayProxyRequest{
				Headers: map[string]string{
					"Authorization": apiKey,
				},
				Body: string(b),
			}

			m := common.MockManagementApi{}
			m.On("PostToConnection", mock.Anything, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
			mockMgrApiMap := map[string]common.ManagementApi{
				test.WsRestApiEndpoint: &m,
			}

			resp := p.HandleWebhook(ctx, apiRequest, mockMgrApiMap)

			posconnectorRespose := PosConnectorResponse{
				EventId:   body["eventId"].(string),
				Action:    body["action"].(string),
				Timestamp: body["timestamp"].(string),
				Data:      body["data"],
			}

			assert.Equal(t, string(posconnectorRespose.Bytes()), string(m.Mock.Calls[0].Arguments[1].(*apigatewaymanagementapi.PostToConnectionInput).Data))
			assert.Nil(t, resp.Error)
			m.AssertExpectations(t)
		})
	}
}

func TestHandleWebhookReversalEventOraclePos(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.ORACLE)
	deviceUuid := uuid.NewString()
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(REVERSAL),
		"data": map[string]any{
			"sessionUuid":             uuid.NewString(),
			"deviceUuid":              deviceUuid,
			"externalReference":       uuid.NewString(),
			"transactionUuid":         uuid.NewString(),
			"type":                    "REVERSAL",
			"amount":                  1500,
			"transactionCurrency":     "AUD",
			"timestamp":               "2023-11-28T11:14:18+11:00",
			"caid":                    "************",
			"originalTransactionUuid": uuid.NewString(),
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	connectionId := uuid.NewString()
	data := getConnectionCoreMap(map[string]string{
		"id":         connectionId,
		"deviceUuid": deviceUuid,
		"provider":   string(types.ORACLE),
	})
	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	m := common.MockManagementApi{}
	mockMgrApiMap := map[string]common.ManagementApi{
		test.WsRestApiEndpoint: &m,
	}
	presp := PosConnectorResponse{
		EventId:   body["eventId"].(string),
		Action:    body["action"].(string),
		Timestamp: body["timestamp"].(string),
		Data:      body["data"],
	}
	m.On("PostToConnection", ctx, &apigatewaymanagementapi.PostToConnectionInput{ConnectionId: &connectionId, Data: presp.Bytes()}).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp := p.HandleWebhook(ctx, apiRequest, mockMgrApiMap)
	assert.Nil(t, resp.Error)
	m.AssertExpectations(t)
}

func TestHandleWebhookWrongEvent(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	resp := p.HandleWebhook(ctx, &events.APIGatewayProxyRequest{}, nil)
	assert.NotNil(t, resp.Error)
	assert.Equal(t, FORBIDDEN, resp.Error.Type)
}

func TestWrongOrderEvent(t *testing.T) {
	apiKey := setApiKey(t, types.HL)
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(SUB_ORDERS_UPDATE),
		"data": map[string]string{
			"orderId": uuid.NewString(),
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	resp := p.HandleWebhook(ctx, apiRequest, nil)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)

	body["action"] = "UNKNOWN"
	body["data"].(map[string]string)["siteUuid"] = uuid.NewString()
	b, _ = json.Marshal(body)
	apiRequest = &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	resp = p.HandleWebhook(ctx, apiRequest, nil)
	assert.Equal(t, INVALID_REQUEST, resp.Error.Type)
	assert.True(t, strings.HasPrefix(resp.Error.Message, "receive unknown action"))
}

func TestHandleWebhookWithoutSubscriber(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	m := common.MockManagementApi{}
	p.publishSubOrdersUpdate(ctx, &PosConnectorRequest{}, &Order{}, mgrApiMap)
	m.AssertNotCalled(t, "PostToConnection")
}

// set up 3 devices, 2 are from same site, 1 is from a different site
func TestMultipleSubscribers(t *testing.T) {
	p := NewFromContext(ctx)
	request := getSubscriberMap(map[string]any{})
	requests := []map[string]any{request}
	requests = append(requests,
		getSubscriberMap(map[string]any{
			"siteUuid":   request["siteUuid"].(string),
			"entityUuid": request["entityUuid"].(string),
		}),
		getSubscriberMap(map[string]any{
			"entityUuid": request["entityUuid"].(string),
		}),
	)
	m := common.MockManagementApi{}
	mockMgrApiMap := map[string]common.ManagementApi{
		test.WsRestApiEndpoint: &m,
	}

	// subscribe
	locationId := uuid.NewString()
	locationName := uuid.NewString()
	orderId := uuid.NewString()
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(SUB_ORDERS_UPDATE),
		"data": map[string]any{
			"siteUuid": request["siteUuid"].(string),
			"location": map[string]string{
				"locationId":   locationId,
				"locationName": locationName,
			},
			"orderId": orderId,
		},
	}
	for i, r := range requests {
		dbRecord := getConnectionCoreMap(map[string]string{
			"id":         r["connectionId"].(string),
			"deviceUuid": r["deviceUuid"].(string),
			"entityUuid": r["entityUuid"].(string),
			"siteUuid":   r["siteUuid"].(string),
			"provider":   string(types.HL),
		})
		attributes, _ := attributevalue.MarshalMap(dbRecord)
		dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
		setPairingData(ctx, t, uuid.NewString(), r["siteUuid"].(string), r["deviceUuid"].(string), []string{locationId}, types.POSINTERFACE_PAIR_DEVICE)
		resp := p.HandleAction(ctx, &r, mgrApiMap)
		assert.Nil(t, resp.Error, resp.Error)
		output, _ := dbClient.Query(test.PairingTableName).Index("typeGsi").Eq("type", fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, r["siteUuid"], r["action"])).Exec(ctx)
		assert.True(t, len(output.Data) > 0)

		if i < 2 {
			resp := PosConnectorResponse{
				EventId:   r["eventId"].(string),
				Action:    body["action"].(string),
				Timestamp: body["timestamp"].(string),
				Data: map[string]any{
					"location": map[string]string{
						"locationId":   locationId,
						"locationName": locationName,
					},
					"orderId": orderId,
				},
			}
			m.On("PostToConnection", ctx, &apigatewaymanagementapi.PostToConnectionInput{ConnectionId: aws.String(dbRecord["id"]), Data: resp.Bytes()}).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil).Times(1)
		}
	}

	apiKey := setApiKey(t, types.HL)
	b, _ := json.Marshal(body)
	resp := p.HandleWebhook(ctx, &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}, mockMgrApiMap)
	assert.Nil(t, resp.Error, resp.Error)
	m.AssertExpectations(t)
}

func TestBuildRequestNoPairing(t *testing.T) {
	defer func() { _ = recover() }()
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	request := getSubscriberMap(map[string]any{})
	dbRecord := map[string]string{
		"id":          request["connectionId"].(string),
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  request["deviceUuid"].(string),
		"entityUuid":  request["entityUuid"].(string),
		"siteUuid":    request["siteUuid"].(string),
		"accessToken": uuid.NewString(),
		"status":      string(connection.CONNECTED),
	}
	attributes, _ := attributevalue.MarshalMap(dbRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	r := p.buildRequest(ctx, &request)
	assert.Nil(t, r, r)

	// paring data missing siteUuid
	pairing := map[string]string{
		"id":         uuid.NewString(),
		"type":       fmt.Sprintf("%s%s", string(types.POSINTERFACE_PAIR_DEVICE), dbRecord["deviceUuid"]),
		"deviceUuid": dbRecord["deviceUuid"],
	}
	attributes, _ = attributevalue.MarshalMap(pairing)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	p.buildRequest(ctx, &request)
	assert.Fail(t, "should have paniced")
}

func TestBuildRequestNoSiteUuid(t *testing.T) {
	defer func() { _ = recover() }()
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	request := getSubscriberMap(map[string]any{})
	dbRecord := map[string]string{
		"id":          request["connectionId"].(string),
		"type":        string(types.CONNECTION_CORE),
		"deviceUuid":  request["deviceUuid"].(string),
		"entityUuid":  request["entityUuid"].(string),
		"siteUuid":    request["siteUuid"].(string),
		"accessToken": uuid.NewString(),
		"status":      string(connection.CONNECTED),
	}
	attributes, _ := attributevalue.MarshalMap(dbRecord)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)

	// paring data missing siteUuid
	pairing := map[string]string{
		"id":         uuid.NewString(),
		"type":       fmt.Sprintf("%s%s", string(types.POSINTERFACE_PAIR_DEVICE), dbRecord["deviceUuid"]),
		"deviceUuid": dbRecord["deviceUuid"],
	}
	attributes, _ = attributevalue.MarshalMap(pairing)
	dbClient.InsertItem(ctx, test.PairingTableName, &attributes, nil)
	p.buildRequest(ctx, &request)
	assert.Fail(t, "should have paniced")
}

// one site pair with 3 locations
func TestParingLocation(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.HL)
	siteUuid := uuid.NewString()
	locationIds := []string{uuid.NewString(), uuid.NewString(), uuid.NewString()}
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(SUB_ORDERS_UPDATE),
		"data": map[string]any{
			"siteUuid": siteUuid,
			"orderId":  uuid.NewString(),
			"location": map[string]string{
				"locationId":   locationIds[0],
				"locationName": uuid.NewString(),
			},
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	// pairing
	connectionId := uuid.NewString()
	data := getSubscriberMap(map[string]any{
		"id":           body["eventId"].(string),
		"type":         fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, siteUuid, body["action"].(string)),
		"connectionId": connectionId,
		"deviceUuid":   uuid.NewString(),
		"locations":    []string{locationIds[0]},
		"provider":     string(types.HL),
	})
	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	m := common.MockManagementApi{}
	mockMgrApiMap := map[string]common.ManagementApi{
		test.WsRestApiEndpoint: &m,
	}
	delete(body["data"].(map[string]any), "siteUuid")
	presp := PosConnectorResponse{
		EventId:   body["eventId"].(string),
		Action:    body["action"].(string),
		Timestamp: body["timestamp"].(string),
		Data:      body["data"],
	}
	m.On("PostToConnection", ctx, &apigatewaymanagementapi.PostToConnectionInput{ConnectionId: &connectionId, Data: presp.Bytes()}).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil).Once()
	resp := p.HandleWebhook(ctx, apiRequest, mockMgrApiMap)
	assert.Nil(t, resp.Error)
	m.AssertExpectations(t)
}

func TestNotPublishWithoutLocationPairing(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.HL)
	siteUuid := uuid.NewString()
	locationIds := []string{uuid.NewString(), uuid.NewString(), uuid.NewString()}
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(SUB_ORDERS_UPDATE),
		"data": map[string]any{
			"siteUuid": siteUuid,
			"orderId":  uuid.NewString(),
			"location": map[string]string{
				"locationId":   uuid.NewString(), // new location
				"locationName": uuid.NewString(),
			},
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	// pairing
	connectionId := uuid.NewString()

	data := getSubscriberMap(map[string]any{
		"id":           body["eventId"].(string),
		"type":         fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, siteUuid, body["action"].(string)),
		"connectionId": connectionId,
		"locations":    []string{locationIds[0]},
	})
	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	m := common.MockManagementApi{}
	delete(body["data"].(map[string]any), "siteUuid")
	presp := PosConnectorResponse{
		EventId:   body["eventId"].(string),
		Action:    body["action"].(string),
		Timestamp: body["timestamp"].(string),
		Data:      body["data"],
	}
	m.On("PostToConnection", ctx, &apigatewaymanagementapi.PostToConnectionInput{ConnectionId: &connectionId, Data: presp.Bytes()}).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp := p.HandleWebhook(ctx, apiRequest, mgrApiMap)
	assert.Nil(t, resp.Error)
	m.AssertNotCalled(t, "PostToConnection")
}

func TestPublishOrderWithoutLocation(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.HL)
	siteUuid := uuid.NewString()
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(SUB_ORDERS_UPDATE),
		"data": map[string]any{
			"siteUuid": siteUuid,
			"orderId":  uuid.NewString(),
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	// pairing
	connectionId := uuid.NewString()
	data := getSubscriberMap(map[string]any{
		"id":           body["eventId"].(string),
		"type":         fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, siteUuid, body["action"].(string)),
		"connectionId": connectionId,
		"provider":     string(types.HL),
	})
	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	m := common.MockManagementApi{}
	mockMgrApiMap := map[string]common.ManagementApi{
		test.WsRestApiEndpoint: &m,
	}
	delete(body["data"].(map[string]any), "siteUuid")
	presp := PosConnectorResponse{
		EventId:   body["eventId"].(string),
		Action:    body["action"].(string),
		Timestamp: body["timestamp"].(string),
		Data:      body["data"],
	}
	m.On("PostToConnection", ctx, &apigatewaymanagementapi.PostToConnectionInput{ConnectionId: &connectionId, Data: presp.Bytes()}).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil).Once()
	resp := p.HandleWebhook(ctx, apiRequest, mockMgrApiMap)
	assert.Nil(t, resp.Error)
	m.AssertExpectations(t)
}

func TestPublishWhenLocationIsNull(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	apiKey := setApiKey(t, types.HL)
	siteUuid := uuid.NewString()
	body := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(SUB_ORDERS_UPDATE),
		"data": map[string]any{
			"siteUuid": siteUuid,
			"orderId":  uuid.NewString(),
			"location": map[string]string{
				"locationId":   uuid.NewString(), // new location
				"locationName": uuid.NewString(),
			},
		},
	}
	b, _ := json.Marshal(body)
	apiRequest := &events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": apiKey,
		},
		Body: string(b),
	}
	// pairing
	connectionId := uuid.NewString()
	data := getSubscriberMap(map[string]any{
		"id":           body["eventId"].(string),
		"type":         fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, siteUuid, body["action"].(string)),
		"connectionId": connectionId,
		"locations":    nil, // no locations
		"provider":     string(types.HL),
	})
	item, _ := attributevalue.MarshalMap(data)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	m := common.MockManagementApi{}
	mockMgrApiMap := map[string]common.ManagementApi{
		test.WsRestApiEndpoint: &m,
	}
	delete(body["data"].(map[string]any), "siteUuid")
	presp := PosConnectorResponse{
		EventId:   body["eventId"].(string),
		Action:    body["action"].(string),
		Timestamp: body["timestamp"].(string),
		Data:      body["data"],
	}
	m.On("PostToConnection", ctx, &apigatewaymanagementapi.PostToConnectionInput{ConnectionId: &connectionId, Data: presp.Bytes()}).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	resp := p.HandleWebhook(ctx, apiRequest, mockMgrApiMap)
	assert.Nil(t, resp.Error)
	m.AssertExpectations(t)
}

func TestGetUniqueSubscriberPerConnection(t *testing.T) {
	p := PosConnectorProtocol{
		connMgr:   connection.New(ctx, dbClient),
		dbClient:  dbClient,
		tableName: test.PairingTableName,
	}
	// generate 10 connections each has 100 subscribers
	subscribers := []map[string]any{}
	for i := 0; i < 1000; i += 1 {
		connId := strconv.Itoa(i % 10)
		subscribers = append(subscribers, map[string]any{"connectionId": connId})
	}
	uniqueSubs := p.getUniqueSubscriberPerConnection(subscribers)
	assert.Equal(t, len(uniqueSubs), 10)
}

func setApiKey(t *testing.T, provider types.PosProvider) string {
	apiKey := uuid.NewString()
	data := map[string]string{
		"id":       string(provider),
		"type":     fmt.Sprintf("%s%s", string(types.METADATA), apiKey),
		"provider": string(provider),
	}
	item, err := attributevalue.MarshalMap(data)
	assert.Nil(t, err, err)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	return apiKey
}

func getConnectionCoreMap(updates map[string]string) map[string]string {
	data := map[string]string{
		"id":                 uuid.NewString(),
		"type":               string(types.CONNECTION_CORE),
		"connectionEndpoint": "https://test.posconnector.myzeller.dev",
		"deviceUuid":         uuid.NewString(),
		"entityUuid":         uuid.NewString(),
		"status":             string(connection.CONNECTED),
		"provider":           uuid.NewString(),
		"deviceType":         string(types.DeviceTypeTerminal),
		"createdTime":        time.Now().UTC().Format(time.RFC3339),
	}
	maps.Copy(data, updates)

	return data
}

func getSubscriberMap(update map[string]any) map[string]any {
	data := map[string]any{
		"eventId":            uuid.NewString(),
		"timestamp":          uuid.NewString(),
		"siteUuid":           uuid.NewString(),
		"entityUuid":         uuid.NewString(),
		"deviceUuid":         uuid.NewString(),
		"connectionId":       uuid.NewString(),
		"connectionEndpoint": test.WsRestApiEndpoint,
		"action":             string(SUB_ORDERS_UPDATE),
		"status":             string(connection.CONNECTED),
	}
	maps.Copy(data, update)

	return data
}
