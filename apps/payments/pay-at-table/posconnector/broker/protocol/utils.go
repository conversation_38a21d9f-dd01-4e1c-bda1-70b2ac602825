package protocol

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	apitypes "github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi/types"
	"github.com/go-playground/validator/v10"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
)

func PostToConnection(ctx context.Context, mgrApi common.ManagementApi, connectionId string, data []byte, aggregate string, uuid string) {
	var err error

	retries, interval := getRetriesAndInterval()

	_, _ = utils.Retry(
		retries,
		interval,
		func(lastErr error, _ int) (*apigatewaymanagementapi.PostToConnectionOutput, error) {
			return mgrApi.PostToConnection(ctx, &apigatewaymanagementapi.PostToConnectionInput{
				ConnectionId: &connectionId,
				Data:         data,
			})
		},
		func(e error) {
			// Not interested in the error, we assume it will fail sometimes, just log it to debug
			// We log all the error messages in the end if all retries fail
			logger.Debug(ctx, fmt.Sprintf("PostToConnection failed with error %v", e.Error()))
			err = e
		},
	)

	if err != nil {
		var gne *apitypes.GoneException
		if errors.As(err, &gne) {
			logger.Warn(ctx, fmt.Sprintf("connection %s is deprecated", connectionId))
		} else {
			logger.Error(ctx, fmt.Sprintf("failed to publish to connection %s, %s %s, error: %s", connectionId, aggregate, uuid, err.Error()))
		}
	} else {
		logger.Info(ctx, fmt.Sprintf("published to connection %s, %s %s", connectionId, aggregate, uuid))
	}
}

func getRetriesAndInterval() (int, time.Duration) {
	retries := 3
	intervalMilliseconds := 3000
	retryRetriesEnv := os.Getenv("RETRY_RETRIES")
	retryIntervalEnv := os.Getenv("RETRY_INTERVAL_MILLISECONDS")

	if retryRetriesEnv != "" {
		i, e := strconv.ParseInt(retryRetriesEnv, 10, 32)
		if e == nil {
			retries = int(i)
		}
	}

	if retryIntervalEnv != "" {
		j, e := strconv.ParseInt(retryIntervalEnv, 10, 32)
		if e == nil {
			intervalMilliseconds = int(j)
		}
	}

	return retries, time.Duration(time.Duration(intervalMilliseconds) * time.Millisecond)
}

func SendPaymentAck(ctx context.Context, request *PosConnectorRequest, mgrApi common.ManagementApi) {
	requestData := request.Data.(map[string]any)
	pAck := NewPaymentAck(ctx, &requestData)
	logger.Info(ctx, fmt.Sprintf("Send payment ack %s, %s", request.EventId, *pAck.OrderId))
	ackResp := PosConnectorResponse{
		EventId:   request.EventId,
		Action:    request.Action,
		Timestamp: getServerTimestamp(),
		Data:      pAck,
	}
	PostToConnection(ctx, mgrApi, request.ConnectionId, ackResp.Bytes(), "site", request.SiteUuid)
}

func getServerTimestamp() string {
	return strconv.FormatInt(time.Now().UnixMilli(), 10)
}

func NewValidatedStruct[T any](ctx context.Context, data *map[string]any) (*T, error) {
	var p T
	b, _ := json.Marshal(data)
	err := json.Unmarshal(b, &p)
	if err != nil {
		logger.Info(ctx, fmt.Sprintf("Failed to create %T struct %s", p, err.Error()))
		return nil, err
	}
	validate := validator.New()
	if err = validate.Struct(p); err != nil {
		return nil, err
	}
	return &p, nil
}
