package protocol

import (
	"context"
	"errors"
	"os"
	"testing"

	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	apitypes "github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi/types"
	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
)

func TestPostToConnectionSuccess(t *testing.T) {
	os.Setenv("RETRY_RETRIES", "3")
	os.Setenv("RETRY_INTERVAL_MILLISECONDS", "100")

	mockMgrApi := common.MockManagementApi{}
	c := context.WithValue(context.Background(), common.ManagementApiConfig{}, &mockMgrApi)

	b := c.Value(common.ManagementApiConfig{})
	api := b.(common.ManagementApi)

	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)

	PostToConnection(ctx, api, uuid.NewString(), []byte{}, "", "")
}

func TestPostToConnectionUnknownError(t *testing.T) {
	os.Setenv("RETRY_RETRIES", "3")
	os.Setenv("RETRY_INTERVAL_MILLISECONDS", "100")

	mockMgrApi := common.MockManagementApi{}
	c := context.WithValue(context.Background(), common.ManagementApiConfig{}, &mockMgrApi)

	b := c.Value(common.ManagementApiConfig{})
	api := b.(common.ManagementApi)

	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, errors.New("Unknown error"))

	PostToConnection(ctx, api, uuid.NewString(), []byte{}, "", "")
}

func TestPostToConnectionGoneException(t *testing.T) {
	os.Setenv("RETRY_RETRIES", "3")
	os.Setenv("RETRY_INTERVAL_MILLISECONDS", "100")

	mockMgrApi := common.MockManagementApi{}
	c := context.WithValue(context.Background(), common.ManagementApiConfig{}, &mockMgrApi)

	b := c.Value(common.ManagementApiConfig{})
	api := b.(common.ManagementApi)
	gne := apitypes.GoneException{
		Message: ptr.String("GONE"),
	}

	mockMgrApi.On("PostToConnection", ctx, mock.Anything).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, &gne)

	PostToConnection(ctx, api, uuid.NewString(), []byte{}, "", "")
}

func TestNewValidatedStruct(t *testing.T) {
	ctx := context.Background()

	t.Run("valid input for Order", func(t *testing.T) {
		data := map[string]any{
			"orderId":     "12345",
			"siteUuid":    uuid.NewString(),
			"totalAmount": 100,
		}

		req, err := NewValidatedStruct[Order](ctx, &data)
		assert.NoError(t, err)
		assert.NotNil(t, req)
		assert.Equal(t, data["orderId"], *req.OrderId)
		assert.Equal(t, data["siteUuid"], *req.SiteUuid)
		assert.Equal(t, data["totalAmount"], *req.TotalAmount)
	})

	t.Run("missing required fields for Order", func(t *testing.T) {
		data := map[string]any{
			"amount": 100,
		}

		req, err := NewValidatedStruct[Order](ctx, &data)
		assert.Error(t, err)
		assert.Nil(t, req)
	})

	t.Run("invalid data types for Order", func(t *testing.T) {
		data := map[string]any{
			"orderId":     12345, // should be a string
			"siteUUid":    uuid.NewString(),
			"totalAmount": "100", // should be an int
		}

		req, err := NewValidatedStruct[Order](ctx, &data)
		assert.Error(t, err)
		assert.Nil(t, req)
	})
}
