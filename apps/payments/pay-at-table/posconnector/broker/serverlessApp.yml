service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-app

plugins:
  - serverless-plugin-resource-tagging
  - serverless-dotenv-plugin
  - serverless-plugin-tracing
  - serverless-domain-manager
  - serverless-plugin-scripts

useDotenv: true
variablesResolutionMode: 20210326
frameworkVersion: '3'

provider:
  name: aws
  runtime: provided.al2023
  region: ${opt:region}
  stackName: ${self:service}
  apiName: ${self:custom.baseName}-rest
  websocketsApiName: ${self:custom.baseName}-websocket
  vpc: ${self:custom.vpc.${opt:stage}, null}
  tracing:
    lambda: true
  deploymentBucket:
    name: ${cf:${env:COMPONENT_NAME}-${env:PART_NAME}-iac-s3.deploymentBucket}
    maxPreviousDeploymentArtifacts: 100
    blockPublicAccess: true
  environment:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    LOG_LEVEL: ${env:LOG_LEVEL}
  tags:
    STAGE: ${opt:stage}
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    service: ${env:COMPONENT_NAME}-${env:PART_NAME}
    env: ${opt:stage}
  stackTags: ${self:provider.tags}
  logs:
    websocket:
      fullExecutionData: ${self:custom.apiLogLevel.${opt:stage}, true}
      accessLogging: true
      executionLogging: true

package:
  exclude:
    - ./**
  include:
    - ./bootstrap

functions:
  - ${file(deployments/app/lambda.yml)}

resources:
  - ${file(deployments/app/iam.yml)}
  - ${file(deployments/app/resources.yml)}

custom:
  accountId: '${aws:accountId}'
  staticStages:
    dev: dev
    staging: staging
    prod: prod
  staticStage: ${self:custom.staticStages.${opt:stage}, 'dev'}
  baseName: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  dynamodbTableStackName: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-dynamodb
  pairingTableStreamArn: ${cf:${self:custom.dynamodbTableStackName}.PairingTableStreamArn}
  pairingTableName: ${self:custom.baseName}-Pairing
  cqrsStackName: ${opt:stage}-${env:COMPONENT_NAME}-cqrs
  posconnectorCqrsSqsUrl: ${cf:${self:custom.cqrsStackName}-iac-sqs.QueueURL}
  posconnectorCqrsSqsArn: ${cf:${self:custom.cqrsStackName}-iac-sqs.QueueARN}
  dbsSessionTable: ${self:custom.staticStage}-dbs-api-dynamodb-SessionCache
  managementAuth0ClientId: ${ssm:/${env:STATIC_ENV_NAME}-mp-api/AUTH0_CLIENT_ID}
  managementAuth0ClientSecret: ${ssm:/${env:STATIC_ENV_NAME}-mp-api/AUTH0_CLIENT_SECRET}
  pairingTableDataTtlInHours: 168
  sdkPosDeviceTypeTtlInHours: 4
  timeout: 30
  apiLogLevel:
    dev: true
    staging: true
    prod: true

  vpc:
    dev:
      securityGroupIds:
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg}
      subnetIds:
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01}
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02}
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03}
    staging:
      securityGroupIds:
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg}
      subnetIds:
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01}
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02}
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03}
    prod:
      securityGroupIds:
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg}
      subnetIds:
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01}
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02}
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03}

  wsApiEndpoint:
    Fn::Join:
      - ''
      - - 'https://'
        - Ref: 'WebsocketsApi'
        - '.execute-api.${self:provider.region}.amazonaws.com/'
        - ${opt:stage}
        - '/'

  # custom domain
  acmArn: ${ssm:/${opt:stage}-${env:COMPONENT_NAME}-acm-arn, ssm:/dev-${env:COMPONENT_NAME}-acm-arn}
  domainRootNames:
    dev: dev
    staging: show
    prod: com
  domainEnables:
    dev: true
    staging: true
    prod: true
  domainRootName: ${self:custom.domainRootNames.${opt:stage}, ""}
  domainEnabled: ${self:custom.domainEnables.${opt:stage}, false}
  domainName: '*.${env:COMPONENT_NAME}.myzeller.${self:custom.domainRootName}'
  customDomain:
    websocket:
      enabled: ${self:custom.domainEnabled}
      domainName: ${self:custom.domainName}
      certificateName: ${self:custom.domainName}
      certificateArn: '${self:custom.acmArn}'
      endpointType: REGIONAL
      securityPolicy: tls_1_2
      createRoute53Record: false
      apiType: websocket
  scripts:
    hooks:
      'after:deploy:deploy': /bin/bash ./bin/tagEventRules.sh -s ${self:service} -e ${opt:stage} -c ${env:COMPONENT_NAME} -p ${env:PART_NAME}
