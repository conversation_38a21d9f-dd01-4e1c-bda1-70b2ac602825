service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-posinterface

plugins:
  - serverless-plugin-resource-tagging
  - serverless-dotenv-plugin
  - serverless-plugin-tracing

useDotenv: true
variablesResolutionMode: 20210326
frameworkVersion: '3'

provider:
  name: aws
  runtime: provided.al2023
  region: ${opt:region}
  stackName: ${self:service}
  apiName: ${self:custom.baseName}-rest
  endpointType: ${self:custom.endpointTypes.${opt:stage}, self:custom.endpointTypes.other}
  apiGateway:
    metrics: true
    resourcePolicy:
      - ${self:custom.resourcePolicy.${opt:stage}, self:custom.resourcePolicy.other}
      - Effect: 'Allow'
        Principal:
          AWS: '*'
        Action: 'execute-api:Invoke'
        Resource: 'execute-api:/*/*/*'
  vpc: ${self:custom.vpc.${opt:stage}, null}
  vpcEndpointIds: ${self:custom.vpcEndpointIds.${opt:stage}, null}

  tracing:
    lambda: true
  deploymentBucket:
    name: ${cf:${env:COMPONENT_NAME}-${env:PART_NAME}-iac-s3.deploymentBucket}
    maxPreviousDeploymentArtifacts: 100
    blockPublicAccess: true
  environment:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    LOG_LEVEL: ${env:LOG_LEVEL}
  tags:
    STAGE: ${opt:stage}
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    service: ${env:COMPONENT_NAME}-${env:PART_NAME}
    env: ${opt:stage}
  stackTags: ${self:provider.tags}
  logs:
    restApi:
      fullExecutionData: ${self:custom.apiLogLevel.${opt:stage}, true}
      accessLogging: true
      executionLogging: true

package:
  exclude:
    - ./**
  include:
    - ./bootstrap

functions:
  - ${file(deployments/posinterface/lambda.yml)}

resources:
  - Conditions:
      ShouldAllowVpn:
        !And [ !Equals [ '${self:provider.region}', '${env:PRIMARY_REGION}' ], !Equals [ '${env:VPC_ENV_NAME}', 'dev' ] ]
  - ${file(deployments/posinterface/iam.yml)}
  - ${file(deployments/posinterface/parameters.yml)}

custom:
  accountId: '${aws:accountId}'
  staticStages:
    dev: dev
    staging: staging
    prod: prod

  endpointTypes:
    dev: private
    staging: private
    prod: private
    other: regional

  resourcePolicy:
    dev:
      Effect: Deny
      Principal:
        AWS: '*'
      Action: 'execute-api:Invoke'
      Resource: 'execute-api:/*/*/*'
      Condition:
        StringNotEquals:
          'aws:sourceVpce': '${self:custom.vpcEndpointIds.${opt:stage}, null}'
    staging:
      Effect: Deny
      Principal:
        AWS: '*'
      Action: 'execute-api:Invoke'
      Resource: 'execute-api:/*/*/*'
      Condition:
        StringNotEquals:
          'aws:sourceVpce': '${self:custom.vpcEndpointIds.${opt:stage}, null}'
    prod:
      Effect: Deny
      Principal:
        AWS: '*'
      Action: 'execute-api:Invoke'
      Resource: 'execute-api:/*/*/*'
      Condition:
        StringNotEquals:
          'aws:sourceVpce': '${self:custom.vpcEndpointIds.${opt:stage}, null}'
    other:
      Effect: Allow
      Principal:
        AWS: '*'
      Action: 'execute-api:Invoke'
      Resource: 'execute-api:/*/*/*'

  staticStage: ${self:custom.staticStages.${opt:stage}, 'dev'}
  baseName: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  dynamodbTableStackName: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-dynamodb
  pairingTableStreamArn: ${cf:${self:custom.dynamodbTableStackName}.PairingTableStreamArn}
  pairingTableName: ${self:custom.baseName}-Pairing
  cqrsStackName: ${opt:stage}-${env:COMPONENT_NAME}-cqrs
  posconnectorCqrsSqsUrl: ${cf:${self:custom.cqrsStackName}-iac-sqs.QueueURL}
  posconnectorCqrsSqsArn: ${cf:${self:custom.cqrsStackName}-iac-sqs.QueueARN}
  dbsSessionTable: ${self:custom.staticStage}-dbs-api-dynamodb-SessionCache
  pairingTableDataTtlInHours: 168
  timeout: 30
  apiLogLevel:
    dev: true
    staging: true
    prod: true

  # vpc
  vpcEndpointIds:
    dev:
      - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-api-gateway-endpoint}
      - !If [ShouldAllowVpn, 'vpce-0d16bbe7c101e4492', !Ref 'AWS::NoValue']
    staging: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-api-gateway-endpoint}
    prod: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-api-gateway-endpoint}
  #vpc
  vpc:
    dev:
      securityGroupIds:
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg}
      subnetIds:
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01}
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02}
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03}
    staging:
      securityGroupIds:
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg}
      subnetIds:
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01}
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02}
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03}
    prod:
      securityGroupIds:
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg}
      subnetIds:
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01}
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02}
        - ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03}
