package test

import (
	"context"
	"fmt"
	"log"
	"maps"
	"net/url"
	"os"
	"time"

	"slices"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	smithyendpoints "github.com/aws/smithy-go/endpoints"
	"github.com/golang-jwt/jwt"
	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	coreTypes "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/types"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	pTypes "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
	goJwt "gopkg.in/square/go-jose.v2/jwt"
)

const PairingTableName = "Pairing"
const DbsSessionCacheTable = "DbsSessionCache"
const DbsAuth0Audience = "https://devices.myzeller.dev"
const PosconnectorSessionCacheTable = "PosconnectorSessionCache"
const SdkSessionCacheTable = "sdk-api-SessionCache"
const SdkAuth0Audience = "https://sdk.myzeller.com"
const WsRestApiEndpoint = "https://test.posconnector.myzeller.dev"
const WsRestApiDomain = "test.posconnector.myzeller.dev"

type MockCredentialsProvider struct{}

func (MockCredentialsProvider) Retrieve(context.Context) (aws.Credentials, error) {
	return aws.Credentials{
		AccessKeyID: "AKID", SecretAccessKey: "SECRET", SessionToken: "SESSION",
		Source: "unit test credentials",
	}, nil
}

func LoadAwsMockConfig(ctx context.Context) context.Context {
	cfg, err := config.LoadDefaultConfig(
		ctx,
		config.WithRegion("local"),
		config.WithCredentialsProvider(MockCredentialsProvider{}),
	)
	if err != nil {
		logger.Panic(ctx, "Failed to load aws config")
	}

	ctx = context.WithValue(ctx, coreTypes.AwsDynamodbConfig{}, cfg)

	ctx = context.WithValue(ctx, coreTypes.AwsDynamodbEndpoint{}, "http://localhost:8000")

	return ctx
}

func SetTestEnv() {
	os.Setenv("PAIRING_TABLE", PairingTableName)
	os.Setenv("TTL_IN_HOURS", "168")
	os.Setenv("SDK_POS_TTL_IN_HOURS", "4")
	os.Setenv(common.DBS_SESSION_TABLE, DbsSessionCacheTable)
	os.Setenv(common.DBS_AUTH0_AUDIENCE, DbsAuth0Audience)
	os.Setenv(common.SESSION_CACHE_TABLE, PosconnectorSessionCacheTable)
	os.Setenv(common.SDK_AUTH0_AUDIENCE, SdkAuth0Audience)
	os.Setenv(common.MANAGEMENT_AUTH0_CLIENT_ID, uuid.NewString())
	os.Setenv(common.MANAGEMENT_AUTH0_CLIENT_SECRET, uuid.NewString())
}

type resolverV2 struct {
	url *string
}

func (r *resolverV2) ResolveEndpoint(ctx context.Context, params dynamodb.EndpointParameters) (
	smithyendpoints.Endpoint, error,
) {
	if r.url != nil {
		u, err := url.Parse(*r.url)
		if err != nil {
			return smithyendpoints.Endpoint{}, err
		}
		return smithyendpoints.Endpoint{
			URI: *u,
		}, nil
	}

	// delegate back to the default v2 resolver otherwise
	return dynamodb.NewDefaultEndpointResolverV2().ResolveEndpoint(ctx, params)
}

func SetupLocalTable(ctx context.Context) {
	cfg, _ := ctx.Value(coreTypes.AwsDynamodbConfig{}).(aws.Config)
	db := dynamodb.NewFromConfig(cfg, func(o *dynamodb.Options) {
		o.EndpointResolverV2 = &resolverV2{url: aws.String("http://localhost:8000")}
	})
	var lastEvaluatedTableName *string
	existTable := false
	for {
		tables, err := db.ListTables(ctx, &dynamodb.ListTablesInput{
			ExclusiveStartTableName: lastEvaluatedTableName,
		})
		if err != nil {
			fmt.Println("err", err)
			break
		}
		fmt.Println("table name", tables.TableNames)
		if slices.Contains(tables.TableNames, PairingTableName) {
			existTable = true
			break
		}
		if tables.LastEvaluatedTableName == nil {
			break
		}
		lastEvaluatedTableName = tables.LastEvaluatedTableName
	}
	if !existTable {
		output, err := SetupPairingTable(ctx, db)
		if err != nil {
			logger.Error(ctx, err.Error())
			log.Panicf("Failed to create db table %s", PairingTableName)
		}
		fmt.Printf("create table response %v\n", &output.TableDescription.TableName)
		output, err = SetupDbsSessionTable(ctx, db)
		if err != nil {
			logger.Error(ctx, err.Error())
			log.Panicf("Failed to create db table %s", DbsSessionCacheTable)
		}
		fmt.Printf("create table response %v\n", &output.TableDescription.TableName)
		output, err = SetupPosconnectorSessionTable(ctx, db)
		if err != nil {
			logger.Error(ctx, err.Error())
			log.Panicf("Failed to create db table %s", PosconnectorSessionCacheTable)
		}
		fmt.Printf("create table response %v\n", &output.TableDescription.TableName)
		output, err = SetupSdkSessionTable(ctx, db)
		if err != nil {
			logger.Error(ctx, err.Error())
			log.Panicf("Failed to create db table %s", SdkSessionCacheTable)
		}
		fmt.Printf("create table response %v\n", &output.TableDescription.TableName)
	}
}

func SetupPairingTable(ctx context.Context, db *dynamodb.Client) (*dynamodb.CreateTableOutput, error) {
	return db.CreateTable(ctx, &dynamodb.CreateTableInput{
		TableName: aws.String(PairingTableName),
		AttributeDefinitions: []types.AttributeDefinition{
			{
				AttributeName: aws.String("id"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("type"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("deviceUuid"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("connectionId"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("action"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("status"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("entityUuid"),
				AttributeType: types.ScalarAttributeTypeS,
			},
		},
		KeySchema: []types.KeySchemaElement{
			{
				AttributeName: aws.String("id"),
				KeyType:       types.KeyTypeHash,
			},
			{
				AttributeName: aws.String("type"),
				KeyType:       types.KeyTypeRange,
			},
		},
		BillingMode: types.BillingModePayPerRequest,
		GlobalSecondaryIndexes: []types.GlobalSecondaryIndex{
			{
				IndexName: aws.String("typeGsi"),
				KeySchema: []types.KeySchemaElement{
					{
						AttributeName: aws.String("type"),
						KeyType:       types.KeyTypeHash,
					},
					{
						AttributeName: aws.String("id"),
						KeyType:       types.KeyTypeRange,
					},
				},
				Projection: &types.Projection{
					ProjectionType: types.ProjectionTypeAll,
				},
			},
			{
				IndexName: aws.String("connectionIdGsi"),
				KeySchema: []types.KeySchemaElement{
					{
						AttributeName: aws.String("connectionId"),
						KeyType:       types.KeyTypeHash,
					},
					{
						AttributeName: aws.String("type"),
						KeyType:       types.KeyTypeRange,
					},
				},
				Projection: &types.Projection{
					ProjectionType: types.ProjectionTypeAll,
				},
			},
			{
				IndexName: aws.String("deviceUuidGsi"),
				KeySchema: []types.KeySchemaElement{
					{
						AttributeName: aws.String("deviceUuid"),
						KeyType:       types.KeyTypeHash,
					},
					{
						AttributeName: aws.String("type"),
						KeyType:       types.KeyTypeRange,
					},
				},
				Projection: &types.Projection{
					ProjectionType: types.ProjectionTypeAll,
				},
			},
			{
				IndexName: aws.String("actionGsi"),
				KeySchema: []types.KeySchemaElement{
					{
						AttributeName: aws.String("action"),
						KeyType:       types.KeyTypeHash,
					},
					{
						AttributeName: aws.String("type"),
						KeyType:       types.KeyTypeRange,
					},
				},
				Projection: &types.Projection{
					ProjectionType: types.ProjectionTypeAll,
				},
			},
			{
				IndexName: aws.String("statusGsi"),
				KeySchema: []types.KeySchemaElement{
					{
						AttributeName: aws.String("status"),
						KeyType:       types.KeyTypeHash,
					},
					{
						AttributeName: aws.String("type"),
						KeyType:       types.KeyTypeRange,
					},
				},
				Projection: &types.Projection{
					ProjectionType: types.ProjectionTypeAll,
				},
			},
			{
				IndexName: aws.String("entityGsi"),
				KeySchema: []types.KeySchemaElement{
					{
						AttributeName: aws.String("entityUuid"),
						KeyType:       types.KeyTypeHash,
					},
					{
						AttributeName: aws.String("type"),
						KeyType:       types.KeyTypeRange,
					},
				},
				Projection: &types.Projection{
					ProjectionType: types.ProjectionTypeAll,
				},
			},
		},
	})
}

func SetupPosconnectorSessionTable(ctx context.Context, db *dynamodb.Client) (*dynamodb.CreateTableOutput, error) {
	return db.CreateTable(ctx, &dynamodb.CreateTableInput{
		TableName: aws.String(PosconnectorSessionCacheTable),
		AttributeDefinitions: []types.AttributeDefinition{
			{
				AttributeName: aws.String("id"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("type"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("accessToken"),
				AttributeType: types.ScalarAttributeTypeS,
			},
		},
		KeySchema: []types.KeySchemaElement{
			{
				AttributeName: aws.String("id"),
				KeyType:       types.KeyTypeHash,
			},
			{
				AttributeName: aws.String("type"),
				KeyType:       types.KeyTypeRange,
			},
		},
		BillingMode: types.BillingModePayPerRequest,
		GlobalSecondaryIndexes: []types.GlobalSecondaryIndex{
			{
				IndexName: aws.String("accessTokenGsi"),
				KeySchema: []types.KeySchemaElement{
					{
						AttributeName: aws.String("accessToken"),
						KeyType:       types.KeyTypeHash,
					},
					{
						AttributeName: aws.String("type"),
						KeyType:       types.KeyTypeRange,
					},
				},
				Projection: &types.Projection{
					ProjectionType: types.ProjectionTypeAll,
				},
			},
		},
	})
}

func SetupDbsSessionTable(ctx context.Context, db *dynamodb.Client) (*dynamodb.CreateTableOutput, error) {
	return db.CreateTable(ctx, &dynamodb.CreateTableInput{
		TableName: aws.String(DbsSessionCacheTable),
		AttributeDefinitions: []types.AttributeDefinition{
			{
				AttributeName: aws.String("id"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("type"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("accessToken"),
				AttributeType: types.ScalarAttributeTypeS,
			},
		},
		KeySchema: []types.KeySchemaElement{
			{
				AttributeName: aws.String("id"),
				KeyType:       types.KeyTypeHash,
			},
			{
				AttributeName: aws.String("type"),
				KeyType:       types.KeyTypeRange,
			},
		},
		BillingMode: types.BillingModePayPerRequest,
		GlobalSecondaryIndexes: []types.GlobalSecondaryIndex{
			{
				IndexName: aws.String("accessTokenGsiv2"),
				KeySchema: []types.KeySchemaElement{
					{
						AttributeName: aws.String("accessToken"),
						KeyType:       types.KeyTypeHash,
					},
					{
						AttributeName: aws.String("type"),
						KeyType:       types.KeyTypeRange,
					},
				},
				Projection: &types.Projection{
					ProjectionType: types.ProjectionTypeAll,
				},
			},
		},
	})
}

func SetupSdkSessionTable(ctx context.Context, db *dynamodb.Client) (*dynamodb.CreateTableOutput, error) {
	return db.CreateTable(ctx, &dynamodb.CreateTableInput{
		TableName: aws.String(SdkSessionCacheTable),
		AttributeDefinitions: []types.AttributeDefinition{
			{
				AttributeName: aws.String("id"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("type"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("accessToken"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("deviceUuid"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("modelSerial"),
				AttributeType: types.ScalarAttributeTypeS,
			},
		},
		KeySchema: []types.KeySchemaElement{
			{
				AttributeName: aws.String("id"),
				KeyType:       types.KeyTypeHash,
			},
			{
				AttributeName: aws.String("type"),
				KeyType:       types.KeyTypeRange,
			},
		},
		BillingMode: types.BillingModePayPerRequest,
		GlobalSecondaryIndexes: []types.GlobalSecondaryIndex{
			{
				IndexName: aws.String("accessTokenGsi"),
				KeySchema: []types.KeySchemaElement{
					{
						AttributeName: aws.String("accessToken"),
						KeyType:       types.KeyTypeHash,
					},
					{
						AttributeName: aws.String("type"),
						KeyType:       types.KeyTypeRange,
					},
				},
				Projection: &types.Projection{
					ProjectionType: types.ProjectionTypeAll,
				},
			},
			{
				IndexName: aws.String("deviceGsi"),
				KeySchema: []types.KeySchemaElement{
					{
						AttributeName: aws.String("deviceUuid"),
						KeyType:       types.KeyTypeHash,
					},
					{
						AttributeName: aws.String("type"),
						KeyType:       types.KeyTypeRange,
					},
				},
				Projection: &types.Projection{
					ProjectionType: types.ProjectionTypeAll,
				},
			},
			{
				IndexName: aws.String("modelSerialGsi"),
				KeySchema: []types.KeySchemaElement{
					{
						AttributeName: aws.String("modelSerial"),
						KeyType:       types.KeyTypeHash,
					},
					{
						AttributeName: aws.String("type"),
						KeyType:       types.KeyTypeRange,
					},
				},
				Projection: &types.Projection{
					ProjectionType:   types.ProjectionTypeInclude,
					NonKeyAttributes: []string{"model", "id", "serial"},
				},
			},
		},
	})
}

func GenerateAccessToken(ttl *time.Duration, aud []string, sub *string, customerUuid *string) string {
	privateKey := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
	ctx := context.Background()
	key, err := jwt.ParseRSAPrivateKeyFromPEM([]byte(privateKey))
	if err != nil {
		logger.Error(ctx, err.Error())
		return ""
	}
	now := time.Now().UTC()

	claims := make(jwt.MapClaims)
	if ttl != nil {
		claims["exp"] = now.Add(*ttl).Unix() // The expiration time after which the token must be disregarded.
	}
	claims["iat"] = now.Unix() // The time at which the token was issued.
	claims["nbf"] = now.Unix() // The time before which the token must be disregarded.
	if sub != nil {
		claims["sub"] = *sub // The time before which the token must be disregarded.
	}
	if aud != nil {
		claims["aud"] = aud
	}

	if customerUuid != nil {
		claims["https://identity.myzeller.com/customerUuid"] = *customerUuid
	}

	token, err := jwt.NewWithClaims(jwt.SigningMethodRS256, claims).SignedString(key)
	if err != nil {
		logger.Error(ctx, "create: sign token: "+err.Error())
		return ""
	}

	t, err := goJwt.ParseSigned(token)
	if err != nil {
		logger.Error(context.Background(), err.Error())
	}
	var claim map[string]any
	err = t.UnsafeClaimsWithoutVerification(&claim)
	if err != nil {
		logger.Error(context.Background(), err.Error())
	}
	return token
}

type MockSqs struct {
	mock.Mock
}

func (sqs *MockSqs) DeleteMessage(ctx context.Context, params *sqs.DeleteMessageInput, optFns ...func(*sqs.Options)) (*sqs.DeleteMessageOutput, error) {
	return nil, nil
}

func GetConnectionCoreMap(updates map[string]string) map[string]string {
	data := map[string]string{
		"id":                 uuid.NewString(),
		"type":               string(pTypes.CONNECTION_CORE),
		"connectionEndpoint": WsRestApiEndpoint,
		"deviceUuid":         uuid.NewString(),
		"entityUuid":         uuid.NewString(),
		"status":             "CONNECTED",
		"provider":           uuid.NewString(),
		"deviceType":         string(pTypes.DeviceTypeTerminal),
		"createdTime":        time.Now().UTC().Format(time.RFC3339),
	}
	maps.Copy(data, updates)

	return data
}

func GetSubscriberMap(update map[string]any) map[string]any {
	data := map[string]any{
		"id":                 uuid.NewString(),
		"eventId":            uuid.NewString(),
		"timestamp":          uuid.NewString(),
		"siteUuid":           uuid.NewString(),
		"entityUuid":         uuid.NewString(),
		"deviceUuid":         uuid.NewString(),
		"connectionId":       uuid.NewString(),
		"connectionEndpoint": WsRestApiEndpoint,
		"action":             "subscribeOrdersUpdate",
		"status":             "CONNECTED",
	}
	maps.Copy(data, update)

	return data
}

func GetPairingMap(update map[string]any) map[string]any {
	deviceUuid := uuid.NewString()
	data := map[string]any{
		"id":          uuid.NewString(),
		"type":        fmt.Sprintf("%s%s", pTypes.POSINTERFACE_PAIR_POS, deviceUuid),
		"venueId":     uuid.NewString(),
		"deviceType":  string(pTypes.DeviceTypePOS),
		"deviceUuid":  deviceUuid,
		"entityUuid":  uuid.NewString(),
		"provider":    string(pTypes.IMPOS),
		"status":      "ACTIVE",
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}
	maps.Copy(data, update)

	return data
}
