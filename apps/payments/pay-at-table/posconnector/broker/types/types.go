package types

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/aws/smithy-go/ptr"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
)

type RequestAuthorizer struct {
	DeviceUuid    string `json:"deviceUuid" validate:"required"`
	EntityUuid    string `json:"entityUuid" validate:"required"`
	SiteUuid      string `json:"siteUuid"`
	Status        string `json:"status" validate:"required"`
	CustomerUuid  string `json:"customerUuid"`
	PrincipalId   string `json:"principalId"`
	Provider      string `json:"provider" validate:"required"`
	DeviceType    string `json:"deviceType" validate:"required"`
	ZellerTraceId string `json:"zellerTraceId,omitempty"`
}

type DbItemType string

const (
	POSINTERFACE_PAIR_DEVICE DbItemType = "posinterface.pair.device."
	POSINTERFACE_PAIR_POS    DbItemType = "posinterface.pair.pos."
	POSINTERFACE_PAIR        DbItemType = "posinterface.pair."
	CONNECTION_CORE          DbItemType = "connection.core"
	SUBSCRIBER               DbItemType = "subscriber."
	METADATA                 DbItemType = "metadata."
)

type PosConnectorAction string

const (
	ActionSubscribeOrdersUpdate PosConnectorAction = "subscribeOrdersUpdate"
	ActionGetOrders             PosConnectorAction = "getOrders"
	ActionGetOrder              PosConnectorAction = "getOrder"
	ActionUpdateOrderItem       PosConnectorAction = "updateOrderItem"
	ActionPayment               PosConnectorAction = "payment"
	ActionPurchase              PosConnectorAction = "purchase"
	ActionRefund                PosConnectorAction = "refund"
	ActionReversal              PosConnectorAction = "reversal"
)

type PosConnectorEvent struct {
	EventId   *string            `json:"eventId"`
	Timestamp *string            `json:"timestamp"`
	Action    PosConnectorAction `json:"action"`
	Data      any                `json:"data"`
}

func NewPosConnectorEvent(action PosConnectorAction, data any, eventId *string) PosConnectorEvent {
	if eventId == nil {
		eventId = ptr.String(uuid.NewString())
	}

	return PosConnectorEvent{
		EventId:   eventId,
		Timestamp: ptr.String(strconv.FormatInt(time.Now().UnixMilli(), 10)),
		Action:    action,
		Data:      data,
	}
}

type PosProvider string

var (
	HL      PosProvider = "HL_POS"
	ORACLE  PosProvider = "ORACLE_POS"
	IMPOS   PosProvider = "IMPOS"
	SDK     PosProvider = "SDK"
	TEVALIS PosProvider = "TEVALIS_POS"
)

type DeviceType string

const (
	DeviceTypeTerminal DeviceType = "TERMINAL"
	DeviceTypePOS      DeviceType = "POS"
)

const (
	AUTHORIZATION          string = "Authorization"
	SEC_WEBSOCKET_PROTOCOL string = "Sec-WebSocket-Protocol"
	DEVICEUUID             string = "DeviceUuid"
	BEARER                 string = "Bearer "
)

func NewRequestAuthorizerFromInterface(ctx context.Context, data any) (*RequestAuthorizer, error) {
	if data == nil {
		errorMsg := "authorizer in request context cant be null"
		logger.Error(ctx, errorMsg)
		return nil, errors.New(errorMsg)
	}
	authBytes, err := json.Marshal(data)
	if err != nil {
		logger.Error(ctx, "unknown authorizer "+err.Error())
		return nil, err
	}
	authorizer := RequestAuthorizer{}
	err = json.Unmarshal(authBytes, &authorizer)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Invalid authorizer struct %s", err.Error()))
		return nil, err
	}

	if authorizer.DeviceType == "" {
		authorizer.DeviceType = string(DeviceTypeTerminal)
	}

	validate := validator.New()
	err = validate.Struct(authorizer)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to validate authorizer struct %s", err.Error()))
		return nil, err
	}
	return &authorizer, nil
}

type TransactionType string

const (
	PURCHASE TransactionType = "PURCHASE"
	REFUND   TransactionType = "REFUND"
	REVERSAL TransactionType = "REVERSAL"
)
