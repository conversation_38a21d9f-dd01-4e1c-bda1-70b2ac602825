package types

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestNewRequestAuthorizerFromInterfaceNilValue(t *testing.T) {
	authorizer, err := NewRequestAuthorizerFromInterface(context.Background(), nil)
	assert.Nil(t, authorizer)
	assert.NotNil(t, err, err)
}

func TestNewRequestAuthorizerFromInterfaceInvalidJson(t *testing.T) {
	authorizer, err := NewRequestAuthorizerFromInterface(context.Background(), "invalid json data")
	assert.Nil(t, authorizer)
	assert.NotNil(t, err, err)
}

func TestNewRequestAuthorizerFromInterfaceMissingRequiredFields(t *testing.T) {
	cases := []map[string]any{
		// missing entityUuid
		{
			"accessToken": uuid.NewString(),
			"deviceUuid":  uuid.NewString(),
			"siteUuid":    uuid.NewString(),
			"status":      "VALID",
			"provider":    string(HL),
		},
		// missing deviceUuid
		{
			"accessToken": uuid.NewString(),
			"entityUuid":  uuid.NewString(),
			"siteUuid":    uuid.NewString(),
			"status":      "VALID",
			"provider":    string(HL),
		},
		// missing status
		{
			"accessToken": uuid.NewString(),
			"entityUuid":  uuid.NewString(),
			"deviceUuid":  uuid.NewString(),
			"siteUuid":    uuid.NewString(),
			"provider":    string(HL),
		},
		// missing provider
		{
			"accessToken": uuid.NewString(),
			"entityUuid":  uuid.NewString(),
			"deviceUuid":  uuid.NewString(),
			"siteUuid":    uuid.NewString(),
			"status":      "VALID",
		},
	}
	for _, c := range cases {
		authorizer, err := NewRequestAuthorizerFromInterface(context.Background(), c)
		assert.Nil(t, authorizer)
		assert.NotNil(t, err, err)
	}
}

func TestNewRequestAuthorizerFromInterfaceMissingCustomerUuid(t *testing.T) {
	data := map[string]string{
		"entityUuid":  uuid.NewString(),
		"deviceUuid":  uuid.NewString(),
		"siteUuid":    uuid.NewString(),
		"accessToken": uuid.NewString(),
		"provider":    string(HL),
		"status":      "VALID",
	}
	authorizer, err := NewRequestAuthorizerFromInterface(context.Background(), data)
	assert.Nil(t, err, err)
	assert.Equal(t, data["entityUuid"], authorizer.EntityUuid)
	assert.Equal(t, data["deviceUuid"], authorizer.DeviceUuid)
	_, ok := data["customerUuid"]
	assert.False(t, ok)
}

func TestNewRequestAuthorizerFromInterfaceWithCustomerUuid(t *testing.T) {
	data := map[string]string{
		"entityUuid":   uuid.NewString(),
		"deviceUuid":   uuid.NewString(),
		"accessToken":  uuid.NewString(),
		"siteUuid":     uuid.NewString(),
		"customerUuid": uuid.NewString(),
		"provider":     string(HL),
		"status":       "VALID",
	}
	authorizer, err := NewRequestAuthorizerFromInterface(context.Background(), data)
	assert.Nil(t, err, err)
	assert.Equal(t, data["entityUuid"], authorizer.EntityUuid)
	assert.Equal(t, data["deviceUuid"], authorizer.DeviceUuid)
	assert.Equal(t, data["siteUuid"], authorizer.SiteUuid)
	assert.Equal(t, data["customerUuid"], authorizer.CustomerUuid)
}

func TestNewPosconnectorEvent(t *testing.T) {
	event := NewPosConnectorEvent(ActionGetOrders, nil, nil)
	assert.NotNil(t, event.EventId)
	assert.NotNil(t, event.Timestamp)
	assert.Equal(t, ActionGetOrders, event.Action)
	assert.Nil(t, event.Data)

	eventId := uuid.NewString()
	event = NewPosConnectorEvent(ActionGetOrders, "test data", &eventId)
	assert.Equal(t, *event.EventId, eventId)
	assert.Equal(t, *&event.Data, "test data")
}
