package tests

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	logger "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/session"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func TestSDKSessionCachePing(t *testing.T) {
	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	customerUuid := uuid.NewString()

	to := createTestObject(types.SDK)
	to.deviceUuid = terminalDeviceUuid
	to.entityUuid = entityUuid
	to.pairedDeviceId = sdkDeviceUuid
	terminalWs := setupConnection(to)
	defer func() {
		terminalWs.wsChannel.Close()
	}()

	// generate token
	claims := AccessTokenClaims{
		Aud:          []string{"https://sdk.myzeller.com"},
		CustomerUuid: &customerUuid,
	}
	accessToken := GenerateAccessToken(claims)

	log.Println("SDK ACCESS TOKEN", accessToken)

	// session cache
	identityCache := session.IdentityCache{
		Id:           &accessToken,
		Type:         (*string)(&session.CacheTypeIdentitySub),
		Auth0Sub:     ptr.String(uuid.NewString()),
		CustomerUuid: &customerUuid,
		EntityUuid:   &entityUuid,
		DeviceUuid:   &sdkDeviceUuid,
		TTL:          ptr.Int(int(time.Now().Add(time.Hour).UnixMilli())),
	}
	SaveIdentityInSdkSessionTable(identityCache)

	deviceCache := session.DeviceCache{
		Id:          &sdkDeviceUuid,
		Type:        (*string)(&session.CacheTypeDeviceUuid),
		EntityUuid:  &entityUuid,
		Model:       ptr.String(uuid.NewString()),
		Serial:      ptr.String(uuid.NewString()),
		ModelSerial: ptr.String(uuid.NewString()),
		Status:      (*string)(&session.DeviceStatusActive),
	}
	SaveDeviceInSdkSessionTable(deviceCache)

	// create pairing
	pairingItem := map[string]any{
		"id":             sdkDeviceUuid,
		"type":           fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, sdkDeviceUuid),
		"entityUuid":     entityUuid,
		"deviceUuid":     sdkDeviceUuid,
		"provider":       string(types.SDK),
		"timestamp":      strconv.FormatInt(time.Now().Unix(), 10),
		"status":         "ACTIVE",
		"deviceType":     string(types.DeviceTypePOS),
		"pairedDeviceId": terminalDeviceUuid,
	}
	logger.Info(ctx, fmt.Sprintf("save pairing item %s, %v", pairingTableName, utils.BestEffortStringify(pairingItem, false)))
	item, _ := attributevalue.MarshalMap(pairingItem)
	_, err := to.dbClient.InsertItem(ctx, pairingTableName, &item, nil)
	if err != nil {
		log.Panic(err.Error())
	}

	var resp *http.Response
	wsChannel, resp, err := websocket.DefaultDialer.Dial(to.wsEndpoint, http.Header{
		"Sec-WebSocket-Protocol": []string{fmt.Sprintf("posconnector, Authorization|%s, DeviceUuid|%s", accessToken, sdkDeviceUuid)},
	})
	logger.Info(to.ctx, "receive status code:"+strconv.Itoa(resp.StatusCode))
	if err != nil {
		b, _ := io.ReadAll(resp.Body)
		fmt.Println("response body:" + string(b))
		log.Panic("dial:", err.Error())
	}

	done := make(chan struct{})
	hasError := make(chan error)
	go func() {
		for {
			messageType, message, err := wsChannel.ReadMessage()
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("message type %d, message %s", messageType, message))
				hasError <- err
			}
			logger.Info(ctx, "get message "+string(message))
			var response map[string]any
			err = json.Unmarshal(message, &response)
			assert.Nil(t, err)
			action, ok := response["action"]
			assert.True(t, ok)
			assert.Equal(t, string(protocol.PING), action)
			data, ok := response["data"].(map[string]any)

			assert.True(t, ok)
			assert.Equal(t, "pong", data["response"])

			assert.Equal(t, data["error"], nil)
			close(done)
		}
	}()
	err = wsChannel.WriteJSON(map[string]string{
		"eventId":   uuid.NewString(),
		"timestamp": fmt.Sprint(time.Now().UnixMilli()),
		"action":    string(protocol.PING),
	})
	assert.Nil(t, err)
	ticker := time.NewTicker(time.Second * 3)

	select {
	case <-ticker.C:
		return
	case e := <-hasError:
		assert.Fail(t, "has error"+e.Error())
	case <-done:
		return
	}
}

func TestSDKSessionCachePingNewSession(t *testing.T) {
	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	customerUuid := uuid.NewString()

	to := createTestObject(types.SDK)
	to.deviceUuid = terminalDeviceUuid
	to.entityUuid = entityUuid
	to.pairedDeviceId = sdkDeviceUuid
	terminalWs := setupConnection(to)
	defer func() {
		terminalWs.wsChannel.Close()
	}()

	// generate token
	claims := AccessTokenClaims{
		Aud:          []string{"https://sdk.myzeller.com"},
		CustomerUuid: &customerUuid,
	}
	accessToken := GenerateAccessToken(claims)

	deviceCache := session.DeviceCache{
		Id:          &sdkDeviceUuid,
		Type:        (*string)(&session.CacheTypeDeviceUuid),
		EntityUuid:  &entityUuid,
		Model:       ptr.String(uuid.NewString()),
		Serial:      ptr.String(uuid.NewString()),
		ModelSerial: ptr.String(uuid.NewString()),
		Status:      (*string)(&session.DeviceStatusActive),
	}
	SaveDeviceInSdkSessionTable(deviceCache)

	// do not create an identity cache

	// create pairing
	pairingItem := map[string]any{
		"id":             sdkDeviceUuid,
		"type":           fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, sdkDeviceUuid),
		"entityUuid":     entityUuid,
		"deviceUuid":     sdkDeviceUuid,
		"provider":       string(types.SDK),
		"timestamp":      strconv.FormatInt(time.Now().Unix(), 10),
		"status":         "ACTIVE",
		"deviceType":     string(types.DeviceTypePOS),
		"pairedDeviceId": terminalDeviceUuid,
	}
	logger.Info(ctx, fmt.Sprintf("save pairing item %s, %v", pairingTableName, utils.BestEffortStringify(pairingItem, false)))
	item, _ := attributevalue.MarshalMap(pairingItem)
	_, err := to.dbClient.InsertItem(ctx, pairingTableName, &item, nil)
	if err != nil {
		log.Panic(err.Error())
	}

	var resp *http.Response
	wsChannel, resp, err := websocket.DefaultDialer.Dial(to.wsEndpoint, http.Header{
		"Sec-WebSocket-Protocol": []string{fmt.Sprintf("posconnector, Authorization|%s, DeviceUuid|%s", accessToken, sdkDeviceUuid)},
	})
	logger.Info(to.ctx, "receive status code:"+strconv.Itoa(resp.StatusCode))
	if err != nil {
		b, _ := io.ReadAll(resp.Body)
		fmt.Println("response body:" + string(b))
		log.Panic("dial:", err.Error())
	}

	done := make(chan struct{})
	hasError := make(chan error)
	go func() {
		for {
			messageType, message, err := wsChannel.ReadMessage()
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("message type %d, message %s", messageType, message))
				hasError <- err
			}
			logger.Info(ctx, "get message "+string(message))
			var response map[string]any
			err = json.Unmarshal(message, &response)
			assert.Nil(t, err)
			action, ok := response["action"]
			assert.True(t, ok)
			assert.Equal(t, string(protocol.PING), action)
			data, ok := response["data"].(map[string]any)

			assert.True(t, ok)
			assert.Equal(t, "pong", data["response"])

			assert.Equal(t, data["error"], nil)
			close(done)
		}
	}()
	err = wsChannel.WriteJSON(map[string]string{
		"eventId":   uuid.NewString(),
		"timestamp": fmt.Sprint(time.Now().UnixMilli()),
		"action":    string(protocol.PING),
	})
	assert.Nil(t, err)
	ticker := time.NewTicker(time.Second * 3)

	select {
	case <-ticker.C:
		return
	case e := <-hasError:
		assert.Fail(t, "has error"+e.Error())
	case <-done:
		return
	}
}

func TestSDKSessionCacheGetPairedDevices(t *testing.T) {
	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	customerUuid := uuid.NewString()

	to := createTestObject(types.SDK)
	to.deviceUuid = terminalDeviceUuid
	to.entityUuid = entityUuid
	to.pairedDeviceId = sdkDeviceUuid
	terminalWs := setupConnection(to)
	defer func() {
		terminalWs.wsChannel.Close()
	}()

	// generate token
	claims := AccessTokenClaims{
		Aud:          []string{"https://sdk.myzeller.com"},
		CustomerUuid: &customerUuid,
	}
	accessToken := GenerateAccessToken(claims)

	log.Println("SDK ACCESS TOKEN", accessToken)

	// session cache
	identityCache := session.IdentityCache{
		Id:           &accessToken,
		Type:         (*string)(&session.CacheTypeIdentitySub),
		Auth0Sub:     ptr.String(uuid.NewString()),
		CustomerUuid: &customerUuid,
		EntityUuid:   &entityUuid,
		DeviceUuid:   &sdkDeviceUuid,
		TTL:          ptr.Int(int(time.Now().Add(time.Hour).UnixMilli())),
	}
	SaveIdentityInSdkSessionTable(identityCache)

	deviceCache := session.DeviceCache{
		Id:          &sdkDeviceUuid,
		Type:        (*string)(&session.CacheTypeDeviceUuid),
		EntityUuid:  &entityUuid,
		Model:       ptr.String(uuid.NewString()),
		Serial:      ptr.String(uuid.NewString()),
		ModelSerial: ptr.String(uuid.NewString()),
		Status:      (*string)(&session.DeviceStatusActive),
	}
	SaveDeviceInSdkSessionTable(deviceCache)

	// create pairing
	pairingItem := map[string]any{
		"id":             sdkDeviceUuid,
		"type":           fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, sdkDeviceUuid),
		"entityUuid":     entityUuid,
		"deviceUuid":     sdkDeviceUuid,
		"provider":       string(types.SDK),
		"timestamp":      strconv.FormatInt(time.Now().Unix(), 10),
		"status":         "ACTIVE",
		"deviceType":     string(types.DeviceTypePOS),
		"pairedDeviceId": terminalDeviceUuid,
	}
	logger.Info(ctx, fmt.Sprintf("save pairing item %s, %v", pairingTableName, utils.BestEffortStringify(pairingItem, false)))
	item, _ := attributevalue.MarshalMap(pairingItem)
	_, err := to.dbClient.InsertItem(ctx, pairingTableName, &item, nil)
	if err != nil {
		log.Panic(err.Error())
	}

	var resp *http.Response
	wsChannel, resp, err := websocket.DefaultDialer.Dial(to.wsEndpoint, http.Header{
		"Sec-WebSocket-Protocol": []string{fmt.Sprintf("posconnector, Authorization|%s, DeviceUuid|%s", accessToken, sdkDeviceUuid)},
	})
	logger.Info(to.ctx, "receive status code:"+strconv.Itoa(resp.StatusCode))
	if err != nil {
		b, _ := io.ReadAll(resp.Body)
		fmt.Println("response body:" + string(b))
		log.Panic("dial:", err.Error())
	}

	done := make(chan struct{})
	hasError := make(chan error)
	go func() {
		for {
			messageType, message, err := wsChannel.ReadMessage()
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("message type %d, message %s", messageType, message))
				hasError <- err
			}
			logger.Info(ctx, "get message "+string(message))
			var response map[string]any
			err = json.Unmarshal(message, &response)
			assert.Nil(t, err)
			action, ok := response["action"]
			assert.True(t, ok)
			assert.Equal(t, string(protocol.GET_PAIRED_DEVICES), action)
			data, ok := response["data"].(map[string]any)

			assert.True(t, ok)
			pairedDevices, ok := data["pairedDevices"].([]any)

			assert.True(t, ok)
			connectedDevices, ok := data["connectedDevices"].([]any)
			assert.True(t, ok)
			assert.Equal(t, pairedDevices, []any{terminalDeviceUuid})
			assert.Equal(t, 1, len(pairedDevices))

			assert.Equal(t, 1, len(connectedDevices))
			assert.Equal(t, connectedDevices[0].(map[string]any)["deviceType"], "TERMINAL")
			assert.Equal(t, connectedDevices[0].(map[string]any)["deviceUuid"], terminalDeviceUuid)
			assert.Equal(t, connectedDevices[0].(map[string]any)["provider"], string(types.SDK))
			assert.Equal(t, connectedDevices[0].(map[string]any)["status"], "CONNECTED")

			assert.Equal(t, data["error"], nil)
			close(done)
		}
	}()
	err = wsChannel.WriteJSON(map[string]string{
		"eventId":   uuid.NewString(),
		"timestamp": fmt.Sprint(time.Now().UnixMilli()),
		"action":    string(protocol.GET_PAIRED_DEVICES),
	})
	assert.Nil(t, err)
	ticker := time.NewTicker(time.Second * 3)

	select {
	case <-ticker.C:
		return
	case e := <-hasError:
		assert.Fail(t, "has error"+e.Error())
	case <-done:
		return
	}
}

func TestSDKSessionCachePurchase(t *testing.T) {
	sdkDeviceUuid := uuid.NewString()
	terminalDeviceUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	customerUuid := uuid.NewString()

	to := createTestObject(types.SDK)
	to.deviceUuid = terminalDeviceUuid
	to.entityUuid = entityUuid
	to.pairedDeviceId = sdkDeviceUuid
	terminalWs := setupConnection(to)
	defer func() {
		terminalWs.wsChannel.Close()
	}()

	// generate token
	claims := AccessTokenClaims{
		Aud:          []string{"https://sdk.myzeller.com"},
		CustomerUuid: &customerUuid,
	}
	accessToken := GenerateAccessToken(claims)

	// session cache
	identityCache := session.IdentityCache{
		Id:           &accessToken,
		Type:         (*string)(&session.CacheTypeIdentitySub),
		Auth0Sub:     ptr.String(uuid.NewString()),
		CustomerUuid: &customerUuid,
		EntityUuid:   &entityUuid,
		DeviceUuid:   &sdkDeviceUuid,
		TTL:          ptr.Int(int(time.Now().Add(time.Hour).UnixMilli())),
	}
	SaveIdentityInSdkSessionTable(identityCache)

	deviceCache := session.DeviceCache{
		Id:          &sdkDeviceUuid,
		Type:        (*string)(&session.CacheTypeDeviceUuid),
		EntityUuid:  &entityUuid,
		Model:       ptr.String(uuid.NewString()),
		Serial:      ptr.String(uuid.NewString()),
		ModelSerial: ptr.String(uuid.NewString()),
		Status:      (*string)(&session.DeviceStatusActive),
	}
	SaveDeviceInSdkSessionTable(deviceCache)

	// create pairing
	pairingItem := map[string]any{
		"id":             sdkDeviceUuid,
		"type":           fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, sdkDeviceUuid),
		"entityUuid":     entityUuid,
		"deviceUuid":     sdkDeviceUuid,
		"provider":       string(types.SDK),
		"timestamp":      strconv.FormatInt(time.Now().Unix(), 10),
		"status":         "ACTIVE",
		"deviceType":     string(types.DeviceTypePOS),
		"pairedDeviceId": terminalDeviceUuid,
	}
	logger.Info(ctx, fmt.Sprintf("save pairing item %s, %v", pairingTableName, utils.BestEffortStringify(pairingItem, false)))
	item, _ := attributevalue.MarshalMap(pairingItem)
	_, err := to.dbClient.InsertItem(ctx, pairingTableName, &item, nil)
	if err != nil {
		log.Panic(err.Error())
	}

	var resp *http.Response
	wsChannel, resp, err := websocket.DefaultDialer.Dial(to.wsEndpoint, http.Header{
		"Sec-WebSocket-Protocol": []string{fmt.Sprintf("posconnector, Authorization|%s, DeviceUuid|%s", accessToken, sdkDeviceUuid)},
	})
	logger.Info(to.ctx, "receive status code:"+strconv.Itoa(resp.StatusCode))
	if err != nil {
		b, _ := io.ReadAll(resp.Body)
		fmt.Println("response body:" + string(b))
		log.Panic("dial:", err.Error())
	}

	terminalDone := make(chan struct{})
	terminalHasError := make(chan error)
	go func() {
		for {
			messageType, message, err := terminalWs.wsChannel.ReadMessage()
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("message type %d, message %s", messageType, message))
				terminalHasError <- err
			}
			logger.Info(ctx, "get message TERMINAL "+string(message))
			var response map[string]any
			err = json.Unmarshal(message, &response)
			assert.Nil(t, err)
			action, ok := response["action"]
			assert.True(t, ok)
			assert.Equal(t, string(protocol.PURCHASE), action)
			data, ok := response["data"].(map[string]any)

			assert.True(t, ok)
			log.Println(utils.BestEffortStringify(data, true))

			err = terminalWs.wsChannel.WriteJSON(map[string]any{
				"eventId":   uuid.NewString(),
				"timestamp": fmt.Sprint(time.Now().UnixMilli()),
				"action":    string(protocol.PURCHASE),
				"data": map[string]any{
					"sessionUuid": data["sessionUuid"],
				},
			})
			assert.Nil(t, err)

			assert.Equal(t, data["error"], nil)
			close(terminalDone)
		}
	}()

	done := make(chan struct{})
	hasError := make(chan error)
	go func() {
		for {
			messageType, message, err := wsChannel.ReadMessage()
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("message type %d, message %s", messageType, message))
				hasError <- err
			}
			logger.Info(ctx, "get message SDK"+string(message))
			var response map[string]any
			err = json.Unmarshal(message, &response)
			assert.Nil(t, err)
			action, ok := response["action"]
			assert.True(t, ok)
			assert.Equal(t, string(protocol.PURCHASE), action)
			data, ok := response["data"].(map[string]any)

			assert.True(t, ok)
			log.Println(utils.BestEffortStringify(data, true))

			assert.Equal(t, data["error"], nil)
			close(done)
		}
	}()
	err = wsChannel.WriteJSON(map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": fmt.Sprint(time.Now().UnixMilli()),
		"action":    string(protocol.PURCHASE),
		"data": map[string]any{
			"amount":              1200,
			"caid":                "000000000272720",
			"externalReference":   uuid.NewString(),
			"sessionUuid":         uuid.NewString(),
			"timestamp":           "2023-11-28T11:14:18+11:00",
			"transactionCurrency": "AUD",
			"transactionUuid":     uuid.NewString(),
			"type":                "PURCHASE",
		},
	})
	assert.Nil(t, err)
	ticker := time.NewTicker(time.Second * 3)

	select {
	case <-ticker.C:
		return
	case e := <-hasError:
		assert.Fail(t, "has error"+e.Error())
	case e := <-terminalHasError:
		assert.Fail(t, "has error"+e.Error())
	case <-done:
		return
	}
}
