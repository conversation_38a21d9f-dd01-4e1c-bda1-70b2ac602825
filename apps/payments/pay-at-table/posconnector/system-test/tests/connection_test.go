package tests

import (
	"encoding/json"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	logger "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

var cto ConnectionTestObject

func beforeEach() {
	cto = setupConnection(createTestObject(types.HL))
}

func afterEach() {
	cto.wsChannel.Close()
}

func TestConnection(t *testing.T) {
	beforeEach()
	defer afterEach()
	done := make(chan struct{})
	hasError := make(chan error)
	go func() {
		for {
			messageType, message, err := cto.wsChannel.ReadMessage()
			if err != nil {
				logger.Error(cto.ctx, fmt.Sprintf("message type %d, message %s", messageType, message))
				hasError <- err
			}
			logger.Info(cto.ctx, "get message "+string(message))
			var response map[string]any
			err = json.Unmarshal(message, &response)
			assert.Nil(t, err, err)
			_, ok := response["data"]
			assert.True(t, ok)
			close(done)
		}
	}()
	err := cto.wsChannel.WriteJSON(map[string]string{
		"eventId":   uuid.NewString(),
		"timestamp": fmt.Sprint(time.Now().UnixMilli()),
		"action":    "getOrders",
	})
	assert.Nil(t, err, err)
	ticker := time.NewTicker(time.Second * 3)
	select {
	case <-ticker.C:
		return
	case e := <-hasError:
		assert.Fail(t, "has error"+e.Error())
	case <-done:
		return
	}
}

func TestPing(t *testing.T) {
	beforeEach()
	defer afterEach()
	done := make(chan struct{})
	hasError := make(chan error)
	go func() {
		for {
			messageType, message, err := cto.wsChannel.ReadMessage()
			if err != nil {
				logger.Error(cto.ctx, fmt.Sprintf("message type %d, message %s", messageType, message))
				hasError <- err
			}
			logger.Info(cto.ctx, "get message "+string(message))
			var response map[string]any
			err = json.Unmarshal(message, &response)
			assert.Nil(t, err)
			data, ok := response["data"]
			assert.True(t, ok)
			assert.Equal(t, data.(map[string]any)["response"], "pong")
			close(done)
		}
	}()
	err := cto.wsChannel.WriteJSON(map[string]string{
		"eventId":   uuid.NewString(),
		"timestamp": fmt.Sprint(time.Now().UnixMilli()),
		"action":    "ping",
	})
	assert.Nil(t, err)
	ticker := time.NewTicker(time.Second * 3)
	select {
	case <-ticker.C:
		return
	case e := <-hasError:
		assert.Fail(t, "has error"+e.Error())
	case <-done:
		return
	}
}

func TestSubscription(t *testing.T) {
	beforeEach()
	defer afterEach()

	// subscription
	req := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": fmt.Sprint(time.Now().UnixMilli()),
		"action":    string(protocol.SUB_ORDERS_UPDATE),
	}
	logger.Info(cto.ctx, "send subscription request:"+req["eventId"].(string))
	err := cto.wsChannel.WriteJSON(req)
	assert.Nil(t, err, err)
	time.Sleep(3 * time.Second)

	output, err := cto.dbClient.Query(cto.connectionTableName).Eq("id", req["eventId"]).Exec(cto.ctx)
	assert.Nil(t, err, err)
	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, "CONNECTED", output.Data[0]["status"])
	assert.Equal(t, cto.entityUuid, output.Data[0]["entityUuid"])
	assert.True(t, strings.HasPrefix(output.Data[0]["type"].(string), "subscriber."))
	assert.Nil(t, output.Data[0]["ttl"])
	time.Sleep(3 * time.Second)

	unsub := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": fmt.Sprint(time.Now().UnixMilli()),
		"action":    string(protocol.UNSUB_ORDERS_UPDATE),
	}

	// publish
	ch := make(chan map[string]any)
	go func(c chan map[string]any, cto ConnectionTestObject) {
		for {
			_, message, _ := cto.wsChannel.ReadMessage()
			if len(message) > 0 {
				logger.Info(cto.ctx, "receive message "+string(message))
				var j map[string]any
				_ = json.Unmarshal(message, &j)
				a, ok := j["action"]
				assert.True(t, ok)
				if a.(string) == string(protocol.SUB_ORDERS_UPDATE) {
					e, ok := j["eventId"]
					assert.True(t, ok)
					assert.Equal(t, req["eventId"], e.(string))
				} else if a.(string) == string(protocol.UNSUB_ORDERS_UPDATE) {
					e, ok := j["eventId"]
					assert.True(t, ok)
					assert.Equal(t, unsub["eventId"], e.(string))
					d, ok := j["data"].(map[string]any)
					assert.True(t, ok)
					assert.Equal(t, "DISCONNECTED", d["status"])
				} else {
					assert.Fail(t, "should not receive this action "+a.(string))
				}
				_, ok = j["timestamp"]
				assert.True(t, ok)
				d, ok := j["data"].(map[string]any)
				assert.True(t, ok)
				ch <- d
			} else {
				break
			}
		}
	}(ch, cto)
	order := NewOrderObject()
	order.SiteUuid = &cto.siteUuid
	PublishOrderUpdateWebhook(cto.TestObject, order, t)
	ticker := time.NewTicker(3 * time.Second)
	select {
	case m := <-ch:
		logger.Info(cto.ctx, fmt.Sprintf("receive response %v", m))
		assert.NotNil(t, m)
		s, ok := m["status"]
		if ok {
			// response to subscribe request
			assert.Equal(t, "CONNECTED", s.(string))
		} else {
			// published data
			ao, _ := protocol.NewOrder(cto.ctx, &m)
			assert.Equal(t, order, *ao)
		}
	case <-ticker.C:
		logger.Error(cto.ctx, "Timed out on waiting for subscription response")
		assert.Fail(t, "Timed out on waiting for subscription response")
	}

	// unsubscribe
	logger.Info(cto.ctx, "send unsubscription request:"+unsub["eventId"].(string))
	err = cto.wsChannel.WriteJSON(unsub)
	assert.Nil(t, err, err)

	time.Sleep(2 * time.Second)
	output, _ = cto.dbClient.Query(cto.connectionTableName).Eq("id", req["eventId"]).Exec(cto.ctx)
	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, cto.siteUuid, protocol.SUB_ORDERS_UPDATE), output.Data[0]["type"])
	assert.Equal(t, "DISCONNECTED", output.Data[0]["status"])
	assert.NotNil(t, output.Data[0]["ttl"])

	// disconnect
	cto.wsChannel.Close()
	time.Sleep(3 * time.Second)
	output, err = cto.dbClient.Query(cto.connectionTableName).Eq("id", req["eventId"]).Exec(cto.ctx)
	assert.Nil(t, err, err)
	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, "DISCONNECTED", output.Data[0]["status"])
	assert.True(t, strings.HasPrefix(output.Data[0]["type"].(string), "subscriber."))
	assert.NotNil(t, output.Data[0]["ttl"])
}

func TestSubscriptionPosConnectionStatus(t *testing.T) {
	beforeEach()
	defer afterEach()

	// subscription
	req := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": fmt.Sprint(time.Now().UnixMilli()),
		"action":    string(protocol.SUB_POS_CONNECTION_STATUS),
	}
	logger.Info(cto.ctx, "send subscription request:"+req["eventId"].(string))
	err := cto.wsChannel.WriteJSON(req)
	assert.Nil(t, err, err)
	time.Sleep(3 * time.Second)

	output, err := cto.dbClient.Query(cto.connectionTableName).Eq("id", req["eventId"]).Exec(cto.ctx)
	assert.Nil(t, err, err)
	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, "CONNECTED", output.Data[0]["status"])
	assert.Equal(t, cto.entityUuid, output.Data[0]["entityUuid"])
	assert.True(t, strings.HasPrefix(output.Data[0]["type"].(string), "subscriber."))
	assert.Nil(t, output.Data[0]["ttl"])
	time.Sleep(3 * time.Second)

	unsub := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": fmt.Sprint(time.Now().UnixMilli()),
		"action":    string(protocol.UNSUB_POS_CONNECTION_STATUS),
	}

	// publish
	ch := make(chan map[string]any)
	go func(c chan map[string]any, cto ConnectionTestObject) {
		for {
			_, message, _ := cto.wsChannel.ReadMessage()
			if len(message) > 0 {
				logger.Info(cto.ctx, "receive message "+string(message))
				var j map[string]any
				_ = json.Unmarshal(message, &j)
				a, ok := j["action"]
				assert.True(t, ok)
				if a.(string) == string(protocol.SUB_POS_CONNECTION_STATUS) {
					e, ok := j["eventId"]
					assert.True(t, ok)
					assert.Equal(t, req["eventId"], e.(string))
				} else if a.(string) == string(protocol.UNSUB_POS_CONNECTION_STATUS) {
					e, ok := j["eventId"]
					assert.True(t, ok)
					assert.Equal(t, unsub["eventId"], e.(string))
					d, ok := j["data"].(map[string]any)
					assert.True(t, ok)
					assert.Equal(t, "DISCONNECTED", d["status"])
				} else {
					assert.Fail(t, "should not receive this action "+a.(string))
				}
				_, ok = j["timestamp"]
				assert.True(t, ok)
				d, ok := j["data"].(map[string]any)
				assert.True(t, ok)
				ch <- d
			} else {
				break
			}
		}
	}(ch, cto)

	lambdaName := fmt.Sprintf("%s-posconnector-api-app-cronjob", stage)
	body := map[string]any{
		"cronJobType": "posConnectionStatus",
	}
	_, err = InvokeLambda(ctx, lambdaName, body)
	assert.Nil(t, err, err)

	ticker := time.NewTicker(3 * time.Second)
	select {
	case m := <-ch:
		logger.Info(cto.ctx, fmt.Sprintf("receive response %v", m))
		assert.NotNil(t, m)
		s, ok := m["status"]
		if ok {
			// response to subscribe request
			assert.Equal(t, "CONNECTED", s.(string))
		}
	case <-ticker.C:
		logger.Error(cto.ctx, "Timed out on waiting for subscription response")
		assert.Fail(t, "Timed out on waiting for subscription response")
	}

	// unsubscribe
	logger.Info(cto.ctx, "send unsubscription request:"+unsub["eventId"].(string))
	err = cto.wsChannel.WriteJSON(unsub)
	assert.Nil(t, err, err)

	time.Sleep(2 * time.Second)
	output, _ = cto.dbClient.Query(cto.connectionTableName).Eq("id", req["eventId"]).Exec(cto.ctx)
	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, cto.siteUuid, protocol.SUB_POS_CONNECTION_STATUS), output.Data[0]["type"])
	assert.Equal(t, "DISCONNECTED", output.Data[0]["status"])
	assert.NotNil(t, output.Data[0]["ttl"])

	// disconnect
	cto.wsChannel.Close()
	time.Sleep(3 * time.Second)
	output, err = cto.dbClient.Query(cto.connectionTableName).Eq("id", req["eventId"]).Exec(cto.ctx)
	assert.Nil(t, err, err)
	assert.Equal(t, 1, len(output.Data))
	assert.Equal(t, "DISCONNECTED", output.Data[0]["status"])
	assert.True(t, strings.HasPrefix(output.Data[0]["type"].(string), "subscriber."))
	assert.NotNil(t, output.Data[0]["ttl"])
}
