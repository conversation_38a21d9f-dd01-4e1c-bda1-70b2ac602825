package tests

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	logger "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func TestWrongAction(t *testing.T) {
	ectd := setupConnection(createTestObject(types.HL))
	resp := make(chan map[string]any)

	events := []map[string]any{
		{
			// unknown action
			"eventId":   uuid.NewString(),
			"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
			"action":    "UNKNOWN",
			"data":      map[string]any{},
		},
		{
			// no action
			"eventId":   uuid.NewString(),
			"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
			"data":      map[string]any{},
		},
		{
			// no event id
			"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
			"action":    string(protocol.SUB_ORDERS_UPDATE),
			"data":      map[string]any{},
		},
		{
			// no timestamp
			"eventId": uuid.NewString(),
			"data":    map[string]any{},
			"action":  string(protocol.GET_ORDER),
		},
	}
	for _, event := range events {
		jevent, _ := json.Marshal(event)
		logger.Info(ctx, fmt.Sprintf("send message %s", string(jevent)))
		go func(ws *websocket.Conn, requested string) {
			_, message, err := ws.ReadMessage()
			assert.Nil(t, err, err)
			logger.Info(ctx, fmt.Sprintf("read message %s, requested %s", string(message), requested))
			var r map[string]any
			err = json.Unmarshal(message, &r)
			assert.Nil(t, err, err)
			resp <- r
		}(ectd.wsChannel, string(jevent))

		err := ectd.wsChannel.WriteMessage(websocket.TextMessage, jevent)
		assert.Nil(t, err, err)
		ticker := time.NewTicker(time.Second * 3)
		select {
		case response := <-resp:
			e, ok := response["error"]
			assert.NotNil(t, e, event)
			responseE := e.(map[string]any)
			assert.True(t, ok)
			assert.Equal(t, "INVALID_REQUEST", responseE["type"].(string))
		case <-ticker.C:
			logger.Error(ctx, "time out")
			assert.Fail(t, "timeout")
		}
	}
}

func TestWebhookInvalidApiKey(t *testing.T) {
	to := createTestObject(types.HL)
	lambdaName := fmt.Sprintf("%s-posconnector-api-posinterface-webhook", to.stage)

	// Create the API Gateway Proxy Request payload
	payload := events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": "invalid",
			"Content-Type":  "application/json",
		},
	}

	invokeOutput, err := InvokeLambda(to.ctx, lambdaName, payload)

	var resp LambdaResponsePayload
	json.Unmarshal(invokeOutput.Payload, &resp)

	assert.Nil(t, err, err)
	assert.Equal(t, 401, resp.StatusCode)

	// Create the API Gateway Proxy Request payload
	payload = events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}

	invokeOutput, err = InvokeLambda(to.ctx, lambdaName, payload)

	json.Unmarshal(invokeOutput.Payload, &resp)

	assert.Nil(t, err, err)
	assert.Equal(t, 401, resp.StatusCode)
}

func TestWebhookSiteNotPaired(t *testing.T) {
	to := createTestObject(types.HL)
	setMetaData(string(types.HL), to)
	o := NewOrderObject()
	b, _ := json.Marshal(o)
	var data map[string]any
	_ = json.Unmarshal(b, &data)
	event := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    string(protocol.SUB_ORDERS_UPDATE),
		"data":      data,
	}
	be, _ := json.Marshal(event)

	lambdaName := fmt.Sprintf("%s-posconnector-api-posinterface-webhook", to.stage)

	payload := events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": "invalid",
			"Content-Type":  "application/json",
		},
		Body: string(be),
	}

	invokeOutput, err := InvokeLambda(to.ctx, lambdaName, payload)

	var resp LambdaResponsePayload
	json.Unmarshal(invokeOutput.Payload, &resp)
	assert.Nil(t, err, err)
	assert.Equal(t, 401, resp.StatusCode)

	var body map[string]any
	json.Unmarshal([]byte(resp.Body), &body)

	assert.Nil(t, err, err)
	errResp := body["error"].(map[string]any)
	assert.Equal(t, "FORBIDDEN", errResp["type"].(string))
	assert.True(t, strings.HasPrefix(errResp["message"].(string), "Not found provider from pairing table"))
}

func TestWebhookOrderMissingField(t *testing.T) {
	missingFields := []map[string]any{
		{"field": "orderId", "errType": "INVALID_REQUEST", "statusCode": 400},
		{"field": "siteUuid", "errType": "INVALID_REQUEST", "statusCode": 400},
	}
	for _, f := range missingFields {
		to := createTestObject(types.HL)
		setupConnection(to)
		o := NewOrderObject()
		b, _ := json.Marshal(o)
		var data map[string]any
		_ = json.Unmarshal(b, &data)
		delete(data, f["field"].(string))
		logger.Info(to.ctx, fmt.Sprintf("send order update %#v", data))
		event := map[string]any{
			"eventId":   uuid.NewString(),
			"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
			"action":    string(protocol.SUB_ORDERS_UPDATE),
			"data":      data,
		}
		be, _ := json.Marshal(event)

		lambdaName := fmt.Sprintf("%s-posconnector-api-posinterface-webhook", to.stage)

		payload := events.APIGatewayProxyRequest{
			Headers: map[string]string{
				"Authorization": to.apiKey,
				"Content-Type":  "application/json",
			},
			Body: string(be),
		}

		invokeOutput, err := InvokeLambda(to.ctx, lambdaName, payload)

		var resp LambdaResponsePayload
		json.Unmarshal(invokeOutput.Payload, &resp)

		assert.Nil(t, err, err)
		assert.Equal(t, f["statusCode"].(int), resp.StatusCode)

		var body map[string]any
		json.Unmarshal([]byte(resp.Body), &body)

		assert.Nil(t, err, err)
		errResp := body["error"].(map[string]any)
		assert.Equal(t, f["errType"], errResp["type"].(string))
	}
}
