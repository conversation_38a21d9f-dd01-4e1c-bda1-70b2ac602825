package tests

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	logger "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

var componentName string = "posconnector"
var hlPairingUuid string
var oraclePairingUuid string
var deviceUuid string
var itemType string
var dbClient db.DynamoDb
var ctx = context.Background()
var pairingTableName string

func init() {
	stage := os.Getenv("STAGE")
	pairingTableName = fmt.Sprintf("%s-posconnector-api-Pairing", stage)
	fmt.Printf("pairingTableName: %#v\n", pairingTableName)
}

func setupDbClient() db.DynamoDb {
	dbClient = *db.NewDynamoDb(ctx)
	fmt.Printf("dbClient: %#v\n", dbClient)
	return dbClient
}

func TestHLPosInterfaceDevicePairedEvent(t *testing.T) {
	hlPairingUuid = uuid.NewString()
	siteUuid := uuid.NewString()
	deviceUuid = uuid.NewString()
	stationId := uuid.NewString()
	locations := []map[string]string{
		{
			"locationId": uuid.NewString(),
		},
		{
			"locationId": uuid.NewString(),
		},
	}

	bodyMap := map[string]any{
		"detail-type": "posconnector.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId": hlPairingUuid,
			"payload": map[string]any{
				"siteUuid":    siteUuid,
				"venueId":     uuid.NewString(),
				"stationId":   stationId,
				"deviceUuid":  deviceUuid,
				"entityUuid":  uuid.NewString(),
				"posProvider": "HL_POS",
				"locations":   locations,
				"timestamp":   strconv.FormatInt(time.Now().Unix(), 10),
			},
			"version":          "1",
			"createdTimestamp": strconv.FormatInt(time.Now().Unix(), 10),
		},
	}

	body, _ := json.Marshal(bodyMap)
	bodyString := string(body)

	sqsQueueUrl := GetCqrsSqsEndpoint(ctx, componentName)
	SendSqsMessage(ctx, &bodyString, sqsQueueUrl)

	projectionDbClient := setupDbClient()
	itemType = strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, "")
	fmt.Println("id", hlPairingUuid)
	fmt.Println("type", itemType)

	var locationIds []string
	for _, location := range locations {
		locationIds = append(locationIds, location["locationId"])
	}

	retriesLeft := 30
	timeToSleep := 1 * time.Second
	for retriesLeft > 0 {
		if passed := validateHLItemInDynamodb(t, &projectionDbClient, hlPairingUuid, siteUuid, deviceUuid, locationIds, itemType, stationId); passed {
			break
		}
		retriesLeft--
		time.Sleep(timeToSleep)
	}
}

func TestImposInterfaceDevicePairedEvent(t *testing.T) {
	hlPairingUuid = uuid.NewString()
	siteUuid := uuid.NewString()
	deviceUuid = uuid.NewString()
	locations := []map[string]string{
		{
			"locationId": uuid.NewString(),
		},
		{
			"locationId": uuid.NewString(),
		},
	}

	bodyMap := map[string]any{
		"detail-type": "posconnector.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId": hlPairingUuid,
			"payload": map[string]any{
				"siteUuid":    siteUuid,
				"venueId":     uuid.NewString(),
				"deviceUuid":  deviceUuid,
				"entityUuid":  uuid.NewString(),
				"posProvider": "IMPOS",
				"locations":   locations,
				"timestamp":   strconv.FormatInt(time.Now().Unix(), 10),
				"type":        types.DeviceTypeTerminal,
			},
			"version":          "1",
			"createdTimestamp": strconv.FormatInt(time.Now().Unix(), 10),
		},
	}

	body, _ := json.Marshal(bodyMap)
	bodyString := string(body)

	sqsQueueUrl := GetCqrsSqsEndpoint(ctx, componentName)
	SendSqsMessage(ctx, &bodyString, sqsQueueUrl)

	projectionDbClient := setupDbClient()
	itemType = strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), deviceUuid}, "")
	fmt.Println("id", hlPairingUuid)
	fmt.Println("type", itemType)

	var locationIds []string
	for _, location := range locations {
		locationIds = append(locationIds, location["locationId"])
	}

	retriesLeft := 30
	timeToSleep := 1 * time.Second
	for retriesLeft > 0 {
		if passed := validateImposItemInDynamodb(t, &projectionDbClient, hlPairingUuid, siteUuid, deviceUuid, locationIds, itemType); passed {
			break
		}
		retriesLeft--
		time.Sleep(timeToSleep)
	}
}

func TestImposInterfacePosDevicePairedEvent(t *testing.T) {
	pairingUuid := uuid.NewString()
	entityUuid := uuid.NewString()
	deviceUuid = uuid.NewString()

	bodyMap := map[string]any{
		"detail-type": "posconnector.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId": pairingUuid,
			"payload": map[string]any{
				"venueId":     deviceUuid,
				"deviceUuid":  deviceUuid,
				"entityUuid":  entityUuid,
				"posProvider": "IMPOS",
				"timestamp":   strconv.FormatInt(time.Now().Unix(), 10),
				"type":        types.DeviceTypePOS,
			},
			"version":          "1",
			"createdTimestamp": strconv.FormatInt(time.Now().Unix(), 10),
		},
	}

	body, _ := json.Marshal(bodyMap)
	bodyString := string(body)

	sqsQueueUrl := GetCqrsSqsEndpoint(ctx, componentName)
	SendSqsMessage(ctx, &bodyString, sqsQueueUrl)

	projectionDbClient := setupDbClient()
	itemType = strings.Join([]string{string(types.POSINTERFACE_PAIR_POS), deviceUuid}, "")
	fmt.Println("id", pairingUuid)
	fmt.Println("type", itemType)

	retriesLeft := 30
	timeToSleep := 1 * time.Second
	for retriesLeft > 0 {
		fmt.Println("Querying table " + pairingTableName)
		output, err := projectionDbClient.Query(pairingTableName).Eq("id", pairingUuid).Eq("type", itemType).Exec(ctx)
		assert.Nil(t, err, err)
		fmt.Println("output.Data", output.Data)

		if err != nil || len(output.Data) != 1 {
			retriesLeft--
			time.Sleep(timeToSleep)
			continue
		}
		assert.Equal(t, pairingUuid, output.Data[0]["id"])
		assert.Equal(t, deviceUuid, output.Data[0]["deviceUuid"])
		assert.Equal(t, deviceUuid, output.Data[0]["venueId"])
		assert.Equal(t, entityUuid, output.Data[0]["entityUuid"])
		assert.Equal(t, string(types.DeviceTypePOS), output.Data[0]["deviceType"])
		break
	}
}

func TestOraclePosInterfaceDevicePairedEvent(t *testing.T) {
	oraclePairingUuid = uuid.NewString()
	siteUuid := uuid.NewString()
	clientId := uuid.NewString()

	bodyMap := map[string]any{
		"detail-type": "posconnector.PosInterface.DevicePaired",
		"detail": map[string]any{
			"aggregateId": oraclePairingUuid,
			"payload": map[string]any{
				"siteUuid":    siteUuid,
				"stationId":   clientId,
				"deviceUuid":  oraclePairingUuid,
				"entityUuid":  uuid.NewString(),
				"posProvider": "ORACLE_POS",
				"timestamp":   strconv.FormatInt(time.Now().Unix(), 10),
			},
			"version":          "1",
			"createdTimestamp": strconv.FormatInt(time.Now().Unix(), 10),
		},
	}

	body, _ := json.Marshal(bodyMap)
	bodyString := string(body)

	sqsQueueUrl := GetCqrsSqsEndpoint(ctx, componentName)
	SendSqsMessage(ctx, &bodyString, sqsQueueUrl)

	projectionDbClient := setupDbClient()
	itemType = strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), oraclePairingUuid}, "")
	fmt.Println("id", oraclePairingUuid)
	fmt.Println("type", itemType)

	retriesLeft := 30
	timeToSleep := 1 * time.Second
	for retriesLeft > 0 {
		if passed := validateOracleItemInDynamodb(t, &projectionDbClient, oraclePairingUuid, siteUuid, oraclePairingUuid, itemType, clientId); passed {
			break
		}
		retriesLeft--
		time.Sleep(timeToSleep)
	}
}

func TestSDKDevicePairedEvent(t *testing.T) {
	sdkDeviceUuid := uuid.NewString()
	terminalUuid := uuid.NewString()
	entityUuid := uuid.NewString()

	sqsQueueUrl := GetCqrsSqsEndpoint(ctx, componentName)

	projectionDbClient := setupDbClient()

	t.Run("POS", func(t *testing.T) {
		bodyMap := map[string]any{
			"detail-type": "posconnector.PosInterface.DevicePaired",
			"detail": map[string]any{
				"aggregateId": sdkDeviceUuid,
				"payload": map[string]any{
					"deviceUuid":     sdkDeviceUuid,
					"entityUuid":     entityUuid,
					"posProvider":    types.SDK,
					"timestamp":      strconv.FormatInt(time.Now().Unix(), 10),
					"pairedDeviceId": terminalUuid,
					"type":           types.DeviceTypePOS,
				},
				"version":          "1",
				"createdTimestamp": strconv.FormatInt(time.Now().Unix(), 10),
			},
		}

		body, _ := json.Marshal(bodyMap)
		bodyString := string(body)

		SendSqsMessage(ctx, &bodyString, sqsQueueUrl)

		retriesLeft := 30
		timeToSleep := 1 * time.Second
		for retriesLeft > 0 {
			if passed := validateSDKItemInDynamodb(t, &projectionDbClient, sdkDeviceUuid, types.DeviceTypePOS, terminalUuid, entityUuid); passed {
				break
			}
			retriesLeft--
			time.Sleep(timeToSleep)
		}
	})

	t.Run("Terminal", func(t *testing.T) {
		bodyMap := map[string]any{
			"detail-type": "posconnector.PosInterface.DevicePaired",
			"detail": map[string]any{
				"aggregateId": terminalUuid,
				"payload": map[string]any{
					"deviceUuid":     terminalUuid,
					"entityUuid":     entityUuid,
					"posProvider":    types.SDK,
					"timestamp":      strconv.FormatInt(time.Now().Unix(), 10),
					"pairedDeviceId": sdkDeviceUuid,
					"type":           types.DeviceTypeTerminal,
				},
				"version":          "1",
				"createdTimestamp": strconv.FormatInt(time.Now().Unix(), 10),
			},
		}

		body, _ := json.Marshal(bodyMap)
		bodyString := string(body)

		SendSqsMessage(ctx, &bodyString, sqsQueueUrl)

		retriesLeft := 30
		timeToSleep := 1 * time.Second
		for retriesLeft > 0 {
			if passed := validateSDKItemInDynamodb(t, &projectionDbClient, terminalUuid, types.DeviceTypeTerminal, sdkDeviceUuid, entityUuid); passed {
				break
			}
			retriesLeft--
			time.Sleep(timeToSleep)
		}
	})
}

func validateHLItemInDynamodb(t *testing.T, projectionDbClient *db.DynamoDb, inputPairingUuid string, inputSiteUuid string, inputDeviceUuid string, locations []string, inputItemType string, inputStationId string) bool {
	fmt.Println("Querying table " + pairingTableName)
	output, err := projectionDbClient.Query(pairingTableName).Eq("id", inputPairingUuid).Eq("type", inputItemType).Exec(ctx)
	assert.Nil(t, err, err)
	fmt.Println("output.Data", output.Data)
	if err != nil || len(output.Data) != 1 {
		return false
	}

	if locations != nil {
		var outputLocations []string
		for _, locationId := range output.Data[0]["locations"].([]any) {
			outputLocations = append(outputLocations, locationId.(string))
		}
		assert.Equal(t, locations, outputLocations)
	}
	assert.Equal(t, inputPairingUuid, output.Data[0]["id"])
	assert.Equal(t, inputSiteUuid, output.Data[0]["siteUuid"])
	assert.Equal(t, inputDeviceUuid, output.Data[0]["deviceUuid"])
	assert.Equal(t, inputStationId, output.Data[0]["stationId"])
	assert.Equal(t, "TERMINAL", output.Data[0]["deviceType"])

	return true
}

func validateImposItemInDynamodb(t *testing.T, projectionDbClient *db.DynamoDb, inputPairingUuid string, inputSiteUuid string, inputDeviceUuid string, locations []string, inputItemType string) bool {
	fmt.Println("Querying table " + pairingTableName)
	output, err := projectionDbClient.Query(pairingTableName).Eq("id", inputPairingUuid).Eq("type", inputItemType).Exec(ctx)
	assert.Nil(t, err, err)
	fmt.Println("output.Data", output.Data)
	if err != nil || len(output.Data) != 1 {
		return false
	}

	if locations != nil {
		var outputLocations []string
		for _, locationId := range output.Data[0]["locations"].([]any) {
			outputLocations = append(outputLocations, locationId.(string))
		}
		assert.Equal(t, locations, outputLocations)
	}
	assert.Equal(t, inputPairingUuid, output.Data[0]["id"])
	assert.Equal(t, inputSiteUuid, output.Data[0]["siteUuid"])
	assert.Equal(t, inputDeviceUuid, output.Data[0]["deviceUuid"])
	assert.Equal(t, string(types.DeviceTypeTerminal), output.Data[0]["deviceType"])

	return true
}

func validateOracleItemInDynamodb(t *testing.T, projectionDbClient *db.DynamoDb, inputPairingUuid string, inputSiteUuid string, inputDeviceUuid string, inputItemType string, inputClientId string) bool {
	fmt.Println("Querying table " + pairingTableName)
	output, err := projectionDbClient.Query(pairingTableName).Eq("id", inputPairingUuid).Eq("type", inputItemType).Exec(ctx)
	assert.Nil(t, err, err)
	fmt.Println("output.Data", output.Data)
	if err != nil || len(output.Data) != 1 {
		return false
	}

	assert.Equal(t, inputPairingUuid, output.Data[0]["id"])
	assert.Equal(t, inputSiteUuid, output.Data[0]["siteUuid"])
	assert.Equal(t, inputDeviceUuid, output.Data[0]["deviceUuid"])
	assert.Equal(t, inputClientId, output.Data[0]["clientId"])
	assert.Equal(t, string(types.DeviceTypeTerminal), output.Data[0]["deviceType"])

	return true
}

func validateSDKItemInDynamodb(t *testing.T, projectionDbClient *db.DynamoDb, inputDeviceUuid string, deviceType types.DeviceType, inputPairedDeviceId string, inputEntityUuid string) bool {
	fmt.Println("Querying table " + pairingTableName)
	itemType = strings.Join([]string{string(types.POSINTERFACE_PAIR_DEVICE), inputDeviceUuid}, "")
	if deviceType == types.DeviceTypePOS {
		itemType = strings.Join([]string{string(types.POSINTERFACE_PAIR_POS), inputDeviceUuid}, "")
	}
	output, err := projectionDbClient.Query(pairingTableName).Eq("id", inputDeviceUuid).Eq("type", itemType).Exec(ctx)
	assert.Nil(t, err, err)
	fmt.Println("output.Data", output.Data)
	if err != nil || len(output.Data) != 1 {
		return false
	}

	assert.Equal(t, inputDeviceUuid, output.Data[0]["id"])
	assert.Equal(t, inputDeviceUuid, output.Data[0]["deviceUuid"])
	assert.Equal(t, inputPairedDeviceId, output.Data[0]["pairedDeviceId"])
	assert.Equal(t, string(types.SDK), output.Data[0]["provider"])
	assert.Equal(t, inputEntityUuid, output.Data[0]["entityUuid"])
	assert.Equal(t, string(deviceType), output.Data[0]["deviceType"])

	return true
}

func validateItemDeletedFromDynamodb(projectionDbClient *db.DynamoDb, inputPairingUuid string, inputItemType string) bool {
	output, _ := projectionDbClient.Query(pairingTableName).Eq("id", inputPairingUuid).Eq("type", inputItemType).Exec(ctx)
	fmt.Println("output.Data", output.Data)
	return len(output.Data) == 0
}

func TestPosInterfaceDeviceUnpairedEvent(t *testing.T) {
	testCases := map[string]string{"HL_POS": hlPairingUuid, "ORACLE_POS": oraclePairingUuid}

	for provider, pairingUuid := range testCases {
		t.Run(provider, func(t *testing.T) {
			bodyMap := map[string]any{
				"detail-type": "posconnector.PosInterface.DeviceUnpaired",
				"detail": map[string]any{
					"aggregateId": pairingUuid,
					"payload": map[string]any{
						"deviceUuid": deviceUuid,
						"timestamp":  strconv.FormatInt(time.Now().Unix(), 10),
					},
					"version":          "1",
					"createdTimestamp": strconv.FormatInt(time.Now().Unix(), 10),
				},
			}

			body, _ := json.Marshal(bodyMap)
			bodyString := string(body)

			sqsQueueUrl := GetCqrsSqsEndpoint(ctx, componentName)
			SendSqsMessage(ctx, &bodyString, sqsQueueUrl)

			projectionDbClient := setupDbClient()
			fmt.Println("id", pairingUuid)
			fmt.Println("type begin", string(types.POSINTERFACE_PAIR_DEVICE))

			retriesLeft := 30
			timeToSleep := 1 * time.Second
			for retriesLeft > 0 {
				if passed := validateItemDeletedFromDynamodb(&projectionDbClient, hlPairingUuid, itemType); passed {
					break
				}
				retriesLeft--
				time.Sleep(timeToSleep)
			}
		})
	}

}

func TestShouldTerminateConnectionWhenUnpair(t *testing.T) {
	cto := setupConnection(createTestObject(types.HL))

	// check connection & pairing record
	output, _ := cto.dbClient.Query(pairingTableName).Index("deviceUuidGsi").Eq("deviceUuid", cto.deviceUuid).Begin("type", types.CONNECTION_CORE).Exec(cto.ctx)
	assert.Equal(t, 1, len(output.Data))
	output, _ = cto.dbClient.Query(pairingTableName).Index("deviceUuidGsi").Eq("deviceUuid", cto.deviceUuid).Begin("type", types.POSINTERFACE_PAIR_DEVICE).Exec(cto.ctx)
	assert.Equal(t, 1, len(output.Data))

	// subscribe
	subOrders := map[string]string{
		"eventId":   uuid.NewString(),
		"timestamp": fmt.Sprint(time.Now().UnixMilli()),
		"action":    string(protocol.SUB_ORDERS_UPDATE),
	}
	err := cto.wsChannel.WriteJSON(subOrders)
	assert.Nil(t, err, err)
	subStatus := map[string]string{
		"eventId":   uuid.NewString(),
		"timestamp": fmt.Sprint(time.Now().UnixMilli()),
		"action":    string(protocol.SUB_POS_CONNECTION_STATUS),
	}
	err = cto.wsChannel.WriteJSON(subStatus)
	assert.Nil(t, err, err)

	// unpair
	c := make(chan any)
	go func() {
		for {
			_, message, err := cto.wsChannel.ReadMessage()
			logger.Info(ctx, "read message "+string(message))
			if err != nil {
				c <- true
			}
		}
	}()
	bodyMap := map[string]any{
		"detail-type": "posconnector.PosInterface.DeviceUnpaired",
		"detail": map[string]any{
			"aggregateId": output.Data[0]["id"].(string),
			"payload": map[string]any{
				"deviceUuid": cto.deviceUuid,
				"timestamp":  strconv.FormatInt(time.Now().Unix(), 10),
			},
			"version":          "1",
			"createdTimestamp": strconv.FormatInt(time.Now().Unix(), 10),
		},
	}
	body, _ := json.Marshal(bodyMap)
	bodyString := string(body)

	sqsQueueUrl := GetCqrsSqsEndpoint(ctx, componentName)
	SendSqsMessage(ctx, &bodyString, sqsQueueUrl)

	ticker := time.NewTicker(10 * time.Second)
	select {
	case <-c:
		logger.Info(ctx, "success")
		// should disconnect connection and subscription
		time.Sleep(5 * time.Second)
		output, _ = cto.dbClient.Query(pairingTableName).Index("deviceUuidGsi").Eq("deviceUuid", cto.deviceUuid).Begin("type", types.CONNECTION_CORE).Exec(cto.ctx)
		assert.Equal(t, 1, len(output.Data))
		assert.Equal(t, "DISCONNECTED", output.Data[0]["status"])
		output, _ = cto.dbClient.Query(pairingTableName).Index("deviceUuidGsi").Eq("deviceUuid", cto.deviceUuid).Begin("type", types.POSINTERFACE_PAIR_DEVICE).Exec(cto.ctx)
		assert.Equal(t, 0, len(output.Data))
		output, _ = cto.dbClient.Query(pairingTableName).Eq("id", subOrders["eventId"]).Exec(ctx)
		assert.Equal(t, "DISCONNECTED", output.Data[0]["status"])
		output, _ = cto.dbClient.Query(pairingTableName).Eq("id", subStatus["eventId"]).Exec(ctx)
		assert.Equal(t, "DISCONNECTED", output.Data[0]["status"])

	case <-ticker.C:
		assert.Fail(t, "timed out")
	}
}
