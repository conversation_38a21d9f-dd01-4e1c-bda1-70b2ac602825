package tests

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

// setup 3 devices, 2 under same site, the third one is from another site
func TestMultipleSubscription(t *testing.T) {
	var subTestObjects []ConnectionTestObject
	defer func() {
		for _, v := range subTestObjects {
			v.wsChannel.Close()
		}
	}()

	to := createTestObject(types.HL)
	to1 := setupConnection(to)
	subTestObjects = append(subTestObjects, to1)

	to.deviceUuid = uuid.NewString()
	to.accessToken = GenerateAccessToken(AccessTokenClaims{})
	to2 := setupConnection(to)
	subTestObjects = append(subTestObjects, to2)

	to.deviceUuid = uuid.NewString()
	to.siteUuid = uuid.NewString()
	to.accessToken = GenerateAccessToken(AccessTokenClaims{})
	to3 := setupConnection(to)

	subTestObjects = append(subTestObjects, to3)

	var requests []map[string]string
	// subscribe
	for _, v := range subTestObjects {
		req := map[string]string{
			"eventId":   uuid.NewString(),
			"timestamp": fmt.Sprint(time.Now().UnixMilli()),
			"action":    string(protocol.SUB_ORDERS_UPDATE),
		}
		requests = append(requests, req)
		assert.NotNil(t, v.wsChannel)
		err := v.wsChannel.WriteJSON(req)
		assert.Nil(t, err, err)
	}
	time.Sleep(3 * time.Second)
	for i, v := range requests {
		output, err := subTestObjects[i].dbClient.Query(subTestObjects[i].connectionTableName).Eq("id", v["eventId"]).Exec(subTestObjects[i].ctx)
		assert.Nil(t, err, err)
		assert.Equal(t, 1, len(output.Data))
		assert.Equal(t, subTestObjects[i].TestObject.deviceUuid, output.Data[0]["deviceUuid"].(string))
		assert.Equal(t, subTestObjects[i].TestObject.entityUuid, output.Data[0]["entityUuid"].(string))
		assert.Equal(t, subTestObjects[i].TestObject.siteUuid, output.Data[0]["siteUuid"].(string))
		assert.Equal(t, "CONNECTED", output.Data[0]["status"].(string))
		assert.Nil(t, output.Data[0]["ttl"])
	}
	// publish
	chans := make([]chan map[string]any, len(subTestObjects))
	for i := 0; i < 3; i += 1 {
		chans[i] = make(chan map[string]any)
	}
	for i, v := range subTestObjects {
		go func(i int, v ConnectionTestObject) {
			for {
				_, message, _ := v.wsChannel.ReadMessage()
				if len(message) > 0 {
					logger.Info(v.ctx, "receive message "+string(message))
					var j map[string]any
					_ = json.Unmarshal(message, &j)
					a, ok := j["action"]
					assert.True(t, ok)
					if a.(string) != string(protocol.SUB_ORDERS_UPDATE) && a.(string) != string(protocol.UNSUB_ORDERS_UPDATE) {
						assert.Fail(t, "should not recevie this action "+a.(string))
					}
					_, ok = j["eventId"]
					assert.True(t, ok)
					_, ok = j["timestamp"]
					assert.True(t, ok)
					d, ok := j["data"].(map[string]any)
					assert.True(t, ok)
					chans[i] <- d
				} else {
					break
				}
			}
		}(i, v)
	}

	var orders []protocol.Order
	for _, v := range subTestObjects {
		order := NewOrderObject()
		order.SiteUuid = &v.siteUuid
		orders = append(orders, order)
		PublishOrderUpdateWebhook(v.TestObject, order, t)
	}

	for i, c := range chans {
		ticker := time.NewTicker(3 * time.Second)
		select {
		case m := <-c:
			logger.Info(subTestObjects[i].ctx, fmt.Sprintf("receive response %v", m))
			assert.NotNil(t, m)
			s, ok := m["status"]
			if ok {
				// response to subscribe request
				assert.Equal(t, "CONNECTED", s.(string))
			} else {
				// published order data
				ao, _ := protocol.NewOrder(subTestObjects[i].ctx, &m)
				assert.Equal(t, orders[i], *ao)
			}
		case <-ticker.C:
			logger.Error(cto.ctx, "Timed out on waiting for subscription response")
			assert.Fail(t, "Timed out on waiting for subscription response")
		}
	}

	// unsubscribe
	for _, v := range subTestObjects {
		req := map[string]string{
			"eventId":   uuid.NewString(),
			"timestamp": fmt.Sprint(time.Now().UnixMilli()),
			"action":    string(protocol.UNSUB_ORDERS_UPDATE),
		}
		assert.NotNil(t, v.wsChannel)
		err := v.wsChannel.WriteJSON(req)
		assert.Nil(t, err, err)
	}
	time.Sleep(5 * time.Second)
	for i, v := range requests {
		output, err := subTestObjects[i].dbClient.Query(subTestObjects[i].connectionTableName).Eq("id", v["eventId"]).Exec(subTestObjects[i].ctx)
		assert.Nil(t, err, err)
		assert.Equal(t, 1, len(output.Data))
		assert.Equal(t, subTestObjects[i].TestObject.deviceUuid, output.Data[0]["deviceUuid"].(string))
		assert.Equal(t, subTestObjects[i].TestObject.entityUuid, output.Data[0]["entityUuid"].(string))
		assert.Equal(t, subTestObjects[i].TestObject.siteUuid, output.Data[0]["siteUuid"].(string))
		assert.Equal(t, "DISCONNECTED", output.Data[0]["status"].(string))
		assert.NotNil(t, output.Data[0]["ttl"])
	}
}

// d1 -> s1
// d2 -> s2 + l1
// d3 -> s2 + l1,l2,l3
func TestMultipleSubscriptionWithLocation1(t *testing.T) {
	dtos := setupSubscription(t)
	d1 := dtos[0]
	d2 := dtos[1]
	d3 := dtos[2]

	defer disconnectWebsocket(d1.wsChannel)
	defer disconnectWebsocket(d2.wsChannel)
	defer disconnectWebsocket(d3.wsChannel)

	chan1 := make(chan map[string]any)
	chan2 := make(chan map[string]any)
	chan3 := make(chan map[string]any)

	// publish s2 = l1
	go shouldNotReceive(d1.ctx, d1.wsChannel, chan1)
	go shouldReceive(t, d2.ctx, d2.wsChannel, chan2)
	go shouldReceive(t, d3.ctx, d3.wsChannel, chan3)
	ticker := time.NewTicker(3 * time.Second)
	order := NewOrderObject()
	order.SiteUuid = &d2.siteUuid
	order.Location = &protocol.Location{
		LocationId:   &d2.locations[0],
		LocationName: &d2.locations[0],
	}
	PublishOrderUpdateWebhook(d2.TestObject, order, t)
	check1 := false
	check2 := false
	order.SiteUuid = nil
	for {
		select {
		case m := <-chan2:
			logger.Info(d2.ctx, fmt.Sprintf("receive device2 %v", m))
			b, _ := json.Marshal(m)
			var o protocol.Order
			_ = json.Unmarshal(b, &o)
			assert.Equal(t, order, o)
			check1 = true
		case m := <-chan3:
			logger.Info(d3.ctx, fmt.Sprintf("receive device3 %v", m))
			b, _ := json.Marshal(m)
			var o protocol.Order
			_ = json.Unmarshal(b, &o)
			assert.Equal(t, order, o)
			check2 = true
		case <-ticker.C:
			assert.Fail(t, "wait for device3 subscription timed out:"+dtos[2].deviceUuid)
		}
		if check1 && check2 {
			break
		}
	}
	ticker = time.NewTicker(3 * time.Second)
	select {
	case <-chan1:
		assert.Fail(t, "should not receive subscription for device "+d2.deviceUuid)
	case <-ticker.C:
		logger.Info(ctx, "finish test for publish s1 l1")
		break
	}

}

// d1 -> s1
// d2 -> s2 + l1
// d3 -> s2 + l1,l2,l3
func TestMultipleSubscriptionWithLocation2(t *testing.T) {
	dtos := setupSubscription(t)

	defer disconnectWebsocket(dtos[0].wsChannel)
	defer disconnectWebsocket(dtos[1].wsChannel)
	defer disconnectWebsocket(dtos[2].wsChannel)

	chan1 := make(chan map[string]any)
	chan2 := make(chan map[string]any)
	chan3 := make(chan map[string]any)

	// publish s2 -> l2
	go shouldNotReceive(dtos[0].ctx, dtos[0].wsChannel, chan1)
	go shouldNotReceive(dtos[1].ctx, dtos[1].wsChannel, chan2)
	go shouldReceive(t, dtos[2].ctx, dtos[2].wsChannel, chan3)

	var orders []protocol.Order
	order := NewOrderObject()
	order.SiteUuid = &dtos[2].siteUuid
	order.Location = &protocol.Location{
		LocationId:   &dtos[2].locations[1],
		LocationName: &dtos[2].locations[1],
	}
	orders = append(orders, order)
	order = NewOrderObject()
	order.SiteUuid = &dtos[2].siteUuid
	order.Location = &protocol.Location{
		LocationId:   &dtos[2].locations[2],
		LocationName: &dtos[2].locations[2],
	}
	orders = append(orders, order)

	for i, o := range orders {
		PublishOrderUpdateWebhook(dtos[i].TestObject, o, t)
		o.SiteUuid = nil
		ticker := time.NewTicker(3 * time.Second)
		select {
		case m := <-chan3:
			logger.Info(dtos[2].ctx, fmt.Sprintf("receive device3 %v", m))
			b, _ := json.Marshal(m)
			var eo protocol.Order
			_ = json.Unmarshal(b, &eo)
			assert.Equal(t, o, eo)
		case <-ticker.C:
			assert.Fail(t, "wait for device3 subscription timed out:"+dtos[2].deviceUuid)
		}
		ticker = time.NewTicker(3 * time.Second)
		select {
		case <-chan1:
			assert.Fail(t, "should not receive subscription for device1 "+dtos[0].deviceUuid)
		case <-chan2:
			assert.Fail(t, "should not receive subscription for device2 "+dtos[1].deviceUuid)
		case <-ticker.C:
			logger.Info(ctx, "finish test for publish s1 l1")
		}
	}
}

func TestMultipleSubscriptionPosConnectionStatus(t *testing.T) {
	var subTestObjects []ConnectionTestObject
	defer func() {
		for _, v := range subTestObjects {
			v.wsChannel.Close()
		}
	}()

	to := createTestObject(types.HL)
	to1 := setupConnection(to)
	subTestObjects = append(subTestObjects, to1)

	to.deviceUuid = uuid.NewString()
	to.accessToken = GenerateAccessToken(AccessTokenClaims{})
	to2 := setupConnection(to)
	subTestObjects = append(subTestObjects, to2)

	to.deviceUuid = uuid.NewString()
	to.siteUuid = uuid.NewString()
	to.accessToken = GenerateAccessToken(AccessTokenClaims{})
	to3 := setupConnection(to)

	subTestObjects = append(subTestObjects, to3)

	var requests []map[string]string
	// subscribe
	for _, v := range subTestObjects {
		req := map[string]string{
			"eventId":   uuid.NewString(),
			"timestamp": fmt.Sprint(time.Now().UnixMilli()),
			"action":    string(protocol.SUB_POS_CONNECTION_STATUS),
		}
		requests = append(requests, req)
		assert.NotNil(t, v.wsChannel)
		err := v.wsChannel.WriteJSON(req)
		assert.Nil(t, err, err)
	}
	time.Sleep(3 * time.Second)
	for i, v := range requests {
		output, err := subTestObjects[i].dbClient.Query(subTestObjects[i].connectionTableName).Eq("id", v["eventId"]).Exec(subTestObjects[i].ctx)
		assert.Nil(t, err, err)
		assert.Equal(t, 1, len(output.Data))
		assert.Equal(t, subTestObjects[i].TestObject.deviceUuid, output.Data[0]["deviceUuid"].(string))
		assert.Equal(t, subTestObjects[i].TestObject.entityUuid, output.Data[0]["entityUuid"].(string))
		assert.Equal(t, subTestObjects[i].TestObject.siteUuid, output.Data[0]["siteUuid"].(string))
		assert.Equal(t, "CONNECTED", output.Data[0]["status"].(string))
		assert.Nil(t, output.Data[0]["ttl"])
	}
	// publish
	chans := make([]chan map[string]any, len(subTestObjects))
	for i := 0; i < 3; i += 1 {
		chans[i] = make(chan map[string]any)
	}
	for i, v := range subTestObjects {
		go func(i int, v ConnectionTestObject) {
			for {
				_, message, _ := v.wsChannel.ReadMessage()
				if len(message) > 0 {
					logger.Info(v.ctx, "receive message "+string(message))
					var j map[string]any
					_ = json.Unmarshal(message, &j)
					a, ok := j["action"]
					assert.True(t, ok)
					if a.(string) != string(protocol.SUB_POS_CONNECTION_STATUS) && a.(string) != string(protocol.UNSUB_POS_CONNECTION_STATUS) {
						assert.Fail(t, "should not recevie this action "+a.(string))
					}
					_, ok = j["eventId"]
					assert.True(t, ok)
					_, ok = j["timestamp"]
					assert.True(t, ok)
					d, ok := j["data"].(map[string]any)
					assert.True(t, ok)
					chans[i] <- d
				} else {
					break
				}
			}
		}(i, v)
	}

	lambdaName := fmt.Sprintf("%s-posconnector-api-app-cronjob", stage)
	body := map[string]any{
		"cronJobType": "posConnectionStatus",
	}
	_, err := InvokeLambda(ctx, lambdaName, body)
	assert.Nil(t, err, err)

	for i, c := range chans {
		ticker := time.NewTicker(3 * time.Second)
		select {
		case m := <-c:
			logger.Info(subTestObjects[i].ctx, fmt.Sprintf("receive response %v", m))
			assert.NotNil(t, m)
			s, ok := m["status"]
			if ok {
				// response to subscribe request
				assert.Equal(t, "CONNECTED", s.(string))
			}
		case <-ticker.C:
			logger.Error(cto.ctx, "Timed out on waiting for subscription response")
			assert.Fail(t, "Timed out on waiting for subscription response")
		}
	}

	// unsubscribe
	for _, v := range subTestObjects {
		req := map[string]string{
			"eventId":   uuid.NewString(),
			"timestamp": fmt.Sprint(time.Now().UnixMilli()),
			"action":    string(protocol.UNSUB_POS_CONNECTION_STATUS),
		}
		assert.NotNil(t, v.wsChannel)
		err := v.wsChannel.WriteJSON(req)
		assert.Nil(t, err, err)
	}
	time.Sleep(5 * time.Second)
	for i, v := range requests {
		output, err := subTestObjects[i].dbClient.Query(subTestObjects[i].connectionTableName).Eq("id", v["eventId"]).Exec(subTestObjects[i].ctx)
		assert.Nil(t, err, err)
		assert.Equal(t, 1, len(output.Data))
		assert.Equal(t, subTestObjects[i].TestObject.deviceUuid, output.Data[0]["deviceUuid"].(string))
		assert.Equal(t, subTestObjects[i].TestObject.entityUuid, output.Data[0]["entityUuid"].(string))
		assert.Equal(t, subTestObjects[i].TestObject.siteUuid, output.Data[0]["siteUuid"].(string))
		assert.Equal(t, "DISCONNECTED", output.Data[0]["status"].(string))
		assert.NotNil(t, output.Data[0]["ttl"])
	}
}

func TestReceivePurchaseRequest(t *testing.T) {
	d := setupConnection(createTestObject(types.ORACLE))

	c := make(chan map[string]any)

	go func() {
		for {
			_, message, _ := d.wsChannel.ReadMessage()
			if len(message) > 0 {
				logger.Info(d.ctx, "receive message "+string(message))
				var j map[string]any
				_ = json.Unmarshal(message, &j)
				a, ok := j["action"]
				assert.True(t, ok)
				if a.(string) != string(protocol.PURCHASE) {
					// t.Fatalf("should not receive this action %s", a.(string))
					assert.Fail(t, "should not receive this action "+a.(string))
				}
				_, ok = j["eventId"]
				assert.True(t, ok)
				_, ok = j["timestamp"]
				assert.True(t, ok)
				d, ok := j["data"].(map[string]any)
				assert.True(t, ok)
				c <- d
			} else {
				break
			}
		}
	}()

	pr := PublishPurchaseRequestWebhook(d.TestObject, t)

	ticker := time.NewTicker(3 * time.Second)
	select {
	case m := <-c:
		logger.Info(d.ctx, fmt.Sprintf("receive response %v", m))
		assert.NotNil(t, m)
		prData := pr["data"].(map[string]any)
		assert.Equal(t, prData["sessionUuid"], m["sessionUuid"].(string))
		assert.Equal(t, prData["externalReference"], m["externalReference"].(string))
		assert.Equal(t, prData["transactionUuid"], m["transactionUuid"].(string))
		assert.Equal(t, prData["caid"], m["caid"].(string))
		assert.Equal(t, prData["amount"], int(m["amount"].(float64)))
	case <-ticker.C:
		logger.Error(cto.ctx, "Timed out on waiting for subscription response")
		assert.Fail(t, "Timed out on waiting for subscription response")
	}
}

func TestRefundRequest(t *testing.T) {
	d := setupConnection(createTestObject(types.ORACLE))

	c := make(chan map[string]any)

	go func() {
		for {
			_, message, _ := d.wsChannel.ReadMessage()
			if len(message) > 0 {
				logger.Info(d.ctx, "receive message "+string(message))
				var j map[string]any
				_ = json.Unmarshal(message, &j)
				a, ok := j["action"]
				assert.True(t, ok)
				if a.(string) != string(protocol.REFUND) {
					assert.Fail(t, "should not receive this action "+a.(string))
				}
				_, ok = j["eventId"]
				assert.True(t, ok)
				_, ok = j["timestamp"]
				assert.True(t, ok)
				d, ok := j["data"].(map[string]any)
				assert.True(t, ok)
				c <- d
			} else {
				break
			}
		}
	}()

	pr := PublishRefundRequestWebhook(d.TestObject, t)

	ticker := time.NewTicker(3 * time.Second)
	select {
	case m := <-c:
		logger.Info(d.ctx, fmt.Sprintf("receive response %v", m))
		assert.NotNil(t, m)
		prData := pr["data"].(map[string]any)
		assert.Equal(t, prData["sessionUuid"], m["sessionUuid"].(string))
		assert.Equal(t, prData["externalReference"], m["externalReference"].(string))
		assert.Equal(t, prData["transactionUuid"], m["transactionUuid"].(string))
		assert.Equal(t, prData["caid"], m["caid"].(string))
		assert.Equal(t, prData["amount"], int(m["amount"].(float64)))
		assert.Equal(t, prData["originalTransactionUuid"], m["originalTransactionUuid"].(string))
		assert.Equal(t, prData["originalIsoProcessingCode"], m["originalIsoProcessingCode"].(string))
	case <-ticker.C:
		logger.Error(d.ctx, "Timed out on waiting for subscription response")
		assert.Fail(t, "Timed out on waiting for subscription response")
	}
}

func TestRefundResponse(t *testing.T) {
	d := setupConnection(createTestObject(types.ORACLE))

	c := make(chan map[string]any)

	go func() {
		for {
			_, message, _ := d.wsChannel.ReadMessage()
			if len(message) > 0 {
				logger.Info(d.ctx, "receive message "+string(message))
				var j map[string]any
				_ = json.Unmarshal(message, &j)
				a, ok := j["action"]
				assert.True(t, ok)
				if a.(string) != string(protocol.REFUND) {
					assert.Fail(t, "should not receive this action "+a.(string))
				}
				_, ok = j["eventId"]
				assert.True(t, ok)
				_, ok = j["timestamp"]
				assert.True(t, ok)
				d, ok := j["data"].(map[string]any)
				assert.True(t, ok)
				c <- d
			} else {
				break
			}
		}
	}()

	// Create a refund response with success status
	refundResponse := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    "refund",
		"data": map[string]any{
			"sessionUuid":               uuid.NewString(),
			"deviceUuid":                d.deviceUuid,
			"transactionUuid":           uuid.NewString(),
			"type":                      "REFUND",
			"amount":                    1500,
			"transactionCurrency":       "AUD",
			"timestamp":                 time.Now().Format(time.RFC3339),
			"caid":                      "************",
			"externalReference":         "123123123",
			"originalTransactionUuid":   uuid.NewString(),
			"originalIsoProcessingCode": "000000",
			"deviceType":                string(types.DeviceTypeTerminal),
		},
	}

	b, _ := json.Marshal(refundResponse)
	req := events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": d.apiKey,
			"Content-Type":  "application/json",
		},
		Body: string(b),
	}

	lambdaName := fmt.Sprintf("%s-posconnector-api-posinterface-webhook", d.stage)
	invokeOutput, err := InvokeLambda(d.ctx, lambdaName, req)
	assert.Nil(t, err)

	var resp LambdaResponsePayload
	json.Unmarshal(invokeOutput.Payload, &resp)
	assert.Equal(t, 200, resp.StatusCode)

	ticker := time.NewTicker(3 * time.Second)
	select {
	case m := <-c:
		logger.Info(d.ctx, fmt.Sprintf("receive response %v", m))
		assert.NotNil(t, m)
		assert.Equal(t, refundResponse["data"].(map[string]any)["sessionUuid"], m["sessionUuid"].(string))
		assert.Equal(t, refundResponse["data"].(map[string]any)["transactionUuid"], m["transactionUuid"].(string))
		assert.Equal(t, refundResponse["data"].(map[string]any)["type"], m["type"].(string))
		assert.Equal(t, refundResponse["data"].(map[string]any)["amount"], int(m["amount"].(float64)))
		assert.Equal(t, refundResponse["data"].(map[string]any)["caid"], m["caid"].(string))
		assert.Equal(t, refundResponse["data"].(map[string]any)["externalReference"], m["externalReference"].(string))
		assert.Equal(t, refundResponse["data"].(map[string]any)["originalTransactionUuid"], m["originalTransactionUuid"].(string))
		assert.Equal(t, refundResponse["data"].(map[string]any)["originalIsoProcessingCode"], m["originalIsoProcessingCode"].(string))
	case <-ticker.C:
		logger.Error(d.ctx, "Timed out on waiting for refund response")
		assert.Fail(t, "Timed out on waiting for refund response")
	}
}

func TestRefundFailureResponse(t *testing.T) {
	d := setupConnection(createTestObject(types.ORACLE))

	c := make(chan map[string]any)

	go func() {
		for {
			_, message, _ := d.wsChannel.ReadMessage()
			if len(message) > 0 {
				logger.Info(d.ctx, "receive message "+string(message))
				var j map[string]any
				_ = json.Unmarshal(message, &j)
				a, ok := j["action"]
				assert.True(t, ok)
				if a.(string) != string(protocol.REFUND) {
					assert.Fail(t, "should not receive this action "+a.(string))
				}
				_, ok = j["eventId"]
				assert.True(t, ok)
				_, ok = j["timestamp"]
				assert.True(t, ok)
				d, ok := j["data"].(map[string]any)
				assert.True(t, ok)
				c <- d
			} else {
				break
			}
		}
	}()

	// Create a refund response with failure status
	refundResponse := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    "refund",
		"data": map[string]any{
			"sessionUuid":               uuid.NewString(),
			"deviceUuid":                d.deviceUuid,
			"transactionUuid":           uuid.NewString(),
			"type":                      "REFUND",
			"amount":                    1500,
			"transactionCurrency":       "AUD",
			"timestamp":                 time.Now().Format(time.RFC3339),
			"caid":                      "************",
			"externalReference":         "123123123",
			"originalTransactionUuid":   uuid.NewString(),
			"originalIsoProcessingCode": "000000",
			"deviceType":                string(types.DeviceTypeTerminal),
		},
	}

	b, _ := json.Marshal(refundResponse)
	req := events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": d.apiKey,
			"Content-Type":  "application/json",
		},
		Body: string(b),
	}

	lambdaName := fmt.Sprintf("%s-posconnector-api-posinterface-webhook", d.stage)
	invokeOutput, err := InvokeLambda(d.ctx, lambdaName, req)
	assert.Nil(t, err)

	var resp LambdaResponsePayload
	json.Unmarshal(invokeOutput.Payload, &resp)
	assert.Equal(t, 200, resp.StatusCode)

	ticker := time.NewTicker(3 * time.Second)
	select {
	case m := <-c:
		logger.Info(d.ctx, fmt.Sprintf("receive response %v", m))
		assert.NotNil(t, m)
		assert.Equal(t, refundResponse["data"].(map[string]any)["sessionUuid"], m["sessionUuid"].(string))
		assert.Equal(t, refundResponse["data"].(map[string]any)["transactionUuid"], m["transactionUuid"].(string))
		assert.Equal(t, refundResponse["data"].(map[string]any)["type"], m["type"].(string))
		assert.Equal(t, refundResponse["data"].(map[string]any)["amount"], int(m["amount"].(float64)))
		assert.Equal(t, refundResponse["data"].(map[string]any)["caid"], m["caid"].(string))
		assert.Equal(t, refundResponse["data"].(map[string]any)["externalReference"], m["externalReference"].(string))
		assert.Equal(t, refundResponse["data"].(map[string]any)["originalTransactionUuid"], m["originalTransactionUuid"].(string))
		assert.Equal(t, refundResponse["data"].(map[string]any)["originalIsoProcessingCode"], m["originalIsoProcessingCode"].(string))
	case <-ticker.C:
		logger.Error(d.ctx, "Timed out on waiting for refund response")
		assert.Fail(t, "Timed out on waiting for refund response")
	}
}

func TestRefundValidation(t *testing.T) {
	testCases := []struct {
		name          string
		refundRequest map[string]any
		expectError   bool
	}{
		{
			name: "missing amount",
			refundRequest: map[string]any{
				"eventId":   uuid.NewString(),
				"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
				"action":    "refund",
				"data": map[string]any{
					"sessionUuid":               uuid.NewString(),
					"deviceUuid":                uuid.NewString(),
					"transactionUuid":           uuid.NewString(),
					"type":                      "REFUND",
					"transactionCurrency":       "AUD",
					"timestamp":                 time.Now().Format(time.RFC3339),
					"caid":                      "************",
					"externalReference":         "123123123",
					"originalTransactionUuid":   uuid.NewString(),
					"originalIsoProcessingCode": "000000",
					"deviceType":                string(types.DeviceTypeTerminal),
				},
			},
			expectError: true,
		},
		{
			name: "valid refund request",
			refundRequest: map[string]any{
				"eventId":   uuid.NewString(),
				"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
				"action":    "refund",
				"data": map[string]any{
					"sessionUuid":               uuid.NewString(),
					"deviceUuid":                uuid.NewString(),
					"transactionUuid":           uuid.NewString(),
					"type":                      "REFUND",
					"amount":                    1500,
					"transactionCurrency":       "AUD",
					"timestamp":                 time.Now().Format(time.RFC3339),
					"caid":                      "************",
					"externalReference":         "123123123",
					"originalTransactionUuid":   uuid.NewString(),
					"originalIsoProcessingCode": "000000",
					"deviceType":                string(types.DeviceTypeTerminal),
					"status":                    "SUCCESS",
					"responseCode":              "00",
				},
			},
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			d := setupConnection(createTestObject(types.ORACLE))
			defer d.wsChannel.Close()

			// Update deviceUuid in the request to match the test object
			if data, ok := tc.refundRequest["data"].(map[string]any); ok {
				data["deviceUuid"] = d.deviceUuid
			}

			b, _ := json.Marshal(tc.refundRequest)
			req := events.APIGatewayProxyRequest{
				Headers: map[string]string{
					"Authorization": d.apiKey,
					"Content-Type":  "application/json",
				},
				Body: string(b),
			}

			lambdaName := fmt.Sprintf("%s-posconnector-api-posinterface-webhook", d.stage)
			invokeOutput, err := InvokeLambda(d.ctx, lambdaName, req)
			assert.Nil(t, err)

			var resp LambdaResponsePayload
			err = json.Unmarshal(invokeOutput.Payload, &resp)
			assert.Nil(t, err)

			if tc.expectError {
				assert.NotEqual(t, 200, resp.StatusCode)
			} else {
				assert.Equal(t, 200, resp.StatusCode)
			}
		})
	}
}

func TestRefundDeviceTypeHandling(t *testing.T) {
	testCases := []struct {
		name       string
		deviceType types.DeviceType
		provider   types.PosProvider
	}{
		{
			name:       "Terminal device refund",
			deviceType: types.DeviceTypeTerminal,
			provider:   types.ORACLE,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			testObj := createTestObject(tc.provider)
			testObj.deviceType = tc.deviceType

			// Set up device-specific configuration
			if tc.deviceType == types.DeviceTypePOS {
				testObj.venueId = uuid.NewString()
				testObj.pairedDeviceId = testObj.deviceUuid

				// Create a pairing record for POS device
				pairingUuid := uuid.NewString()
				pairingItem := map[string]any{
					"id":             pairingUuid,
					"type":           fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, testObj.deviceUuid),
					"entityUuid":     testObj.entityUuid,
					"siteUuid":       testObj.siteUuid,
					"deviceUuid":     testObj.deviceUuid,
					"provider":       string(tc.provider),
					"timestamp":      strconv.FormatInt(time.Now().Unix(), 10),
					"status":         "ACTIVE",
					"deviceType":     string(tc.deviceType),
					"venueId":        testObj.venueId,
					"pairedDeviceId": testObj.pairedDeviceId,
				}

				item, _ := attributevalue.MarshalMap(pairingItem)
				_, err := testObj.dbClient.InsertItem(testObj.ctx, testObj.connectionTableName, &item, nil)
				assert.Nil(t, err)
			}

			d := setupConnection(testObj)
			defer d.wsChannel.Close()

			c := make(chan map[string]any)

			go func() {
				for {
					_, message, _ := d.wsChannel.ReadMessage()
					if len(message) > 0 {
						logger.Info(d.ctx, "receive message "+string(message))
						var j map[string]any
						_ = json.Unmarshal(message, &j)
						a, ok := j["action"]
						assert.True(t, ok)
						if a.(string) != string(protocol.REFUND) {
							assert.Fail(t, "should not receive this action "+a.(string))
						}
						_, ok = j["eventId"]
						assert.True(t, ok)
						_, ok = j["timestamp"]
						assert.True(t, ok)
						d, ok := j["data"].(map[string]any)
						assert.True(t, ok)
						c <- d
					} else {
						break
					}
				}
			}()

			// Create a refund request with device type specific data
			refundRequest := map[string]any{
				"eventId":   uuid.NewString(),
				"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
				"action":    "refund",
				"data": map[string]any{
					"sessionUuid":               uuid.NewString(),
					"deviceUuid":                d.deviceUuid,
					"transactionUuid":           uuid.NewString(),
					"type":                      "REFUND",
					"amount":                    1500,
					"transactionCurrency":       "AUD",
					"timestamp":                 time.Now().Format(time.RFC3339),
					"caid":                      "************",
					"externalReference":         "123123123",
					"originalTransactionUuid":   uuid.NewString(),
					"originalIsoProcessingCode": "000000",
					"deviceType":                string(tc.deviceType),
					"status":                    "SUCCESS",
					"responseCode":              "00",
				},
			}

			if tc.deviceType == types.DeviceTypePOS {
				refundRequest["data"].(map[string]any)["venueId"] = testObj.venueId
				refundRequest["data"].(map[string]any)["pairedDeviceId"] = testObj.pairedDeviceId
			}

			b, _ := json.Marshal(refundRequest)
			req := events.APIGatewayProxyRequest{
				Headers: map[string]string{
					"Authorization": d.apiKey,
					"Content-Type":  "application/json",
				},
				Body: string(b),
			}

			lambdaName := fmt.Sprintf("%s-posconnector-api-posinterface-webhook", d.stage)
			invokeOutput, err := InvokeLambda(d.ctx, lambdaName, req)
			assert.Nil(t, err)

			var resp LambdaResponsePayload
			json.Unmarshal(invokeOutput.Payload, &resp)
			assert.Equal(t, 200, resp.StatusCode)

			ticker := time.NewTicker(3 * time.Second)
			select {
			case m := <-c:
				logger.Info(d.ctx, fmt.Sprintf("receive response %v", m))
				assert.NotNil(t, m)
				assert.Equal(t, refundRequest["data"].(map[string]any)["sessionUuid"], m["sessionUuid"].(string))
				assert.Equal(t, refundRequest["data"].(map[string]any)["transactionUuid"], m["transactionUuid"].(string))
				assert.Equal(t, refundRequest["data"].(map[string]any)["type"], m["type"].(string))
				assert.Equal(t, refundRequest["data"].(map[string]any)["amount"], int(m["amount"].(float64)))
				assert.Equal(t, refundRequest["data"].(map[string]any)["caid"], m["caid"].(string))
				assert.Equal(t, refundRequest["data"].(map[string]any)["externalReference"], m["externalReference"].(string))
				assert.Equal(t, refundRequest["data"].(map[string]any)["originalTransactionUuid"], m["originalTransactionUuid"].(string))
				assert.Equal(t, refundRequest["data"].(map[string]any)["originalIsoProcessingCode"], m["originalIsoProcessingCode"].(string))

				// Check deviceType if it exists in the response
				if deviceType, ok := m["deviceType"]; ok {
					assert.Equal(t, string(tc.deviceType), deviceType.(string))
				}
			case <-ticker.C:
				logger.Error(d.ctx, "Timed out on waiting for refund response")
				assert.Fail(t, "Timed out on waiting for refund response")
			}
		})
	}
}

// d1 -> s1
// d2 -> s2 + l1
// d3 -> s2 + l1,l2,l3
func setupSubscription(t *testing.T) []ConnectionTestObject {
	locations := []string{uuid.NewString(), uuid.NewString(), uuid.NewString()}
	d1 := setupConnection(createTestObject(types.HL))

	cto2 := createTestObject(types.HL)
	cto2.siteUuid = uuid.NewString()
	cto2.locations = []string{locations[0]}
	d2 := setupConnection(cto2)

	cto3 := createTestObject(types.HL)
	cto3.siteUuid = cto2.siteUuid
	cto3.locations = []string{locations[0], locations[1], locations[2]}
	d3 := setupConnection(cto3)

	// subscribe
	for _, d := range []ConnectionTestObject{d1, d2, d3} {
		req := map[string]any{
			"eventId":   uuid.NewString(),
			"timestamp": fmt.Sprint(time.Now().UnixMilli()),
			"action":    string(protocol.SUB_ORDERS_UPDATE),
			"data": map[string]string{
				"siteUuid": d.siteUuid,
			},
		}
		err := d.wsChannel.WriteJSON(req)
		assert.Nil(t, err, err)
	}
	return []ConnectionTestObject{d1, d2, d3}
}

func disconnectWebsocket(wsChann *websocket.Conn) {
	wsChann.Close()
}

func shouldReceive(t *testing.T, ctx context.Context, wsChann *websocket.Conn, c chan map[string]any) {
	defer func() { _ = recover() }()
	for {
		_, message, _ := wsChann.ReadMessage()
		if len(message) > 0 {
			logger.Info(ctx, "receive message "+string(message))
			var j map[string]any
			_ = json.Unmarshal(message, &j)
			a, ok := j["action"]
			assert.True(t, ok)
			assert.Equal(t, "subscribeOrdersUpdate", a.(string))
			_, ok = j["eventId"]
			assert.True(t, ok)
			_, ok = j["timestamp"]
			assert.True(t, ok)
			d, ok := j["data"].(map[string]any)
			assert.True(t, ok)
			s, ok := d["status"]
			if ok {
				// response to subscribe request
				logger.Info(ctx, "get connected response")
				assert.Equal(t, "CONNECTED", s.(string))
			} else {
				c <- d
			}
		}
	}
}
func shouldNotReceive(ctx context.Context, wsChann *websocket.Conn, c chan map[string]any) {
	defer func() { _ = recover() }()
	for {
		_, m, _ := wsChann.ReadMessage()
		if len(m) > 0 {
			var j map[string]any
			_ = json.Unmarshal(m, &j)
			if d, ok := j["data"].(map[string]any); ok {
				if s, o := d["status"]; o {
					if s.(string) == "CONNECTED" {
						continue
					}
				}
			}
			logger.Error(ctx, "should not recieve "+string(m))
			c <- map[string]any{}
		}
	}
}
