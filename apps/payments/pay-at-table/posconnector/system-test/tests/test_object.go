package tests

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"testing"
	"time"

	"slices"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/ssm"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

var awsRegionToEndpoint = map[string]string{
	"ap-southeast-2": "wss://syd.posconnector.myzeller.dev",
	"eu-west-2":      "wss://lon.posconnector.myzeller.dev",
}

type LambdaResponsePayload struct {
	StatusCode int    `json:"statusCode"`
	Body       string `json:"body"`
}

type TestObject struct {
	wsChannel           *websocket.Conn
	ctx                 context.Context
	dbClient            db.DynamoDb
	provider            types.PosProvider
	connectionTableName string
	stage               string
	entityUuid          string
	siteUuid            string
	deviceUuid          string
	accessToken         string
	wsEndpoint          string
	apiKey              string
	deviceType          types.DeviceType
	locations           []string
	pairedDeviceId      string
	venueId             string
}

type ConnectionTestObject struct {
	TestObject
	pairingUuid string
}

func createTestObject(provider types.PosProvider) TestObject {
	var testObject TestObject
	testObject.stage = os.Getenv("STAGE")
	testObject.connectionTableName = fmt.Sprintf("%s-posconnector-api-Pairing", testObject.stage)
	testObject.ctx = context.Background()
	testObject.dbClient = *db.NewDynamoDb(testObject.ctx)
	testObject.accessToken = GenerateAccessToken(AccessTokenClaims{})
	testObject.entityUuid = uuid.NewString()
	testObject.siteUuid = uuid.NewString()
	testObject.deviceUuid = uuid.NewString()
	testObject.provider = provider
	testObject.deviceType = types.DeviceTypeTerminal
	websocketClientProviders := []types.PosProvider{types.IMPOS, types.SDK}
	if checkIsInSystemTestStage(testObject.stage) || slices.Contains(websocketClientProviders, provider) {
		testObject.apiKey = uuid.NewString()
	} else {
		o, _ := testObject.dbClient.Query(testObject.connectionTableName).Eq("id", provider).Begin("type", types.METADATA).Exec(testObject.ctx)
		err := fmt.Errorf("cant find provider metadata %s", provider)
		if len(o.Data) == 0 {
			logger.Error(testObject.ctx, err.Error())
			log.Panic(err)
		}
		apiKey, ok := o.Data[0]["apiKey"].(string)
		if !ok {
			logger.Error(testObject.ctx, err.Error())
			panic(err)
		}
		testObject.apiKey = apiKey
	}

	logger.Info(context.Background(), "generate api key:"+testObject.apiKey)
	fmt.Print(testObject.accessToken)

	cfg, _ := config.LoadDefaultConfig(context.TODO())
	client := ssm.NewFromConfig(cfg)

	if testObject.stage == "dev" {
		wsEndpoint, found := awsRegionToEndpoint[cfg.Region]
		if !found || wsEndpoint == "" {
			log.Fatal("ws endpoint is not set for dev stage in this region in awsRegionToEndpoint: " + cfg.Region)
		}
		testObject.wsEndpoint = wsEndpoint
	} else {
		input := ssm.GetParameterInput{
			Name: aws.String(testObject.stage + "-posconnector-api-websocket-endpoint"),
		}
		logger.Info(testObject.ctx, input)
		output, err := client.GetParameter(context.TODO(), &input)
		if err != nil {
			logger.Error(testObject.ctx, err.Error())
			log.Panic(err.Error())
		}
		testObject.wsEndpoint = *output.Parameter.Value
	}
	logger.Info(testObject.ctx, "get ws endpoint "+testObject.wsEndpoint)
	return testObject
}

func getPosinterfacePairType(deviceType types.DeviceType) string {
	if deviceType == types.DeviceTypePOS {
		return string(types.POSINTERFACE_PAIR_POS)
	}
	return string(types.POSINTERFACE_PAIR_DEVICE)
}

func setupConnection(to TestObject) ConnectionTestObject {
	var err error
	cto := ConnectionTestObject{TestObject: to}

	err = SaveTokenInDbsSessionTable(TokenSession{
		Id:          cto.deviceUuid,
		EntityUuid:  cto.entityUuid,
		Status:      "ACTIVE",
		AccessToken: cto.accessToken,
	})
	if err != nil {
		log.Fatal(err.Error())
	} else {
		logger.Info(cto.ctx, "save access token successfully")
	}

	cto.pairingUuid = uuid.NewString()
	log.Printf("connecting to %s, %s", cto.wsEndpoint, cto.accessToken)
	sessionItem := map[string]any{
		"id":         cto.pairingUuid,
		"type":       fmt.Sprintf("%s%s", getPosinterfacePairType(cto.deviceType), cto.deviceUuid),
		"entityUuid": cto.entityUuid,
		"siteUuid":   cto.siteUuid,
		"deviceUuid": cto.deviceUuid,
		"provider":   string(to.provider),
		"timestamp":  strconv.FormatInt(time.Now().Unix(), 10),
		"status":     "ACTIVE",
		"deviceType": string(cto.deviceType),
	}
	if cto.locations != nil {
		sessionItem["locations"] = cto.locations
	}
	if cto.venueId != "" {
		sessionItem["venueId"] = cto.venueId
	}
	if cto.pairedDeviceId != "" {
		sessionItem["pairedDeviceId"] = cto.pairedDeviceId
	}
	logger.Info(cto.ctx, fmt.Sprintf("save session item %s, %v", cto.connectionTableName, sessionItem))
	item, _ := attributevalue.MarshalMap(sessionItem)
	_, err = cto.dbClient.InsertItem(cto.ctx, cto.connectionTableName, &item, nil)
	if err != nil {
		log.Panic(err.Error())
	}
	setMetaData(string(to.provider), cto.TestObject)

	var resp *http.Response
	cto.wsChannel, resp, err = websocket.DefaultDialer.Dial(cto.wsEndpoint, http.Header{
		"Authorization": []string{"Bearer " + cto.accessToken},
		"DeviceUuid":    []string{cto.deviceUuid},
	})
	logger.Info(cto.ctx, "receive status code:"+strconv.Itoa(resp.StatusCode))
	if err != nil {
		b, _ := io.ReadAll(resp.Body)
		fmt.Println("response body:" + string(b))
		log.Panic("dial:", err.Error())
	}
	return cto
}

func checkIsInSystemTestStage(stage string) bool {
	return strings.HasPrefix(stage, "st") && stage != "staging"
}

func setMetaData(posName string, to TestObject) {
	if !checkIsInSystemTestStage(to.stage) {
		return
	}
	metaData := map[string]string{
		"id":       posName,
		"type":     fmt.Sprintf("%s%s", types.METADATA, to.apiKey),
		"endpoint": uuid.NewString(),
	}
	item, _ := attributevalue.MarshalMap(metaData)
	_, err := to.dbClient.InsertItem(to.ctx, to.connectionTableName, &item, nil)
	if err != nil {
		log.Panic("failed to save api key")
	}
}

func PublishOrderUpdateWebhook(to TestObject, o protocol.Order, t *testing.T) map[string]any {
	order := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    "subscribeOrdersUpdate",
		"data": map[string]any{
			"orderId":      o.OrderId,
			"siteUuid":     o.SiteUuid,
			"tableNumber":  o.TableNumber,
			"tableName":    o.TableName,
			"subTableName": o.SubTableName,
			"totalAmount":  o.TotalAmount,
			"owedAmount":   o.OwedAmount,
			"location":     *o.Location,
		},
	}
	b, _ := json.Marshal(order)

	// Create the API Gateway Proxy Request payload
	req := events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": to.apiKey,
			"Content-Type":  "application/json",
		},
		Body: string(b),
	}

	lambdaName := fmt.Sprintf("%s-posconnector-api-posinterface-webhook", to.stage)

	invokeOutput, err := InvokeLambda(to.ctx, lambdaName, req)

	logger.Info(to.ctx, fmt.Sprintf("publish to lambda %s, data %s", lambdaName, string(b)))

	var resp LambdaResponsePayload
	json.Unmarshal(invokeOutput.Payload, &resp)

	assert.Nil(t, err)
	assert.Equal(t, 200, resp.StatusCode)
	return order
}

func PublishPurchaseRequestWebhook(to TestObject, t *testing.T) map[string]any {
	purchaseRequest := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    "purchase",
		"data": map[string]any{
			"sessionUuid":         uuid.NewString(),
			"deviceUuid":          to.deviceUuid,
			"externalReference":   "123123123",
			"transactionUuid":     uuid.NewString(),
			"type":                "PURCHASE",
			"amount":              1500,
			"transactionCurrency": "AUD",
			"timestamp":           "2023-11-28T11:14:18+11:00",
			"caid":                "************",
		},
	}
	b, _ := json.Marshal(purchaseRequest)

	// Create the API Gateway Proxy Request payload
	req := events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": to.apiKey,
			"Content-Type":  "application/json",
		},
		Body: string(b),
	}

	lambdaName := fmt.Sprintf("%s-posconnector-api-posinterface-webhook", to.stage)

	invokeOutput, err := InvokeLambda(to.ctx, lambdaName, req)

	logger.Info(to.ctx, fmt.Sprintf("publish to lambda %s, data %s", lambdaName, string(b)))

	var resp LambdaResponsePayload
	json.Unmarshal(invokeOutput.Payload, &resp)

	assert.Nil(t, err)
	assert.Equal(t, 200, resp.StatusCode)
	return purchaseRequest
}

func PublishRefundRequestWebhook(to TestObject, t *testing.T) map[string]any {
	refundRequest := map[string]any{
		"eventId":   uuid.NewString(),
		"timestamp": strconv.Itoa(int(time.Now().UnixMilli())),
		"action":    "refund",
		"data": map[string]any{
			"sessionUuid":               uuid.NewString(),
			"deviceUuid":                to.deviceUuid,
			"externalReference":         "123123123",
			"transactionUuid":           uuid.NewString(),
			"type":                      "REFUND",
			"amount":                    1500,
			"transactionCurrency":       "AUD",
			"timestamp":                 "2023-11-28T11:14:18+11:00",
			"caid":                      "************",
			"originalTransactionUuid":   uuid.NewString(),
			"originalIsoProcessingCode": uuid.NewString(),
		},
	}
	b, _ := json.Marshal(refundRequest)

	// Create the API Gateway Proxy Request payload
	req := events.APIGatewayProxyRequest{
		Headers: map[string]string{
			"Authorization": to.apiKey,
			"Content-Type":  "application/json",
		},
		Body: string(b),
	}

	lambdaName := fmt.Sprintf("%s-posconnector-api-posinterface-webhook", to.stage)

	invokeOutput, err := InvokeLambda(to.ctx, lambdaName, req)

	logger.Info(to.ctx, fmt.Sprintf("publish to lambda %s, data %s", lambdaName, string(b)))

	var resp LambdaResponsePayload
	json.Unmarshal(invokeOutput.Payload, &resp)

	assert.Nil(t, err)
	assert.Equal(t, 200, resp.StatusCode)
	return refundRequest
}

func NewOrderObject() protocol.Order {
	o := map[string]any{
		"orderId":      uuid.NewString(),
		"siteUuid":     uuid.NewString(),
		"tableNumber":  uuid.NewString(),
		"tableName":    uuid.NewString(),
		"subTableName": uuid.NewString(),
		"totalAmount":  10,
		"owedAmount":   5,
		"location": map[string]string{
			"locationId":   uuid.NewString(),
			"locationName": uuid.NewString(),
		},
	}
	var order protocol.Order
	b, _ := json.Marshal(o)
	_ = json.Unmarshal(b, &order)
	return order
}
