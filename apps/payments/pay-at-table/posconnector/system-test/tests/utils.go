package tests

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/ssm"
	"github.com/aws/aws-sdk-go/aws"
	jwt "github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	coreSession "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/session"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
)

var dbsSessionTable = "dev-dbs-api-dynamodb-SessionCache"
var stage = os.Getenv("STAGE")
var sdkSessionTable = "dev-sdk-api-dynamodb-SessionCache"

type TokenSession struct {
	Id           string `dynamodbav:"id"`
	Type         string `dynamodbav:"type"`
	AccessToken  string `dynamodbav:"accessToken"`
	CustomerUuid string `dynamodbav:"customerUuid"`
	DeviceUuid   string `dynamodbav:"deviceUuid"`
	EntityUuid   string `dynamodbav:"entityUuid"`
	Role         string `dynamodbav:"role"`
	Status       string `dynamodbav:"status"`
}

func SaveTokenInDbsSessionTable(session TokenSession) error {
	dbClient := *db.NewDynamoDb(context.Background())
	session.Type = "deviceUuid"
	item, _ := attributevalue.MarshalMap(session)
	logger.Info(context.Background(), fmt.Sprintf("save item %#v", item))
	_, err := dbClient.InsertItem(context.Background(), dbsSessionTable, &item, nil)
	return err
}

func SaveIdentityInSdkSessionTable(session coreSession.IdentityCache) error {
	dbClient := *db.NewDynamoDb(context.Background())
	item, _ := attributevalue.MarshalMap(coreSession.ToMap(session))
	logger.Info(context.Background(), fmt.Sprintf("save item %s", utils.BestEffortStringify(item, false)))
	_, err := dbClient.InsertItem(context.Background(), sdkSessionTable, &item, nil)
	if err != nil {
		panic(err)
	}
	return err
}

func SaveDeviceInSdkSessionTable(session coreSession.DeviceCache) error {
	dbClient := *db.NewDynamoDb(context.Background())
	item, _ := attributevalue.MarshalMap(coreSession.ToMap(session))
	logger.Info(context.Background(), fmt.Sprintf("save item %s", utils.BestEffortStringify(item, false)))
	_, err := dbClient.InsertItem(context.Background(), sdkSessionTable, &item, nil)
	if err != nil {
		panic(err)
	}
	return err
}

type AccessTokenClaims struct {
	Exp          *int64 //  time.Now().Unix()
	Sub          *string
	Aud          []string
	CustomerUuid *string
}

func GenerateAccessToken(tokenClaims AccessTokenClaims) string {
	privateKey := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
	key, err := jwt.ParseRSAPrivateKeyFromPEM([]byte(privateKey))
	if err != nil {
		logger.Error(context.Background(), err.Error())
		return ""
	}
	now := time.Now().UTC()

	claims := make(jwt.MapClaims)
	claims["iat"] = now.Unix()                               // The time at which the token was issued.
	claims["nbf"] = now.Unix()                               // The time before which the token must be disregarded.
	claims["sub"] = uuid.NewString()                         // The time before which the token must be disregarded.
	claims["exp"] = now.Add(time.Hour * 1).Unix()            // The expiration time after which the token must be disregarded.
	claims["aud"] = []string{"https://devices.myzeller.com"} // The time before which the token must be disregarded.

	if tokenClaims.Exp != nil {
		claims["exp"] = *tokenClaims.Exp
	}

	if tokenClaims.Aud != nil {
		claims["aud"] = tokenClaims.Aud
	}

	if tokenClaims.CustomerUuid != nil {
		claims["https://identity.myzeller.com/customerUuid"] = *tokenClaims.CustomerUuid
	}

	token, err := jwt.NewWithClaims(jwt.SigningMethodRS256, claims).SignedString(key)
	if err != nil {
		logger.Error(context.Background(), "create: sign token: "+err.Error())
		return ""
	}
	return token
}

func GetRestEndpoint() string {
	cfg, _ := config.LoadDefaultConfig(context.TODO())
	client := ssm.NewFromConfig(cfg)
	input := ssm.GetParameterInput{
		Name:           aws.String(fmt.Sprintf("%s-posconnector-api-rest-endpoint", stage)),
		WithDecryption: aws.Bool(false),
	}
	output, err := client.GetParameter(context.TODO(), &input)
	if err != nil {
		log.Panic("Failed to load rest endpoint ssm " + err.Error())
	}
	return *output.Parameter.Value
}

func HttpResponseBodyToMap(resp *http.Response) (*map[string]any, error) {
	b, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error(context.TODO(), err.Error())
		return nil, err
	}
	var data map[string]any
	_ = json.Unmarshal(b, &data)
	return &data, nil
}
