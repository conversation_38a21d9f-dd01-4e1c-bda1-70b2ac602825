package tests

import (
	"context"
	"encoding/json"

	"github.com/aws/aws-sdk-go-v2/service/lambda"
)

func InvokeLambda(ctx context.Context, functionName string, body any) (*lambda.InvokeOutput, error) {
	cfg := loadAwsConfig(ctx)
	client := lambda.NewFromConfig(cfg)

	payload, _ := json.Marshal(body)

	params := lambda.InvokeInput{
		FunctionName: &functionName,
		Payload:      payload,
	}

	return client.Invoke(ctx, &params)
}
