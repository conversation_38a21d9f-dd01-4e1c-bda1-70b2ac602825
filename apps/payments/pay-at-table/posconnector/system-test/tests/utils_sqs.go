package tests

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/cloudformation"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
)

func loadAwsConfig(ctx context.Context) aws.Config {
	cfg, ok := ctx.Value(AwsConfig{}).(aws.Config)
	if !ok {
		localcfg, e := config.LoadDefaultConfig(ctx)
		if e != nil {
			log.Fatalf("unable to load SDK config, %v", ok)
		}
		cfg = localcfg
	}
	return cfg
}

func GetCqrsSqsEndpoint(ctx context.Context, componentName string) string {
	cfg := loadAwsConfig(ctx)
	client := cloudformation.NewFromConfig(cfg)
	var cqrsSqsEndpoint string
	stage := os.Getenv("STAGE")
	stackName := strings.Join([]string{stage, componentName, "cqrs", "iac", "sqs"}, "-")
	params := cloudformation.DescribeStacksInput{StackName: &stackName}
	describeStacksOutput, _ := client.DescribeStacks(ctx, &params)
	stackOutputs := describeStacksOutput.Stacks[0].Outputs
	for _, output := range stackOutputs {
		expectedOutputKey := "QueueURL"
		if *(output.OutputKey) == expectedOutputKey {
			cqrsSqsEndpoint = *output.OutputValue
		}
	}

	return cqrsSqsEndpoint
}

func SendSqsMessage(ctx context.Context, body *string, sqsQueueUrl string) {
	cfg := loadAwsConfig(ctx)
	client := sqs.NewFromConfig(cfg)

	params := sqs.SendMessageInput{
		QueueUrl:    &sqsQueueUrl,
		MessageBody: body,
	}
	logger.Info(ctx)
	output, _ := client.SendMessage(ctx, &params)
	logger.Info(ctx, fmt.Sprintf("send message to sqs %s %#v %#v", sqsQueueUrl, params, output))
}
