import { CommonServicesModule } from '@npco/component-dbs-mp-common';

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { EnvService } from '../config/envService';
import { CatalogRepository } from '../repositories/catalogRepository';
import { DeviceRepository } from '../repositories/deviceRepository';
import { OrderRepository } from '../repositories/orderRepository';
import { TransactionRepository } from '../repositories/transactionRepository';
import { ZposEngineApi } from '../services/engine/zposEngineApi';
import { QueryService } from '../services/query/queryService';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    CommonServicesModule,
  ],
  providers: [
    OrderRepository,
    QueryService,
    EnvService,
    ZposEngineApi,
    TransactionRepository,
    DeviceRepository,
    CatalogRepository,
  ],
})
export class QueryModule {}
