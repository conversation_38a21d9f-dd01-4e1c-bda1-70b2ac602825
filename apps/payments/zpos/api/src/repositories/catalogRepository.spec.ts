import { BffDynamoDbClient } from '@npco/component-bff-core/dist/dynamodb/__mocks__/bffDynamoDbClient';
import { DynamodbService } from '@npco/component-dbs-mp-common/dist/dynamodb/dynamodbService';
import {
  CatalogDiscountConfig,
  CatalogItemType,
  CatalogServiceChargeConfig,
  CatalogUnit,
} from '@npco/component-dto-catalog';
import { DbRecordType } from '@npco/component-dto-core';

import { v4 } from 'uuid';

import type {
  CreateOrderDiscountInput,
  CreateOrderItemModifierInput,
  CreateOrderServiceChargeInput,
} from '../services/mutation/inputs/types';
import { OrderItemType } from '../services/mutation/inputs/types';

import { CatalogRepository } from './catalogRepository';

describe('Catalog repository test suite', () => {
  const envService = {
    catalogComponentTable: 'Catalogs',
  } as any;
  const dbService = new DynamodbService(envService);
  const repository = new CatalogRepository(dbService, envService);

  beforeAll(() => {
    const mockDynamoDbClient = new BffDynamoDbClient();
    repository.dynamodbService.documentClient = mockDynamoDbClient as any;
  });

  describe('Catalog Settings', () => {
    it('should be able to query catalog settings', async () => {
      const entityUuid = v4();
      const settings = { entityUuid, itemsTaxInclusive: true };
      await repository.dynamodbService.put({
        TableName: 'Catalogs',
        Item: {
          id: entityUuid,
          type: DbRecordType.CATALOG_SETTINGS,
          ...settings,
        },
      });
      const res = await repository.getSettings(entityUuid);
      expect(res).toEqual({ ...settings, id: entityUuid, type: DbRecordType.CATALOG_SETTINGS });
    });

    it('should throw error when catalog settings not found', async () => {
      await expect(repository.getSettings('unknown')).rejects.toThrow('Db record not found');
    });
  });

  describe('Catalog Items', () => {
    it('catalogRecord should return null when catalogItemUuid is not provided', async () => {
      const entityUuid = v4();
      const orderItemInput = {
        id: v4(),
        name: 'Order #1',
        price: '30',
        type: OrderItemType.SINGLE,
        ordinal: 1,
        unit: CatalogUnit.QUANTITY,
        description: 'Just a description',
        quantity: 1,
        taxes: [
          {
            enabled: true,
            name: 'GST',
            percent: 10,
          },
        ],
      };
      const res = await repository.getCatalogDbRecords(entityUuid, orderItemInput as any);
      expect(res).toEqual({
        catalogDiscounts: [],
        catalogServiceCharges: [],
        catalogItem: null,
        catalogModifiers: [],
      });
    });

    it('should be able to query catalog db records for order item type SINGLE', async () => {
      const entityUuid = v4();
      const item = { id: v4(), entityUuid, type: DbRecordType.CATALOG_ITEM, name: 'coke', price: '100' };
      await repository.dynamodbService.put({
        TableName: 'Catalogs',
        Item: item,
      });
      const res = await repository.getCatalogDbRecords(entityUuid, {
        catalogItemUuid: item.id,
        type: CatalogItemType.SINGLE,
      } as any);
      expect(res).toEqual({
        catalogDiscounts: [],
        catalogServiceCharges: [],
        catalogItem: { id: item.id, name: 'coke', price: '100' },
        catalogModifiers: [],
      });
    });
    it('should be able to query catalog item with modifier, discounts and serviceCharges', async () => {
      const entityUuid = v4();
      const modifier = {
        id: v4(),
        type: DbRecordType.CATALOG_MODIFIER_SET,
        entityUuid,
        name: 'Colors',
        modifiers: [
          {
            id: v4(),
            name: 'red',
            price: '1000',
          },
        ],
      };
      const discount = {
        id: v4(),
        entityUuid,
        type: DbRecordType.CATALOG_DISCOUNT,
        name: '10$ off',
        config: 'AMOUNT',
        value: '1000',
      };
      const serviceCharge = {
        id: v4(),
        entityUuid,
        type: DbRecordType.CATALOG_SERVICE_CHARGE,
        name: '10$ Service Charge',
        config: 'AMOUNT',
        value: '50',
      };

      const item = { id: v4(), entityUuid, type: DbRecordType.CATALOG_ITEM, name: 'coke', price: '100' };
      await Promise.all([
        repository.dynamodbService.put({
          TableName: 'Catalogs',
          Item: item,
        }),
        repository.dynamodbService.put({
          TableName: 'Catalogs',
          Item: modifier,
        }),
        repository.dynamodbService.put({
          TableName: 'Catalogs',
          Item: discount,
        }),
        repository.dynamodbService.put({
          TableName: 'Catalogs',
          Item: serviceCharge,
        }),
      ]);

      const res = await repository.getCatalogDbRecords(entityUuid, {
        catalogItemUuid: item.id,
        type: CatalogItemType.SINGLE,
        modifiers: [{ catalogModifierSetUuid: modifier.id, catalogModifierUuid: modifier.modifiers[0].id }],
        discounts: [{ catalogDiscountUuid: discount.id }],
        serviceCharges: [{ catalogServiceChargeUuid: serviceCharge.id }],
      } as any);

      expect(res).toEqual({
        catalogDiscounts: [{ id: discount.id, name: '10$ off', config: 'AMOUNT', value: '1000' }],
        catalogServiceCharges: [{ id: serviceCharge.id, name: '10$ Service Charge', config: 'AMOUNT', value: '50' }],
        catalogItem: { id: item.id, name: 'coke', price: '100' },
        catalogModifiers: [{ id: modifier.modifiers[0].id, name: 'red', price: '1000' }],
      });
    }, 9999999);

    it('should be able to selectively query discount, serviceCharge and modifier references if value is passed/undefined from the input', async () => {
      const entityUuid = v4();
      const modifier = {
        id: v4(),
        type: DbRecordType.CATALOG_MODIFIER_SET,
        entityUuid,
        name: 'Colors',
        modifiers: [
          {
            id: v4(),
            name: 'red',
            price: '1000',
          },
        ],
      };
      const discount = {
        id: v4(),
        entityUuid,
        type: DbRecordType.CATALOG_DISCOUNT,
        name: '10$ off',
        config: 'AMOUNT',
        value: '1000',
      };
      const serviceCharge = {
        id: v4(),
        entityUuid,
        type: DbRecordType.CATALOG_SERVICE_CHARGE,
        name: '10$ Service Charge',
        config: 'AMOUNT',
        value: '50',
      };

      const item = { id: v4(), entityUuid, type: DbRecordType.CATALOG_ITEM, name: 'coke', price: '100' };
      await Promise.all([
        repository.dynamodbService.put({
          TableName: 'Catalogs',
          Item: item,
        }),
        repository.dynamodbService.put({
          TableName: 'Catalogs',
          Item: modifier,
        }),
        repository.dynamodbService.put({
          TableName: 'Catalogs',
          Item: discount,
        }),
        repository.dynamodbService.put({
          TableName: 'Catalogs',
          Item: serviceCharge,
        }),
      ]);

      const res = await repository.getCatalogDbRecords(entityUuid, {
        catalogItemUuid: item.id,
        type: CatalogItemType.SINGLE,
        modifiers: [
          { catalogModifierSetUuid: modifier.id, catalogModifierUuid: modifier.modifiers[0].id }, // existing modifier
          { id: v4(), name: 'adhoc modifier', price: '10', ordinal: 1, unit: CatalogUnit.QUANTITY, quantity: 10 }, // adhoc modifier
        ],
        discounts: [
          { catalogDiscountUuid: discount.id }, // existing discount item's id is passed into 'catalogDiscountUuid'
          { id: v4(), name: 'adhoc discount', config: CatalogDiscountConfig.AMOUNT, value: '10' }, // without 'catalogDiscountUuid'
        ],
        serviceCharges: [
          { catalogServiceChargeUuid: serviceCharge.id }, // existing service charge item's id is passed into 'catalogServiceChargeUuid'
          { id: v4(), name: 'adhoc service charge', config: CatalogDiscountConfig.AMOUNT, value: '10' }, // without 'catalogServiceChargeUuid'
        ],
      } as any);

      expect(res).toEqual({
        catalogDiscounts: [{ id: discount.id, name: '10$ off', config: 'AMOUNT', value: '1000' }],
        catalogServiceCharges: [{ id: serviceCharge.id, name: '10$ Service Charge', config: 'AMOUNT', value: '50' }],
        catalogItem: { id: item.id, name: 'coke', price: '100' },
        catalogModifiers: [{ id: modifier.modifiers[0].id, name: 'red', price: '1000' }],
      });
    });

    it('getCatalogDiscount should throw error if "catalogUuidDiscount" value is invalid/falsy', async () => {
      // "catalogDiscountUuid" is not defined in the input
      const invalidInput: CreateOrderDiscountInput = {
        id: v4(),
        config: CatalogDiscountConfig.AMOUNT,
        value: '10',
        ordinal: 1,
      };
      await expect(repository.getCatalogDiscount('entityUuid', invalidInput)).rejects.toThrow(
        'Invalid "CatalogDiscountUuid" value: "undefined"',
      );
    });

    it('getCatalogServiceCharge should throw error if "catalogServiceChargeUuid" value is invalid/falsy', async () => {
      // "catalogServiceChargeUuid" is not defined in the input
      const invalidInput: CreateOrderServiceChargeInput = {
        id: v4(),
        config: CatalogServiceChargeConfig.AMOUNT,
        value: '10',
        ordinal: 1,
      };
      await expect(repository.getCatalogServiceCharge('entityUuid', invalidInput)).rejects.toThrow(
        'Invalid "CatalogServiceChargeUuid" value: "undefined"',
      );
    });

    it('getCatalogModifier should throw error if "catalogModifierSetUuid" value is invalid/falsy', async () => {
      // "catalogDiscountUuid" is not defined in the input
      const invalidInput: CreateOrderItemModifierInput = {
        id: v4(),
        name: 'modifier',
        price: '10',
        quantity: 10,
        unit: CatalogUnit.QUANTITY,
        ordinal: 1,
      };

      await expect(repository.getCatalogModifier('entityUuid', invalidInput)).rejects.toThrow(
        'Invalid "catalogModifierSetUuid" value: "undefined"',
      );
    });

    it('should throw error when querying not supported item type', async () => {
      await expect(
        repository.getCatalogDbRecords('entityUuid', { catalogItemUuid: 'id', type: 'VARIANT' } as any),
      ).rejects.toThrow('Db record not found');
    });

    it('should be able to query whole catalog item record using getWholeCatalogItemRecord', async () => {
      const entityUuid = v4();
      const item = {
        id: v4(),
        entityUuid,
        type: DbRecordType.CATALOG_ITEM,
        name: 'Pepsi',
        price: '50',
      };

      await repository.dynamodbService.put({
        TableName: 'Catalogs',
        Item: item,
      });

      const res = await repository.getWholeCatalogItemRecord(entityUuid, item.id);

      expect(res).toEqual(item);
    });
  });
});
