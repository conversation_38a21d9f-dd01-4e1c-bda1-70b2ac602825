import { ServerError } from '@npco/component-bff-core/dist/error';
import { info } from '@npco/component-bff-core/dist/utils/logger';
import { DynamodbService } from '@npco/component-dbs-mp-common/dist';
import { DbRecordType } from '@npco/component-dto-core/dist';
import { OrderItemType } from '@npco/component-dto-order';

import { EnvService } from '../config/envService';
import type {
  CreateOrderDiscountInput,
  CreateOrderItemInput,
  CreateOrderItemModifierInput,
  CreateOrderServiceChargeInput,
} from '../services/mutation/inputs/types';
import { injectableWrapper } from '../utils/injectableWrapper';

import { BaseRepository } from './baseRepository';

@injectableWrapper()
export class CatalogRepository extends BaseRepository {
  constructor(readonly dbService: DynamodbService, private readonly environmentService: EnvService) {
    super(dbService, environmentService);
  }

  getSettings = async (entityUuid: string) => {
    return this.queryRecordOrThrowNotFound(
      entityUuid,
      entityUuid,
      DbRecordType.CATALOG_SETTINGS,
      this.environmentService.catalogComponentTable,
    );
  };

  getCatalogItem = async (entityUuid: string, item: CreateOrderItemInput) => {
    if (!item.catalogItemUuid) {
      info('catalogItemUuid is not provided');
      return null;
    }

    const dbRecordType = [OrderItemType.SINGLE, OrderItemType.VARIANT].includes(item.type)
      ? DbRecordType.CATALOG_ITEM
      : 'not_supported';
    const catalogItem = await this.queryRecordOrThrowNotFound(
      entityUuid,
      item.catalogItemUuid,
      dbRecordType,
      this.environmentService.catalogComponentTable,
    );
    return {
      id: catalogItem.id,
      name: catalogItem.name,
      price: catalogItem.price,
    };
  };

  getCatalogDbRecords = async (entityUuid: string, item: CreateOrderItemInput) => {
    const getItemProm = this.getCatalogItem(entityUuid, item);
    const getItemModifierProm = this.getCatalogItemModifiers(entityUuid, item.modifiers);
    const getItemDiscountProm = this.getCatalogDiscounts(entityUuid, item.discounts);
    const getItemServiceChargeProm = this.getCatalogServiceCharges(entityUuid, item.serviceCharges);
    const [catalogItem, catalogModifiers, catalogDiscounts, catalogServiceCharges] = await Promise.all([
      getItemProm,
      getItemModifierProm,
      getItemDiscountProm,
      getItemServiceChargeProm,
    ]);
    return {
      catalogItem,
      catalogModifiers,
      catalogDiscounts,
      catalogServiceCharges,
    };
  };

  getCatalogModifier = async (entityUuid: string, modifierInput: CreateOrderItemModifierInput) => {
    const { catalogModifierSetUuid, catalogModifierUuid } = modifierInput;

    if (!catalogModifierSetUuid) {
      throw new ServerError(`Invalid "catalogModifierSetUuid" value: "${catalogModifierSetUuid}"`);
    }

    const modifierSet = await this.queryRecordOrThrowNotFound(
      entityUuid,
      catalogModifierSetUuid,
      DbRecordType.CATALOG_MODIFIER_SET,
      this.environmentService.catalogComponentTable,
    );
    return modifierSet.modifiers?.find(
      (modifier: { id: string; name: string; price: string }) => modifier.id === catalogModifierUuid,
    );
  };

  getCatalogItemModifiers = async (entityUuid: string, modifiers?: CreateOrderItemModifierInput[]) => {
    const proms = (modifiers ?? [])
      .filter((m) => m.catalogModifierSetUuid)
      .map((modifier) => {
        return this.getCatalogModifier(entityUuid, modifier);
      });
    return Promise.all(proms);
  };

  getCatalogDiscount = async (entityUuid: string, discountInput: CreateOrderDiscountInput) => {
    const { catalogDiscountUuid } = discountInput;

    if (!catalogDiscountUuid) {
      throw new ServerError(`Invalid "CatalogDiscountUuid" value: "${catalogDiscountUuid}"`);
    }

    const discount = await this.queryRecordOrThrowNotFound(
      entityUuid,
      catalogDiscountUuid,
      DbRecordType.CATALOG_DISCOUNT,
      this.environmentService.catalogComponentTable,
    );

    return {
      id: discount.id,
      name: discount.name,
      config: discount.config,
      value: discount.value,
    };
  };

  getCatalogServiceCharge = async (entityUuid: string, serviceChargeInput: CreateOrderServiceChargeInput) => {
    const { catalogServiceChargeUuid } = serviceChargeInput;

    if (!catalogServiceChargeUuid)
      throw new ServerError(`Invalid "CatalogServiceChargeUuid" value: "${catalogServiceChargeUuid}"`);

    const serviceCharge = await this.queryRecordOrThrowNotFound(
      entityUuid,
      catalogServiceChargeUuid,
      DbRecordType.CATALOG_SERVICE_CHARGE,
      this.environmentService.catalogComponentTable,
    );

    return {
      id: serviceCharge.id,
      name: serviceCharge.name,
      config: serviceCharge.config,
      value: serviceCharge.value,
    };
  };

  getCatalogDiscounts = async (entityUuid: string, discounts?: CreateOrderDiscountInput[]) => {
    const proms = (discounts ?? [])
      .filter((d) => d.catalogDiscountUuid)
      .map((discount) => this.getCatalogDiscount(entityUuid, discount));
    return Promise.all(proms);
  };

  getCatalogServiceCharges = async (entityUuid: string, serviceCharges?: CreateOrderServiceChargeInput[]) => {
    const promises = (serviceCharges ?? [])
      .filter((d) => d.catalogServiceChargeUuid)
      .map((serviceCharge) => this.getCatalogServiceCharge(entityUuid, serviceCharge));
    return Promise.all(promises);
  };

  getWholeCatalogItemRecord = async (entityUuid: string, catalogItemUuid: string) => {
    return this.queryRecord(
      entityUuid,
      catalogItemUuid,
      DbRecordType.CATALOG_ITEM,
      this.environmentService.catalogComponentTable,
    );
  };
}
