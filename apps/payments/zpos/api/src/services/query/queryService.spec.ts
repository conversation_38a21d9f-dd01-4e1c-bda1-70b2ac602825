import { mock, mockReset } from 'jest-mock-extended';
import { v4 } from 'uuid';

import type { CatalogRepository } from '../../repositories/catalogRepository';
import type { DeviceRepository } from '../../repositories/deviceRepository';
import type { OrderRepository } from '../../repositories/orderRepository';
import type { TransactionRepository } from '../../repositories/transactionRepository';
import { getOrderDto } from '../../testData';

import { QueryService } from './queryService';

describe('query service tests', () => {
  let queryService: QueryService;
  const mockZposRepository = mock<OrderRepository>();
  const mockTxnRepository = mock<TransactionRepository>();
  const mockDeviceRepository = mock<DeviceRepository>();
  const mockCatalogRepository = mock<CatalogRepository>();
  beforeEach(() => {
    mockReset(mockZposRepository);
    queryService = new QueryService(mockZposRepository, mockTxnRepository, mockDeviceRepository, mockCatalogRepository);
  });

  it('should throw NOT_FOUND error when order not found', async () => {
    mockZposRepository.getOrder.mockResolvedValue(undefined);
    await expect(
      queryService.getOrder({ entityUuid: 'entityUuid', id: 'orderId', customerRole: 'ADMIN', customerUuid: v4() }),
    ).rejects.toThrow('Order not found');
  });

  it('query service should be defined', async () => {
    const order = getOrderDto('entityUuid');
    mockZposRepository.getOrders.mockResolvedValue({ orders: [order], nextToken: undefined });
    mockZposRepository.getOrder.mockResolvedValue({ ...order });
    mockZposRepository.getSite.mockResolvedValue([]);
    const getOrderResult = await queryService.getOrder({
      entityUuid: 'entityUuid',
      id: order.id,
      customerRole: 'ADMIN',
      customerUuid: v4(),
    });
    expect(getOrderResult).toEqual({
      ...order,
    });
    const orders = await queryService.getOrders({
      entityUuid: 'entityUuid',
      limit: 1,
      nextToken: undefined,
      customerRole: 'MANAGER',
      customerUuid: 'customerUuid',
    });
    expect(orders).toEqual({
      orders: [order],
    });
  });

  it('query service with catalog should be defined', async () => {
    const order = getOrderDto('entityUuid');
    if (order.items && order.items.length > 0) {
      order.items[0].catalogItem = {
        id: v4(),
        name: 'catalog 1',
        price: 5000000,
      } as any;
    }

    mockZposRepository.getOrders.mockResolvedValue({ orders: [order], nextToken: undefined });
    mockZposRepository.getOrder.mockResolvedValue({ ...order });
    mockZposRepository.getSite.mockResolvedValue([]);
    const getOrderResult = await queryService.getOrder({
      entityUuid: 'entityUuid',
      id: order.id,
      customerRole: 'ADMIN',
      customerUuid: v4(),
    });
    expect(getOrderResult).toEqual({
      ...order,
      items: [
        {
          ...(order.items && order.items[0] ? order.items[0] : {}),
          catalogItem: {
            id: expect.any(String),
            name: 'catalog 1',
            price: {
              value: '5000000',
              currency: 'AUD',
            },
          },
        },
      ],
    });
    const orders = await queryService.getOrders({
      entityUuid: 'entityUuid',
      limit: 1,
      nextToken: undefined,
      customerRole: 'MANAGER',
      customerUuid: 'customerUuid',
    });
    expect(orders).toEqual({
      orders: [
        {
          ...order,
          items: [
            {
              ...(order.items && order.items[0] ? order.items[0] : {}),
              catalogItem: {
                id: expect.any(String),
                name: 'catalog 1',
                price: {
                  value: '5000000',
                  currency: 'AUD',
                },
              },
            },
          ],
        },
      ],
    });
  });

  it('query service with catalog price undefined', async () => {
    const order = getOrderDto('entityUuid');
    if (order.items && order.items.length > 0) {
      order.items[0].catalogItem = {
        id: v4(),
        name: 'catalog 1',
      } as any;
    }

    mockZposRepository.getOrders.mockResolvedValue({ orders: [order], nextToken: undefined });
    mockZposRepository.getOrder.mockResolvedValue({ ...order });
    mockZposRepository.getSite.mockResolvedValue([]);
    const getOrderResult = await queryService.getOrder({
      entityUuid: 'entityUuid',
      id: order.id,
      customerRole: 'ADMIN',
      customerUuid: v4(),
    });
    expect(getOrderResult).toEqual({
      ...order,
      items: [
        {
          ...(order.items && order.items[0] ? order.items[0] : {}),
          catalogItem: {
            id: expect.any(String),
            name: 'catalog 1',
            price: {
              value: '0',
              currency: 'AUD',
            },
          },
        },
      ],
    });
    const orders = await queryService.getOrders({
      entityUuid: 'entityUuid',
      limit: 1,
      nextToken: undefined,
      customerRole: 'MANAGER',
      customerUuid: 'customerUuid',
    });
    expect(orders).toEqual({
      orders: [
        {
          ...order,
          items: [
            {
              ...(order.items && order.items[0] ? order.items[0] : {}),
              catalogItem: {
                id: expect.any(String),
                name: 'catalog 1',
                price: {
                  value: '0',
                  currency: 'AUD',
                },
              },
            },
          ],
        },
      ],
    });
  });

  it('should get order with payment transactions', async () => {
    const order = getOrderDto('entityUuid');
    const orderPayments = [
      {
        id: v4(),
        value: '1000',
        currency: 'USD',
      },
    ];
    const transactions = [
      {
        id: v4(),
        amount: { value: '1000' },
        currency: 'USD',
      },
    ];
    mockZposRepository.getOrder.mockResolvedValue({ ...order, payments: orderPayments });
    mockTxnRepository.getTransactionForCardPayment.mockResolvedValue(transactions[0] as any);
    // act
    const getOrderResult = await queryService.getOrder({
      entityUuid: 'entityUuid',
      id: order.id,
      customerRole: 'ADMIN',
      customerUuid: v4(),
    });
    // verify
    expect(getOrderResult).toEqual({
      ...order,
      payments: orderPayments.map((payment, index) => {
        return {
          ...payment,
          transaction: transactions[index],
        };
      }),
    });
  });

  it('should be able to get order with whole catalog item record against each order item', async () => {
    const entityUuid = v4();
    const order = getOrderDto(entityUuid);
    if (order.items && order.items.length > 0) {
      order.items[0].catalogItem = {
        id: v4(),
        name: 'catalog 1',
        price: 5000000,
      } as any;
    }
    const catalogItem = {
      ...order?.items?.[0]?.catalogItem,
      name: 'catalog 1',
      reportingCategoryColor: '#FFFFFF',
      images: [
        {
          id: v4(),
          sizes: [
            {
              size: 'small',
              url: 'https://example.com/small.jpg',
            },
          ],
        },
      ],
    };
    mockZposRepository.getOrders.mockResolvedValue({ orders: [order], nextToken: undefined });
    mockZposRepository.getOrder.mockResolvedValue({ ...order });
    mockZposRepository.getSite.mockResolvedValue([]);
    mockCatalogRepository.getWholeCatalogItemRecord.mockResolvedValue(catalogItem);
    const getOrderResult = await queryService.getOrder({
      entityUuid,
      id: order.id,
      customerRole: 'ADMIN',
      customerUuid: v4(),
    });
    expect(getOrderResult).toEqual({
      ...order,
      items: [
        {
          ...(order.items && order.items[0] ? order.items[0] : {}),
          catalogItem: {
            id: expect.any(String),
            name: 'catalog 1',
            price: {
              value: '5000000',
              currency: 'AUD',
            },
            reportingCategoryColor: '#FFFFFF',
            images: [
              {
                id: expect.any(String),
                sizes: [
                  {
                    size: 'small',
                    url: 'https://example.com/small.jpg',
                  },
                ],
              },
            ],
          },
        },
      ],
    });
  });

  it('should get order with deviceName', async () => {
    const order = getOrderDto('entityUuid');
    mockZposRepository.getOrder.mockResolvedValue({ ...order });
    mockDeviceRepository.getDeviceName.mockResolvedValue('deviceName');
    // act
    const getOrderResult = await queryService.getOrder({
      entityUuid: 'entityUuid',
      id: order.id,
      customerRole: 'ADMIN',
      customerUuid: v4(),
    });
    // verify
    expect(getOrderResult).toEqual({
      ...order,
      deviceName: 'deviceName',
    });
  });
});
