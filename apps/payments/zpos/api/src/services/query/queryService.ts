import { NotFoundError } from '@npco/component-bff-core/dist/error';
import type { CatalogItem } from '@npco/component-dto-catalog/dist/types';
import { ISO4217 } from '@npco/component-dto-core';
import type { OrderItem, OrderPayment } from '@npco/component-dto-order';

import { CatalogRepository } from '../../repositories/catalogRepository';
import { DeviceRepository } from '../../repositories/deviceRepository';
import { OrderRepository } from '../../repositories/orderRepository';
import { TransactionRepository } from '../../repositories/transactionRepository';
import { injectableWrapper } from '../../utils/injectableWrapper';
import { BaseService } from '../base';

import type { GetOrderInput, GetOrdersInput } from './types';

@injectableWrapper()
export class QueryService extends BaseService {
  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly transactionRepository: TransactionRepository,
    private readonly deviceRepository: DeviceRepository,
    private readonly catalogRepository: CatalogRepository,
  ) {
    super();
  }

  setTransactionsInPayments = async (orderPayments?: OrderPayment[]) => {
    if (orderPayments?.length) {
      const transactionPromises = orderPayments.map((payment) => {
        return this.transactionRepository.getTransactionForCardPayment(payment);
      });
      const transactions = await Promise.all(transactionPromises);
      return orderPayments.map((payment, index) => {
        return {
          ...payment,
          transaction: transactions[index],
        };
      });
    }
    return [];
  };

  getOrder = async (input: GetOrderInput) => {
    const { entityUuid, id } = input;
    const orderData = await this.orderRepository.getOrder(entityUuid, id);
    if (!orderData) {
      return Promise.reject(new NotFoundError('Order not found')); // NOSONAR
    }
    // set transactions in payments, only used in the getOrder GraphQL query
    const [orderPayments, deviceName, orderItemsWithCatalogItem] = await Promise.all([
      this.setTransactionsInPayments(orderData.payments),
      this.deviceRepository.getDeviceName(orderData as any),
      this.setCatalogItemToEachOrderItem(entityUuid, orderData.items),
    ]);
    const order = {
      ...orderData,
      items: orderItemsWithCatalogItem?.map(this.transformOrderItem),
      payments: orderPayments,
      deviceName,
    };

    return order;
  };

  getOrders = async (input: GetOrdersInput) => {
    const orderList = await this.orderRepository.getOrders(input);

    const orders = {
      ...orderList,
      orders: orderList.orders.map((order) => {
        return {
          ...order,
          items: order.items?.map(this.transformOrderItem),
        };
      }),
    };

    return orders;
  };

  private readonly transformOrderItem = (item: OrderItem) => {
    if (item.catalogItem) {
      return {
        ...item,
        catalogItem: {
          ...item.catalogItem,
          price: {
            value: String(item.catalogItem.price || 0),
            currency: ISO4217.AUD,
          },
        },
      };
    }
    return item;
  };

  private readonly setCatalogItemToEachOrderItem = async (entityUuid: string, orderItems: OrderItem[]) => {
    if (!orderItems?.length) return orderItems;

    const catalogItemPromises = orderItems.map((orderItem) =>
      this.catalogRepository.getWholeCatalogItemRecord(entityUuid, orderItem?.catalogItem?.id as string),
    );

    const catalogItems = (await Promise.all(catalogItemPromises)).filter(Boolean) as CatalogItem[];

    if (!catalogItems?.length) return orderItems;

    const catalogItemLookup = catalogItems.reduce((acc, item) => {
      acc[item.id] = item;
      return acc;
    }, {} as Record<string, CatalogItem>);

    return orderItems.map((orderItem) => {
      const catalogItemUuid = orderItem?.catalogItem?.id as string;
      const catalogItem = catalogItemLookup[catalogItemUuid];
      return (catalogItem ? { ...orderItem, catalogItem } : orderItem) as OrderItem;
    });
  };
}
