import { CatalogUnit } from '@npco/component-dto-catalog/dist';
import { OrderItemType } from '@npco/component-dto-order/dist';

import {
  Entity,
  Column,
  PrimaryColumn,
  ManyToOne,
  OneToMany,
  TableInheritance,
  ChildEntity,
  JoinColumn,
} from 'typeorm';

import { Order } from './order';
import type { OrderDiscount } from './orderDiscount'; // NOSONAR
import type { OrderServiceCharge } from './orderServiceCharge';
import { ColumnBigIntToNumberTransformer } from './transformers';

@Entity('OrderItems')
@TableInheritance({ column: { type: 'varchar', name: 'type' } })
export class BaseOrderItem {
  @PrimaryColumn({ type: 'uuid' })
  id!: string;

  @Column({ type: 'uuid' })
  entityUuid!: string;

  @Column({ type: 'uuid' })
  transactionUuid?: string;

  @Column({ type: 'uuid' })
  catalogItemUuid?: string;

  /* 
    This only stores the following properties of the catalogItem "id", "name" and "price
    sent by zpos-api when creating an order.
  */
  @Column({ type: 'jsonb' })
  catalogItem?: any;

  @Column({ type: 'text' })
  name!: string;

  @Column({ type: 'enum', enum: OrderItemType })
  type!: OrderItemType;

  @Column({ type: 'text' })
  sku?: string;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  price!: number;

  @Column({ type: 'enum', enum: CatalogUnit })
  unit!: CatalogUnit;

  @Column({ type: 'float' })
  quantity!: number;

  @Column({ type: 'jsonb' })
  taxes?: any;

  @Column({ type: 'smallint' })
  ordinal?: number;

  @Column({ type: 'integer' })
  createdTime!: number;

  @Column({ type: 'integer' })
  updatedTime?: number;

  // top level uuid of the catalogModifier that was used to create this modifier for quick lookup
  @Column({ type: 'uuid' })
  catalogModifierUuid?: string;

  // instance of the catalogModifier that was used to create this modifier
  @Column({ type: 'jsonb' })
  catalogModifier: any;

  @OneToMany('OrderDiscount', 'orderItem', {
    cascade: true,
  })
  discounts?: OrderDiscount[];

  @OneToMany('OrderServiceCharge', 'orderItem', {
    cascade: true,
  })
  serviceCharges?: OrderServiceCharge[];

  @Column({ type: 'uuid' })
  orderId!: string;

  @Column({ type: 'varchar' })
  variantName?: string;

  @ManyToOne('Order', 'items', { nullable: false, eager: false, orphanedRowAction: 'delete' })
  public order!: Order;

  @OneToMany('OrderItemModifier', 'parent', {
    cascade: true,
    nullable: true,
  })
  @JoinColumn({ referencedColumnName: 'parentId' })
  modifiers?: OrderItemModifier[];

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  subtotalAmount?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  totalAmount?: number;
}

@ChildEntity(OrderItemType.SINGLE)
export class OrderItem extends BaseOrderItem {}

@ChildEntity(OrderItemType.MODIFIER)
export class OrderItemModifier extends OrderItem {
  @Column({ type: 'uuid', nullable: true })
  parentId!: string;

  @ManyToOne('OrderItem', 'modifiers', { nullable: true, eager: false, orphanedRowAction: 'delete' })
  parent?: OrderItem;
}
