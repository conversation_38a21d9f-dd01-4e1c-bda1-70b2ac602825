import { ComponentClients } from '@npco/bff-systemtest-utils';
import { testIf } from '@npco/bff-systemtest-utils/dist/utils/testIf';
import { CustomerRole, DbRecordType } from '@npco/component-dto-core';
import { OrderStatus } from '@npco/component-dto-order';

import { v4 } from 'uuid';

import { ApiTestHelper, getOrderDto } from './utils/testHelper';

describe('ZPOS mp api system tests', () => {
  const apiTestHelper = new ApiTestHelper(ComponentClients.MerchantPortal);

  let entityUuid: string;

  beforeAll(async () => {
    await apiTestHelper.beforeAll();
    entityUuid = apiTestHelper.testData.entityUuid;
    await apiTestHelper.sleep(10000);
  });

  const getLambdaFullName = (name: string) => `graphqlHandlers-${name}`;

  describe('getOrder', () => {
    it('should return the order', async () => {
      const siteUuid = v4();
      const status = OrderStatus.OPEN;
      const orderDto = getOrderDto(entityUuid, siteUuid, status);
      // create the order by event
      await apiTestHelper.putOrder(orderDto);
      const result = await apiTestHelper.invokeLambda(getLambdaFullName('query'), {
        requestName: 'getOrders',
        args: {
          limit: 5,
          filter: {
            siteUuid,
            status,
          },
        },
      });
      expect(result.orders[0]).toEqual({
        id: orderDto.id,
        entityUuid,
        type: DbRecordType.ORDER,
        createdFromDeviceUuid: orderDto.createdFromDeviceUuid,
        createdTime: orderDto.createdTime,
        sortCreatedTime: orderDto.sortCreatedTime,
        referenceNumber: orderDto.referenceNumber,
        siteUuid: orderDto.siteUuid,
        status: orderDto.status,
        version: 0,
      });
    });

    it('should return the order with "status" filter', async () => {
      const siteUuid = v4();
      const status = OrderStatus.PAID;
      const orderDto = getOrderDto(entityUuid, siteUuid, status);
      // create the order by event
      await apiTestHelper.putOrder(orderDto);
      const result = await apiTestHelper.invokeLambda(getLambdaFullName('query'), {
        requestName: 'getOrders',
        args: {
          limit: 5,
          filter: {
            status,
          },
        },
      });
      expect(result.orders[0]).toEqual({
        id: orderDto.id,
        entityUuid,
        type: DbRecordType.ORDER,
        createdFromDeviceUuid: orderDto.createdFromDeviceUuid,
        createdTime: orderDto.createdTime,
        sortCreatedTime: orderDto.sortCreatedTime,
        referenceNumber: orderDto.referenceNumber,
        siteUuid: orderDto.siteUuid,
        status: orderDto.status,
        version: 0,
      });
    });

    it('should return the order with "siteUuid" filter', async () => {
      const siteUuid = v4();
      const status = OrderStatus.PAID;
      const orderDto = getOrderDto(entityUuid, siteUuid, status);
      // create the order by event
      await apiTestHelper.putOrder(orderDto);
      const result = await apiTestHelper.invokeLambda(getLambdaFullName('query'), {
        requestName: 'getOrders',
        args: {
          limit: 5,
          filter: {
            siteUuid,
          },
        },
      });
      expect(result.orders[0]).toEqual({
        id: orderDto.id,
        entityUuid,
        type: DbRecordType.ORDER,
        createdFromDeviceUuid: orderDto.createdFromDeviceUuid,
        createdTime: orderDto.createdTime,
        sortCreatedTime: orderDto.sortCreatedTime,
        referenceNumber: orderDto.referenceNumber,
        siteUuid: orderDto.siteUuid,
        status: orderDto.status,
        version: 0,
      });
    });

    it('should return empty order when no filter condition is met', async () => {
      const siteUuid = v4();
      const status = OrderStatus.PAID;
      const orderDto = getOrderDto(entityUuid, siteUuid, status);
      // create the order by event
      await apiTestHelper.putOrder(orderDto);
      const result = await apiTestHelper.invokeLambda(getLambdaFullName('query'), {
        requestName: 'getOrders',
        args: {
          limit: 5,
          filter: {
            siteUuid: 'non-existent-site-uuid',
            status: 'non-existent-status',
          },
        },
      });
      expect(result.orders).toEqual([]);
    });

    it('should return order with payment transaction', async () => {
      const siteUuid = v4();
      const status = OrderStatus.OPEN;
      const transactionUuid = v4();
      const orderDto = getOrderDto(entityUuid, siteUuid, status);
      const orderDtoWithPayment = {
        ...orderDto,
        payments: [
          {
            entityUuid,
            transactionUuid,
            tenderType: 'CARD',
          } as any,
        ],
      };
      // create the order by event
      await apiTestHelper.putOrder(orderDtoWithPayment);
      const entitiesTable = `dev-mp-api-dynamodb-Entities`;
      await apiTestHelper.dbClient.put({
        TableName: entitiesTable,
        Item: {
          id: transactionUuid,
          type: DbRecordType.TRANSACTION,
          entityUuid,
          amount: {
            value: '1000',
            currency: 'USD',
          },
        },
      });

      const result = await apiTestHelper.invokeLambda(getLambdaFullName('query'), {
        requestName: 'getOrder',
        args: {
          id: orderDto.id,
          entityUuid,
        },
      });
      expect(result).toEqual({
        id: orderDto.id,
        entityUuid,
        type: DbRecordType.ORDER,
        createdFromDeviceUuid: orderDto.createdFromDeviceUuid,
        createdTime: orderDto.createdTime,
        sortCreatedTime: orderDto.sortCreatedTime,
        referenceNumber: orderDto.referenceNumber,
        siteUuid: orderDto.siteUuid,
        status: orderDto.status,
        payments: [
          {
            entityUuid,
            transactionUuid,
            tenderType: 'CARD',
            transaction: {
              entityUuid,
              currency: 'USD',
              id: transactionUuid,
              amount: 1000,
              scheme: 'OTHER',
            },
          },
        ],
        version: 0,
      });
    });

    it('should return order with deviceName', async () => {
      const siteUuid = v4();
      const status = OrderStatus.OPEN;
      const createdFromDeviceUuid = v4();
      const orderDto = getOrderDto(entityUuid, siteUuid, status);
      const orderDtoWithDeviceUuid = {
        ...orderDto,
        createdFromDeviceUuid,
      };
      // create the order by event
      await apiTestHelper.putOrder(orderDtoWithDeviceUuid);
      const entitiesTable = `dev-mp-api-dynamodb-Entities`;
      await apiTestHelper.dbClient.put({
        TableName: entitiesTable,
        Item: {
          id: createdFromDeviceUuid,
          type: DbRecordType.DEVICE_SETTINGS,
          entityUuid,
          deviceName: 'deviceName',
        },
      });

      const result = await apiTestHelper.invokeLambda(getLambdaFullName('query'), {
        requestName: 'getOrder',
        args: {
          id: orderDto.id,
          entityUuid,
        },
      });
      expect(result).toEqual({
        id: orderDto.id,
        entityUuid,
        type: DbRecordType.ORDER,
        createdFromDeviceUuid,
        createdTime: orderDto.createdTime,
        sortCreatedTime: orderDto.sortCreatedTime,
        referenceNumber: orderDto.referenceNumber,
        siteUuid: orderDto.siteUuid,
        status: orderDto.status,
        deviceName: 'deviceName',
        payments: [],
        version: 0,
      });
    });

    testIf(apiTestHelper.getStage() === 'dev', 'should return the orders for MANAGER user with sites', async () => {
      const siteUuid = v4();
      const status = OrderStatus.PAID;
      const customerSiteOrder = getOrderDto(entityUuid, siteUuid, status);
      await apiTestHelper.assignCustomerToSiteBySiteId(siteUuid);
      await apiTestHelper.setTestCustomerAsRole(CustomerRole.MANAGER);
      await Promise.all([
        apiTestHelper.putOrder(customerSiteOrder),
        apiTestHelper.putOrder(getOrderDto(entityUuid, v4(), status)),
        apiTestHelper.putOrder(getOrderDto(entityUuid, v4(), status)),
      ]);

      const result = await apiTestHelper.invokeLambda(getLambdaFullName('query'), {
        requestName: 'getOrders',
        args: {
          limit: 5,
        },
      });

      expect(result.orders.length).toEqual(1);
      expect(result.orders[0]).toEqual({
        id: customerSiteOrder.id,
        entityUuid,
        type: DbRecordType.ORDER,
        createdFromDeviceUuid: customerSiteOrder.createdFromDeviceUuid,
        createdTime: customerSiteOrder.createdTime,
        sortCreatedTime: customerSiteOrder.sortCreatedTime,
        referenceNumber: customerSiteOrder.referenceNumber,
        siteUuid: customerSiteOrder.siteUuid,
        status: customerSiteOrder.status,
        version: 0,
      });
    });

    // Running this test only in 'dev' as 'st' does not create mp Catalogs table
    testIf(
      apiTestHelper.getStage() === 'dev',
      'should return order with whole catalog item against each order',
      async () => {
        const siteUuid = v4();
        const status = OrderStatus.OPEN;
        const catalogItemUuid = v4();
        const orderDto = getOrderDto(entityUuid, siteUuid, status);
        const orderDtoWithCatalogItem = {
          ...orderDto,
          items: [
            {
              catalogItem: { id: catalogItemUuid },
            },
          ],
        };
        // create the order by event
        await apiTestHelper.putOrder(orderDtoWithCatalogItem);
        const catalogsTable = `dev-mp-api-dynamodb-Catalogs`;
        const catalogItem = {
          id: catalogItemUuid,
          type: DbRecordType.CATALOG_ITEM,
          entityUuid,
          name: 'Test Catalog Item',
          description: 'Test Description',
          reportingCategoryColor: '#102011',
          images: [
            {
              id: v4(),
              sizes: [
                {
                  size: 'SMALL',
                  url: 'http://image.png',
                },
              ],
            },
          ],
        };
        await apiTestHelper.dbClient.put({
          TableName: catalogsTable,
          Item: catalogItem,
        });

        const result = await apiTestHelper.invokeLambda(getLambdaFullName('query'), {
          requestName: 'getOrder',
          args: {
            id: orderDto.id,
            entityUuid,
          },
        });
        expect(result).toEqual({
          id: orderDto.id,
          entityUuid,
          type: DbRecordType.ORDER,
          createdFromDeviceUuid: orderDto.createdFromDeviceUuid,
          createdTime: orderDto.createdTime,
          sortCreatedTime: orderDto.sortCreatedTime,
          referenceNumber: orderDto.referenceNumber,
          siteUuid: orderDto.siteUuid,
          status: orderDto.status,
          items: [
            {
              catalogItem: {
                ...catalogItem,
                price: {
                  value: '0',
                  currency: 'AUD',
                },
              },
            },
          ],
          payments: [],
          version: 0,
        });
      },
    );
  });
});
