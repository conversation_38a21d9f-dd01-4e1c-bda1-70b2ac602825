import { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';

import { ContactService } from 'src/service/contact/contactService';

import { TransactionDefaultCategoryService } from '../../service/debitCardAccountTransaction/transactionDefaultCategory/transactionDefaultCategoryService';
import { dynamodbClient } from '../../service/dynamodb/client';
import { EntityQueryService } from '../../service/entity/entityQueryService';
import { EnvironmentService } from '../../service/environment/environmentService';
import { MerchantService } from '../../service/richData/merchantService';
import type { SqsProjectionHandler } from '../types';

const envService = new EnvironmentService();
const dynamodbService = new DynamodbService(envService, dynamodbClient);
const entityQueryService = new EntityQueryService(envService, dynamodbService);
const merchantService = new MerchantService(envService, dynamodbService);
const contactService = new ContactService(dynamodbService);

const transactionDefaultCategoryService = new TransactionDefaultCategoryService(
  dynamodbService,
  envService,
  entityQueryService,
  merchantService,
  contactService,
);

const lambdaHandler: SqsProjectionHandler = async (event) => {
  await transactionDefaultCategoryService.processEvents(event.Records);
};

export const handler = withMiddlewaresV2({ eventType: LambdaEventSource.SQS }, lambdaHandler);
