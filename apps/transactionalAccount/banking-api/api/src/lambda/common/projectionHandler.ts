import { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';

import { InterestSummaryStorageProjectionService } from 'src/service/interestSummary/interestSummaryStorage/interestSummaryStorageProjectionService';
import { ScheduledTransferProjectionService } from 'src/service/scheduledTransfers/scheduledTransferProjectionService';

import { AccountStatementStorageProjectionService } from '../../service/accountStatement/accountStatementStorage/accountStatementStorageProjectionService';
import { AccountStatementTransactionProjectionService } from '../../service/accountStatement/accountStatementTransaction/accountStatementTransactionProjectionService';
import { ContactService } from '../../service/contact/contactService';
import { SavingsAccountProjectionService } from '../../service/debitCardAccount/savingsAccount/savingsAccountProjectionService';
import { DebitCardAccountTransactionProjectionService } from '../../service/debitCardAccountTransaction/projection/dcaTxnProjectionService';
import { SavingsAccountTransactionProjectionService } from '../../service/debitCardAccountTransaction/savingsAccountTransaction/savingsAccountTransactionProjectionService';
import { TransactionDefaultCategoryService } from '../../service/debitCardAccountTransaction/transactionDefaultCategory/transactionDefaultCategoryService';
import { dynamodbClient } from '../../service/dynamodb/client';
import { EntityQueryService } from '../../service/entity/entityQueryService';
import { EnvironmentService } from '../../service/environment/environmentService';
import { ProjectionService } from '../../service/projection/projectionService';
import { MerchantService } from '../../service/richData/merchantService';
import { EntitySavingsAccountProductProjectionService } from '../../service/savingsAccountProduct/entitySavingsAccountProductProjectionService';
import { SavingsAccountProductProjectionService } from '../../service/savingsAccountProduct/savingsAccountProductProjectionService';
import { TransferScheduledExecProjectionService } from '../../service/scheduledTransfers/transferScheduledExecProjectionService';
import type { SqsProjectionHandler } from '../types';

const envService = new EnvironmentService();
const dynamodbService = new DynamodbService(envService, dynamodbClient);
const entityQueryService = new EntityQueryService(envService, dynamodbService);
const accountStatementTransactionProjectionService = new AccountStatementTransactionProjectionService(
  envService,
  dynamodbService,
);

const accountStatementStorageProjectionService = new AccountStatementStorageProjectionService(
  envService,
  dynamodbService,
);
const savingsAccountProjectionService = new SavingsAccountProjectionService(envService, dynamodbService);
const savingsAccountTransactionBaseEvent = new SavingsAccountTransactionProjectionService(
  envService,
  dynamodbService,
  entityQueryService,
);

const savingsAccountProductProjectionService = new SavingsAccountProductProjectionService(envService, dynamodbService);

const entitySavingsAccountProductProjectionService = new EntitySavingsAccountProductProjectionService(
  envService,
  dynamodbService,
);

const interestSummaryStorageProjectionService = new InterestSummaryStorageProjectionService(
  envService,
  dynamodbService,
);

const scheduledTransferProjectionService = new ScheduledTransferProjectionService(envService, dynamodbService);

const transferScheduledExecProjectionService = new TransferScheduledExecProjectionService(envService, dynamodbService);

const merchantService = new MerchantService(envService, dynamodbService);
const contactService = new ContactService(dynamodbService);
const transactionDefaultCategoryService = new TransactionDefaultCategoryService(
  dynamodbService,
  envService,
  entityQueryService,
  merchantService,
  contactService,
);
const debitCardAccountTransactionProjectionService = new DebitCardAccountTransactionProjectionService(
  dynamodbService,
  envService,
  entityQueryService,
  transactionDefaultCategoryService,
  merchantService,
);

const projectionService = new ProjectionService(
  envService,
  accountStatementTransactionProjectionService,
  accountStatementStorageProjectionService,
  savingsAccountProjectionService,
  savingsAccountTransactionBaseEvent,
  savingsAccountProductProjectionService,
  entitySavingsAccountProductProjectionService,
  interestSummaryStorageProjectionService,
  scheduledTransferProjectionService,
  transferScheduledExecProjectionService,
  debitCardAccountTransactionProjectionService,
);

const lambdaHandler: SqsProjectionHandler = async (event) => {
  await projectionService.projectEvent(event.Records);
};

export const handler = withMiddlewaresV2({ eventType: LambdaEventSource.SQS }, lambdaHandler, []);
