import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware/xrayAggregateMiddleware';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import type { Icon } from '@npco/component-dto-core/dist/types';

import { DebitCardAccount } from '../../../service/debitCardAccount/models/graphql/debitCardAccount';
import { coreBankingService } from '../../dependencies/debitCardDependencies';
import type { ResolverHandler } from '../../types';

const getAggregateId = (e: Parameters<typeof lambdaHandler>[0]) => e.args.input.id;

const lambdaHandler: ResolverHandler<
  {
    input: {
      id: string;
      name: string;
      icon: Icon;
      entityUuid?: string;
    };
  },
  DebitCardAccount
> = async (e, c) => {
  const { id, name, icon } = e.args.input;
  const entityUuid = e.args.input.entityUuid ?? c.entityUuid;
  const result = await coreBankingService.personaliseDebitCardAccount({
    entityUuid,
    accountUuid: id,
    displayName: name,
    icon,
  });

  return new DebitCardAccount(result);
};

export const handler = withMiddlewaresV2(
  { component: ZellerComponent.CRMS, eventType: LambdaEventSource.APPSYNC },
  lambdaHandler,
  [xrayAggregateMiddleware(getAggregateId)],
);
