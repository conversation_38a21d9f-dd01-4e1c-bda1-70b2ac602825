import { BffDynamoDbClient } from '@npco/component-bff-core/dist/dynamodb';
import { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';

import { AbusiveDescriptionCommandService } from '../../service/abusiveDescription/abusiveDescriptionCommandService';
import { BedrockService } from '../../service/bedrock/client';
import { getSupportModel } from '../../service/bedrock/types';
import { CoreBankingService } from '../../service/coreBanking/coreBankingService';
import { CoreBankingTransactionService } from '../../service/coreBanking/coreBankingTransactionService';
import { DebitCardQueryService } from '../../service/debitCard/debitCardQueryService';
import { DebitCardAccountCommandService } from '../../service/debitCardAccount/debitCardAccountCommandService';
import { DebitCardAccountQueryService } from '../../service/debitCardAccount/debitCardAccountQueryService';
import { IssuingAccountQueryService } from '../../service/debitCardAccount/issuingAccountQueryService';
import { SavingsAccountQueryService } from '../../service/debitCardAccount/savingsAccount/savingsAccountQueryService';
import { DebitCardAccountCardService } from '../../service/debitCardAccountFromMpCommon/debitCardAccountCard/debitCardAccountCardService';
import { DebitCardAccountService } from '../../service/debitCardAccountFromMpCommon/debitCardAccountService';
import { DebitCardAccountTxnService } from '../../service/debitCardAccountFromMpCommon/debitCardAccountTxn/debitCardAccountTxnService';
import { DebitCardAccountTransactionCommandService } from '../../service/debitCardAccountTransaction/debitCardAccountTransactionCommandService';
import { BankingDynamoDbService } from '../../service/dynamodb/bankingDynamoDbService';
import { dynamodbClient } from '../../service/dynamodb/client';
import { EntityQueryService } from '../../service/entity/entityQueryService';
import { EnvironmentService } from '../../service/environment/environmentService';
import { PaymentInstrumentQueryService } from '../../service/paymentInstrument/paymentInstrumentQueryService';
import { MerchantService as RichDataService } from '../../service/richData/merchantService';
import { EntitySavingsAccountProductQueryService } from '../../service/savingsAccountProduct/entitySavingsAccountProductQueryService';
import { SavingsAccountProductQueryService } from '../../service/savingsAccountProduct/savingsAccountProductQueryService';

export const envService = new EnvironmentService();
export const dynamodbService = new DynamodbService(envService, dynamodbClient);
const bankingDynamoDbService = new BankingDynamoDbService(envService, new BffDynamoDbClient());

export const entityQueryService = new EntityQueryService(envService, dynamodbService);
export const debitCardAccountQueryService = new DebitCardAccountQueryService(dynamodbService);
export const issuingAccountQueryService = new IssuingAccountQueryService(envService, dynamodbService);
export const paymentInstrumentQueryService = new PaymentInstrumentQueryService(dynamodbService);
export const debitCardQueryService = new DebitCardQueryService(dynamodbService);
const coreBankingService = new CoreBankingService(envService);

export const coreBankingTransactionService = new CoreBankingTransactionService(envService);

const model = getSupportModel(process.env.BEDROCK_MODEL_NAME);
const region = process.env.BEDROCK_REGION ?? 'eu-west-2';
const bedrockService = new BedrockService(model, region);
const abusiveDescriptionCommandService = new AbusiveDescriptionCommandService(bedrockService, dynamodbService);

const richDataService = new RichDataService(envService, bankingDynamoDbService);
const debitCardAccountCardService = new DebitCardAccountCardService(envService, bankingDynamoDbService);
const debitCardAccountTxnService = new DebitCardAccountTxnService(envService, bankingDynamoDbService, richDataService);

export const debitCardAccountTransactionCommandService = new DebitCardAccountTransactionCommandService(
  coreBankingTransactionService,
  debitCardAccountQueryService,
  paymentInstrumentQueryService,
  abusiveDescriptionCommandService,
);

export const entitySavingsAccountProductQueryService = new EntitySavingsAccountProductQueryService(
  envService,
  dynamodbService,
);

export const savingsAccountQueryService = new SavingsAccountQueryService(
  entityQueryService,
  envService,
  dynamodbService,
);

export const savingsAccountProductQueryService = new SavingsAccountProductQueryService(
  envService,
  dynamodbService,
  savingsAccountQueryService,
  entityQueryService,
  entitySavingsAccountProductQueryService,
);

export const debitCardAccountCommandService = new DebitCardAccountCommandService(
  entityQueryService,
  coreBankingService,
);

export const dcaMpCommonService = new DebitCardAccountService(
  envService,
  bankingDynamoDbService,
  debitCardAccountCardService,
  debitCardAccountTxnService,
);
