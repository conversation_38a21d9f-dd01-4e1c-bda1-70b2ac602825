import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';

import type { Handler } from 'aws-lambda';

import type { DebitCardAccountTxnQueryInput } from '../../../service/debitCardAccountFromMpCommon/types';
import { dcaMpCommonService } from '../../dependencies/debitCardAccountDependencies';

export enum DebitCardAccountHandlerType {
  GET_DEBIT_CARD_V2,
  GET_DEBIT_CARDS_V2,
  GET_DEBIT_CARD_ACCOUNT_V2,
  GET_DEBIT_CARD_ACCOUNTS_V2,
  GET_DEBIT_CARD_ACCOUNT_BALANCES,
  GET_DEBIT_CARDS_BY_ACCOUNT_ID,
  GET_DEBIT_CARD_ACCOUNT_CARDS_COUNT_V2,
  GET_DEBIT_CARD_ACCOUNT_TXN,
  GET_DEBIT_CARD_ACCOUNT_TXNS,
}

const handlerMap: Record<DebitCardAccountHandlerType, Handler> = {
  [DebitCardAccountHandlerType.GET_DEBIT_CARD_V2]: withMiddlewaresV2(
    { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
    async (event, context: any) => {
      const { cardId } = event.args;
      const { customerUuid, role, entityUuid } = context;
      return dcaMpCommonService.getDebitCardAccountCardV2(entityUuid, cardId, { customerUuid, role });
    },
    [],
  ),

  [DebitCardAccountHandlerType.GET_DEBIT_CARDS_V2]: withMiddlewaresV2(
    { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
    async (event, context: any) => {
      const { limit, nextToken, filter } = event.args;
      const { customerUuid, role, entityUuid } = context;
      return dcaMpCommonService.getDebitCardAccountCardsV2(
        entityUuid,
        limit,
        nextToken,
        { customerUuid, role },
        filter,
      );
    },
    [],
  ),

  [DebitCardAccountHandlerType.GET_DEBIT_CARD_ACCOUNT_V2]: withMiddlewaresV2(
    { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
    async (event, context: any) => {
      const { debitCardAccountUuid } = event.args;
      const entityUuid = event.args.entityUuid ?? context.entityUuid;
      return dcaMpCommonService.getIssuingAccount(entityUuid, debitCardAccountUuid);
    },
    [],
  ),

  [DebitCardAccountHandlerType.GET_DEBIT_CARD_ACCOUNTS_V2]: withMiddlewaresV2(
    { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
    async (event, context: any) => {
      const entityUuid = event.args.entityUuid ?? context.entityUuid;
      const { limit, nextToken } = event.args;
      return dcaMpCommonService.getIssuingAccounts(entityUuid, limit, nextToken);
    },
    [],
  ),

  [DebitCardAccountHandlerType.GET_DEBIT_CARD_ACCOUNT_BALANCES]: withMiddlewaresV2(
    { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
    async (event, context: any) => {
      const { timeZone, range, debitCardAccountUuid } = event.args;
      const { entityUuid } = context;
      return dcaMpCommonService.getDebitCardAccountBalanceHistory(range, timeZone, entityUuid, debitCardAccountUuid);
    },
    [],
  ),

  [DebitCardAccountHandlerType.GET_DEBIT_CARDS_BY_ACCOUNT_ID]: withMiddlewaresV2(
    { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
    async (event, context: any) => {
      const entityUuid = event.args.entityUuid ?? context.entityUuid;
      const { debitCardAccountUuid } = event.args;
      return dcaMpCommonService.getDebitCardsByAccountId(entityUuid, debitCardAccountUuid);
    },
    [],
  ),

  [DebitCardAccountHandlerType.GET_DEBIT_CARD_ACCOUNT_CARDS_COUNT_V2]: withMiddlewaresV2(
    { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
    async (event, _context) => {
      const { entityUuid, debitCardAccountUuid } = event.args;
      return dcaMpCommonService.getDebitCardAccountCardsCountV2(entityUuid, debitCardAccountUuid);
    },
    [],
  ),

  [DebitCardAccountHandlerType.GET_DEBIT_CARD_ACCOUNT_TXN]: withMiddlewaresV2(
    { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
    async (event, context: any) => {
      const entityUuid = event.args.entityUuid ?? context.entityUuid;
      const { debitCardTransactionUuid } = event.args;
      return dcaMpCommonService.getDebitCardAccountTransactionV2(entityUuid, debitCardTransactionUuid);
    },
    [],
  ),

  [DebitCardAccountHandlerType.GET_DEBIT_CARD_ACCOUNT_TXNS]: withMiddlewaresV2(
    { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
    async (event, context: any) => {
      const args = event.args as DebitCardAccountTxnQueryInput;
      const entityUuid = event.args.entityUuid ?? context.entityUuid;

      return dcaMpCommonService.getDebitCardAccountTransactionsV2(args, entityUuid);
    },
    [],
  ),
};

export const getDebitCardAccountHandler = (type: DebitCardAccountHandlerType): Handler => {
  if (!(type in handlerMap)) {
    throw new Error(`Unsupported handler type: ${type}`);
  }
  return handlerMap[type];
};
