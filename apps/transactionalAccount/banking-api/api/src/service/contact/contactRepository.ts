import type { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';
import { warn } from '@npco/component-bff-core/dist/utils/logger';
import { DbRecordType } from '@npco/component-dto-core/dist/types';

import type { AttributeMap } from '../dynamodb/utils';

export class ContactRepository {
  constructor(private readonly dynamodbService: DynamodbService) {}

  getContact = async (entityUuid: string, contactUuid: string) => {
    const contactDbResult = await this.dynamodbService.queryIdByType(entityUuid, contactUuid, DbRecordType.CONTACT);
    if (contactDbResult.Items && contactDbResult.Items.length > 0) {
      const { id, firstName, lastName, businessName, icon, contactType, category, subcategoryUuid, status } =
        contactDbResult.Items[0] as AttributeMap;
      // only pass the necessary data for contact basic info
      if (status !== 'DELETED')
        return {
          id,
          contactType,
          firstName,
          lastName,
          businessName,
          icon,
          category,
          subcategoryUuid,
        };
    }
    warn(`Contact with uuid: '${contactUuid}' is NOT found`);
    return undefined;
  };

  getContactUuidByPaymentInstrumentUuid = async (
    entityUuid: string,
    paymentInstrumentUuid: string,
  ): Promise<string | undefined> => {
    const paymentInstrumentDbResult = await this.dynamodbService.queryIdByType(
      entityUuid,
      paymentInstrumentUuid,
      DbRecordType.PAYMENT_INSTRUMENT,
    );
    if (paymentInstrumentDbResult.Items && paymentInstrumentDbResult.Items.length > 0) {
      const [item] = paymentInstrumentDbResult.Items;
      return item?.contactUuid;
    }
    warn(`Contact associated with paymentInstrumentUuid: '${paymentInstrumentUuid}' is NOT found`);
    return undefined;
  };

  getContactUuidByMerchantId = async (entityUuid: string, merchantId: string): Promise<string | undefined> => {
    const merchantLink = await this.dynamodbService.queryIdByType(
      entityUuid,
      `${DbRecordType.MERCHANT_LINK}${merchantId}`,
      DbRecordType.CONTACT_LINK,
    );

    if (merchantLink.Items && merchantLink.Items.length > 0) {
      const [item] = merchantLink.Items;
      return item?.contactUuid;
    }
    warn(`Contact associated with merchantId: '${merchantId}' is NOT found`);
    return undefined;
  };

  getContactUuidBySenderLink = async (entityUuid: string, senderUuid: string): Promise<string | undefined> => {
    const contactLink = await this.dynamodbService.queryIdByType(
      entityUuid,
      `${DbRecordType.DCA_TRANSACTION_SENDER_LINK}${senderUuid}`,
      DbRecordType.CONTACT_LINK,
    );

    if (contactLink.Items && contactLink.Items.length > 0) {
      const [item] = contactLink.Items;
      return item?.contactUuid;
    }

    warn(`Contact associated with senderUuid: '${senderUuid}' is NOT found`);
    return undefined;
  };
}
