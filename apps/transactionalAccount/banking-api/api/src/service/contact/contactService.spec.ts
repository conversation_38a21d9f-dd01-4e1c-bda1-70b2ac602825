import { DynamodbService } from '@npco/component-bff-core/dist/dynamodb';
import { DbRecordType } from '@npco/component-dto-core/dist';

import { v4 } from 'uuid';

import { dynamodbClient } from '../dynamodb/client';
import type { EnvironmentService } from '../environment/environmentService';

import { ContactService } from './contactService';

const envService = {
  componentTableName: 'Entities',
} as jest.Mocked<EnvironmentService>;

describe('ContactService test suite', () => {
  const contactUuid = v4();
  const entityUuid = v4();
  let dynamodbService: DynamodbService;
  let contactService: ContactService;

  const saveDbItem = async (item: any) => {
    return dynamodbService.put({
      TableName: envService.componentTableName,
      Item: { ...item },
    });
  };

  beforeAll(async () => {
    dynamodbService = new DynamodbService(envService, dynamodbClient);
    contactService = new ContactService(dynamodbService);
  });

  it('should be able to find contact by uuid', async () => {
    const contactRecord = {
      id: contactUuid,
      contactUuid,
      entityUuid,
      type: DbRecordType.CONTACT,
      firstName: 'John',
      lastName: 'Doe',
    };
    await saveDbItem(contactRecord);
    const result = await contactService.getContactByUuid(entityUuid, contactUuid);
    expect(result).toEqual({
      id: contactUuid,
      firstName: 'John',
      lastName: 'Doe',
    });
  });

  it('should be able to get contactUuid by paymentInstrumentUuid', async () => {
    const paymentInstrumentUuid = v4();
    const paymentInstrumentRecord = {
      id: paymentInstrumentUuid,
      contactUuid,
      entityUuid,
      paymentInstrumentUuid,
      type: DbRecordType.PAYMENT_INSTRUMENT,
    };
    await saveDbItem(paymentInstrumentRecord);
    const result = await contactService.getContactUuidByPaymentInstrumentUuid(entityUuid, paymentInstrumentUuid);
    expect(result).toEqual(contactUuid);
  });

  it('should be able to get contactUuid by merchantId', async () => {
    const merchantId = v4();
    const merchantContactRecord = {
      id: `${DbRecordType.MERCHANT_LINK}${merchantId}`,
      type: DbRecordType.CONTACT_LINK,
      contactUuid,
      entityUuid,
    };
    await saveDbItem(merchantContactRecord);
    const result = await contactService.getContactUuidByMerchantId(entityUuid, merchantId);
    expect(result).toEqual(contactUuid);
  });

  it('should be able to find contactUui by senderLink', async () => {
    const senderUuid = v4();
    const contactLinkRecord = {
      id: `${DbRecordType.DCA_TRANSACTION_SENDER_LINK}${senderUuid}`,
      type: DbRecordType.CONTACT_LINK,
      contactUuid,
      entityUuid,
    };
    await saveDbItem(contactLinkRecord);
    const result = await contactService.getContactUuidBySenderLink(entityUuid, senderUuid);
    expect(result).toEqual(contactUuid);
  });
});
