import type { DynamodbService } from '@npco/component-bff-core/dist/dynamodb';
import type { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction/dist';

import {
  paymentInstrumentTransactionTypes,
  merchantTransactionTypes,
  senderTransactionTypes,
} from '../debitCardAccountTransaction/transactionDefaultCategory/utils';

import { ContactRepository } from './contactRepository';

export class ContactService {
  private readonly repo: ContactRepository;

  constructor(dynamodbService: DynamodbService) {
    this.repo = new ContactRepository(dynamodbService);
  }

  getContactByUuid = async (entityUuid: string, contactUuid?: string) => {
    if (!contactUuid) {
      return undefined;
    }
    return this.repo.getContact(entityUuid, contactUuid);
  };

  getContactUuidByPaymentInstrumentUuid = async (entityUuid: string, paymentInstrumentUuid: string) => {
    return this.repo.getContactUuidByPaymentInstrumentUuid(entityUuid, paymentInstrumentUuid);
  };

  getContactUuidByMerchantId = async (entityUuid: string, merchantId: string) => {
    return this.repo.getContactUuidByMerchantId(entityUuid, merchantId);
  };

  getContactUuidBySenderLink = async (entityUuid: string, senderUuid: string) => {
    return this.repo.getContactUuidBySenderLink(entityUuid, senderUuid);
  };

  getContactUuidByTransactionType = async (
    entityUuid: string,
    searchId: string,
    transactionType: DebitCardTransactionTypeV2,
  ) => {
    if (paymentInstrumentTransactionTypes.includes(transactionType)) {
      // retrieve contactUuid by paymentInstrumentUuid
      const contactUuid = await this.getContactUuidByPaymentInstrumentUuid(entityUuid, searchId);
      // double check if the contact exists
      if (!contactUuid) {
        return undefined;
      }

      const contactRecord = await this.getContactByUuid(entityUuid, contactUuid);

      if (contactRecord) {
        return contactRecord.id;
      }

      return undefined;
    }

    if (merchantTransactionTypes.includes(transactionType)) {
      return this.getContactUuidByMerchantId(entityUuid, searchId);
    }

    if (senderTransactionTypes.includes(transactionType)) {
      return this.getContactUuidBySenderLink(entityUuid, searchId);
    }

    return undefined;
  };
}
