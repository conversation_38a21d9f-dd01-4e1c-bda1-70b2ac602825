import { NotFoundError } from '@npco/component-bff-core/dist/error';
import { CustomerRole, ISO4217 } from '@npco/component-dto-core/dist';
import { DebitCardAccountType } from '@npco/component-dto-issuing-account/dist';
import type { DebitCardAccountCard } from '@npco/component-dto-issuing-card/dist';
import {
  DebitCardColour,
  DebitCardProductType,
  DebitCardStatus,
  DebitCardType,
} from '@npco/component-dto-issuing-card/dist';

import { v4 } from 'uuid';

import type { ProjectionDto } from '../projection/types';
import { MerchantService } from '../richData/merchantService';

import { DebitCardAccountCardService } from './debitCardAccountCard/debitCardAccountCardService';
import {
  createAccountData,
  createCardData,
  createSavingsAccountData,
  createSavingsV2AccountData,
} from './debitCardAccountCard/testcases/testUtils';
import { DebitCardAccountService } from './debitCardAccountService';
import { DebitCardAccountTxnService } from './debitCardAccountTxn/debitCardAccountTxnService';
import { convertDbItemToDebitCardTransaction } from './debitCardAccountTxn/debitCardTransactionUtils';

jest.mock('axios');

const mockGetMerchantDetails = jest.fn();

const mockGetDebitCardAccountTxn = jest.fn();
const mockGetDebitCardAccountTxns = jest.fn();
const mockMaterialiseTransaction = jest.fn();

jest.mock('./debitCardAccountTxn/debitCardAccountTxnService', () => {
  return {
    DebitCardAccountTxnService: jest.fn().mockImplementation(() => {
      return {
        getDebitCardAccountTxn: mockGetDebitCardAccountTxn,
        getDebitCardAccountTransactionsV2: mockGetDebitCardAccountTxns,
        materialiseTransaction: mockMaterialiseTransaction,
      };
    }),
  };
});

const mockGetDebitCardAccount = jest.fn();
const mockSaveDebitCardAccount = jest.fn();
const mockGetDebitCardAccounts = jest.fn();
const mockGetSavingsAccountV2 = jest.fn();
const mockGetSavingsAccountsV2 = jest.fn();
const mockGetSavingsAccounts = jest.fn();
const mockGetEztaDebitCardAccounts = jest.fn();
const mockGetEztaAndSavingsAccounts = jest.fn();
const mockGetDebitCardAccountBalanceAtTime = jest.fn();
const mockGetDebitCardAccountBalanceHistoryBetweenTime = jest.fn();
const mockGetAllDebitCardAccountBalanceHistoryBetweenTime = jest.fn();

jest.mock('./debitCardAccountDb', () => {
  return {
    DebitCardAccountDb: jest.fn().mockImplementation(() => {
      return {
        getDebitCardAccount: mockGetDebitCardAccount,
        getDebitCardAccounts: mockGetDebitCardAccounts,
        getSavingsAccounts: mockGetSavingsAccounts,
        getSavingsAccountV2: mockGetSavingsAccountV2,
        getSavingsAccountsV2: mockGetSavingsAccountsV2,
        getEztaDebitCardAccounts: mockGetEztaDebitCardAccounts,
        getEztaAndSavingsAccounts: mockGetEztaAndSavingsAccounts,
        saveDebitCardAccount: mockSaveDebitCardAccount,
        getDebitCardAccountBalanceAtTime: mockGetDebitCardAccountBalanceAtTime,
        getDebitCardAccountBalanceHistoryBetweenTime: mockGetDebitCardAccountBalanceHistoryBetweenTime,
        getAllDebitCardAccountBalanceHistoryBetweenTime: mockGetAllDebitCardAccountBalanceHistoryBetweenTime,
      };
    }),
  };
});

const mockGetDebitCardAccountCard = jest.fn();
const mockGetDebitCardAccountCards = jest.fn();
const mockMaterialiseDebitCardAccountCard = jest.fn();
const mockUpdateDebitCardAccountCard = jest.fn();

jest.mock('./debitCardAccountCard/debitCardAccountCardService', () => {
  return {
    DebitCardAccountCardService: jest.fn().mockImplementation(() => {
      return {
        getDebitCardAccountCard: mockGetDebitCardAccountCard,
        getDebitCardAccountCards: mockGetDebitCardAccountCards,
        materialiseDebitCardAccountCard: mockMaterialiseDebitCardAccountCard,
        updateDebitCardAccountCard: mockUpdateDebitCardAccountCard,
      };
    }),
  };
});

describe('debit card account service unit tests', () => {
  let debitCardAccountService: DebitCardAccountService;

  beforeEach(() => {
    debitCardAccountService = new DebitCardAccountService(
      {} as any,
      {} as any,
      new DebitCardAccountCardService({} as any, {} as any),
      new DebitCardAccountTxnService({} as any, {} as any, {} as any),
    );
    mockGetDebitCardAccount.mockReset();
    mockGetDebitCardAccountCard.mockReset();
    mockGetDebitCardAccountCards.mockReset();
    mockGetMerchantDetails.mockReset();
    mockSaveDebitCardAccount.mockReset();
    mockGetDebitCardAccountTxns.mockReset();
    mockGetDebitCardAccountTxn.mockReset();
    mockMaterialiseDebitCardAccountCard.mockReset();
    mockMaterialiseDebitCardAccountCard.mockReset();
    mockUpdateDebitCardAccountCard.mockReset();
    mockGetDebitCardAccounts.mockReset();
    mockGetSavingsAccounts.mockReset();
    mockGetSavingsAccountV2.mockReset();
    mockGetSavingsAccountsV2.mockReset();
  });

  describe('Debit Card Account Test', () => {
    it('materialiseDebitCardAccount can call debit card account db service to save the account', async () => {
      expect(mockSaveDebitCardAccount).not.toBeCalled();
      const data: ProjectionDto<any> = {
        aggregateId: v4(),
        uri: v4(),
        version: 1,
        createdTimestamp: v4(),
        payload: {
          id: v4(),
        },
      };
      await debitCardAccountService.materialiseDebitCardAccount(data);
      expect(mockSaveDebitCardAccount).toHaveBeenCalledWith(data.payload);
    });
  });

  describe('Debit Card Account Get Card Counts', () => {
    it('should return 0 cards count when account not found', async () => {
      mockGetDebitCardAccount.mockRejectedValueOnce(new NotFoundError('Not found'));
      const cardCount = await debitCardAccountService.getDebitCardAccountCardsCountV2(v4(), v4());

      expect(cardCount).toEqual({
        debit: 0,
        total: 0,
        expense: 0,
      });
    });

    it('should bubble up any unexpected errors when failing to get debit card account', async () => {
      const error = new Error('not happy');
      mockGetDebitCardAccount.mockRejectedValueOnce(error);
      const cardCount = debitCardAccountService.getDebitCardAccountCardsCountV2(v4(), v4());

      await expect(cardCount).rejects.toEqual(error);
    });

    it.each([
      ['banking', DebitCardStatus.SUSPENDED],
      ['cms', DebitCardStatus.LOST],
      [undefined, DebitCardStatus.LOST],
    ])('should be able to get debit card account cards count with source = %s', async (source, statusLost) => {
      const accountData = createAccountData();
      mockGetDebitCardAccount.mockImplementationOnce(() => {
        return {
          $response: {} as any,
          Items: [{ ...accountData, source }],
        };
      });
      const entityUuid = v4();
      const debitCardAccountUuid = v4();
      const cardsData = [
        createCardData(),
        { ...createCardData(), productType: DebitCardProductType.EXPENSE },
        createCardData(),
      ].map((c) => ({
        ...c,
        entityUuid,
        debitCardAccountUuid,
      }));
      mockGetDebitCardAccountCards.mockImplementation(() => {
        return { cards: cardsData, nextToken: 'fakeNextToken' };
      });

      const cardCount = await debitCardAccountService.getDebitCardAccountCardsCountV2(
        cardsData[0]!.entityUuid,
        cardsData[0]!.debitCardAccountUuid,
      );

      expect(mockGetDebitCardAccountCards).toHaveBeenCalledWith(
        entityUuid,
        expect.any(Number),
        undefined,
        expect.objectContaining({
          attrValues: expect.objectContaining({
            ':debitCardAccountUuid': debitCardAccountUuid,
            ':statusLost': statusLost,
          }),
        }),
      );

      expect(cardCount).toEqual({
        debit: 2,
        total: 3,
        expense: 1,
      });
    });

    it('should be able return debit card counts if product type is falsy', async () => {
      const accountData = createAccountData();
      mockGetDebitCardAccount.mockImplementationOnce(() => {
        return {
          $response: {} as any,
          Items: [{ ...accountData }],
        };
      });
      const entityUuid = v4();
      const debitCardAccountUuid = v4();
      const { productType, ...cardDataWithoutProductType } = createCardData();
      const cardsData = [
        createCardData(),
        cardDataWithoutProductType,
        { ...createCardData(), productType: null },
        { ...createCardData(), productType: undefined },
      ].map((c) => ({
        ...c,
        entityUuid,
        debitCardAccountUuid,
      }));
      mockGetDebitCardAccountCards.mockImplementation(() => {
        return { cards: cardsData, nextToken: 'fakeNextToken' };
      });

      const cardCount = await debitCardAccountService.getDebitCardAccountCardsCountV2(
        cardsData[0]!.entityUuid,
        cardsData[0]!.debitCardAccountUuid,
      );

      expect(mockGetDebitCardAccountCards).toHaveBeenCalledWith(
        entityUuid,
        expect.any(Number),
        undefined,
        expect.objectContaining({
          attrValues: expect.objectContaining({
            ':debitCardAccountUuid': debitCardAccountUuid,
            ':statusLost': DebitCardStatus.LOST,
          }),
        }),
      );

      expect(cardCount).toEqual({
        debit: 4,
        total: 4,
        expense: 0,
      });
    });
  });

  describe('Debit Card Account Card v2 Test', () => {
    const accountData = createAccountData();
    it('should be about to get debit card account', async () => {
      mockGetDebitCardAccount.mockImplementationOnce(() => {
        return {
          $response: {} as any,
          Items: [accountData],
        };
      });
      const result = await debitCardAccountService.getDebitCardAccount(accountData.entityUuid, accountData.id);
      const expectObject = {
        ...accountData,
        type: accountData.debitCardAccountType,
      };
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      delete expectObject.debitCardAccountType;
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      delete expectObject.updatedAt;
      expect(result).toMatchObject(expectObject);
    });

    describe('should throw not found error if debit card account is not found', () => {
      it('without Items parameter', async () => {
        mockGetDebitCardAccount.mockImplementationOnce(() => {
          return {
            $response: {} as any,
          };
        });
        await expect(debitCardAccountService.getDebitCardAccount('a', 'b')).rejects.toThrow(
          'Debit Card Account not found - b',
        );
      });

      it('with Items parameter - empty', async () => {
        mockGetDebitCardAccount.mockImplementationOnce(() => {
          return {
            $response: {} as any,
            Items: [],
          };
        });
        await expect(debitCardAccountService.getDebitCardAccount('a', 'b')).rejects.toThrow(
          'Debit Card Account not found - b',
        );
      });
    });

    describe('getDebitCardAccountCardV2 test suite', () => {
      it('Should be able to return the card details', async () => {
        const cardData = createCardData();
        mockGetDebitCardAccountCard.mockImplementation(() => {
          return cardData;
        });
        const card = await debitCardAccountService.getDebitCardAccountCardV2(cardData.entityUuid, cardData.id);
        expect(card).toBe(cardData);
        expect(mockGetDebitCardAccountCard).toHaveBeenCalledWith(cardData.entityUuid, cardData.id);
      });

      it('should be able to return card if customer is admin', async () => {
        const cardData = createCardData();
        mockGetDebitCardAccountCard.mockImplementation(() => {
          return cardData;
        });
        const card = await debitCardAccountService.getDebitCardAccountCardV2(cardData.entityUuid, cardData.id, {
          customerUuid: v4(),
          role: CustomerRole.ADMIN,
        });
        expect(card).toBe(cardData);
        expect(mockGetDebitCardAccountCard).toHaveBeenCalledWith(cardData.entityUuid, cardData.id);
      });

      it('should be able to return card if customer is manager and is card owner', async () => {
        const cardData = createCardData();
        mockGetDebitCardAccountCard.mockImplementation(() => {
          return cardData;
        });
        const card = await debitCardAccountService.getDebitCardAccountCardV2(cardData.entityUuid, cardData.id, {
          customerUuid: cardData.customerUuid || '',
          role: CustomerRole.MANAGER,
        });
        expect(card).toBe(cardData);
        expect(mockGetDebitCardAccountCard).toHaveBeenCalledWith(cardData.entityUuid, cardData.id);
      });

      it('should be able to return card if customer is manager and not card owner', async () => {
        const cardData = createCardData();
        mockGetDebitCardAccountCard.mockImplementation(() => {
          return cardData;
        });

        await expect(
          debitCardAccountService.getDebitCardAccountCardV2(cardData.entityUuid, cardData.id, {
            customerUuid: v4(),
            role: CustomerRole.MANAGER,
          }),
        ).rejects.toThrowError();
      });
    });

    describe('getDebitCardAccountCardsV2 test suite', () => {
      it('Should be able to return the cards details', async () => {
        const cardData = createCardData();
        mockGetDebitCardAccountCards.mockImplementation(() => {
          return { cards: [cardData], nextToken: 'fakeNextToken' };
        });
        const cardsConnection = await debitCardAccountService.getDebitCardAccountCardsV2(cardData.entityUuid, 10);
        expect(cardsConnection.cards).toEqual([cardData]);
        expect(cardsConnection.nextToken).toEqual('fakeNextToken');
        expect(mockGetDebitCardAccountCards).toHaveBeenCalledWith(cardData.entityUuid, 10, undefined, undefined);
      });

      it('should be able to return all cards if customer is admin', async () => {
        const cardData = createCardData();
        mockGetDebitCardAccountCards.mockImplementation(() => {
          return { cards: [cardData], nextToken: 'fakeNextToken' };
        });

        const cardsConnection = await debitCardAccountService.getDebitCardAccountCardsV2(
          cardData.entityUuid,
          10,
          undefined,
          {
            customerUuid: v4(),
            role: CustomerRole.ADMIN,
          },
        );
        expect(cardsConnection.cards).toEqual([cardData]);
        expect(cardsConnection.nextToken).toEqual('fakeNextToken');
        expect(mockGetDebitCardAccountCards).toHaveBeenCalledWith(cardData.entityUuid, 10, undefined, undefined);
      });

      it('should be able to return filtered cards if customer is not admin', async () => {
        const cardData = createCardData();
        mockGetDebitCardAccountCards.mockImplementation(() => {
          return { cards: [cardData], nextToken: 'fakeNextToken' };
        });

        const customerUuid = v4();
        const cardsConnection = await debitCardAccountService.getDebitCardAccountCardsV2(
          cardData.entityUuid,
          10,
          undefined,
          {
            customerUuid,
            role: CustomerRole.MANAGER,
          },
        );
        expect(cardsConnection.cards).toEqual([cardData]);
        expect(cardsConnection.nextToken).toEqual('fakeNextToken');
        expect(mockGetDebitCardAccountCards).toHaveBeenCalledWith(cardData.entityUuid, 10, undefined, {
          filterExp: ' AND #customerUuid=:customerUuid',
          attrValues: { ':customerUuid': customerUuid },
          attrNames: { '#customerUuid': 'customerUuid' },
        });
      });

      it(`should be able to return filtered cards for ${CustomerRole.ADMIN} role by product type`, async () => {
        const cardData = createCardData();
        mockGetDebitCardAccountCards.mockImplementation(() => {
          return { cards: [cardData], nextToken: 'fakeNextToken' };
        });

        const customerUuid = v4();
        const cardsConnection = await debitCardAccountService.getDebitCardAccountCardsV2(
          cardData.entityUuid,
          10,
          undefined,
          {
            customerUuid,
            role: CustomerRole.ADMIN,
          },
          JSON.stringify({
            expression: '#productType=:productType_eq',
            expressionNames: { '#productType': 'productType' },
            expressionValues: {
              ':productType_eq': { S: DebitCardProductType.EXPENSE },
            },
          }),
        );
        expect(cardsConnection.cards).toEqual([cardData]);
        expect(cardsConnection.nextToken).toEqual('fakeNextToken');
        expect(mockGetDebitCardAccountCards).toHaveBeenCalledWith(cardData.entityUuid, 10, undefined, {
          filterExp: ' AND #productType=:productType_eq',
          attrValues: { ':productType_eq': DebitCardProductType.EXPENSE },
          attrNames: { '#productType': 'productType' },
        });
      });

      it(`should be able to return filtered cards for ${CustomerRole.MANAGER} role by product type`, async () => {
        const cardData = createCardData();
        mockGetDebitCardAccountCards.mockImplementation(() => {
          return { cards: [cardData], nextToken: 'fakeNextToken' };
        });

        const customerUuid = v4();
        const cardsConnection = await debitCardAccountService.getDebitCardAccountCardsV2(
          cardData.entityUuid,
          10,
          undefined,
          {
            customerUuid,
            role: CustomerRole.MANAGER,
          },
          JSON.stringify({
            expression: '#productType = :productType_eq',
            expressionNames: { '#productType': 'productType' },
            expressionValues: {
              ':productType_eq': { S: DebitCardProductType.EXPENSE },
            },
          }),
        );
        expect(cardsConnection.cards).toEqual([cardData]);
        expect(cardsConnection.nextToken).toEqual('fakeNextToken');
        expect(mockGetDebitCardAccountCards).toHaveBeenCalledWith(cardData.entityUuid, 10, undefined, {
          filterExp: ' AND #customerUuid=:customerUuid AND #productType = :productType_eq',
          attrValues: { ':customerUuid': customerUuid, ':productType_eq': 'EXPENSE' },
          attrNames: { '#customerUuid': 'customerUuid', '#productType': 'productType' },
        });
      });
    });

    it('Should be able to return the cards details by Account ID', async () => {
      const cardData = createCardData();
      mockGetDebitCardAccountCards.mockImplementation(() => {
        return { cards: [cardData], nextToken: 'fakeNextToken' };
      });
      const cards = await debitCardAccountService.getDebitCardsByAccountId(
        cardData.entityUuid,
        cardData.debitCardAccountUuid,
      );
      expect(cards).toEqual([cardData]);
    });

    it('Should throw error if card is not found', async () => {
      mockGetDebitCardAccountCard.mockImplementation(() => {
        return null;
      });
      const cardId = v4();
      await expect(debitCardAccountService.getDebitCardAccountCardV2(v4(), cardId)).rejects.toThrow(
        `Debit Card Account Card not found ${cardId}`,
      );
    });

    it('should call the debit card account card service for the card materialisation', async () => {
      expect(mockMaterialiseDebitCardAccountCard).toHaveBeenCalledTimes(0);
      const data: ProjectionDto<any> = {
        aggregateId: v4(),
        uri: v4(),
        version: 1,
        createdTimestamp: v4(),
        payload: {
          id: v4(),
        },
      };
      await debitCardAccountService.materialiseDebitCardAccountCard(data);
      expect(mockMaterialiseDebitCardAccountCard).toHaveBeenCalledWith(data);
    });

    it('should call the debit card account card service for the card update', async () => {
      expect(mockUpdateDebitCardAccountCard).toHaveBeenCalledTimes(0);
      const cardData: DebitCardAccountCard = {
        id: v4(),
        entityUuid: v4(),
        debitCardAccountUuid: v4(),
        name: v4(),
        status: DebitCardStatus.CLOSED,
        colour: DebitCardColour.GREEN,
        type: DebitCardType.MASTERCARD_DEBIT,
        updatedTime: Date.now(),
        maskedPan: '0000',
      };
      await debitCardAccountService.updateDebitCardAccountCard(cardData);
      expect(mockUpdateDebitCardAccountCard).toHaveBeenCalledWith(cardData);
    });
  });

  describe('Debit Card Account Transactions for rich data', () => {
    const transaction = {
      id: '140e6d3cc-30e2-4825-83a8-8a6236c7183e',
      type: 'dca.transaction.*************',
      status: 'APPROVED',
      timestamp: '2021-11-23T22:01:41.000Z',
      debitCardAccountUuid: 'dd6df363-2bb6-48c0-956e-de4045f12a4f',
      merchantName: 'Bunnings Warehouse - Port Melbourne',
      debitCardName: 'My Debit Card',
      transactionType: 'PURCHASE',
      reference: '10b0d456-f153-4722-88e2-8bd2ea38d177',
      balance: {
        value: '35000',
        currency: 'AUD',
      },
      debitCardId: 'dd6df363-2bb6-48c0-956e-de4045f12a4f-1',
      accountingCategory: 'OFFICE_EQUIPMENT',
      amount: {
        value: '1200',
        currency: 'AUD',
      },
      updatedTime: '*************',
      debitCardAccountName: 'A Better name than 1234',
      entityUuid: 'd9a24091-ef25-4431-8dc6-f8f1fea75bb9',
      merchantId: '5dbc67c2-b489-4834-914a-7a20eb09536c',
      merchantIcon: {
        colour: 'G',
        letter: 'B',
      },
    };

    it('should be able to get Debit Card Account transaction based on uuid', async () => {
      mockGetDebitCardAccountTxn.mockImplementationOnce(() => {
        return {
          id: 'debitCardTransactionUuid',
        };
      });
      const txn = await debitCardAccountService.getDebitCardAccountTransactionV2(
        'entityUuid',
        'debitCardTransactionUuid',
      );
      expect(txn.id).toBe('debitCardTransactionUuid');
    });

    it('should throw error if Debit Card Account transaction uuid not found', async () => {
      mockGetDebitCardAccountTxn.mockImplementationOnce(() => {
        return null;
      });
      await expect(
        debitCardAccountService.getDebitCardAccountTransactionV2('entityUuid', 'debitCardTransactionUuid'),
      ).rejects.toThrow(Error);
    });

    it('Should be able to handle the mapping of the transaction', async () => {
      mockGetDebitCardAccountTxn.mockImplementationOnce(() => {
        return convertDbItemToDebitCardTransaction(transaction);
      });

      const mockRichDataService = new MerchantService({} as any, {} as any);
      (mockRichDataService.getMerchantDetails as jest.Mock) = jest.fn().mockImplementationOnce({} as any);

      const txn = await debitCardAccountService.getDebitCardAccountTransactionV2(
        'entityUuid',
        'debitCardTransactionUuid',
      );
      expect(txn.id).toBe(transaction.id);
      expect(txn.type).toBe(transaction.transactionType);
    });

    it('Should be able to handle debit card account transactions based on uuid', async () => {
      mockGetDebitCardAccountTxns.mockImplementationOnce(() => {
        return {
          transactions: [convertDbItemToDebitCardTransaction(transaction)],
        };
      });
      const txn = await debitCardAccountService.getDebitCardAccountTransactionsV2(
        { limit: 100 },
        'debitCardTransactionUuid',
      );
      expect(txn.transactions.length).toBe(1);
      expect(txn.transactions[0]!.id).toBe(transaction.id);
      expect(txn.transactions[0]!.type).toBe(transaction.transactionType);
    });

    it('Should be able to handle debit card account transactions and having nexttoken', async () => {
      mockGetDebitCardAccountTxns.mockImplementationOnce(() => {
        return {
          transactions: [convertDbItemToDebitCardTransaction(transaction)],
          nextToken: {},
        };
      });
      const txn = await debitCardAccountService.getDebitCardAccountTransactionsV2(
        { limit: 100 },
        'debitCardTransactionUuid',
      );
      expect(txn.transactions.length).toBe(1);
      expect(txn.transactions[0]!.id).toBe(transaction.id);
      expect(txn.transactions[0]!.type).toBe(transaction.transactionType);
      expect(txn.nextToken).toStrictEqual({});
    });

    it('Should be able to handle no transactions for the debit card account', async () => {
      mockGetDebitCardAccountTxns.mockImplementationOnce(() => {
        return {
          transactions: [],
        };
      });
      const txn = await debitCardAccountService.getDebitCardAccountTransactionsV2(
        { limit: 100 },
        'debitCardTransactionUuid',
      );
      expect(txn.transactions.length).toBe(0);
    });

    it('Call the debit card account txn Service to materialise the debit card txn', async () => {
      expect(mockMaterialiseTransaction).toHaveBeenCalledTimes(0);
      const data: ProjectionDto<any> = {
        aggregateId: v4(),
        uri: v4(),
        version: 1,
        createdTimestamp: v4(),
        payload: {
          id: v4(),
        },
      };
      await debitCardAccountService.materialiseTransaction(data);
      expect(mockMaterialiseTransaction).toHaveBeenCalledWith(data);
    });
  });

  describe('get issuing account test suite', () => {
    it('should be able to get debit card accounts with empty array savings account', async () => {
      const accountsData = [createAccountData(), createAccountData(), createAccountData()];
      const lastEvaluatedKey = {
        entityUuid: v4(),
        id: v4(),
        type: v4(),
      };
      mockGetSavingsAccountsV2.mockResolvedValueOnce({
        Items: [],
        LastEvaluatedKey: undefined,
      });
      mockGetSavingsAccounts.mockResolvedValueOnce({
        Items: [],
        LastEvaluatedKey: undefined,
      });
      mockGetDebitCardAccounts.mockResolvedValueOnce({
        Items: accountsData,
        LastEvaluatedKey: lastEvaluatedKey,
      });
      const result = await debitCardAccountService.getIssuingAccounts('entityUuid', 5);
      expect(result.accounts.length).toBe(3);
      const expected = accountsData
        .map((c) => {
          const { type, updatedAt, ...rest } = c;
          return rest;
        })
        .map((c) => {
          const { debitCardAccountType, ...rest } = c;
          return { ...rest, type: debitCardAccountType };
        });
      expect(result.accounts).toStrictEqual(expected);
      expect(result.nextToken).toStrictEqual(lastEvaluatedKey);
    });

    it('should be able to get debit card accounts with savings account Items undefined', async () => {
      const accountsData = [createAccountData(), createAccountData(), createAccountData()];
      const lastEvaluatedKey = {
        entityUuid: v4(),
        id: v4(),
        type: v4(),
      };
      mockGetSavingsAccountsV2.mockResolvedValueOnce({
        Items: undefined,
        LastEvaluatedKey: undefined,
      });
      mockGetSavingsAccounts.mockResolvedValueOnce({
        Items: undefined,
        LastEvaluatedKey: undefined,
      });
      mockGetDebitCardAccounts.mockResolvedValueOnce({
        Items: accountsData,
        LastEvaluatedKey: lastEvaluatedKey,
      });
      const result = await debitCardAccountService.getIssuingAccounts('entityUuid', 5);
      expect(result.accounts.length).toBe(3);
      const expected = accountsData
        .map((c) => {
          const { type, updatedAt, ...rest } = c;
          return rest;
        })
        .map((c) => {
          const { debitCardAccountType, ...rest } = c;
          return { ...rest, type: debitCardAccountType };
        });
      expect(result.accounts).toStrictEqual(expected);
      expect(result.nextToken).toStrictEqual(lastEvaluatedKey);
    });

    it('should be able to get empty array if no debit cards accounts are found', async () => {
      mockGetSavingsAccountsV2.mockResolvedValueOnce({
        Items: [],
        LastEvaluatedKey: undefined,
      });
      mockGetSavingsAccounts.mockResolvedValueOnce({
        Items: [],
        LastEvaluatedKey: undefined,
      });
      mockGetDebitCardAccounts.mockResolvedValueOnce({
        Items: [],
        LastEvaluatedKey: undefined,
      });
      const result = await debitCardAccountService.getIssuingAccounts('entityUuid', 5);
      expect(result.accounts).toEqual([]);
    });

    it('should be able to get savings accounts in the first', async () => {
      const savingsAccountsData = [createSavingsAccountData()];
      const accountsData = [createAccountData(), createAccountData(), createAccountData()];
      const lastEvaluatedKey = {
        entityUuid: v4(),
        id: v4(),
        type: v4(),
      };
      mockGetSavingsAccountsV2.mockResolvedValueOnce({
        Items: [],
        LastEvaluatedKey: undefined,
      });
      mockGetSavingsAccounts.mockResolvedValueOnce({
        Items: savingsAccountsData,
        LastEvaluatedKey: lastEvaluatedKey,
      });
      mockGetDebitCardAccounts.mockResolvedValueOnce({
        Items: accountsData,
        LastEvaluatedKey: lastEvaluatedKey,
      });
      const result = await debitCardAccountService.getIssuingAccounts('entityUuid', 5);
      expect(result.accounts.length).toBe(4);
      const expectedSavings = savingsAccountsData
        .map((c) => {
          const { type, ...rest } = c;
          return rest;
        })
        .map((c) => {
          const { debitCardAccountType, ...rest } = c;
          return { ...rest, type: debitCardAccountType };
        });
      const expectedDebit = accountsData
        .map((c) => {
          const { type, updatedAt, ...rest } = c;
          return rest;
        })
        .map((c) => {
          const { debitCardAccountType, ...rest } = c;
          return { ...rest, type: debitCardAccountType };
        });
      const expected = [...expectedSavings, ...expectedDebit];
      expect(result.accounts).toStrictEqual(expected);
      expect(result.nextToken).toStrictEqual(lastEvaluatedKey);
    });

    it('should be able to get savings accounts V2 in the first', async () => {
      const savingsAccountsData = [createSavingsV2AccountData()];
      const accountsData = [createAccountData(), createAccountData(), createAccountData()];
      const lastEvaluatedKey = {
        entityUuid: v4(),
        id: v4(),
        type: v4(),
      };
      mockGetSavingsAccountsV2.mockResolvedValueOnce({
        Items: savingsAccountsData,
        LastEvaluatedKey: undefined,
      });
      mockGetSavingsAccounts.mockResolvedValueOnce({
        Items: [],
        LastEvaluatedKey: lastEvaluatedKey,
      });
      mockGetDebitCardAccounts.mockResolvedValueOnce({
        Items: accountsData,
        LastEvaluatedKey: lastEvaluatedKey,
      });
      const result = await debitCardAccountService.getIssuingAccounts('entityUuid', 5);
      expect(result.accounts.length).toBe(4);
      const expectedSavings = savingsAccountsData
        .map((c) => {
          const { ...rest } = c;
          return rest;
        })
        .map((c) => {
          const { ...rest } = c;
          return { ...rest, type: DebitCardAccountType.SAVINGS };
        });
      const expectedDebit = accountsData
        .map((c) => {
          const { type, updatedAt, ...rest } = c;
          return rest;
        })
        .map((c) => {
          const { debitCardAccountType, ...rest } = c;
          return { ...rest, type: debitCardAccountType };
        });
      const expected = [...expectedSavings, ...expectedDebit];
      expect(result.accounts).toStrictEqual(expected);
      expect(result.nextToken).toStrictEqual(lastEvaluatedKey);
    });

    it('should be able to get savings account v2', async () => {
      const savingsAccountsData = [createSavingsV2AccountData()];
      const accountsData = [createAccountData()];
      const lastEvaluatedKey = {
        entityUuid: v4(),
        id: v4(),
        type: v4(),
      };
      mockGetSavingsAccountV2.mockResolvedValueOnce({
        Items: savingsAccountsData,
        LastEvaluatedKey: undefined,
      });
      mockGetDebitCardAccount.mockResolvedValueOnce({
        Items: accountsData,
        LastEvaluatedKey: lastEvaluatedKey,
      });
      const result = await debitCardAccountService.getIssuingAccount('entityUuid', savingsAccountsData[0]!.id);

      const expectedSavings = savingsAccountsData
        .map((c) => {
          const { ...rest } = c;
          return rest;
        })
        .map((c) => {
          const { ...rest } = c;
          return { ...rest, type: DebitCardAccountType.SAVINGS };
        });
      expect(result).toStrictEqual(expectedSavings[0]);
    });

    it('should be able to return debit card account if savings account v2 is empty', async () => {
      const accountsData = [createAccountData()];
      const lastEvaluatedKey = {
        entityUuid: v4(),
        id: v4(),
        type: v4(),
      };
      mockGetSavingsAccountV2.mockResolvedValueOnce({
        Items: [],
        LastEvaluatedKey: undefined,
      });
      mockGetDebitCardAccount.mockResolvedValueOnce({
        Items: accountsData,
        LastEvaluatedKey: lastEvaluatedKey,
      });
      const result = await debitCardAccountService.getIssuingAccount('entityUuid', accountsData[0]!.id);

      const expectedDebit = accountsData
        .map((c) => {
          const { type, updatedAt, ...rest } = c;
          return rest;
        })
        .map((c) => {
          const { debitCardAccountType, ...rest } = c;
          return { ...rest, type: debitCardAccountType };
        });
      expect(result).toStrictEqual(expectedDebit[0]);
    });
  });

  describe('get debit card Account test suite', () => {
    it('should be able to get debit card accounts', async () => {
      const accountsData = [createAccountData(), createAccountData(), createAccountData()];
      const lastEvaluatedKey = {
        entityUuid: v4(),
        id: v4(),
        type: v4(),
      };
      mockGetDebitCardAccounts.mockResolvedValueOnce({
        Items: accountsData,
        LastEvaluatedKey: lastEvaluatedKey,
      });
      const result = await debitCardAccountService.getDebitCardAccounts('entityUuid', 5);
      expect(result.accounts.length).toBe(3);
      const expected = accountsData
        .map((c) => {
          const { type, updatedAt, ...rest } = c;
          return rest;
        })
        .map((c) => {
          const { debitCardAccountType, ...rest } = c;
          return { ...rest, type: debitCardAccountType };
        });
      expect(result.accounts).toStrictEqual(expected);
      expect(result.nextToken).toStrictEqual(lastEvaluatedKey);
    });

    it('should be able to get empty array if no debit cards accounts are found', async () => {
      mockGetDebitCardAccounts.mockResolvedValueOnce({
        Items: [],
        LastEvaluatedKey: undefined,
      });
      const result = await debitCardAccountService.getDebitCardAccounts('entityUuid', 5);
      expect(result.accounts).toEqual([]);
    });
  });

  describe('balance api test suite', () => {
    beforeEach(() => {
      mockGetDebitCardAccountBalanceAtTime.mockResolvedValue({
        value: 0,
        currency: ISO4217.AUD,
      });
      mockGetDebitCardAccountBalanceHistoryBetweenTime.mockResolvedValue([]);
    });

    describe('Single Account Balance test', () => {
      it('return 0 if the account history balance is not found', async () => {
        const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
          { startDate: '2023-01-01', endDate: '2023-01-05' },
          'Australia/Melbourne',
          v4(),
          v4(),
        );
        expect(result).toEqual([
          {
            date: '2023-01-01',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-02',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-03',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-04',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-05',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
        ]);
      });
      it('return previous balance if the account history balance is not found', async () => {
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValue({
          value: 100,
          currency: ISO4217.AUD,
        });
        const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
          { startDate: '2023-01-01', endDate: '2023-01-05' },
          'Australia/Melbourne',
          v4(),
          v4(),
        );
        expect(result).toEqual([
          {
            date: '2023-01-01',
            balance: {
              value: 100,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-02',
            balance: {
              value: 100,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-03',
            balance: {
              value: 100,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-04',
            balance: {
              value: 100,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-05',
            balance: {
              value: 100,
              currency: ISO4217.AUD,
            },
          },
        ]);
      });
      it('return the previous balance + history balance if the first record is not on the first day', async () => {
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValue({
          value: 100,
          currency: ISO4217.AUD,
        });
        mockGetDebitCardAccountBalanceHistoryBetweenTime.mockResolvedValue([
          {
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
            updatedTime: new Date('2023-01-02T00:01:00+11:00').getTime(),
            updatedAt: '2023-01-02T00:01:00+11:00',
          },
        ]);
        const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
          { startDate: '2023-01-01', endDate: '2023-01-05' },
          'Australia/Melbourne',
          v4(),
          v4(),
        );
        expect(result).toEqual([
          {
            date: '2023-01-01',
            balance: {
              value: 100,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-02',
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-03',
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-04',
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-05',
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
          },
        ]);
      });
      it('return the daily balance correctly', async () => {
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValue({
          value: 100,
          currency: ISO4217.AUD,
        });
        mockGetDebitCardAccountBalanceHistoryBetweenTime.mockResolvedValue([
          {
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
            updatedTime: new Date('2023-01-01T00:01:00+11:00').getTime(),
            updatedAt: '2023-01-01T00:01:00+11:00',
          },
        ]);
        const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
          { startDate: '2023-01-01', endDate: '2023-01-05' },
          'Australia/Melbourne',
          v4(),
          v4(),
        );
        expect(result).toEqual([
          {
            date: '2023-01-01',
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-02',
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-03',
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-04',
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-05',
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
          },
        ]);
      });
      it('return the latest result per day', async () => {
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValue({
          value: 100,
          currency: ISO4217.AUD,
        });
        mockGetDebitCardAccountBalanceHistoryBetweenTime.mockResolvedValue([
          {
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
            updatedTime: new Date('2023-01-02T23:00:00+11:00').getTime(),
            updatedAt: '2023-01-02T23:00:00+11:00',
          },
          {
            balance: {
              value: 80,
              currency: ISO4217.AUD,
            },
            updatedTime: new Date('2023-01-02T23:10:00+11:00').getTime(),
            updatedAt: '2023-01-02T23:10:00+11:00',
          },
          {
            balance: {
              value: 75,
              currency: ISO4217.AUD,
            },
            updatedTime: new Date('2023-01-04T20:10:00+11:00').getTime(),
            updatedAt: '2023-01-04T20:10:00+11:00',
          },
        ]);
        const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
          { startDate: '2023-01-01', endDate: '2023-01-05' },
          'Australia/Melbourne',
          v4(),
          v4(),
        );
        expect(result).toEqual([
          {
            date: '2023-01-01',
            balance: {
              value: 100,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-02',
            balance: {
              value: 80,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-03',
            balance: {
              value: 80,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-04',
            balance: {
              value: 75,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-05',
            balance: {
              value: 75,
              currency: ISO4217.AUD,
            },
          },
        ]);
      });
      describe('return correctly based on the timezone and daily light saving', () => {
        beforeEach(() => {
          jest.useFakeTimers({
            now: new Date('2024-01-01'),
          });
          mockGetDebitCardAccountBalanceAtTime.mockResolvedValue({
            value: 100,
            currency: ISO4217.AUD,
          });
          mockGetDebitCardAccountBalanceHistoryBetweenTime.mockResolvedValue([
            {
              balance: {
                value: 40,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-04-01T23:59:00+11:00').getTime(),
              updatedAt: '2023-04-01T23:59:00+11:00',
            },
            {
              balance: {
                value: 80,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-04-02T00:29:00+11:00').getTime(),
              updatedAt: '2023-04-02T00:29:00+11:00',
            },
            {
              balance: {
                value: 110,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-04-02T00:59:00+11:00').getTime(),
              updatedAt: '2023-04-02T00:59:00+11:00',
            },
            {
              balance: {
                value: 90,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-04-02T02:59:00+11:00').getTime(),
              updatedAt: '2023-04-02T02:59:00+11:00',
            },
            {
              balance: {
                value: 150,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-04-02T23:59:00+10:00').getTime(),
              updatedAt: '2023-04-02T23:59:00+10:00',
            },
            {
              balance: {
                value: 180,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-04-03T00:29:00+10:00').getTime(),
              updatedAt: '2023-04-03T00:29:00+10:00',
            },
            {
              balance: {
                value: 20,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-04-03T01:59:00+10:00').getTime(),
              updatedAt: '2023-04-03T01:59:00+10:00',
            },
          ]);
        });
        afterEach(jest.useRealTimers);
        it('can handle the timezone the day light saving changing in VIC/NSW/TAS', async () => {
          expect(
            await debitCardAccountService.getDebitCardAccountBalanceHistory(
              { startDate: '2023-04-01', endDate: '2023-04-04' },
              'Australia/Melbourne',
              v4(),
              v4(),
            ),
          ).toEqual([
            {
              date: '2023-04-01',
              balance: {
                value: 40,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-04-02',
              balance: {
                value: 150,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-04-03',
              balance: {
                value: 20,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-04-04',
              balance: {
                value: 20,
                currency: ISO4217.AUD,
              },
            },
          ]);
        });
        it('can handle the timezone in QLD', async () => {
          expect(
            await debitCardAccountService.getDebitCardAccountBalanceHistory(
              { startDate: '2023-04-01', endDate: '2023-04-04' },
              'Australia/Brisbane',
              v4(),
              v4(),
            ),
          ).toEqual([
            {
              date: '2023-04-01',
              balance: {
                value: 110,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-04-02',
              balance: {
                value: 150,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-04-03',
              balance: {
                value: 20,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-04-04',
              balance: {
                value: 20,
                currency: ISO4217.AUD,
              },
            },
          ]);
        });
        it('can handle the timezone the day light saving changing in SA', async () => {
          expect(
            await debitCardAccountService.getDebitCardAccountBalanceHistory(
              { startDate: '2023-04-01', endDate: '2023-04-04' },
              'Australia/Adelaide',
              v4(),
              v4(),
            ),
          ).toEqual([
            {
              date: '2023-04-01',
              balance: {
                value: 80,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-04-02',
              balance: {
                value: 180,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-04-03',
              balance: {
                value: 20,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-04-04',
              balance: {
                value: 20,
                currency: ISO4217.AUD,
              },
            },
          ]);
        });
        it('can handle the timezone in WA', async () => {
          expect(
            await debitCardAccountService.getDebitCardAccountBalanceHistory(
              { startDate: '2023-04-01', endDate: '2023-04-04' },
              'Australia/Perth',
              v4(),
              v4(),
            ),
          ).toEqual([
            {
              date: '2023-04-01',
              balance: {
                value: 90,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-04-02',
              balance: {
                value: 20,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-04-03',
              balance: {
                value: 20,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-04-04',
              balance: {
                value: 20,
                currency: ISO4217.AUD,
              },
            },
          ]);
        });
      });
    });

    describe('All Account Balance test', () => {
      beforeEach(() => {
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValue({
          value: 0,
          currency: ISO4217.AUD,
        });
        mockGetAllDebitCardAccountBalanceHistoryBetweenTime.mockResolvedValue([]);
      });
      it('return 0 if there is no EZTA account', async () => {
        mockGetSavingsAccountsV2.mockResolvedValue({
          Items: [],
        });
        mockGetEztaAndSavingsAccounts.mockResolvedValue({
          Items: [],
        });
        const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
          { startDate: '2023-01-01', endDate: '2023-01-05' },
          'Australia/Melbourne',
          v4(),
        );
        expect(result).toEqual([
          {
            date: '2023-01-01',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-02',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-03',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-04',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-05',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
        ]);
      });
      it('return 0 if there is no balance history', async () => {
        mockGetSavingsAccountsV2.mockResolvedValue({
          Items: [],
        });
        mockGetEztaAndSavingsAccounts.mockResolvedValue({
          Items: [
            {
              id: v4(),
            },
            {
              id: v4(),
            },
            {
              id: v4(),
            },
          ],
        });
        const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
          { startDate: '2023-01-01', endDate: '2023-01-05' },
          'Australia/Melbourne',
          v4(),
        );
        expect(result).toEqual([
          {
            date: '2023-01-01',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-02',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-03',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-04',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-05',
            balance: {
              value: 0,
              currency: ISO4217.AUD,
            },
          },
        ]);
      });

      it('can populate the previous balance', async () => {
        const accountIds = [v4(), v4(), v4()];
        mockGetSavingsAccountsV2.mockResolvedValue({
          Items: [],
        });
        mockGetEztaAndSavingsAccounts.mockResolvedValue({
          Items: [{ id: accountIds[0] }, { id: accountIds[1] }, { id: accountIds[2] }],
        });
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValueOnce({ value: 10, currency: ISO4217.AUD });
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValueOnce({ value: 20, currency: ISO4217.AUD });
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValueOnce({ value: 30, currency: ISO4217.AUD });
        const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
          { startDate: '2023-01-01', endDate: '2023-01-05' },
          'Australia/Melbourne',
          v4(),
        );
        expect(result).toEqual([
          {
            date: '2023-01-01',
            balance: {
              value: 60,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-02',
            balance: {
              value: 60,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-03',
            balance: {
              value: 60,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-04',
            balance: {
              value: 60,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-05',
            balance: {
              value: 60,
              currency: ISO4217.AUD,
            },
          },
        ]);
      });

      it('can populate the daily balance based on the balance history', async () => {
        const accountIds = [v4(), v4(), v4()];
        mockGetSavingsAccountsV2.mockResolvedValue({
          Items: [],
        });
        mockGetEztaAndSavingsAccounts.mockResolvedValue({
          Items: [{ id: accountIds[0] }, { id: accountIds[1] }, { id: accountIds[2] }],
        });
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValueOnce({ value: 10, currency: ISO4217.AUD });
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValueOnce({ value: 20, currency: ISO4217.AUD });
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValueOnce({ value: 30, currency: ISO4217.AUD });
        mockGetAllDebitCardAccountBalanceHistoryBetweenTime.mockResolvedValue([
          {
            id: accountIds[2],
            balance: {
              value: 10,
              currency: ISO4217.AUD,
            },
            updatedTime: new Date('2023-01-01T00:03:00+11:00').getTime(),
            updatedAt: '2023-01-01T00:03:00+11:00',
          },
          {
            id: accountIds[0],
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
            updatedTime: new Date('2023-01-02T00:01:00+11:00').getTime(),
            updatedAt: '2023-01-02T00:01:00+11:00',
          },
          {
            id: accountIds[1],
            balance: {
              value: 50,
              currency: ISO4217.AUD,
            },
            updatedTime: new Date('2023-01-04T00:01:00+11:00').getTime(),
            updatedAt: '2023-01-04T00:01:00+11:00',
          },
        ]);
        const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
          { startDate: '2023-01-01', endDate: '2023-01-05' },
          'Australia/Melbourne',
          v4(),
        );
        expect(result).toEqual([
          {
            date: '2023-01-01',
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-02',
            balance: {
              value: 70,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-03',
            balance: {
              value: 70,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-04',
            balance: {
              value: 100,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-05',
            balance: {
              value: 100,
              currency: ISO4217.AUD,
            },
          },
        ]);
      });

      it('can populate the daily balance based on the balance history with savings account v2', async () => {
        const accountIds = [v4(), v4(), v4()];
        mockGetSavingsAccountsV2.mockResolvedValue({
          Items: [{ id: accountIds[0] }],
        });
        mockGetEztaDebitCardAccounts.mockResolvedValue({
          Items: [{ id: accountIds[1] }, { id: accountIds[2] }],
        });
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValueOnce({ value: 10, currency: ISO4217.AUD });
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValueOnce({ value: 20, currency: ISO4217.AUD });
        mockGetDebitCardAccountBalanceAtTime.mockResolvedValueOnce({ value: 30, currency: ISO4217.AUD });

        mockGetAllDebitCardAccountBalanceHistoryBetweenTime.mockResolvedValue([
          {
            id: accountIds[2],
            balance: {
              value: 10,
              currency: ISO4217.AUD,
            },
            updatedTime: new Date('2023-01-01T00:03:00+11:00').getTime(),
            updatedAt: '2023-01-01T00:03:00+11:00',
          },
          {
            id: accountIds[0],
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
            updatedTime: new Date('2023-01-02T00:01:00+11:00').getTime(),
            updatedAt: '2023-01-02T00:01:00+11:00',
          },
          {
            id: accountIds[1],
            balance: {
              value: 50,
              currency: ISO4217.AUD,
            },
            updatedTime: new Date('2023-01-04T00:01:00+11:00').getTime(),
            updatedAt: '2023-01-04T00:01:00+11:00',
          },
        ]);
        const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
          { startDate: '2023-01-01', endDate: '2023-01-05' },
          'Australia/Melbourne',
          v4(),
        );
        expect(result).toEqual([
          {
            date: '2023-01-01',
            balance: {
              value: 40,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-02',
            balance: {
              value: 70,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-03',
            balance: {
              value: 70,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-04',
            balance: {
              value: 100,
              currency: ISO4217.AUD,
            },
          },
          {
            date: '2023-01-05',
            balance: {
              value: 100,
              currency: ISO4217.AUD,
            },
          },
        ]);
      });
      describe('can handle different timezone', () => {
        const accountIds = [v4(), v4(), v4()];
        beforeEach(() => {
          mockGetSavingsAccountsV2.mockResolvedValue({
            Items: [],
          });
          mockGetEztaAndSavingsAccounts.mockResolvedValue({
            Items: [{ id: accountIds[0] }, { id: accountIds[1] }, { id: accountIds[2] }],
          });
          mockGetDebitCardAccountBalanceAtTime.mockResolvedValueOnce({ value: 10, currency: ISO4217.AUD });
          mockGetDebitCardAccountBalanceAtTime.mockResolvedValueOnce({ value: 20, currency: ISO4217.AUD });
          mockGetDebitCardAccountBalanceAtTime.mockResolvedValueOnce({ value: 30, currency: ISO4217.AUD });
          mockGetAllDebitCardAccountBalanceHistoryBetweenTime.mockResolvedValue([
            {
              id: accountIds[2],
              balance: {
                value: 10,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-01-01T23:59:00+11:00').getTime(),
              updatedAt: '2023-01-01T23:59:00+11:00',
            },
            {
              id: accountIds[2],
              balance: {
                value: 20,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-01-02T00:29:00+11:00').getTime(),
              updatedAt: '2023-01-02T00:29:00+11:00',
            },
            {
              id: accountIds[2],
              balance: {
                value: 15,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-01-02T00:59:00+11:00').getTime(),
              updatedAt: '2023-01-02T00:59:00+11:00',
            },
            {
              id: accountIds[2],
              balance: {
                value: 80,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-01-02T02:59:00+11:00').getTime(),
              updatedAt: '2023-01-02T02:59:00+11:00',
            },
            {
              id: accountIds[0],
              balance: {
                value: 40,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-01-03T00:01:00+11:00').getTime(),
              updatedAt: '2023-01-03T00:01:00+11:00',
            },
            {
              id: accountIds[1],
              balance: {
                value: 50,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-01-03T20:00:00+11:00').getTime(),
              updatedAt: '2023-01-03T20:00:00+11:00',
            },
            {
              id: accountIds[1],
              balance: {
                value: 50,
                currency: ISO4217.AUD,
              },
              updatedTime: new Date('2023-01-04T00:01:00+11:00').getTime(),
              updatedAt: '2023-01-04T00:01:00+11:00',
            },
          ]);
        });

        it('Can return VIC/NSW/TAS time correctly', async () => {
          const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
            { startDate: '2023-01-01', endDate: '2023-01-05' },
            'Australia/Sydney',
            v4(),
          );
          expect(result).toEqual([
            {
              date: '2023-01-01',
              balance: {
                value: 40,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-02',
              balance: {
                value: 110,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-03',
              balance: {
                value: 170,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-04',
              balance: {
                value: 170,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-05',
              balance: {
                value: 170,
                currency: ISO4217.AUD,
              },
            },
          ]);
        });
        it('Can return QLD time correctly', async () => {
          const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
            { startDate: '2023-01-01', endDate: '2023-01-05' },
            'Australia/Brisbane',
            v4(),
          );
          expect(result).toEqual([
            {
              date: '2023-01-01',
              balance: {
                value: 45,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-02',
              balance: {
                value: 140,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-03',
              balance: {
                value: 170,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-04',
              balance: {
                value: 170,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-05',
              balance: {
                value: 170,
                currency: ISO4217.AUD,
              },
            },
          ]);
        });
        it('Can return SA time correctly', async () => {
          const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
            { startDate: '2023-01-01', endDate: '2023-01-05' },
            'Australia/Adelaide',
            v4(),
          );
          expect(result).toEqual([
            {
              date: '2023-01-01',
              balance: {
                value: 50,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-02',
              balance: {
                value: 140,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-03',
              balance: {
                value: 170,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-04',
              balance: {
                value: 170,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-05',
              balance: {
                value: 170,
                currency: ISO4217.AUD,
              },
            },
          ]);
        });
        it('Can return WA time correctly', async () => {
          const result = await debitCardAccountService.getDebitCardAccountBalanceHistory(
            { startDate: '2023-01-01', endDate: '2023-01-05' },
            'Australia/Perth',
            v4(),
          );
          expect(result).toEqual([
            {
              date: '2023-01-01',
              balance: {
                value: 110,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-02',
              balance: {
                value: 140,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-03',
              balance: {
                value: 170,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-04',
              balance: {
                value: 170,
                currency: ISO4217.AUD,
              },
            },
            {
              date: '2023-01-05',
              balance: {
                value: 170,
                currency: ISO4217.AUD,
              },
            },
          ]);
        });
      });
    });

    describe('Can handle Invalid Input', () => {
      afterEach(jest.useRealTimers);

      it('Can handle invalid start date', async () => {
        await expect(
          debitCardAccountService.getDebitCardAccountBalanceHistory(
            {
              startDate: '2023-01-00',
              endDate: '2023-02-01',
            },
            'Australia/Melbourne',
            v4(),
            v4(),
          ),
        ).rejects.toThrow('Invalid Date Range/Timezone input');
      });

      it('Can handle invalid end date', async () => {
        await expect(
          debitCardAccountService.getDebitCardAccountBalanceHistory(
            {
              startDate: '2023-01-01',
              endDate: '2023-01-32',
            },
            'Australia/Melbourne',
            v4(),
            v4(),
          ),
        ).rejects.toThrow('Invalid Date Range/Timezone input');
      });

      it('Can handle if start date is after end date', async () => {
        await expect(
          debitCardAccountService.getDebitCardAccountBalanceHistory(
            {
              startDate: '2023-02-01',
              endDate: '2023-01-31',
            },
            'Australia/Melbourne',
            v4(),
            v4(),
          ),
        ).rejects.toThrow('Invalid Date Range/Timezone input');
      });

      it('Can handle if the timezone is invalid', async () => {
        await expect(
          debitCardAccountService.getDebitCardAccountBalanceHistory(
            {
              startDate: '2023-01-01',
              endDate: '2023-01-31',
            },
            'Australia/Doncaster',
            v4(),
            v4(),
          ),
        ).rejects.toThrow('Invalid Date Range/Timezone input');
      });

      it('return error if the range is 3 full months or more', async () => {
        await expect(
          debitCardAccountService.getDebitCardAccountBalanceHistory(
            {
              startDate: '2023-01-01',
              endDate: '2023-04-01',
            },
            'Australia/Melbourne',
            v4(),
            v4(),
          ),
        ).rejects.toThrow('Max date range is 3 months');
      });

      it('should resolve leap year', async () => {
        await expect(
          debitCardAccountService.getDebitCardAccountBalanceHistory(
            {
              startDate: '2023-11-29',
              endDate: '2024-02-28',
            },
            'Australia/Melbourne',
            v4(),
            v4(),
          ),
        ).resolves.not.toThrow('Max date range is 3 months');
      });

      describe('show not return error if the range within 3 full months even date time saving is cancelled', () => {
        it('Melbourne', async () => {
          await expect(
            debitCardAccountService.getDebitCardAccountBalanceHistory(
              {
                startDate: '2023-01-05',
                endDate: '2023-04-04',
              },
              'Australia/Melbourne',
              v4(),
              v4(),
            ),
          ).resolves.not.toThrow('Max date range is 3 months');
        });

        it('Paraguay', async () => {
          await expect(
            debitCardAccountService.getDebitCardAccountBalanceHistory(
              {
                startDate: '2022-12-28',
                endDate: '2023-03-27',
              },
              'America/Asuncion',
              v4(),
              v4(),
            ),
          ).resolves.not.toThrow('Max date range is 3 months');
        });

        it('London', async () => {
          await expect(
            debitCardAccountService.getDebitCardAccountBalanceHistory(
              {
                startDate: '2022-08-01',
                endDate: '2022-10-31',
              },
              'Europe/London',
              v4(),
              v4(),
            ),
          ).resolves.not.toThrow('Max date range is 3 months');
        });
      });

      it('return error if the end date is 3 days from now or more', async () => {
        jest.useFakeTimers({
          now: new Date('2023-01-01T12:00:00+11:00'),
        });

        await expect(
          debitCardAccountService.getDebitCardAccountBalanceHistory(
            {
              startDate: '2023-01-01',
              endDate: '2023-01-04',
            },
            'Australia/Melbourne',
            v4(),
            v4(),
          ),
        ).rejects.toThrow('Future date is not allowed');

        expect(
          await debitCardAccountService.getDebitCardAccountBalanceHistory(
            {
              startDate: '2023-01-01',
              endDate: '2023-01-03',
            },
            'Australia/Melbourne',
            v4(),
            v4(),
          ),
        ).toEqual([
          {
            date: '2023-01-01',
            balance: {
              value: 0,
              currency: 'AUD',
            },
          },
          {
            date: '2023-01-02',
            balance: {
              value: 0,
              currency: 'AUD',
            },
          },
          {
            date: '2023-01-03',
            balance: {
              value: 0,
              currency: 'AUD',
            },
          },
        ]);
      });
    });
  });
});
