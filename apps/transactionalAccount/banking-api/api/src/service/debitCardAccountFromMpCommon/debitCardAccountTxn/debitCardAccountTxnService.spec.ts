import { BffDynamoDbClient } from '@npco/component-bff-core/dist/dynamodb';
import { ContactDcaTransactionSenderLinkEventDto } from '@npco/component-dto-addressbook/dist';
import { DbRecordType } from '@npco/component-dto-core/dist';
import { DebitCardTransactionStatusV2, DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction/dist';

import { instance, mock, when } from 'ts-mockito';
import { v4 } from 'uuid';

import { BankingDynamoDbService } from '../../dynamodb/bankingDynamoDbService';
import type { EnvironmentService } from '../../environment/environmentService';
import { MerchantService as RichDataService } from '../../richData/merchantService';

import { DebitCardAccountTxnDb } from './debitCardAccountTxnDb';
import { DebitCardAccountTxnService } from './debitCardAccountTxnService';
import {
  convertDbItemToDebitCardTransaction,
  adjustmentTransaction,
  senderTransactionTypes,
  paymentInstrumentTransactionTypes,
  merchantTransactionTypes,
} from './debitCardTransactionUtils';

const id = v4();
const txnData = {
  payload: {
    id,
    status: 'DECLINED',
    debitCardAccountUuid: v4(),
    debitCardName: 'My Debit Card',
    type: 'PURCHASE',
    reference: 'REFERENCE',
    description: 'DESCRIPTION',
    debitCardId: v4(),
    expenseCategory: 'OFFICE_EQUIPMENT',
    amount: {
      value: '1200',
      currency: 'AUD',
    },
    debitCardAccountName: 'A Better name than 1234',
    updatedTime: '*************',
    entityUuid: v4(),
    merchant: {
      id: v4(),
      name: 'Bunnings Warehouse - Port Melbourne',
      icon: {
        colour: 'G',
        letter: 'B',
      },
      updatedTime: '*********',
    },
    timestamp: '*************',
  },
  uri: 'uri',
  version: 1,
  createdTimestamp: '*************',
  aggregateId: id,
};

const incomingExternalTransaction = [
  DebitCardTransactionTypeV2.NPP_IN,
  DebitCardTransactionTypeV2.ATM_IN,
  DebitCardTransactionTypeV2.BPAY_IN,
  DebitCardTransactionTypeV2.DE_IN,
];

const incomingReversalTransaction = [
  DebitCardTransactionTypeV2.NPP_OUT_RETURN,
  DebitCardTransactionTypeV2.DE_OUT_RETURN,
];

const outgoingReversalTransaction = [DebitCardTransactionTypeV2.NPP_IN_RETURN];

const reversalTransaction = [...incomingReversalTransaction, ...outgoingReversalTransaction];

const mockGetMerchantDetails = jest.fn();
const mockSaveMerchant = jest.fn();
jest.mock('@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient');

jest.mock('../../richData/merchantService', () => {
  return {
    MerchantService: jest.fn().mockImplementation(() => {
      return {
        getMerchantDetails: mockGetMerchantDetails,
        saveMerchant: mockSaveMerchant,
        getMerchant: jest.fn(),
      };
    }),
  };
});

const mockDb = mock(DebitCardAccountTxnDb);

describe('DebitCardAccountTxnService test suite', () => {
  let dbService: BankingDynamoDbService;
  let service: DebitCardAccountTxnService;
  let richDataService: RichDataService;
  const envService = {
    componentTableName: 'Entities',
    materialiseIncomingExternalNonApprovedTransaction: false,
  } as EnvironmentService;

  const documentClient = new BffDynamoDbClient(envService);

  beforeAll(async () => {
    dbService = new BankingDynamoDbService(envService, documentClient);
    richDataService = new RichDataService(envService, dbService);
  });

  beforeEach(() => {
    service = new DebitCardAccountTxnService(envService, dbService, richDataService);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getDebitCardAccountTxn test suite', () => {
    afterEach(() => {
      jest.restoreAllMocks();
    });

    describe('should be able to save and get debit card account transaction', () => {
      describe('Can perform materialise operation correctly', () => {
        beforeAll(() => {
          jest.useFakeTimers();
          jest.setSystemTime(new Date('2020-01-01T00:00:00.000Z'));
        });

        beforeEach(jest.resetAllMocks);

        afterAll(() => {
          jest.useRealTimers();
        });

        it('save the merchant if it is available', async () => {
          const spy = jest.spyOn(richDataService, 'saveMerchant');
          expect(spy).not.toHaveBeenCalled();
          await service.materialiseTransaction(txnData);
          expect(spy).toBeCalledTimes(1);
        });

        it('do not call the richData Service if the merchant is not available', async () => {
          const newId = v4();
          const dataWithNoMerchant = {
            ...txnData,
            payload: {
              ...txnData.payload,
              id: newId,
              merchant: undefined,
            },
            aggregateId: newId,
          };
          let result = await service.getDebitCardAccountTxn(dataWithNoMerchant.payload.entityUuid, newId);
          expect(result).toBeNull();
          const spy = jest.spyOn(richDataService, 'saveMerchant');
          expect(spy).not.toHaveBeenCalled();
          await service.materialiseTransaction(dataWithNoMerchant);
          expect(spy).not.toHaveBeenCalled();
          result = await service.getDebitCardAccountTxn(dataWithNoMerchant.payload.entityUuid, newId);
          expect(result).not.toBeNull();
        });
      });

      it('save with the correct materialise view', async () => {
        const dbRecordType = `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}${txnData.payload.timestamp}`;
        const result = (
          await dbService.get({
            TableName: envService.componentTableName,
            Key: {
              id: txnData.payload.id,
              type: dbRecordType,
            },
          })
        ).Item;

        const expectedRecord = {
          ...txnData.payload,
          merchantId: txnData.payload.merchant.id,
          type: dbRecordType,
          transactionType: txnData.payload.type,
          secondaryPk: txnData.payload.merchant.id,
          sortKey: `${DbRecordType.ISSUING_TRANSACTION}${txnData.payload.timestamp}`,
          amount: {
            value: Number(txnData.payload.amount.value),
            currency: txnData.payload.amount.currency,
          },
          referenceLowerCase: txnData.payload.reference.toLowerCase(),
          descriptionLowerCase: txnData.payload.description.toLowerCase(),
          timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
          updatedAt: new Date(Number(txnData.payload.updatedTime)).toISOString(),
          lastUpdatedAt: '2020-01-01T00:00:00.000Z',
          issuingAccountUuid: txnData.payload.debitCardAccountUuid,
        };
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        delete expectedRecord.merchant;
        expect(result).toEqual(expectedRecord);
      });

      it('get the correct transaction dto', async () => {
        const result = await service.getDebitCardAccountTxn(txnData.payload.entityUuid, txnData.payload.id);
        const expectedTransaction = {
          ...txnData.payload,
          secondaryPk: txnData.payload.merchant.id,
          sortKey: `${DbRecordType.ISSUING_TRANSACTION}${txnData.payload.timestamp}`,
          merchantId: txnData.payload.merchant.id,
          timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
          issuingAccountUuid: txnData.payload.debitCardAccountUuid,
        };
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        delete expectedTransaction.merchant;
        expect(result).toEqual(expectedTransaction);
      });

      it('should not overwrite the record if the updatedTime is not larger then the current one', async () => {
        const txnData2 = {
          ...txnData,
          payload: {
            ...txnData.payload,
            debitCardName: v4(),
            // Remove if after reply completed
            updatedTime: '*************',
          },
        };

        await service.materialiseTransaction(txnData2);
        const result = await service.getDebitCardAccountTxn(txnData2.payload.entityUuid, txnData2.payload.id);
        const expectedTransaction = {
          ...txnData.payload,
          secondaryPk: txnData.payload.merchant.id,
          sortKey: `${DbRecordType.ISSUING_TRANSACTION}${txnData.payload.timestamp}`,
          merchantId: txnData.payload.merchant.id,
          timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
          issuingAccountUuid: txnData.payload.debitCardAccountUuid,
        };
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        delete expectedTransaction.merchant;
        expect(result).toEqual(expectedTransaction);
      });

      it('should be able to materialise transaction data with existing information where the updatedTime has increased', async () => {
        const updates = {
          ...txnData,
          payload: {
            ...txnData.payload,
            debitCardName: v4(),
            updatedTime: '*************',
          },
        };
        await service.materialiseTransaction(updates);
        const result = await service.getDebitCardAccountTxn(updates.payload.entityUuid, updates.payload.id);
        const expectedTransaction = {
          ...updates.payload,
          secondaryPk: updates.payload.merchant.id,
          sortKey: `${DbRecordType.ISSUING_TRANSACTION}${txnData.payload.timestamp}`,
          merchantId: updates.payload.merchant.id,
          timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
          issuingAccountUuid: txnData.payload.debitCardAccountUuid,
        };
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        delete expectedTransaction.merchant;
        expect(result).toEqual(expectedTransaction);
      });

      describe('Materialise Non Approval External Incoming Transaction flag test suite', () => {
        const incomingTxnData = {
          payload: {
            debitCardAccountUuid: v4(),
            debitCardName: 'My Debit Card',
            reference: 'REFERENCE',
            description: 'DESCRIPTION',
            amount: {
              value: '1200',
              currency: 'AUD',
            },
            debitCardAccountName: 'A Better name than 1234',
            updatedTime: '*************',
            entityUuid: v4(),
            timestamp: '*************',
          },
          uri: 'uri',
          version: 1,
          createdTimestamp: '*************',
        };

        describe('The flag set to false', () => {
          describe.each(
            Object.keys(DebitCardTransactionStatusV2).filter(
              (status) => status !== DebitCardTransactionStatusV2.APPROVED,
            ),
          )('For Transaction Status: %s', (status) => {
            const nonMaterialiseTransactions = [...incomingExternalTransaction, ...adjustmentTransaction];
            const nonGeneralTransactions = [...nonMaterialiseTransactions, ...reversalTransaction];
            it.each(nonMaterialiseTransactions)('Transaction type: %s should not be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status,
                  type,
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);
              expect(result).toBeNull();
            });

            it.each(
              Object.keys(DebitCardTransactionTypeV2).filter(
                (type) => !nonGeneralTransactions.includes(type as DebitCardTransactionTypeV2),
              ),
            )('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status,
                  type,
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);
              expect(result).toStrictEqual({
                ...eventData.payload,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              });
            });

            it.each(incomingReversalTransaction)('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status,
                  type,
                  reference: v4(),
                  payeeDetails: {},
                  payerDetails: {
                    accountDetails: {
                      name: v4(),
                    },
                  },
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);
              expect(result).toStrictEqual({
                ...eventData.payload,
                reference: `Return - ${eventData.payload.reference}`,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              });
            });

            it.each(outgoingReversalTransaction)('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status,
                  type,
                  description: v4(),
                  payeeDetails: {
                    accountDetails: {
                      name: v4(),
                    },
                  },
                  payerDetails: {},
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);

              const expectedResult = {
                ...eventData.payload,
                description: `Return - ${eventData.payload.description}`,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              };
              expect(result).toStrictEqual(expectedResult);
            });
          });

          describe('For Transaction Status: APPROVED', () => {
            const nonGeneralTransactions = [...adjustmentTransaction, ...reversalTransaction];
            it.each(
              Object.keys(DebitCardTransactionTypeV2).filter(
                (type) => !nonGeneralTransactions.includes(type as DebitCardTransactionTypeV2),
              ),
            )('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status: DebitCardTransactionStatusV2.APPROVED,
                  type,
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);
              expect(result).toStrictEqual({
                ...eventData.payload,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              });
            });

            it.each(incomingReversalTransaction)('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status: DebitCardTransactionStatusV2.APPROVED,
                  type,
                  reference: v4(),
                  payeeDetails: {},
                  payerDetails: {
                    accountDetails: {
                      name: v4(),
                    },
                  },
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);
              expect(result).toStrictEqual({
                ...eventData.payload,
                reference: `Return - ${eventData.payload.reference}`,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              });
            });

            it.each(outgoingReversalTransaction)('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status: DebitCardTransactionStatusV2.APPROVED,
                  type,
                  description: v4(),
                  payeeDetails: {
                    accountDetails: {
                      name: v4(),
                    },
                  },
                  payerDetails: {},
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);

              const expectedResult = {
                ...eventData.payload,
                description: `Return - ${eventData.payload.description}`,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              };
              expect(result).toStrictEqual(expectedResult);
            });

            it.each(adjustmentTransaction)('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status: DebitCardTransactionStatusV2.APPROVED,
                  type,
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);
              expect(result).toStrictEqual({
                ...eventData.payload,
                reference: undefined,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              });
            });
          });
        });
        describe('The flag set to true', () => {
          beforeEach(() => {
            service = new DebitCardAccountTxnService(
              { ...envService, materialiseIncomingExternalNonApprovedTransaction: true } as EnvironmentService,
              dbService,
              richDataService,
            );
          });
          describe.each(
            Object.keys(DebitCardTransactionStatusV2).filter(
              (status) => status !== DebitCardTransactionStatusV2.APPROVED,
            ),
          )('For Transaction Status: %s', (status) => {
            const nonGeneralTransactions = [...adjustmentTransaction, ...reversalTransaction];
            it.each(adjustmentTransaction)('Transaction type: %s should not be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status,
                  type,
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);
              expect(result).toBeNull();
            });

            it.each(
              Object.keys(DebitCardTransactionTypeV2).filter(
                (type) => !nonGeneralTransactions.includes(type as DebitCardTransactionTypeV2),
              ),
            )('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status,
                  type,
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);
              expect(result).toStrictEqual({
                ...eventData.payload,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              });
            });

            it.each(incomingReversalTransaction)('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status,
                  type,
                  reference: v4(),
                  payeeDetails: {},
                  payerDetails: {
                    accountDetails: {
                      name: v4(),
                    },
                  },
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);
              expect(result).toStrictEqual({
                ...eventData.payload,
                reference: `Return - ${eventData.payload.reference}`,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              });
            });

            it.each(outgoingReversalTransaction)('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status,
                  type,
                  description: v4(),
                  payeeDetails: {
                    accountDetails: {
                      name: v4(),
                    },
                  },
                  payerDetails: {},
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);

              const expectedResult = {
                ...eventData.payload,
                description: `Return - ${eventData.payload.description}`,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              };
              expect(result).toStrictEqual(expectedResult);
            });
          });

          describe('For Transaction Status: APPROVED', () => {
            const nonGeneralTransactions = [...adjustmentTransaction, ...reversalTransaction];
            it.each(adjustmentTransaction)('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status: DebitCardTransactionStatusV2.APPROVED,
                  type,
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);
              expect(result).toStrictEqual({
                ...eventData.payload,
                reference: undefined,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              });
            });

            it.each(
              Object.keys(DebitCardTransactionTypeV2).filter(
                (type) => !nonGeneralTransactions.includes(type as DebitCardTransactionTypeV2),
              ),
            )('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status: DebitCardTransactionStatusV2.APPROVED,
                  type,
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);
              expect(result).toStrictEqual({
                ...eventData.payload,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              });
            });

            it.each(incomingReversalTransaction)('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status: DebitCardTransactionStatusV2.APPROVED,
                  type,
                  reference: v4(),
                  payeeDetails: {},
                  payerDetails: {
                    accountDetails: {
                      name: v4(),
                    },
                  },
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);
              expect(result).toStrictEqual({
                ...eventData.payload,
                reference: `Return - ${eventData.payload.reference}`,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              });
            });

            it.each(outgoingReversalTransaction)('Transaction type: %s should be materialised', async (type) => {
              const transactionId = v4();
              const eventData = {
                ...incomingTxnData,
                aggregateId: transactionId,
                payload: {
                  ...incomingTxnData.payload,
                  id: transactionId,
                  status: DebitCardTransactionStatusV2.APPROVED,
                  type,
                  description: v4(),
                  payeeDetails: {
                    accountDetails: {
                      name: v4(),
                    },
                  },
                  payerDetails: {},
                },
              };
              await service.materialiseTransaction(eventData);
              const result = await service.getDebitCardAccountTxn(eventData.payload.entityUuid, eventData.payload.id);

              const expectedResult = {
                ...eventData.payload,
                description: `Return - ${eventData.payload.description}`,
                timestamp: new Date(Number(txnData.payload.timestamp)).toISOString(),
                issuingAccountUuid: eventData.payload.debitCardAccountUuid,
              };
              expect(result).toStrictEqual(expectedResult);
            });
          });
        });
      });

      describe('Materialise dca transaction with default category test suite', () => {
        it.each(senderTransactionTypes)(
          'should be able to resolve category when saving external (sender) related transaction of type %s',
          async (transactionType) => {
            const data = {
              ...txnData,
              payload: {
                ...txnData.payload,
                id: v4(),
                entityUuid: v4(),
                status: DebitCardTransactionStatusV2.APPROVED,
                type: transactionType,
                payerDetails: {
                  debitCardAccountUuid: v4(),
                  senderUuid: v4(),
                },
                payeeDetails: {
                  senderUuid: v4(),
                },
              },
            };

            const expectedSenderUuid =
              transactionType === DebitCardTransactionTypeV2.DDA_OUT
                ? data.payload.payeeDetails.senderUuid
                : data.payload.payerDetails.senderUuid;

            const {
              payload: { id: dcaTransactionUuid, entityUuid, timestamp },
            } = data;

            const contactUuid = v4();

            // created contact
            await dbService.put({
              TableName: envService.componentTableName,
              Item: {
                id: contactUuid,
                type: 'contact.core',
                entityUuid,
                contactUuid,
                category: 'ADVERTISING',
                subcategoryUuid: 'PROFESSIONAL_SERVICES',
              },
            });

            // created contact sender link
            await dbService.put({
              TableName: envService.componentTableName,
              Item: {
                id: `${DbRecordType.DCA_TRANSACTION_SENDER_LINK}${expectedSenderUuid}`,
                type: `${DbRecordType.CONTACT_LINK}${contactUuid}`,
                entityUuid,
                contactUuid,
                senderUuid: expectedSenderUuid,
              },
            });

            await service.materialiseTransaction(data);
            const materializedDcaTransaction = await service.getDebitCardAccountTxn(entityUuid, dcaTransactionUuid);

            expect(materializedDcaTransaction?.secondaryPk).toEqual(expectedSenderUuid);
            expect(materializedDcaTransaction?.contactUuid).toEqual(contactUuid);
            expect(materializedDcaTransaction?.defaultCategory).toEqual('ADVERTISING');
            expect(materializedDcaTransaction?.defaultSubcategory).toEqual('PROFESSIONAL_SERVICES');
            expect(materializedDcaTransaction?.sortKey).toEqual(`${DbRecordType.ISSUING_TRANSACTION}${timestamp}`);
          },
        );

        it.each([
          ...paymentInstrumentTransactionTypes,
          DebitCardTransactionTypeV2.DE_OUT_RETURN,
          DebitCardTransactionTypeV2.NPP_OUT_RETURN,
        ])(
          'should be able to resolve category when saving payment instrument related transaction of type %s',
          async (transactionType) => {
            const data = {
              ...txnData,
              payload: {
                ...txnData.payload,
                id: v4(),
                entityUuid: v4(),
                status: DebitCardTransactionStatusV2.APPROVED,
                type: transactionType,
                payeeDetails: {
                  debitCardAccountUuid: v4(),
                  recipientUuid: v4(),
                },
              },
            };

            const {
              payload: {
                id: dcaTransactionUuid,
                entityUuid,
                timestamp,
                payeeDetails: { recipientUuid },
              },
            } = data;

            const contactUuid = v4();

            // create contact
            await dbService.put({
              TableName: envService.componentTableName,
              Item: {
                id: contactUuid,
                type: 'contact.core',
                entityUuid,
                contactUuid,
                category: 'INSURANCE',
                subcategoryUuid: 'PROFESSIONAL_SERVICES',
              },
            });

            // create contact payment instrument link
            await dbService.put({
              TableName: envService.componentTableName,
              Item: {
                id: recipientUuid,
                type: DbRecordType.PAYMENT_INSTRUMENT,
                entityUuid,
                contactUuid,
                paymentInstrumentUuid: recipientUuid,
              },
            });

            await service.materialiseTransaction(data);
            const materializedDcaTransaction = await service.getDebitCardAccountTxn(entityUuid, dcaTransactionUuid);

            expect(materializedDcaTransaction?.secondaryPk).toEqual(recipientUuid);
            expect(materializedDcaTransaction?.contactUuid).toEqual(contactUuid);
            expect(materializedDcaTransaction?.defaultCategory).toEqual('INSURANCE');
            expect(materializedDcaTransaction?.defaultSubcategory).toEqual('PROFESSIONAL_SERVICES');
            expect(materializedDcaTransaction?.sortKey).toEqual(`${DbRecordType.ISSUING_TRANSACTION}${timestamp}`);
          },
        );

        it.each(merchantTransactionTypes)(
          'should be able to resolve category when saving merchant related dca transaction of type %s',
          async (transactionType) => {
            const data = {
              ...txnData,
              payload: {
                ...txnData.payload,
                id: v4(),
                entityUuid: v4(),
                status: DebitCardTransactionStatusV2.APPROVED,
                type: transactionType,
                merchant: {
                  id: v4(),
                },
              },
            };

            const {
              payload: {
                id: debitCardTransactionUuid,
                entityUuid,
                timestamp,
                merchant: { id: merchantId },
              },
            } = data;

            const contactUuid = v4();

            // create contact
            await dbService.put({
              TableName: envService.componentTableName,
              Item: {
                id: contactUuid,
                type: 'contact.core',
                entityUuid,
                contactUuid,
                category: 'PURCHASES',
                subcategoryUuid: 'INSURANCE',
              },
            });

            // create merchant contact
            await dbService.put({
              TableName: envService.componentTableName,
              Item: {
                id: `merchant#${merchantId}`,
                type: `contact#${contactUuid}`,
                entityUuid,
                contactUuid,
                merchantUuid: merchantId,
              },
            });

            await service.materialiseTransaction(data);
            const materializedDcaTransaction = await service.getDebitCardAccountTxn(
              entityUuid,
              debitCardTransactionUuid,
            );

            expect(materializedDcaTransaction?.secondaryPk).toEqual(merchantId);
            expect(materializedDcaTransaction?.contactUuid).toEqual(contactUuid);
            expect(materializedDcaTransaction?.defaultCategory).toEqual('PURCHASES');
            expect(materializedDcaTransaction?.defaultSubcategory).toEqual('INSURANCE');
            expect(materializedDcaTransaction?.sortKey).toEqual(`${DbRecordType.ISSUING_TRANSACTION}${timestamp}`);
          },
        );

        it('should use merchant.locationOf to resolve category when saving merchant related dca transaction', async () => {
          const data = {
            ...txnData,
            payload: {
              ...txnData.payload,
              id: v4(),
              entityUuid: v4(),
              status: DebitCardTransactionStatusV2.APPROVED,
              type: DebitCardTransactionTypeV2.PURCHASE,
              merchant: {
                id: v4(),
                locationOf: v4(),
              },
            },
          };

          const {
            payload: {
              id: debitCardTransactionUuid,
              entityUuid,
              timestamp,
              merchant: { locationOf },
            },
          } = data;

          const contactUuid = v4();

          // create contact
          await dbService.put({
            TableName: envService.componentTableName,
            Item: {
              id: contactUuid,
              type: 'contact.core',
              entityUuid,
              contactUuid,
              category: 'PURCHASES',
              subcategoryUuid: 'INSURANCE',
            },
          });

          // create merchant contact
          await dbService.put({
            TableName: envService.componentTableName,
            Item: {
              id: `merchant#${locationOf}`,
              type: `contact#${contactUuid}`,
              entityUuid,
              contactUuid,
              merchantUuid: locationOf,
            },
          });

          await service.materialiseTransaction(data);
          const materializedDcaTransaction = await service.getDebitCardAccountTxn(entityUuid, debitCardTransactionUuid);

          expect(materializedDcaTransaction?.secondaryPk).toEqual(locationOf);
          expect(materializedDcaTransaction?.contactUuid).toEqual(contactUuid);
          expect(materializedDcaTransaction?.defaultCategory).toEqual('PURCHASES');
          expect(materializedDcaTransaction?.defaultSubcategory).toEqual('INSURANCE');
          expect(materializedDcaTransaction?.sortKey).toEqual(`${DbRecordType.ISSUING_TRANSACTION}${timestamp}`);
        });

        it('should use default transaction category if exists', async () => {
          const data = {
            ...txnData,
            payload: {
              ...txnData.payload,
              id: v4(),
              entityUuid: v4(),
              status: DebitCardTransactionStatusV2.APPROVED,
              type: DebitCardTransactionTypeV2.PURCHASE,
              category: 'mock-category',
              subcategory: 'mock-subcategory',
              merchant: {
                id: 'mock-merchant-id',
              },
            },
          };

          const {
            payload: {
              id: debitCardTransactionUuid,
              entityUuid,
              timestamp,
              merchant: { id: merchantId },
            },
          } = data;

          await service.materialiseTransaction(data);
          const materializedDcaTransaction = await service.getDebitCardAccountTxn(entityUuid, debitCardTransactionUuid);

          expect(materializedDcaTransaction?.secondaryPk).toEqual(merchantId);
          expect(materializedDcaTransaction?.defaultCategory).toEqual(undefined);
          expect(materializedDcaTransaction?.defaultSubcategory).toEqual(undefined);
          expect(materializedDcaTransaction?.sortKey).toEqual(`${DbRecordType.ISSUING_TRANSACTION}${timestamp}`);
        });

        it('should use default merchant transaction category if exists', async () => {
          const data = {
            ...txnData,
            payload: {
              ...txnData.payload,
              id: v4(),
              entityUuid: v4(),
              status: DebitCardTransactionStatusV2.APPROVED,
              type: DebitCardTransactionTypeV2.PURCHASE,
              merchant: {
                id: 'mocked-merchant-id',
                category: 'mock-category',
                subcategory: 'mock-subcategory',
              },
            },
          };

          const {
            payload: {
              id: debitCardTransactionUuid,
              entityUuid,
              timestamp,
              merchant: { id: merchantId, category: merchantCategory, subcategory: merchantSubcategory },
            },
          } = data;

          await service.materialiseTransaction(data);
          const materializedDcaTransaction = await service.getDebitCardAccountTxn(entityUuid, debitCardTransactionUuid);

          expect(materializedDcaTransaction?.secondaryPk).toEqual(merchantId);
          expect(materializedDcaTransaction?.defaultCategory).toEqual(merchantCategory);
          expect(materializedDcaTransaction?.defaultSubcategory).toEqual(merchantSubcategory);
          expect(materializedDcaTransaction?.sortKey).toEqual(`${DbRecordType.ISSUING_TRANSACTION}${timestamp}`);
        });
      });
    });

    it('should get null if transaction not found', async () => {
      const result = await service.getDebitCardAccountTxn('a', 'b');
      expect(result).toBe(null);
    });

    it('should be able to retry txns if failed', async () => {
      const originalDb = dbService.documentClient;
      dbService.documentClient = {
        put: () => ({
          promise: jest.fn().mockRejectedValue(new Error('failed')),
        }),
      } as any;
      try {
        await service.materialiseTransaction({
          payload: {
            transactionUuid: 'transactionUuid',
            timestamp: '0',
            amount: { value: '0', currency: 'AUD' },
            updatedTime: '1',
          },
        } as any);
      } catch (e: any) {
        expect(e.message).toEqual('Failed to materialise to db.');
      } finally {
        dbService.documentClient = originalDb;
      }
    });

    describe('can perform getDebitCardAccountTransactionsV2', () => {
      const transaction = {
        id: '140e6d3cc-30e2-4825-83a8-8a6236c7183e',
        type: 'dca.transaction.*************',
        status: 'APPROVED',
        timestamp: '2021-11-23T22:01:41.000Z',
        debitCardAccountUuid: 'dd6df363-2bb6-48c0-956e-de4045f12a4f',
        merchantName: 'Bunnings Warehouse - Port Melbourne',
        debitCardName: 'My Debit Card',
        transactionType: 'PURCHASE',
        reference: '10b0d456-f153-4722-88e2-8bd2ea38d177',
        balance: {
          value: '35000',
          currency: 'AUD',
        },
        debitCardId: 'dd6df363-2bb6-48c0-956e-de4045f12a4f-1',
        accountingCategory: 'OFFICE_EQUIPMENT',
        amount: {
          value: '1200',
          currency: 'AUD',
        },
        updatedTime: '*************',
        debitCardAccountName: 'A Better name than 1234',
        entityUuid: 'd9a24091-ef25-4431-8dc6-f8f1fea75bb9',
        merchantId: '5dbc67c2-b489-4834-914a-7a20eb09536c',
        merchantIcon: {
          colour: 'G',
          letter: 'B',
        },
      };

      beforeEach(() => {
        service.debitCardAccountTxnDb = instance(mockDb);
      });

      it('Should be able to handle no transactions for the debit card account', async () => {
        when(mockDb.getDebitCardAccountTxns).thenReturn(() => Promise.resolve({ Items: [] }));
        const result = await service.getDebitCardAccountTransactionsV2({ limit: 100 }, 'b');
        expect(result).toEqual({
          transactions: [],
        });
      });

      it('Should be able to handle the mapping of the transactions', async () => {
        when(mockDb.getDebitCardAccountTxns).thenReturn(() => Promise.resolve({ Items: [transaction] } as any));
        const result = await service.getDebitCardAccountTransactionsV2({ limit: 100 }, 'b');
        expect(result).toEqual({
          transactions: [convertDbItemToDebitCardTransaction(transaction)],
        });
      });

      it('Should be able to handle debit card account transactions and having next token', async () => {
        when(mockDb.getDebitCardAccountTxns).thenReturn(() =>
          Promise.resolve({
            Items: [transaction],
            LastEvaluatedKey: {},
          } as any),
        );
        const result = await service.getDebitCardAccountTransactionsV2({ limit: 100 }, 'b');
        expect(result).toEqual({
          transactions: [convertDbItemToDebitCardTransaction(transaction)],
          nextToken: {},
        });
      });
    });
  });

  describe('getContactUuidFromSenderUuid', () => {
    const saveContactSenderLink = (dto: ContactDcaTransactionSenderLinkEventDto) =>
      dbService.put({
        TableName: envService.componentTableName,
        Item: {
          id: `${DbRecordType.DCA_TRANSACTION_SENDER_LINK}${dto.senderUuid}`,
          type: `${DbRecordType.CONTACT_LINK}${dto.contactUuid}`,
          ...dto,
        },
      });

    const dto = new ContactDcaTransactionSenderLinkEventDto({
      entityUuid: v4(),
      senderUuid: v4(),
      contactUuid: v4(),
    });

    beforeAll(async () => {
      await saveContactSenderLink(dto);
    });

    it('should be able to get contact uuid from sender uuid', async () => {
      const contactUuid = await service.getContactUuidFromSenderUuid(dto.senderUuid, dto.entityUuid);
      expect(dto.contactUuid).toEqual(contactUuid);
    });
  });
});
