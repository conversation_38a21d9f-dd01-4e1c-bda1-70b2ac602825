import type { AttributeMap } from '@npco/component-bff-core/dist/dynamodb';
import { debug } from '@npco/component-bff-core/dist/utils/logger';
import { DbRecordType } from '@npco/component-dto-core/dist';
import type {
  DebitCardTransactionConnectionV2,
  DebitCardTransactionV2,
} from '@npco/component-dto-issuing-transaction/dist';
import { DebitCardTransactionStatusV2, DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction/dist';
import type { Merchant } from '@npco/component-dto-richdata/dist';

import type { BankingDynamoDbService } from '../../dynamodb/bankingDynamoDbService';
import type { EnvironmentService } from '../../environment/environmentService';
import type { Payload, ProjectionDto } from '../../projection/types';
import type { MerchantService as RichDataService } from '../../richData/merchantService';
import type { DebitCardAccountTxnQueryInput, EntityGsiKeySchema } from '../types';

import { DebitCardAccountTxnDb } from './debitCardAccountTxnDb';
import {
  convertDbItemToContactDcaTransactionSenderLinkEventDto,
  convertDbItemToDebitCardTransaction,
  resolveSecondaryPkValue,
} from './debitCardTransactionUtils';

export class DebitCardAccountTxnService {
  debitCardAccountTxnDb: DebitCardAccountTxnDb;

  constructor(
    private readonly envService: EnvironmentService,
    private readonly dynamodbService: BankingDynamoDbService,
    private readonly richDataService: RichDataService,
  ) {
    this.debitCardAccountTxnDb = new DebitCardAccountTxnDb(this.envService, this.dynamodbService);
  }

  getDebitCardAccountTxn = async (
    entityUuid: string,
    debitCardTransactionUuid: string,
  ): Promise<DebitCardTransactionV2 | null> => {
    const output = await this.debitCardAccountTxnDb.getDebitCardAccountTxn(entityUuid, debitCardTransactionUuid);
    return output.Items?.length ? convertDbItemToDebitCardTransaction(output.Items[0] as AttributeMap) : null;
  };

  getDebitCardAccountTransactionsV2 = async (
    filter: DebitCardAccountTxnQueryInput,
    entityUuid: string,
  ): Promise<DebitCardTransactionConnectionV2> => {
    const output = await this.debitCardAccountTxnDb.getDebitCardAccountTxns(filter, entityUuid);
    if (output?.Items?.length) {
      const transactions = output.Items.map<DebitCardTransactionV2>((item: any) =>
        convertDbItemToDebitCardTransaction(item),
      );
      const nextToken = output.LastEvaluatedKey ? (output.LastEvaluatedKey as any as EntityGsiKeySchema) : undefined;
      return nextToken ? { transactions, nextToken } : { transactions };
    }
    return { transactions: [] };
  };

  materialiseTransaction = async (data: ProjectionDto<Payload>) => {
    debug(`Debit Card Account Transaction Service - materialiseTransaction: ${JSON.stringify(data)}`);
    if (
      !this.envService.materialiseIncomingExternalNonApprovedTransaction &&
      [
        DebitCardTransactionTypeV2.DE_IN,
        DebitCardTransactionTypeV2.BPAY_IN,
        DebitCardTransactionTypeV2.NPP_IN,
        DebitCardTransactionTypeV2.ATM_IN,
      ].includes((data.payload as DebitCardTransactionV2).type) &&
      (data.payload as DebitCardTransactionV2).status !== DebitCardTransactionStatusV2.APPROVED
    ) {
      return;
    }
    if (
      [DebitCardTransactionTypeV2.ADJUSTMENT_DEPOSIT, DebitCardTransactionTypeV2.ADJUSTMENT_WITHDRAWAL].includes(
        (data.payload as DebitCardTransactionV2).type,
      ) &&
      (data.payload as DebitCardTransactionV2).status !== DebitCardTransactionStatusV2.APPROVED
    ) {
      return;
    }
    const secondaryPk = resolveSecondaryPkValue(data.payload as DebitCardTransactionV2);
    const contactUuid =
      secondaryPk &&
      (await this.debitCardAccountTxnDb.getContactUuidByTransactionType({
        entityUuid: data.payload.entityUuid,
        searchId: secondaryPk,
        transactionType: data.payload.type,
      }));

    const dcaTransactionPayload = {
      ...(data.payload as DebitCardTransactionV2),
      contactUuid,
      secondaryPk,
      ...(secondaryPk ? { sortKey: `${DbRecordType.ISSUING_TRANSACTION}${data.payload.timestamp}` } : undefined),
    };

    const defaultCategory = await this.debitCardAccountTxnDb.getDefaultCategory({
      ...dcaTransactionPayload,
    });

    await Promise.all([
      this.debitCardAccountTxnDb.saveDebitCardTransaction({
        ...dcaTransactionPayload,
        ...defaultCategory,
      } as DebitCardTransactionV2),
      (data.payload as DebitCardTransactionV2).merchant
        ? this.richDataService.saveMerchant((data.payload as DebitCardTransactionV2).merchant as Merchant)
        : undefined,
    ]);
  };

  getContactUuidFromSenderUuid = async (senderUuid: string, entityUuid: string): Promise<string | undefined> => {
    const res = await this.debitCardAccountTxnDb.getContactSenderLink({
      senderUuid,
      entityUuid,
    });

    if (!res) {
      return undefined;
    }

    const dto = convertDbItemToContactDcaTransactionSenderLinkEventDto(res);
    return dto.contactUuid;
  };
}
