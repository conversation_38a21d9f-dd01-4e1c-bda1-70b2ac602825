import { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';
import { ISO4217 } from '@npco/component-dto-core/dist/types';
import {
  DebitCardTransactionStatusV2,
  DebitCardTransactionTypeV2,
  type DebitCardTransactionV2,
} from '@npco/component-dto-issuing-transaction/dist';

import { ContactService } from '../../contact/contactService';
import { dynamodbClient } from '../../dynamodb/client';
import type { EntityQueryService } from '../../entity/entityQueryService';
import { EnvironmentService } from '../../environment/environmentService';
import { MerchantService } from '../../richData/merchantService';
import { TransactionDefaultCategoryService } from '../transactionDefaultCategory/transactionDefaultCategoryService';

import { ADJUSTED_TRANSACTION_TYPES, DebitCardAccountTransactionProjectionService } from './dcaTxnProjectionService';

const mockEntityQueryService = {
  getEntity: jest.fn(),
} as unknown as jest.Mocked<EntityQueryService>;

describe('dcaTxnProjectionService', () => {
  const envService = new EnvironmentService();
  const dbService = new DynamodbService(envService, dynamodbClient);
  const merchantService = new MerchantService(envService, dbService);
  const contactService = new ContactService(dbService);
  const transactionDefaultCategoryService = new TransactionDefaultCategoryService(
    dbService,
    envService,
    mockEntityQueryService,
    merchantService,
    contactService,
  );

  const projectionService = new DebitCardAccountTransactionProjectionService(
    dbService,
    envService,
    mockEntityQueryService,
    transactionDefaultCategoryService,
    merchantService,
  );

  const payload: DebitCardTransactionV2 = {
    id: '123',
    entityUuid: 'entity-uuid',
    debitCardAccountUuid: 'debit-card-account-uuid',
    status: DebitCardTransactionStatusV2.APPROVED,
    type: DebitCardTransactionTypeV2.PURCHASE,
    timestamp: '*************',
    amount: {
      value: '100',
      currency: ISO4217.AUD,
    },
    balance: {
      value: '100',
      currency: ISO4217.AUD,
    },
    updatedTime: new Date().getTime(),
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be able to saveProjection', async () => {
    await expect(() => projectionService.saveProjection(payload)).not.toThrow();
    jest.clearAllMocks();
    const merchantServiceSpy = jest.spyOn(merchantService, 'saveMerchant');
    expect(merchantServiceSpy).not.toHaveBeenCalled();
  });

  it('should call saveMerchant when payload includes merchant', async () => {
    await projectionService.saveProjection({
      ...payload,
      merchant: {
        name: 'merchant-name',
        id: 'merchant-id',
        updatedTime: new Date().getTime(),
      },
    });
    const merchantServiceSpy = jest.spyOn(merchantService, 'saveMerchant');
    expect(merchantServiceSpy).toHaveBeenCalled();
  });

  it.each([ADJUSTED_TRANSACTION_TYPES])(
    'should not materialise when type is ADJUSTED_TRANSACTION_TYPES and status is not APPROVED',
    async (type) => {
      const spyOnRepo = jest.spyOn(projectionService.repo, 'saveDebitCardTransaction');
      await projectionService.saveProjection({
        ...payload,
        type,
        status: DebitCardTransactionStatusV2.PROCESSING,
      });
      expect(spyOnRepo).not.toHaveBeenCalled();
    },
  );
});
