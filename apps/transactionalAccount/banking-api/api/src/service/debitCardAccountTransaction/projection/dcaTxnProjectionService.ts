import type { DynamodbService } from '@npco/component-bff-core/dist/dynamodb';
import { filterKeys } from '@npco/component-bff-core/dist/dynamodb/dynamodbUtils';
import {
  DebitCardTransactionStatusV2,
  DebitCardTransactionTypeV2,
  type DebitCardTransactionV2,
} from '@npco/component-dto-issuing-transaction/dist';

import type { MerchantService } from 'src/service/richData/merchantService';

// Temporarily importinging this function from 'debitCardAccountFromMpCommon', to avoid code duplication.
import { convertDebitCardTransactionCounterPartyAttribute } from '../../debitCardAccountFromMpCommon/debitCardAccountTxn/debitCardTransactionUtils';
import type { EntityQueryService } from '../../entity/entityQueryService';
import type { EnvironmentService } from '../../environment/environmentService';
import { DebitCardAccountTransactionRepository } from '../debitCardAccountTransactionRepository';
import type { TransactionDefaultCategoryService } from '../transactionDefaultCategory/transactionDefaultCategoryService';

export const KEYS_TO_FILTER = ['type', 'merchant', 'ZellerInternalFieldsDoNotExposeToCustomer'];

export const ADJUSTED_TRANSACTION_TYPES = [
  DebitCardTransactionTypeV2.ADJUSTMENT_DEPOSIT,
  DebitCardTransactionTypeV2.ADJUSTMENT_WITHDRAWAL,
];

export class DebitCardAccountTransactionProjectionService {
  readonly repo: DebitCardAccountTransactionRepository;

  constructor(
    private readonly dynamodbService: DynamodbService,
    private readonly envService: EnvironmentService,
    private readonly entityQueryService: EntityQueryService,
    private readonly transactionDefaultCategoryService: TransactionDefaultCategoryService,
    private readonly merchantService: MerchantService,
  ) {
    this.repo = new DebitCardAccountTransactionRepository(
      this.dynamodbService,
      this.envService,
      this.entityQueryService,
    );
  }

  async saveProjection(payload: DebitCardTransactionV2) {
    if (ADJUSTED_TRANSACTION_TYPES.includes(payload.type) && payload.status !== DebitCardTransactionStatusV2.APPROVED) {
      return;
    }

    const updatedProjection = await this.transactionDefaultCategoryService.resolveTransactionDefaultCategory({
      ...payload,
    });

    if (payload?.merchant) {
      await this.merchantService.saveMerchant(payload.merchant);
    }

    await this.repo.saveDebitCardTransaction(
      filterKeys(
        {
          ...convertDebitCardTransactionCounterPartyAttribute(updatedProjection),
          merchantId: updatedProjection.merchant?.id,
          merchantLocationOf: updatedProjection.merchant?.locationOf,
          transactionType: updatedProjection.type,
          timestamp: new Date(Number(updatedProjection.timestamp)).toISOString(),
          referenceLowerCase: updatedProjection.reference?.toLowerCase(),
          referencePayeeLowerCase: updatedProjection.referencePayee?.toLowerCase(),
          descriptionLowerCase: updatedProjection.description?.toLowerCase(),
          updatedAt: new Date(Number(updatedProjection.updatedTime)).toISOString(),
          amount: {
            ...updatedProjection.amount,
            value: Number(updatedProjection.amount.value),
          },
          debitCardMaskedPan: updatedProjection.debitCardMaskedPan?.slice(-4),
          lastUpdatedAt: new Date().toISOString(),
        },
        KEYS_TO_FILTER,
      ),
    );
  }
}
