import {
  DebitCardAccountTransactionCreatedEventDto,
  DebitCardAccountTransactionUpdatedEventDto,
} from '@npco/component-dto-issuing-transaction/dist';

import type { FuncDictionary } from '../../types';

const mapping: FuncDictionary<string, any> = {
  'ProjectionDebitCardAccountTransaction.update': (
    dto: DebitCardAccountTransactionUpdatedEventDto,
    aggregateId: string,
    uri: string,
    createdTimestamp: number,
    version: number,
  ) => ({
    aggregateId,
    uri,
    version,
    createdTimestamp,
    payload: new DebitCardAccountTransactionUpdatedEventDto(dto),
  }),
  'ProjectionDebitCardAccountTransaction.create': (
    dto: DebitCardAccountTransactionCreatedEventDto,
    aggregateId: string,
    uri: string,
    createdTimestamp: number,
    version: number,
  ) => ({
    aggregateId,
    uri,
    version,
    createdTimestamp,
    payload: new DebitCardAccountTransactionCreatedEventDto(dto),
  }),
};

export const DebitCardAccountTransaction = {
  ...mapping,
};
