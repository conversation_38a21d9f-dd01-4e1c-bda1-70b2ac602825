import { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';
import { EntityCategories } from '@npco/component-dto-core/dist';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction/dist';
import type { Merchant } from '@npco/component-dto-richdata/dist';

import type { SQSRecord } from 'aws-lambda';
import { v4 } from 'uuid';

import { ContactService } from '../../contact/contactService';
import { dynamodbClient } from '../../dynamodb/client';
import type { EntityQueryService } from '../../entity/entityQueryService';
import type { EnvironmentService } from '../../environment/environmentService';
import { MerchantService } from '../../richData/merchantService';
import { DebitCardAccountTransactionRepository } from '../debitCardAccountTransactionRepository';

import {
  createTransaction,
  createContact,
  createMerchantContact,
  entityUuid,
  createContactPaymentInstrument,
  createContactDcaSenderLink,
} from './testUtils';
import { TransactionDefaultCategoryService } from './transactionDefaultCategoryService';

const mockMerchant: Merchant = {
  id: v4(),
  name: 'merchant name',
  category: EntityCategories.ADVERTISING,
  subcategory: 'subcategory',
  updatedTime: new Date().getTime(),
  locationOf: v4(),
};

const mockEntityQueryService = {
  getEntity: jest.fn(),
} as unknown as jest.Mocked<EntityQueryService>;

jest.mock('@aws-sdk/client-sqs', () => {
  return {
    SQSClient: jest.fn(() => ({
      send: jest.fn(),
    })),
    DeleteMessageCommand: jest.fn((result) => result),
  };
});

const envService = {
  componentTableName: 'Entities',
  projectionSqsUrl: 'https://sqs-url',
} as jest.Mocked<EnvironmentService>;

describe('transactionDefaultCategoryService test suite', () => {
  let repo: DebitCardAccountTransactionRepository;
  let dynamodbService: DynamodbService;
  let contactService: ContactService;
  let merchantService: MerchantService;
  let transactionDefaultCategoryService: TransactionDefaultCategoryService;

  const saveDbItem = async (item: any) => {
    return dynamodbService.put({
      TableName: envService.componentTableName,
      Item: { ...item },
    });
  };

  beforeAll(async () => {
    dynamodbService = new DynamodbService(envService, dynamodbClient);
    contactService = new ContactService(dynamodbService);
    merchantService = new MerchantService(
      { merchantTableName: 'Merchant' } as jest.Mocked<EnvironmentService>,
      dynamodbService,
    );
    transactionDefaultCategoryService = new TransactionDefaultCategoryService(
      dynamodbService,
      envService,
      mockEntityQueryService,
      merchantService,
      contactService,
    );
    repo = new DebitCardAccountTransactionRepository(dynamodbService, envService, mockEntityQueryService);
  });

  it('should skip defaultCategory/defaultSubcategory re-calculation when category is populated', async () => {
    const transactionId = v4();

    // create dca transaction WITH category/subcategory values being populated
    const mockTransaction = createTransaction({
      id: transactionId,
      transactionType: DebitCardTransactionTypeV2.PURCHASE,
      overrides: {
        merchantId: mockMerchant.id,
        category: EntityCategories.ADVERTISING,
        subcategory: 'COMMERCIAL_ADVERTISING',
        secondaryPk: mockMerchant.id, // this is to simulate the case where secondaryPk is already populated
      },
    });

    // assume the transaction has already been saved
    await repo.saveDebitCardTransaction(mockTransaction);

    // assume the transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: transactionId,
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];
    await transactionDefaultCategoryService.processEvents(events);
    const recalculatedDcaTransaction = await repo.getDebitCardAccountTransaction({ id: transactionId, entityUuid });

    // expect the category/subcategory to be the same as 'mockTransaction'
    expect(recalculatedDcaTransaction?.category).toEqual(mockTransaction.category);
    expect(recalculatedDcaTransaction?.subcategory).toEqual(mockTransaction.subcategory);
  });

  it('should resolve defaultCategory/defaultSubcategory via contact', async () => {
    const transactionId = v4();
    const contactUuid = v4();

    // creating a contact with category/subcategory
    const mockContact = createContact({
      contactUuid,
      overrides: { category: EntityCategories.CONSULTING_ACCOUNTING, subcategoryUuid: 'PRIVATE_SECTOR' },
    });
    await saveDbItem(mockContact);

    // create merchant-contact link
    const mockMerchantContact = createMerchantContact({ contactUuid, merchantId: mockMerchant.id });
    await saveDbItem(mockMerchantContact);

    // create dca transaction without category/subcategory
    const mockTransaction = createTransaction({
      id: transactionId,
      transactionType: DebitCardTransactionTypeV2.PURCHASE,
      overrides: {
        merchantId: mockMerchant.id,
      },
    });

    // assume the transaction has already been saved
    await repo.saveDebitCardTransaction(mockTransaction);

    // assume the transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: transactionId,
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];
    await transactionDefaultCategoryService.processEvents(events);
    const recalculatedDcaTransaction = await repo.getDebitCardAccountTransaction({ id: transactionId, entityUuid });

    expect(recalculatedDcaTransaction?.defaultCategory).toEqual(mockContact.category);
    expect(recalculatedDcaTransaction?.defaultSubcategory).toEqual(mockContact.subcategoryUuid);
  });

  it('should resolve defaultCategory/defaultSubcategory via richDataService.getMerchant()', async () => {
    const transactionId = v4();
    const merchantMocked: Merchant = {
      id: v4(),
      name: 'merchant name',
      category: EntityCategories.COMMISSION,
      subcategory: 'subcategory-123',
      updatedTime: new Date().getTime(),
      locationOf: v4(),
    };

    const merchantId = merchantMocked.id;

    const mockTransaction = createTransaction({
      id: transactionId,
      transactionType: DebitCardTransactionTypeV2.PURCHASE,
      overrides: {
        merchantId, // only provide merchantId
      },
    });

    console.log('mockTransaction', mockTransaction);

    // assume the transaction has already been saved
    await repo.saveDebitCardTransaction(mockTransaction);

    // save merchant record to the database
    await merchantService.saveMerchant(merchantMocked);

    // assume the transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: transactionId,
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];
    await transactionDefaultCategoryService.processEvents(events);
    const recalculatedDcaTransaction = await repo.getDebitCardAccountTransaction({ id: transactionId, entityUuid });
    expect(recalculatedDcaTransaction?.defaultCategory).toEqual(merchantMocked.category);
    expect(recalculatedDcaTransaction?.defaultSubcategory).toEqual(merchantMocked.subcategory);
  });

  it('should resolve defaultCategory/defaultSubcategory paymentInstrument transaction', async () => {
    const transactionId = v4();
    const contactUuid = v4();
    const paymentInstrumentUuid = v4();

    // creating a contact with category/subcategory
    const mockContact = createContact({
      contactUuid,
      overrides: { category: EntityCategories.CONSULTING_ACCOUNTING, subcategoryUuid: 'PRIVATE_SECTOR' },
    });
    await saveDbItem(mockContact);

    // create contact-payment link
    const mockContactPaymentInstrument = createContactPaymentInstrument({ contactUuid, paymentInstrumentUuid });
    await saveDbItem(mockContactPaymentInstrument);

    const mockTransaction = createTransaction({
      id: transactionId,
      transactionType: DebitCardTransactionTypeV2.DE_OUT,
      overrides: {
        payeeDetails: {
          paymentInstrumentUuid,
        },
      },
    });

    // assume the transaction has already been saved
    await repo.saveDebitCardTransaction(mockTransaction);

    // assume the transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: transactionId,
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];
    await transactionDefaultCategoryService.processEvents(events);
    const recalculatedDcaTransaction = await repo.getDebitCardAccountTransaction({ id: transactionId, entityUuid });

    // expect the category/subcategory to be the same as 'mockTransaction'
    expect(recalculatedDcaTransaction?.defaultCategory).toEqual(mockContact.category);
    expect(recalculatedDcaTransaction?.defaultSubcategory).toEqual(mockContact.subcategoryUuid);
  });

  // The context is when a contact that was linked to a specific paymentInstrument was deleted (not unlinked, but deleted)
  it('should resolve defaultCategory/defaultSubcategory to undefined when paymentInstrument transaction related contact was deleted', async () => {
    const transactionId = v4();
    const contactUuid = v4();
    const paymentInstrumentUuid = v4();

    // create contact-payment link
    const mockContactPaymentInstrument = createContactPaymentInstrument({ contactUuid, paymentInstrumentUuid });
    await saveDbItem(mockContactPaymentInstrument);

    // creating a contact with category/subcategory
    const mockContact = createContact({
      contactUuid,
      overrides: {
        category: EntityCategories.CONSULTING_ACCOUNTING,
        subcategoryUuid: 'PRIVATE_SECTOR',
        status: 'DELETED', // adding 'status' to simulate an existing contact record was deleted
      },
    });
    await saveDbItem(mockContact);

    const mockTransaction = createTransaction({
      id: transactionId,
      transactionType: DebitCardTransactionTypeV2.DE_OUT,
      overrides: {
        contactUuid, // simulate the case where the transaction was materialised before.
        payeeDetails: {
          paymentInstrumentUuid,
        },
      },
    });

    // assume the transaction has already been saved
    await repo.saveDebitCardTransaction(mockTransaction);

    // assume the transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: transactionId,
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];
    await transactionDefaultCategoryService.processEvents(events);
    const recalculatedDcaTransaction = await repo.getDebitCardAccountTransaction({ id: transactionId, entityUuid });

    expect(recalculatedDcaTransaction?.defaultCategory).toEqual(undefined);
    expect(recalculatedDcaTransaction?.defaultSubcategory).toEqual(undefined);
    expect(recalculatedDcaTransaction?.contactUuid).toEqual(undefined);
  });

  it('should resolve defaultCategory/defaultSubcategory as undefined when handling invalid paymentInstrument-contact link', async () => {
    const transactionId = v4();
    const paymentInstrumentUuid = v4();

    const mockTransaction = createTransaction({
      id: transactionId,
      transactionType: DebitCardTransactionTypeV2.DE_OUT,
      overrides: {
        payeeDetails: {
          paymentInstrumentUuid,
        },
      },
    });

    // assume the transaction has already been saved
    await repo.saveDebitCardTransaction(mockTransaction);

    // assume the transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: transactionId,
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];
    await transactionDefaultCategoryService.processEvents(events);
    const recalculatedDcaTransaction = await repo.getDebitCardAccountTransaction({ id: transactionId, entityUuid });

    // expect the category/subcategory to be undefined as contact are not linked to the transaction
    expect(recalculatedDcaTransaction?.defaultCategory).toEqual(undefined);
    expect(recalculatedDcaTransaction?.defaultSubcategory).toEqual(undefined);
  });

  it('should resolve defaultCategory/defaultSubcategory incomingExternal transaction', async () => {
    const transactionId = v4();
    const contactUuid = v4();
    const senderUuid = v4();

    // creating a contact with category/subcategory
    const mockContact = createContact({
      contactUuid,
      overrides: { category: EntityCategories.CONSULTING_ACCOUNTING, subcategoryUuid: 'PRIVATE_SECTOR' },
    });
    await saveDbItem(mockContact);

    // create contact-dcaSender link
    const mockContactDcaSenderLink = createContactDcaSenderLink({ contactUuid, senderUuid });
    await saveDbItem(mockContactDcaSenderLink);

    const mockTransaction = createTransaction({
      id: transactionId,
      transactionType: DebitCardTransactionTypeV2.DE_IN,
      overrides: {
        payerDetails: {
          senderUuid,
        },
      },
    });

    // assume the transaction has already been saved
    await repo.saveDebitCardTransaction(mockTransaction);

    // assume the transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: transactionId,
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];
    await transactionDefaultCategoryService.processEvents(events);
    const recalculatedDcaTransaction = await repo.getDebitCardAccountTransaction({ id: transactionId, entityUuid });

    // expect the category/subcategory to be the same as 'mockTransaction'
    expect(recalculatedDcaTransaction?.defaultCategory).toEqual(mockContact.category);
    expect(recalculatedDcaTransaction?.defaultSubcategory).toEqual(mockContact.subcategoryUuid);
  });

  it('should resolve defaultCategory/defaultSubcategory incomingExternal DDA_OUT transaction', async () => {
    const transactionId = v4();
    const contactUuid = v4();
    const senderUuid = v4();

    // creating a contact with category/subcategory
    const mockContact = createContact({
      contactUuid,
      overrides: { category: EntityCategories.CONSULTING_ACCOUNTING, subcategoryUuid: 'PRIVATE_SECTOR' },
    });
    await saveDbItem(mockContact);

    // create contact-dcaSender link
    const mockContactDcaSenderLink = createContactDcaSenderLink({ contactUuid, senderUuid });
    await saveDbItem(mockContactDcaSenderLink);

    const mockTransaction = createTransaction({
      id: transactionId,
      transactionType: DebitCardTransactionTypeV2.DDA_OUT,
      overrides: {
        payeeDetails: {
          senderUuid,
        },
      },
    });

    // assume the transaction has already been saved
    await repo.saveDebitCardTransaction(mockTransaction);

    // assume the transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: transactionId,
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];
    await transactionDefaultCategoryService.processEvents(events);
    const recalculatedDcaTransaction = await repo.getDebitCardAccountTransaction({ id: transactionId, entityUuid });

    // expect the category/subcategory to be the same as 'mockTransaction'
    expect(recalculatedDcaTransaction?.defaultCategory).toEqual(mockContact.category);
    expect(recalculatedDcaTransaction?.defaultSubcategory).toEqual(mockContact.subcategoryUuid);
  });

  it('should resolve defaultCategory/defaultSubcategory as undefined when handling invalid contact-sender link', async () => {
    const transactionId = v4();
    const contactUuid = v4();
    const senderUuid = v4();

    // creating a contact with category/subcategory
    const mockContact = createContact({
      contactUuid,
      overrides: { category: EntityCategories.CONSULTING_ACCOUNTING, subcategoryUuid: 'PRIVATE_SECTOR' },
    });
    await saveDbItem(mockContact);

    // create contact-dcaSender link
    // const mockContactDcaSenderLink = createContactDcaSenderLink({ contactUuid, senderUuid });
    // await saveDbItem(mockContactDcaSenderLink);

    const mockTransaction = createTransaction({
      id: transactionId,
      transactionType: DebitCardTransactionTypeV2.DE_IN,
      overrides: {
        payerDetails: {
          senderUuid,
        },
      },
    });

    // assume the transaction has already been saved
    await repo.saveDebitCardTransaction(mockTransaction);

    // assume the transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: transactionId,
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];
    await transactionDefaultCategoryService.processEvents(events);
    const recalculatedDcaTransaction = await repo.getDebitCardAccountTransaction({ id: transactionId, entityUuid });

    // expect the category/subcategory to be the same as 'mockTransaction'
    expect(recalculatedDcaTransaction?.defaultCategory).toEqual(undefined);
    expect(recalculatedDcaTransaction?.defaultSubcategory).toEqual(undefined);
  });

  it('should resolve defaultCategory/defaultSubcategory as undefined when contact-sender link is unlinked', async () => {
    const transactionId = v4();
    const contactUuid = v4();
    const paymentInstrumentUuid = v4();

    // creating a contact with category/subcategory
    const mockContact = createContact({
      contactUuid,
      overrides: { category: EntityCategories.CONSULTING_ACCOUNTING, subcategoryUuid: 'PRIVATE_SECTOR' },
    });
    await saveDbItem(mockContact);

    // create contact-dcaSender link
    const mockContactPaymentInstrumentLink = {
      ...createContactPaymentInstrument({ contactUuid, paymentInstrumentUuid }),
      contactUuid: undefined, // simulate the case where contact-paymentInstrument is unlinked
    };

    await saveDbItem(mockContactPaymentInstrumentLink);

    const mockTransaction = createTransaction({
      id: transactionId,
      transactionType: DebitCardTransactionTypeV2.DE_OUT,
      overrides: {
        payeeDetails: {
          paymentInstrumentUuid,
        },
        defaultCategory: EntityCategories.COMMISSION,
        defaultSubcategory: 'OTHER',
        contactUuid,
      },
    });

    // assume the transaction has already been saved
    await repo.saveDebitCardTransaction(mockTransaction);

    // assume the transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: transactionId,
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];
    await transactionDefaultCategoryService.processEvents(events);
    const recalculatedDcaTransaction = await repo.getDebitCardAccountTransaction({ id: transactionId, entityUuid });

    // expect the category/subcategory & contactUuid to be undefined
    expect(recalculatedDcaTransaction?.defaultCategory).toEqual(undefined);
    expect(recalculatedDcaTransaction?.defaultSubcategory).toEqual(undefined);
    expect(recalculatedDcaTransaction?.contactUuid).toEqual(undefined);
  });

  it('should resolve defaultCategory/defaultSubcategory as undefined when secondaryPk is undefined', async () => {
    const transactionId = v4();
    const senderUuid = v4();

    const mockTransaction = createTransaction({
      id: transactionId,
      transactionType: 'unknown-transaction-type' as unknown as DebitCardTransactionTypeV2,
      overrides: {
        payeeDetails: {
          senderUuid,
        },
        secondaryPk: 'unknown-transaction-type',
      },
    });

    // assume the transaction has already been saved
    await repo.saveDebitCardTransaction(mockTransaction);

    // assume the transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: transactionId,
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];
    await transactionDefaultCategoryService.processEvents(events);
    const recalculatedDcaTransaction = await repo.getDebitCardAccountTransaction({ id: transactionId, entityUuid });

    expect(recalculatedDcaTransaction?.defaultCategory).toEqual(undefined);
    expect(recalculatedDcaTransaction?.defaultSubcategory).toEqual(undefined);
  });

  it('should resolve defaultCategory/defaultSubcategory as undefined when transaction type is unknown', async () => {
    const transactionId = v4();
    const senderUuid = v4();

    const mockTransaction = createTransaction({
      id: transactionId,
      transactionType: 'unknown-transaction-type' as unknown as DebitCardTransactionTypeV2,
      overrides: {
        payeeDetails: {
          senderUuid,
        },
      },
    });

    // assume the transaction has already been saved
    await repo.saveDebitCardTransaction(mockTransaction);

    // assume the transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: transactionId,
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];
    await transactionDefaultCategoryService.processEvents(events);
    const recalculatedDcaTransaction = await repo.getDebitCardAccountTransaction({ id: transactionId, entityUuid });

    expect(recalculatedDcaTransaction?.defaultCategory).toEqual(undefined);
    expect(recalculatedDcaTransaction?.defaultSubcategory).toEqual(undefined);
  });

  it('should thrown an error when the transaction cannot be found', async () => {
    // assume a non-existent transaction to be pushed to SQS for processing
    const events = [
      {
        body: JSON.stringify({
          entityUuid,
          id: 'non-existing-transaction-id',
        }),
        receiptHandle: v4(),
      },
    ] as SQSRecord[];

    await expect(transactionDefaultCategoryService.processEvents(events)).rejects.toThrowError(
      `process event ${JSON.stringify(events)} failed.`,
    );
  });
});
