import type { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';
import { error, info } from '@npco/component-bff-core/dist/utils/logger';
import { DbRecordType } from '@npco/component-dto-core/dist/types';
import type { DebitCardTransactionV2 } from '@npco/component-dto-issuing-transaction/dist';

import { SQSClient, DeleteMessageCommand } from '@aws-sdk/client-sqs';
import type { SQSRecord } from 'aws-lambda';
import type { ContactService } from 'src/service/contact/contactService';

import type { EntityQueryService } from '../../entity/entityQueryService';
import type { EnvironmentService } from '../../environment/environmentService';
import type { EventBridgeEventData } from '../../projection/types';
import type { MerchantService } from '../../richData/merchantService';
import { DebitCardAccountTransactionRepository } from '../debitCardAccountTransactionRepository';

import { resolveSecondaryPkValue } from './utils';

export class TransactionDefaultCategoryService {
  private readonly sqs: SQSClient;

  private readonly repo: DebitCardAccountTransactionRepository;

  constructor(
    private readonly dynamodbService: DynamodbService,
    private readonly envService: EnvironmentService,
    private readonly entityQueryService: EntityQueryService,
    private readonly merchantService: MerchantService,
    private readonly contactService: ContactService,
  ) {
    this.repo = new DebitCardAccountTransactionRepository(this.dynamodbService, envService, this.entityQueryService);
    this.sqs = new SQSClient({});
  }

  async processEvents(events: SQSRecord[]) {
    const data: EventBridgeEventData[] = events.map((e) => JSON.parse(e.body));
    const errorEvents: any = [];

    await Promise.all(
      data.map(async (parsedEventBody, index) => {
        try {
          info(`processing SQS record - ${JSON.stringify(parsedEventBody)}`);
          const { id, entityUuid } = parsedEventBody;
          const dcaTransactionRecord = await this.repo.getDebitCardAccountTransaction({ id, entityUuid });
          if (!dcaTransactionRecord) {
            error(`Cannot find transaction with id: ${id}`);
            throw new Error('Cannot find transaction');
          }

          // if category is not set, then we need to get the default category via contact/merchant/getMerchant
          if (!dcaTransactionRecord.category) {
            const updatedTransaction = await this.resolveTransactionDefaultCategory({
              ...dcaTransactionRecord,
            } as unknown as DebitCardTransactionV2);

            await this.repo.saveDebitCardTransaction({ ...updatedTransaction } as DebitCardTransactionV2);
          }

          await this.deleteMessage(events[index], this.envService.dcaRematerialisationSqsUrl);
        } catch (e) {
          error(e);
          error(`Error processing event ${JSON.stringify(events[index], null, 2)}`);
          errorEvents.push(events[index]);
        }
      }),
    );
    this.checkErrors(errorEvents);
  }

  resolveTransactionDefaultCategory = async (data: Record<string, any>) => {
    // re-calculate value for secondaryPk & contactUuid if not exist
    const secondaryPk = data.secondaryPk ?? resolveSecondaryPkValue(data);
    const contactUuid =
      secondaryPk &&
      (await this.contactService.getContactUuidByTransactionType(data.entityUuid, secondaryPk, data.transactionType));

    const defaultCategory = await this.getDefaultCategory({
      ...data,
      contactUuid,
    } as unknown as DebitCardTransactionV2);

    return {
      ...(data as DebitCardTransactionV2),
      ...defaultCategory,
      sortKey: `${DbRecordType.ISSUING_TRANSACTION}${data.timestamp}`,
      secondaryPk,
      contactUuid,
    };
  };

  private readonly checkErrors = (
    /* istanbul ignore next: difficult to test the default value */ errorEvents: any = [],
  ) => {
    info(`errorlength: ${errorEvents.length}`);
    if (errorEvents.length > 0) {
      const errorMessage = `process event ${JSON.stringify(errorEvents)} failed.`;
      error(errorMessage);
      throw new Error(errorMessage);
    }
  };

  private readonly deleteMessage = async (event: any, sqsQueue: string) => {
    const output = await this.sqs.send(
      new DeleteMessageCommand({ QueueUrl: sqsQueue, ReceiptHandle: event.receiptHandle }),
    );
    info(`delete message from sqs ${JSON.stringify(event)} ${JSON.stringify(output)}`);
  };

  private async getDefaultCategory(
    data: DebitCardTransactionV2,
  ): Promise<{ defaultCategory: string | undefined; defaultSubcategory: string | undefined }> {
    const contact = await this.contactService.getContactByUuid(data.entityUuid, data.contactUuid);
    if (contact?.category) {
      return {
        defaultCategory: contact.category,
        defaultSubcategory: contact.subcategoryUuid,
      };
    }

    const merchantId = data?.merchantLocationOf ?? data.merchantId;
    const retrievedMerchant = merchantId ? await this.merchantService.getMerchant(merchantId) : null;
    if (retrievedMerchant?.category) {
      return {
        defaultCategory: retrievedMerchant.category,
        defaultSubcategory: retrievedMerchant.subcategory,
      };
    }

    return {
      defaultCategory: undefined,
      defaultSubcategory: undefined,
    };
  }
}
