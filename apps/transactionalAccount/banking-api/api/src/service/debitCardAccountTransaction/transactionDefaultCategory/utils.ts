import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction/dist';

const incomingSenderTransactionTypes = [
  DebitCardTransactionTypeV2.DE_IN,
  DebitCardTransactionTypeV2.NPP_IN,
  DebitCardTransactionTypeV2.NPP_IN_RETURN,
  DebitCardTransactionTypeV2.BPAY_IN,
  DebitCardTransactionTypeV2.ATM_IN,
];

const outgoingSenderTransactionTypes = [DebitCardTransactionTypeV2.DDA_OUT];

export const senderTransactionTypes = [...incomingSenderTransactionTypes, ...outgoingSenderTransactionTypes];

export const paymentInstrumentTransactionTypes = [
  DebitCardTransactionTypeV2.DE_OUT,
  DebitCardTransactionTypeV2.NPP_OUT,
  DebitCardTransactionTypeV2.BPAY_OUT,
  DebitCardTransactionTypeV2.DE_OUT_RETURN,
  DebitCardTransactionTypeV2.NPP_OUT_RETURN,
];

export const merchantTransactionTypes = [
  DebitCardTransactionTypeV2.PURCHASE,
  DebitCardTransactionTypeV2.PURCHASE_CNP,
  DebitCardTransactionTypeV2.REFUND,
  DebitCardTransactionTypeV2.REFUND_CNP,
];

export const resolveSecondaryPkValue = (data: any): string | undefined => {
  // As 'data' is supposed to be a DebitCardTransactionV2,
  // attribute 'type' corresponds to DynamoDB record 'type' instead of DebitCardTransactionV2's 'type'
  const { transactionType } = data;

  if (paymentInstrumentTransactionTypes.includes(transactionType)) {
    return data?.payeeDetails?.paymentInstrumentUuid;
  }

  if (merchantTransactionTypes.includes(transactionType)) {
    return data?.merchantLocationOf ?? data?.merchantId;
  }

  if (incomingSenderTransactionTypes.includes(transactionType)) {
    return data?.payerDetails?.senderUuid;
  }

  if (outgoingSenderTransactionTypes.includes(transactionType)) {
    return data?.payeeDetails?.senderUuid;
  }

  return undefined;
};
