import type { ContactType } from '@npco/component-dto-addressbook/dist/';
import type { Icon } from '@npco/component-dto-core/dist/';
import type { Money } from '@npco/component-dto-core/dist/types';
import type {
  FundsTransferFrequency,
  FundsTransferPayeeType,
  MoneyInput,
} from '@npco/component-dto-issuing-transaction/dist/types';

export enum AdjustmentCategory {
  DISPUTE_DEPOSIT = 'DISPUTE_DEPOSIT',
  DISPUTE_WITHDRAW = 'DISPUTE_WITHDRAW',
  FUNDS_RELEASED = 'FUNDS_RELEASED',
  FUNDS_HELD = 'FUNDS_HELD',
}

export enum CoreBankingAdjustmentCategory {
  DISPUTE_DEPOSIT = 'Dispute Deposit',
  DISPUTE_WITHDRAW = 'Dispute Withdraw',
  FUNDS_RELEASED = 'Funds Released',
  FUNDS_HELD = 'Funds Held',
}

export enum AdjustmentTransactionType {
  DEPOSIT = 'DEPOSIT',
  WITHDRAWAL = 'WITHDRAWAL',
}

export type AddAdjustmentInput = {
  transactionType: AdjustmentTransactionType;
  adjustmentCategory: AdjustmentCategory;
  accountUuid: string;
  amount: Money;
  description: string;
  reference: string;
  notes?: string | null;
};

export type AddAdjustmentPayload = {
  adjustmentCategory: CoreBankingAdjustmentCategory;
  accountUuid: string;
  amount: Money;
  zellerReference: string;
  merchantReference: string;
  zellerUserId: string;
  idempotencyKey?: string;
  originalTransactionUuid?: string;
  adjustmentNotes?: string;
};

export type UpdateDebitCardTransactionNoteInput = {
  transactionUuid: string;
  entityUuid?: string;
  note: string | undefined;
};

export type BpayBillersSearchResponse = {
  nextPageToken: string | null;
  bpayBillers: BpayBiller[];
};

export type BpayBiller = {
  billerCode: string;
  // only return `longName` from BPAY api
  billerName: string;
};

export type BpayBillerDetail = {
  billerCode: string;
  // only return `longName` from BPAY api
  billerName: string;
  icrnIndicator: boolean;
  crnLengths: number[];
};

export type ValidateBpayPaymentInput = {
  billerCode: string;
  crn: string;
  amount: MoneyInput;
};

export type InvalidBpayPaymentReason = {
  // e.g. 107
  responseCode: string;
  // e.g. The BPAY Biller code is not valid. Please check your entry and retry
  errorDescription: string;
};

export type ValidateBpayPaymentResponse =
  | { isValid: true }
  | {
      isValid: false;
      invalidReason: InvalidBpayPaymentReason;
    };

export type SubmitStaticCrnBpayPaymentInput = {
  amount: MoneyInput;
  debitCardAccountUuid: string;
  paymentInstrumentUuid: string;
};

export type SubmitDynamicCrnBpayPaymentInput = {
  amount: MoneyInput;
  debitCardAccountUuid: string;
  paymentInstrumentUuid: string;
  icrn: string;
};

export type SubmitBpayPaymentResponse = {
  result?: SubmitBpayPaymentResponseResult;
};

export type SubmitBpayPaymentResponseResult = {
  id: string;
  debitCardAccountUuid: string;
  paymentInstrumentUuid: string;
  billerName: string;
  billerCode: string;
  crn: string;
  amount: Money;
};

export interface InternalTransferFundsInput {
  entityUuid: string;
  payeeAccountUuid: string;
  payerAccountUuid: string;
  payeeReference?: string;
  amount: Money;
}

export interface ExternalTransferFundsInput {
  entityUuid: string;
  payerAccountUuid: string;
  recipientUuid: string;
  payeeReference?: string;
  payerReference?: string;
  amount: Money;
  forceDeOut?: boolean;
  lookUpId?: string;
}

export interface TransferFundsInput {
  entityUuid: string;
  payerAccountUuid: string;
  payeeType: FundsTransferPayeeType;
  payeeAccountUuid: string; // to the payment instrument Uuid or Account details
  payerReference?: string;
  payeeReference?: string;
  amount: Money;
  frequency: FundsTransferFrequency;
  frequencyUnit?: number;
  startDate?: Date;
  endDate?: Date;
  forceDeOut?: boolean;
}

export enum NppReachability {
  NPP_REACHABLE = 'NPP_REACHABLE',
  NOT_NPP_REACHABLE = 'NOT_NPP_REACHABLE',
  OSKO_REACHABLE = 'OSKO_REACHABLE',
}

export type DebitCardTransactionsQueryInput = {
  debitCardId: string;
  isOutstanding?: boolean;
  filter?: string;
  limit?: number;
  timestampFilter?: string;
  transactionType?: string;
  nextToken?: string;
};

export type ContactBasicInfo = {
  id: string;
  contactType: ContactType;
  firstName?: string;
  lastName?: string;
  businessName?: string;
  icon?: Icon;
  category?: string;
  subcategoryUuid?: string;
};
