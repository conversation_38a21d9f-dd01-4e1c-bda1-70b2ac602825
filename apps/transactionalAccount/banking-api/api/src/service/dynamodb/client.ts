import { BffDynamoDbClient } from '@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient';

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { NodeHttpHandler } from '@smithy/node-http-handler';

const isTest = process.env.JEST_WORKER_ID;

export const dynamodbClient = isTest
  ? new DynamoDBClient({
      requestHandler: new NodeHttpHandler({
        connectionTimeout: 5000,
        socketTimeout: 5000,
      }),
      maxAttempts: 3,
      region: 'local-env',
      credentials: {
        accessKeyId: 'fakeMyKeyId',
        secretAccessKey: 'fakeSecretAccessKey',
      },
      endpoint: 'http://localhost:8001',
    })
  : new BffDynamoDbClient({
      awsRegion: process.env.AWS_REGION,
      dynamodbConnectTimeout: 5000,
      dynamodbTimeout: 5000,
      maxRetries: 3,
    });
