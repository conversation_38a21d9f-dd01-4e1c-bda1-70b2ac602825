import { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';
import * as Logger from '@npco/component-bff-core/dist/utils/logger';

import type { SQSRecord } from 'aws-lambda';

import { AccountStatementStorageProjectionService } from '../accountStatement/accountStatementStorage/accountStatementStorageProjectionService';
import { AccountStatementTransactionProjectionService } from '../accountStatement/accountStatementTransaction/accountStatementTransactionProjectionService';
import { ContactService } from '../contact/contactService';
import { SavingsAccountProjectionService } from '../debitCardAccount/savingsAccount/savingsAccountProjectionService';
import { DebitCardAccountTransactionProjectionService } from '../debitCardAccountTransaction/projection/dcaTxnProjectionService';
import { SavingsAccountTransactionProjectionService } from '../debitCardAccountTransaction/savingsAccountTransaction/savingsAccountTransactionProjectionService';
import { TransactionDefaultCategoryService } from '../debitCardAccountTransaction/transactionDefaultCategory/transactionDefaultCategoryService';
import { dynamodbClient } from '../dynamodb/client';
import type { EntityQueryService } from '../entity/entityQueryService';
import type { EnvironmentService } from '../environment/environmentService';
import { InterestSummaryStorageProjectionService } from '../interestSummary/interestSummaryStorage/interestSummaryStorageProjectionService';
import { MerchantService } from '../richData/merchantService';
import { EntitySavingsAccountProductProjectionService } from '../savingsAccountProduct/entitySavingsAccountProductProjectionService';
import { SavingsAccountProductProjectionService } from '../savingsAccountProduct/savingsAccountProductProjectionService';
import { ScheduledTransferProjectionService } from '../scheduledTransfers/scheduledTransferProjectionService';
import { TransferScheduledExecProjectionService } from '../scheduledTransfers/transferScheduledExecProjectionService';

import { ProjectionService } from './projectionService';

const mockSend = jest.fn();

jest.mock('@aws-sdk/lib-dynamodb');
jest.mock('@npco/component-bff-core/dist/dynamodb/dynamodbService');
jest.mock('../environment/environmentService');
jest.mock('@aws-sdk/client-sqs', () => {
  return {
    SQSClient: jest.fn(() => ({
      send: mockSend,
    })),
    DeleteMessageCommand: jest.fn((result) => result),
  };
});

jest.mock('@npco/component-bff-core/dist/utils/logger', () => {
  const originalModule = jest.requireActual('@npco/component-bff-core/dist/utils/logger');
  return {
    __esModule: true, // Use it when dealing with esModules
    ...originalModule,
    info: jest.fn(),
    error: jest.fn(),
  };
});

jest.mock('../accountStatement/accountStatementTransaction/accountStatementTransactionProjectionService');
jest.mock('../accountStatement/accountStatementStorage/accountStatementStorageProjectionService');
jest.mock('../debitCardAccount/savingsAccount/savingsAccountProjectionService');
jest.mock('../debitCardAccountTransaction/projection/dcaTxnProjectionService');
jest.mock('../debitCardAccountTransaction/savingsAccountTransaction/savingsAccountTransactionProjectionService');
jest.mock('../savingsAccountProduct/savingsAccountProductProjectionService');
jest.mock('../savingsAccountProduct/entitySavingsAccountProductProjectionService');
jest.mock('../interestSummary/interestSummaryStorage/interestSummaryStorageProjectionService');
jest.mock('../scheduledTransfers/scheduledTransferProjectionService');
jest.mock('../scheduledTransfers/transferScheduledExecProjectionService');
const mockAccountStatementTransactionProjectionService = jest.mocked(AccountStatementTransactionProjectionService);
const mockAccountStatementStorageProjectionService = jest.mocked(AccountStatementStorageProjectionService);
const mockSavingsAccountProjectionService = jest.mocked(SavingsAccountProjectionService);
const mockDebitCardAccountTransactionProjectionService = jest.mocked(DebitCardAccountTransactionProjectionService);
const mockSavingsAccountTransactionProjectionService = jest.mocked(SavingsAccountTransactionProjectionService);
const mockSavingsAccountProductProjectionService = jest.mocked(SavingsAccountProductProjectionService);
const mockEntitySavingsAccountProductProjectionService = jest.mocked(EntitySavingsAccountProductProjectionService);
const mockInterestSummaryStorageProjectionService = jest.mocked(InterestSummaryStorageProjectionService);
const mockScheduledTransferProjectionService = jest.mocked(ScheduledTransferProjectionService);
const mockTransferScheduledExecProjectionService = jest.mocked(TransferScheduledExecProjectionService);

const mockSqsUrl = 'https://sqs-url';

describe('projectionService test suite', () => {
  const mockEnvService = {
    projectionSqsUrl: mockSqsUrl,
  } as jest.Mocked<EnvironmentService>;
  const mockEntityQueryService = {
    getEntity: jest.fn(),
  } as unknown as jest.Mocked<EntityQueryService>;
  const dynamodbService = new DynamodbService(mockEnvService, dynamodbClient);
  const accountStatementTransactionProjectionService = new AccountStatementTransactionProjectionService(
    mockEnvService,
    dynamodbService,
  );
  const accountStatementStorageProjectionService = new AccountStatementStorageProjectionService(
    mockEnvService,
    dynamodbService,
  );
  const savingsAccountProjectionService = new SavingsAccountProjectionService(mockEnvService, dynamodbService);
  const savingsAccountTransactionProjectionService = new SavingsAccountTransactionProjectionService(
    mockEnvService,
    dynamodbService,
    mockEntityQueryService,
  );
  const savingsAccountProductProjectionService = new SavingsAccountProductProjectionService(
    mockEnvService,
    dynamodbService,
  );
  const entitySavingsAccountProductProjectionService = new EntitySavingsAccountProductProjectionService(
    mockEnvService,
    dynamodbService,
  );
  const interestSummaryStorageProjectionService = new InterestSummaryStorageProjectionService(
    mockEnvService,
    dynamodbService,
  );

  const scheduledTransferProjectionService = new ScheduledTransferProjectionService(mockEnvService, dynamodbService);

  const transferScheduledExecProjectionService = new TransferScheduledExecProjectionService(
    mockEnvService,
    dynamodbService,
  );

  const merchantService = new MerchantService(mockEnvService, dynamodbService);
  const contactService = new ContactService(dynamodbService);
  const transactionDefaultCategoryService = new TransactionDefaultCategoryService(
    dynamodbService,
    mockEnvService,
    mockEntityQueryService,
    merchantService,
    contactService,
  );
  const debitCardAccountTransactionProjectionService = new DebitCardAccountTransactionProjectionService(
    dynamodbService,
    mockEnvService,
    mockEntityQueryService,
    transactionDefaultCategoryService,
    merchantService,
  );

  const service = new ProjectionService(
    mockEnvService,
    accountStatementTransactionProjectionService,
    accountStatementStorageProjectionService,
    savingsAccountProjectionService,
    savingsAccountTransactionProjectionService,
    savingsAccountProductProjectionService,
    entitySavingsAccountProductProjectionService,
    interestSummaryStorageProjectionService,
    scheduledTransferProjectionService,
    transferScheduledExecProjectionService,
    debitCardAccountTransactionProjectionService,
  );

  beforeEach(() => {
    mockSend.mockReset();
    mockSend.mockReturnValue({ promise: jest.fn() });
  });

  it.each([
    [
      'debit card account statement transaction projection',
      'mp.ProjectionDebitCardAccountStatementTransaction.create',
      mockAccountStatementTransactionProjectionService.prototype.saveDebitCardAccountStatmentProjection,
    ],
    [
      'savings account statement transaction projection',
      'mp.ProjectionSavingsAccountStatementTransaction.create',
      mockAccountStatementTransactionProjectionService.prototype.saveSavingsAccountStatmentProjection,
    ],
    [
      'debit card account statement storage projection',
      'mp.DebitCardAccount.StatementStorageCreated',
      mockAccountStatementStorageProjectionService.prototype.saveProjection,
    ],
    [
      'savings account projection',
      'mp.ProjectionSavingsAccount.create',
      mockSavingsAccountProjectionService.prototype.saveProjection,
    ],
    [
      'debit card account transaction projection',
      'mp.ProjectionDebitCardAccountTransaction.create',
      mockDebitCardAccountTransactionProjectionService.prototype.saveProjection,
    ],
    [
      'debit card account transaction projection',
      'mp.ProjectionDebitCardAccountTransaction.update',
      mockDebitCardAccountTransactionProjectionService.prototype.saveProjection,
    ],
    [
      'savings account transaction created projection',
      'mp.ProjectionSavingsAccountTransaction.create',
      mockSavingsAccountTransactionProjectionService.prototype.saveProjection,
    ],
    [
      'savings account transaction updated projection',
      'mp.ProjectionSavingsAccountTransaction.update',
      mockSavingsAccountTransactionProjectionService.prototype.saveProjection,
    ],
    [
      'savings account product created projection',
      'mp.ProjectionSavingsAccountProduct.create',
      mockSavingsAccountProductProjectionService.prototype.saveProjection,
    ],
    [
      'savings account transaction updated projection',
      'mp.ProjectionSavingsAccountProduct.update',
      mockSavingsAccountProductProjectionService.prototype.saveProjection,
    ],
    [
      'entity savings account product promotion created projection',
      'mp.Entity.SavingsAccountProductPromotionCreated',
      mockEntitySavingsAccountProductProjectionService.prototype.saveProjection,
    ],
    [
      'entity savings account product promotion updated projection',
      'mp.Entity.SavingsAccountProductPromotionUpdated',
      mockEntitySavingsAccountProductProjectionService.prototype.saveProjection,
    ],
    [
      'interest summary storage created projection',
      'mp.SavingsAccount.InterestTaxSummaryStorageCreated',
      mockInterestSummaryStorageProjectionService.prototype.saveProjection,
    ],
    [
      'scheduled transfer created projection',
      'mp.ScheduledTransfer.Created',
      mockScheduledTransferProjectionService.prototype.saveScheduledTransferProjection,
    ],
    [
      'scheduled transfer updated projection',
      'mp.ScheduledTransfer.Updated',
      mockScheduledTransferProjectionService.prototype.updateScheduledTransferProjection,
    ],
    [
      'scheduled transfer completed projection',
      'mp.ScheduledTransfer.Completed',
      mockScheduledTransferProjectionService.prototype.saveScheduledTransferStatusProjection,
    ],
    [
      'scheduled transfer cancelled projection',
      'mp.ScheduledTransfer.Cancelled',
      mockScheduledTransferProjectionService.prototype.saveScheduledTransferStatusProjection,
    ],
    [
      'scheduled transfer skipped projection',
      'mp.ScheduledTransfer.Skipped',
      mockScheduledTransferProjectionService.prototype.saveScheduledTransferSkippedProjection,
    ],
    [
      'scheduled transfer unskipped projection',
      'mp.ScheduledTransfer.UnSkipped',
      mockScheduledTransferProjectionService.prototype.updateScheduledTransferUnskippedProjection,
    ],
    [
      'transfer scheduled execution projection',
      'mp.ProjectionScheduledTransferExecution.create',
      mockTransferScheduledExecProjectionService.prototype.materialiseScheduledTransferExecution,
    ],
  ])('should be able to call %s handler', async (_, eventUri, expectedHandler) => {
    const events = [
      {
        body: JSON.stringify({
          'detail-type': eventUri,
          detail: {
            aggregateId: '01',
            payload: {},
          },
          uri: eventUri,
        }),
        receiptHandle: '',
      },
    ] as SQSRecord[];

    const data = events.map((e) => JSON.parse(e.body));
    const eventData = data[0];

    jest.spyOn(Logger, 'debug').mockReset();
    const spy = jest.spyOn(Logger, 'debug');

    await service.projectEvent(events);
    expect(spy).toHaveBeenCalled();
    expect(spy).toBeCalledWith(`projecting event - ${JSON.stringify(eventData)}`);
    expect(expectedHandler).toBeCalledTimes(1);
    expect(mockSend).toBeCalledTimes(1);
    expect(mockSend).toBeCalledWith({ QueueUrl: mockSqsUrl, ReceiptHandle: '' });
    spy.mockReset();
    expectedHandler.mockReset();
  });

  it('should throw no handler found error when no dto found', async () => {
    const event: SQSRecord[] = [
      {
        body: JSON.stringify({
          'detail-type': 'non-existing-uri',
          detail: {
            aggregateId: '01',
            payload: {},
          },
          uri: 'non-existing-uri',
        }),
        receiptHandle: '',
      } as SQSRecord,
    ];

    await expect(service.projectEvent(event)).rejects.toThrowError();
  });
});
