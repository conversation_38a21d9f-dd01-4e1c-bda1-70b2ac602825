import { debug, error, info, warn } from '@npco/component-bff-core/dist/utils/logger';

import { SQSClient, DeleteMessageCommand } from '@aws-sdk/client-sqs';
import type { SQSRecord } from 'aws-lambda';

import type { AccountStatementStorageProjectionService } from '../accountStatement/accountStatementStorage/accountStatementStorageProjectionService';
import { DebitCardAccountStatementStorage } from '../accountStatement/accountStatementStorage/projectionModel';
import type { AccountStatementTransactionProjectionService } from '../accountStatement/accountStatementTransaction/accountStatementTransactionProjectionService';
import { AccountStatementTransaction } from '../accountStatement/accountStatementTransaction/projectionModel';
import { SavingsAccount } from '../debitCardAccount/savingsAccount/projectionModel';
import type { SavingsAccountProjectionService } from '../debitCardAccount/savingsAccount/savingsAccountProjectionService';
import type { DebitCardAccountTransactionProjectionService } from '../debitCardAccountTransaction/projection/dcaTxnProjectionService';
import { DebitCardAccountTransaction } from '../debitCardAccountTransaction/projection/projectionModel';
import { SavingsAccountTransaction } from '../debitCardAccountTransaction/savingsAccountTransaction/projectionModel';
import type { SavingsAccountTransactionProjectionService } from '../debitCardAccountTransaction/savingsAccountTransaction/savingsAccountTransactionProjectionService';
import type { EnvironmentService } from '../environment/environmentService';
import type { InterestSummaryStorageProjectionService } from '../interestSummary/interestSummaryStorage/interestSummaryStorageProjectionService';
import { SavingsAccountInterestTaxSummaryStorage } from '../interestSummary/interestSummaryStorage/projectionModel';
import type { EntitySavingsAccountProductProjectionService } from '../savingsAccountProduct/entitySavingsAccountProductProjectionService';
import { SavingsAccountProduct } from '../savingsAccountProduct/projectionModel';
import type { SavingsAccountProductProjectionService } from '../savingsAccountProduct/savingsAccountProductProjectionService';
import { ScheduledTransferMappings } from '../scheduledTransfers/projectionModel';
import type { ScheduledTransferProjectionService } from '../scheduledTransfers/scheduledTransferProjectionService';
import type { TransferScheduledExecProjectionService } from '../scheduledTransfers/transferScheduledExecProjectionService';

import type { EventBridgeEventData, ProjectionDto } from './types';

export class ProjectionService {
  private readonly sqs: SQSClient;

  private readonly materialisationHandlers: {
    [key: string]: (handler: any) => Promise<void>;
  };

  constructor(
    private readonly envService: EnvironmentService,
    private readonly accountStatementTransactionProjectionService: AccountStatementTransactionProjectionService,
    private readonly accountStatementStorageProjectionService: AccountStatementStorageProjectionService,
    private readonly savingsAccountProjectionService: SavingsAccountProjectionService,
    private readonly savingsAccountTransactionProjectionService: SavingsAccountTransactionProjectionService,
    private readonly savingsAccountProductProjectionService: SavingsAccountProductProjectionService,
    private readonly entitySavingsAccountProductProjectionService: EntitySavingsAccountProductProjectionService,
    private readonly interestSummaryStorageProjectionService: InterestSummaryStorageProjectionService,
    private readonly scheduledTransferProjectionService: ScheduledTransferProjectionService,
    private readonly transferScheduledExecProjectionService: TransferScheduledExecProjectionService,
    private readonly debitCardAccountTransactionProjectionService: DebitCardAccountTransactionProjectionService,
  ) {
    this.sqs = new SQSClient({});

    // Initialize the materialisation handlers
    this.materialisationHandlers = {
      'Entity.SavingsAccountProductPromotionCreated':
        this.entitySavingsAccountProductProjectionService.saveProjection.bind(
          this.entitySavingsAccountProductProjectionService,
        ),
      'Entity.SavingsAccountProductPromotionUpdated':
        this.entitySavingsAccountProductProjectionService.saveProjection.bind(
          this.entitySavingsAccountProductProjectionService,
        ),
      'SavingsAccount.InterestTaxSummaryStorageCreated':
        this.interestSummaryStorageProjectionService.saveProjection.bind(this.interestSummaryStorageProjectionService),
      'ProjectionDebitCardAccountStatementTransaction.create':
        this.accountStatementTransactionProjectionService.saveDebitCardAccountStatmentProjection.bind(
          this.accountStatementTransactionProjectionService,
        ),
      'ProjectionSavingsAccountStatementTransaction.create':
        this.accountStatementTransactionProjectionService.saveSavingsAccountStatmentProjection.bind(
          this.accountStatementTransactionProjectionService,
        ),
      'DebitCardAccount.StatementStorageCreated': this.accountStatementStorageProjectionService.saveProjection.bind(
        this.accountStatementStorageProjectionService,
      ),
      'ProjectionSavingsAccount.create': this.savingsAccountProjectionService.saveProjection.bind(
        this.savingsAccountProjectionService,
      ),
      'ProjectionSavingsAccountTransaction.create': this.savingsAccountTransactionProjectionService.saveProjection.bind(
        this.savingsAccountTransactionProjectionService,
      ),
      'ProjectionSavingsAccountTransaction.update': this.savingsAccountTransactionProjectionService.saveProjection.bind(
        this.savingsAccountTransactionProjectionService,
      ),
      'ProjectionSavingsAccountProduct.create': this.savingsAccountProductProjectionService.saveProjection.bind(
        this.savingsAccountProductProjectionService,
      ),
      'ProjectionSavingsAccountProduct.update': this.savingsAccountProductProjectionService.saveProjection.bind(
        this.savingsAccountProductProjectionService,
      ),
      'ScheduledTransfer.Created': this.scheduledTransferProjectionService.saveScheduledTransferProjection.bind(
        this.scheduledTransferProjectionService,
      ),
      'ScheduledTransfer.Updated': this.scheduledTransferProjectionService.updateScheduledTransferProjection.bind(
        this.scheduledTransferProjectionService,
      ),
      'ScheduledTransfer.Completed': this.scheduledTransferProjectionService.saveScheduledTransferStatusProjection.bind(
        this.scheduledTransferProjectionService,
      ),
      'ScheduledTransfer.Cancelled': this.scheduledTransferProjectionService.saveScheduledTransferStatusProjection.bind(
        this.scheduledTransferProjectionService,
      ),
      'ScheduledTransfer.Skipped': this.scheduledTransferProjectionService.saveScheduledTransferSkippedProjection.bind(
        this.scheduledTransferProjectionService,
      ),
      'ScheduledTransfer.UnSkipped':
        this.scheduledTransferProjectionService.updateScheduledTransferUnskippedProjection.bind(
          this.scheduledTransferProjectionService,
        ),
      'ProjectionScheduledTransferExecution.create':
        this.transferScheduledExecProjectionService.materialiseScheduledTransferExecution.bind(
          this.transferScheduledExecProjectionService,
        ),
      'ProjectionDebitCardAccountTransaction.create':
        this.debitCardAccountTransactionProjectionService.saveProjection.bind(
          this.debitCardAccountTransactionProjectionService,
        ),
      'ProjectionDebitCardAccountTransaction.update':
        this.debitCardAccountTransactionProjectionService.saveProjection.bind(
          this.debitCardAccountTransactionProjectionService,
        ),
    };
  }

  async projectEvent(event: SQSRecord[]) {
    const projectionLookups = {
      ...AccountStatementTransaction,
      ...DebitCardAccountStatementStorage,
      ...SavingsAccountProduct,
      ...SavingsAccount,
      ...DebitCardAccountTransaction,
      ...SavingsAccountTransaction,
      ...SavingsAccountInterestTaxSummaryStorage,
      ...ScheduledTransferMappings,
    };

    const data: EventBridgeEventData[] = event.map((e) => JSON.parse(e.body));
    const errorEvents: any = [];

    await Promise.all(
      data.map(async (eventData, index) => {
        try {
          debug(`projecting event - ${JSON.stringify(eventData)}`);
          const { detail } = eventData;
          const detailType = eventData['detail-type'];
          const { aggregateId, payload, version, createdTimestamp } = detail;
          const [, aggregate, aggregateEvent] = detailType.split('.');
          const deconstructedUri = `${aggregate}.${aggregateEvent}`;
          const dto = projectionLookups[deconstructedUri];
          if (!dto) {
            error(`Handler not found ${detailType} ${JSON.stringify(payload, null, 2)}`);
            throw new Error('Handler not found');
          }
          const dtoEvent: ProjectionDto<any> = dto(payload, aggregateId, detailType, createdTimestamp, version);
          info(`projectionService:projectEvent -> get dtoEvent ${JSON.stringify(dtoEvent)}`, dtoEvent.aggregateId);
          await this.callMaterialisationFunction(deconstructedUri, dtoEvent);
          await this.deleteMessage(event[index], this.envService.projectionSqsUrl);
        } catch (err: any) {
          warn(`get error: ${err}`);
          warn('Failed to project events, need to retry.');
          errorEvents.push(eventData);
          error(err.message, eventData.aggregateId);
        }
      }),
    );
    this.checkErrors(errorEvents);
  }

  private readonly callMaterialisationFunction = async (uri: string, dtoEvent: ProjectionDto<any>) => {
    const handler = this.materialisationHandlers[uri];
    if (!handler) {
      error(`Materialisation function not found for URI: ${uri}`);
      throw new Error('Materialisation function not found');
    }

    return handler(dtoEvent.payload);
  };

  private readonly deleteMessage = async (event: any, sqsQueue: string) => {
    const output = await this.sqs.send(
      new DeleteMessageCommand({ QueueUrl: sqsQueue, ReceiptHandle: event.receiptHandle }),
    );
    info(`delete message from sqs ${JSON.stringify(event)} ${JSON.stringify(output)}`);
  };

  private readonly checkErrors = (
    /* istanbul ignore next: difficult to test the default value */ errorEvents: any = [],
  ) => {
    info(`errorlength: ${errorEvents.length}`);
    if (errorEvents.length > 0) {
      const errorMessage = `process event ${JSON.stringify(errorEvents)} failed.`;
      error(errorMessage);
      throw new Error(errorMessage);
    }
  };
}
