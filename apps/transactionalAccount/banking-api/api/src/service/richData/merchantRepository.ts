import type { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';
import { DbRecordType } from '@npco/component-dto-core/dist';
import type { Merchant } from '@npco/component-dto-richdata/dist';

import type { AttributeMap } from '../dynamodb/utils';

import { convertDbItemToMerchant, merchantIconConversion } from './utils';

export class MerchantRepository {
  constructor(private readonly dynamodbService: DynamodbService, private readonly tableName: string) {}

  getMerchantDbItem = async (merchantId: string, filterDeleted = false): Promise<AttributeMap | null> => {
    const item = await this.dynamodbService.queryIdByType('', merchantId, DbRecordType.MERCHANT, {
      ignoreEntity: true,
      filterDeleted,
      tableName: this.tableName,
    });

    if (item.Items && item.Items.length > 0) {
      return item.Items[0] as AttributeMap;
    }

    return null;
  };

  getMerchant = async (merchantId: string): Promise<Merchant | null> => {
    const merchantRecord = await this.getMerchantDbItem(merchantId);
    return merchantRecord ? convertDbItemToMerchant(merchantRecord) : null;
  };

  saveMerchant = async (merchant: Merchant) => {
    await this.dynamodbService.saveConditionallyByUpdatedTime(
      this.tableName,
      { id: merchant.id, type: DbRecordType.MERCHANT },
      {
        ...merchantIconConversion(merchant),
        updatedAt: new Date(Number(merchant.updatedTime)).toISOString(),
        lastUpdatedAt: new Date().toISOString(),
      },
    );
  };
}
