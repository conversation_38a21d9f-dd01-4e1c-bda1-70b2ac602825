import { MerchantDetailsSource } from '@npco/component-dto-richdata/dist';

// eslint-disable-next-line import/no-extraneous-dependencies
import { instance, mock, when } from 'ts-mockito';
import { v4 } from 'uuid';

import { EnvironmentService } from '../environment/environmentService';

import { MerchantRepository } from './merchantRepository';
import { MerchantService } from './merchantService';
import { merchantAddressToString } from './utils';

describe('Rich Data Service test suite', () => {
  const mockMerchantRepository = mock(MerchantRepository);

  const envService = new EnvironmentService();
  let merchantService: MerchantService;

  const merchant = {
    name: 'Test Merchant',
    address: {
      street1: 'Test Street',
      street2: 'Test Street 2',
      suburb: 'Test Suburb',
      state: 'VIC',
      postcode: '3000',
      country: 'Australia',
    },
    icon: {
      image: 'i',
      letter: 'l',
      colour: 'c',
    },
    url: 'url',
    phone: 'phone',
    email: 'email',
    location: '0,0',
    locationAccuracy: 0.1,
    hours: 'hours',
    rating: 5,
    dataSource: MerchantDetailsSource.LWC,
    updatedTime: Date.now(),
  };

  beforeEach(() => {
    merchantService = new MerchantService(envService, {} as any);
    merchantService.repo = instance(mockMerchantRepository);
  });

  describe('should handle getMerchant and getMerchantDetails', () => {
    it('if the value existed', async () => {
      const id = v4();
      when(mockMerchantRepository.getMerchant).thenReturn(() => Promise.resolve({ id, ...merchant } as any));
      const res = (await merchantService.getMerchant(id)) as any;
      expect(res).toStrictEqual({
        id,
        ...merchant,
      });
      const res2 = (await merchantService.getMerchantDetails(id)) as any;
      const expectMerchantDetails = {
        ...merchant,
        id,
        address: merchant.address,
        addressUnformatted: merchantAddressToString(merchant.address),
      };
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      delete expectMerchantDetails.updatedTime;
      expect(res2).toStrictEqual(expectMerchantDetails);
    });

    it('if the value is not existed', async () => {
      const id = v4();
      when(mockMerchantRepository.getMerchant).thenReturn(() => Promise.resolve(null as any));
      const res = (await merchantService.getMerchant(id)) as any;
      expect(res).toBeNull();
      await expect(merchantService.getMerchantDetails(id)).rejects.toThrowError(`Merchant not found. ${id}`);
    });
  });
});
