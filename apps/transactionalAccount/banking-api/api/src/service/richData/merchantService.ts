import type { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';
import { NotFoundError } from '@npco/component-bff-core/dist/error';
import type { MerchantDetails, Merchant } from '@npco/component-dto-richdata/dist';

import type { EnvironmentService } from '../environment/environmentService';

import { MerchantRepository } from './merchantRepository';
import { convertMerchantToMerchantDetails } from './utils';

export class MerchantService {
  repo: MerchantRepository;

  constructor(private readonly envService: EnvironmentService, private readonly dynamodbService: DynamodbService) {
    this.repo = new MerchantRepository(this.dynamodbService, this.envService.merchantTableName);
  }

  getMerchant = (merchantId: string): Promise<Merchant | null> => {
    return this.repo.getMerchant(merchantId);
  };

  getMerchantDetails = async (merchantId: string): Promise<MerchantDetails> => {
    const merchant = await this.getMerchant(merchantId);
    return merchant
      ? convertMerchantToMerchantDetails(merchant)
      : Promise.reject(new NotFoundError('Merchant not found.', merchantId)); // NOSONAR
  };

  saveMerchant = (merchant: Merchant) => {
    return this.repo.saveMerchant(merchant);
  };
}
