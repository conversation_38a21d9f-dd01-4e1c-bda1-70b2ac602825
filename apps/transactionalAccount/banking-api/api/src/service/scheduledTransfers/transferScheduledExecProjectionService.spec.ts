import { DynamodbService } from '@npco/component-bff-core/dist/dynamodb';
import type { TransferScheduleExecution } from '@npco/component-dto-scheduled-transfer/dist';

import { v4 } from 'uuid';

import { dynamodbClient } from '../dynamodb/client';

import { TransferScheduledExecProjectionService } from './transferScheduledExecProjectionService';

const mockSaveScheduledTransferExecution = jest.fn();
jest.mock('./transferScheduledExecRepository', () => ({
  TransferScheduledExecRepository: jest.fn().mockImplementation(() => ({
    saveTransferScheduledExecution: (...args: any[]) => mockSaveScheduledTransferExecution(...args),
  })),
}));

describe('TransferScheduledExecutionService test suite', () => {
  const tableName = 'Entities';
  let service: TransferScheduledExecProjectionService;
  const env = { componentTableName: tableName, entityGsi: 'entityGsi' } as any;
  const dynamodbService = new DynamodbService(env, dynamodbClient);
  beforeEach(() => {
    service = new TransferScheduledExecProjectionService(env, dynamodbService);
    mockSaveScheduledTransferExecution.mockReset();
  });
  it('should materialise transfer scheduled exec event', async () => {
    const data = {
      id: v4(),
      updatedTime: Date.now(),
    };
    await service.materialiseScheduledTransferExecution(data as TransferScheduleExecution);
    expect(mockSaveScheduledTransferExecution).toHaveBeenCalledWith(data);
  });
});
