import type { DynamodbService } from '@npco/component-bff-core/dist/dynamodb';
import type { TransferScheduleExecution } from '@npco/component-dto-scheduled-transfer/dist';

import type { EnvironmentService } from '../environment/environmentService';

import { TransferScheduledExecRepository } from './transferScheduledExecRepository';

export class TransferScheduledExecProjectionService {
  private readonly repo: TransferScheduledExecRepository;

  constructor(envService: EnvironmentService, dynamodbService: DynamodbService) {
    this.repo = new TransferScheduledExecRepository(envService, dynamodbService);
  }

  async materialiseScheduledTransferExecution(payload: TransferScheduleExecution) {
    await this.repo.saveTransferScheduledExecution(payload);
  }
}
