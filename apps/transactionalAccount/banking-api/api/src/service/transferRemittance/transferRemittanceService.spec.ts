import { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';
import { ISO4217 } from '@npco/component-dto-core/dist/types';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction/dist/types';

import { v4 } from 'uuid';

import { CustomerService } from '../customer/customerService';
import { createCustomer } from '../customer/testing/utils';
import { DebitCardAccountQueryService } from '../debitCardAccount/debitCardAccountQueryService';
import { DebitCardAccountTransactionRepository } from '../debitCardAccountTransaction/debitCardAccountTransactionRepository';
import { createTransaction } from '../debitCardAccountTransaction/transactionDefaultCategory/testUtils';
import { dynamodbClient } from '../dynamodb/client';
import type { EntityQueryService } from '../entity/entityQueryService';
import type { EnvironmentService } from '../environment/environmentService';

import { ReceiptNotificationType, TransferRemittanceErrorEnum } from './models/graphql/types';
import { TransferRemittanceRepository } from './transferRemittanceRepository';
import { TransferRemittanceService } from './transferRemittanceService';

jest.mock('../customer/customerService');
const mockCustomerService = CustomerService as jest.MockedClass<typeof CustomerService>;

const envService = {
  componentTableName: 'Entities',
} as unknown as jest.Mocked<EnvironmentService>;

const mockGetEntity = jest.fn();
const mockEntityQueryService = {
  getEntity: mockGetEntity,
} as unknown as jest.Mocked<EntityQueryService>;

const mockSqsSend = jest.fn();
jest.mock('@aws-sdk/client-sqs', () => {
  return {
    SQSClient: jest.fn(() => ({
      send: mockSqsSend,
    })),
    SendMessageCommand: jest.fn((result) => result),
  };
});

const mockSendSms = jest.fn().mockResolvedValue(null);
jest.mock('@npco/component-bff-core/dist/notification/smsService', () => ({
  SmsService: jest.fn().mockImplementation(() => ({ sendSms: mockSendSms })),
}));

const mockSendEmail = jest.fn().mockResolvedValue(null);
jest.mock('@npco/component-bff-core/dist/notification/emailService', () => ({
  EmailService: jest.fn().mockImplementation(() => ({ sendRawEmail: mockSendEmail })),
}));

jest.mock('../debitCardAccount/debitCardAccountQueryService');
const mockDCAQuery = DebitCardAccountQueryService as jest.MockedClass<typeof DebitCardAccountQueryService>;

const entityUuid = v4();
const customer = createCustomer({
  email: '<EMAIL>',
  phone: '**********',
  id: v4(),
  entityUuid,
});

describe('TransferRemittanceService', () => {
  const env = process.env;

  describe('sendTransferRemittanceNotification', () => {
    let repo: DebitCardAccountTransactionRepository;
    let dynamodbService: DynamodbService;
    let transferRemittanceService: TransferRemittanceService;
    const customerId = v4();

    beforeAll(async () => {
      process.env = {
        ...env,
        SOURCE_EMAIL_ADDRESS: '<EMAIL>',
        TRANSFER_REMITTANCE_SUCCESS_QUEUE_URL: 'successQueue',
        TRANSFER_REMITTANCE_FAILURE_QUEUE_URL: 'failureQueue',
      };
      dynamodbService = new DynamodbService(envService, dynamodbClient);
      transferRemittanceService = new TransferRemittanceService(envService, dynamodbService, mockEntityQueryService);
      repo = new DebitCardAccountTransactionRepository(dynamodbService, envService, mockEntityQueryService);
    });

    beforeEach(() => {
      mockSqsSend.mockClear();
      jest.clearAllMocks();
    });

    it('should be able to send SMS transfer remittance notification to success queue and return', async () => {
      mockCustomerService.prototype.getCustomer.mockResolvedValueOnce(customer);
      const transactionId = v4();
      mockSqsSend.mockResolvedValueOnce({});
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.SMS,
        recipientMobile: '0000000',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
        senderMobileNumber: '**********',
      };
      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
          amount: {
            value: '100',
            currency: ISO4217.AUD,
          },
          payeeDetails: {
            debitCardAccountUuid: v4(),
          },
        },
      });
      mockDCAQuery.prototype.getAccountById.mockResolvedValueOnce({
        accountDetails: {
          name: 'Bens Farming corporation',
          account: '154 165 178',
          bsb: '123-123-234',
        },
      } as any);
      mockGetEntity.mockResolvedValue({ name: 'Zeller' });
      jest.spyOn(transferRemittanceService, 'sendToSqsQueue');

      await repo.saveDebitCardTransaction(mockTransaction);
      const response = await transferRemittanceService.sendTransferRemittanceNotification(input);
      expect(transferRemittanceService.sendToSqsQueue).toHaveBeenCalledTimes(1);
      expect(mockSqsSend).toHaveBeenCalledTimes(1);
      expect(transferRemittanceService.sendToSqsQueue).toHaveBeenCalledTimes(1);
      expect(transferRemittanceService.sendToSqsQueue).toHaveBeenCalledWith({
        dcaTransactionUuid: transactionId,
        queueUrl: 'successQueue',
        type: ReceiptNotificationType.SMS,
        mobileNumber: '0000000',
        senderMobileNumber: '**********',
        ccEmail: customer.email,
        isSenderNotified: input.isSenderNotified,
        entityUuid: input.entityUuid,
        messageDetails: {
          businessName: 'Zeller',
          amount: '$1.00',
          accountEnding: '*178',
        },
      });
      expect(response).toEqual({ isTransferRemittanceQueued: true });
    });

    it('should be able to send EMAIL transfer remittance notification to success queue and return', async () => {
      const transactionId = v4();
      mockCustomerService.prototype.getCustomer.mockResolvedValueOnce(customer);
      mockSqsSend.mockResolvedValueOnce({});
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.EMAIL,
        recipientEmail: '<EMAIL>',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
      };
      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
          amount: {
            value: '100',
            currency: ISO4217.AUD,
          },
          payeeDetails: {
            debitCardAccountUuid: v4(),
          },
        },
      });
      mockDCAQuery.prototype.getAccountById.mockResolvedValueOnce({
        accountDetails: {
          name: 'Bens Farming corporation',
          account: '154 165 178',
          bsb: '123-123-234',
        },
      } as any);
      mockGetEntity.mockResolvedValue({ name: 'Zeller' });
      jest.spyOn(transferRemittanceService, 'sendToSqsQueue');

      await repo.saveDebitCardTransaction(mockTransaction);
      const response = await transferRemittanceService.sendTransferRemittanceNotification(input);
      expect(mockSqsSend).toHaveBeenCalledTimes(1);
      expect(transferRemittanceService.sendToSqsQueue).toHaveBeenCalledTimes(1);
      expect(transferRemittanceService.sendToSqsQueue).toHaveBeenCalledWith({
        dcaTransactionUuid: transactionId,
        queueUrl: 'successQueue',
        type: ReceiptNotificationType.EMAIL,
        email: '<EMAIL>',
        senderMobileNumber: '**********',
        ccEmail: customer.email,
        entityUuid: input.entityUuid,
        isSenderNotified: input.isSenderNotified,
        messageDetails: {
          businessName: 'Zeller',
          amount: '$1.00',
          accountEnding: '*178',
        },
      });
      expect(response).toEqual({ isTransferRemittanceQueued: true });
    });

    it('should be able to send transfer remittance notification to success queue without sender details and return', async () => {
      const transactionId = v4();
      mockCustomerService.prototype.getCustomer.mockResolvedValueOnce(customer);
      mockSqsSend.mockResolvedValueOnce({});
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.SMS,
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: false,
        recipientMobile: '0000000',
      };
      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
          amount: {
            value: '100',
            currency: ISO4217.AUD,
          },
          payeeDetails: {
            debitCardAccountUuid: v4(),
          },
        },
      });
      mockDCAQuery.prototype.getAccountById.mockResolvedValueOnce({
        accountDetails: {
          name: 'Bens Farming corporation',
          account: '154 165 178',
          bsb: '123-123-234',
        },
      } as any);
      mockGetEntity.mockResolvedValue({ name: 'Zeller' });
      jest.spyOn(transferRemittanceService, 'sendToSqsQueue');

      await repo.saveDebitCardTransaction(mockTransaction);
      const response = await transferRemittanceService.sendTransferRemittanceNotification(input);
      expect(mockSqsSend).toHaveBeenCalledTimes(1);
      expect(transferRemittanceService.sendToSqsQueue).toHaveBeenCalledTimes(1);
      expect(transferRemittanceService.sendToSqsQueue).toHaveBeenCalledWith({
        dcaTransactionUuid: transactionId,
        queueUrl: 'successQueue',
        type: ReceiptNotificationType.SMS,
        mobileNumber: '0000000',
        entityUuid: input.entityUuid,
        isSenderNotified: input.isSenderNotified,
        messageDetails: {
          businessName: 'Zeller',
          amount: '$1.00',
          accountEnding: '*178',
        },
      });
      expect(response).toEqual({ isTransferRemittanceQueued: true });
    });

    it('should be able to send transfer remittance notification into failure queue and return', async () => {
      const transactionId = v4();
      mockCustomerService.prototype.getCustomer.mockResolvedValueOnce(customer);
      mockSqsSend.mockResolvedValueOnce({});
      const input = {
        dcaTransactionUuid: v4(),
        type: ReceiptNotificationType.SMS,
        recipientMobile: '**********',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: false,
      };
      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
        },
      });

      jest.spyOn(transferRemittanceService, 'sendToSqsQueue');
      await repo.saveDebitCardTransaction(mockTransaction);
      const response = await transferRemittanceService.sendTransferRemittanceNotification(input);
      expect(mockSqsSend).toHaveBeenCalledTimes(1);
      expect(transferRemittanceService.sendToSqsQueue).toHaveBeenCalledTimes(1);
      expect(transferRemittanceService.sendToSqsQueue).toHaveBeenCalledWith({
        dcaTransactionUuid: input.dcaTransactionUuid,
        queueUrl: 'failureQueue',
        type: ReceiptNotificationType.SMS,
        transaction: undefined,
        isSenderNotified: input.isSenderNotified,
        entityUuid: input.entityUuid,
        customerUuid: customerId,
        mobileNumber: input.recipientMobile,
      });
      expect(response).toEqual({ isTransferRemittanceQueued: true });
    });

    it('should be able to return if the transaction is retried from the failure queue', async () => {
      const transactionId = v4();
      mockCustomerService.prototype.getCustomer.mockResolvedValueOnce(customer);
      mockSqsSend.mockResolvedValueOnce({});
      const input = {
        dcaTransactionUuid: v4(),
        type: ReceiptNotificationType.SMS,
        recipientMobile: '**********',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: false,
      };
      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
        },
      });

      jest.spyOn(transferRemittanceService, 'sendToSqsQueue');
      await repo.saveDebitCardTransaction(mockTransaction);
      const response = await transferRemittanceService.sendTransferRemittanceNotification(input, true);
      expect(mockSqsSend).not.toHaveBeenCalledTimes(1); // should not put into queue again
      expect(response).toEqual({
        isTransferRemittanceQueued: true,
        error: TransferRemittanceErrorEnum.TRANSACTION_NOT_FOUND,
      });
    });
  });

  describe('handle failure queue', () => {
    let dynamodbService: DynamodbService;
    let transferRemittanceService: TransferRemittanceService;
    let repo: DebitCardAccountTransactionRepository;

    beforeAll(async () => {
      process.env = {
        ...env,
        SOURCE_EMAIL_ADDRESS: '<EMAIL>',
        TRANSFER_REMITTANCE_SUCCESS_QUEUE_URL: 'successQueue',
        TRANSFER_REMITTANCE_FAILURE_QUEUE_URL: 'failureQueue',
      };
      dynamodbService = new DynamodbService(envService, dynamodbClient);
      transferRemittanceService = new TransferRemittanceService(envService, dynamodbService, mockEntityQueryService);
      repo = new DebitCardAccountTransactionRepository(dynamodbService, envService, mockEntityQueryService);
    });

    beforeEach(() => {
      mockSqsSend.mockClear();
      jest.clearAllMocks();
    });

    it('should be able to retry and push into success queue', async () => {
      mockCustomerService.prototype.getCustomer.mockResolvedValueOnce(customer);
      const transactionId = v4();
      const customerId = v4();
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.EMAIL,
        recipientEmail: '<EMAIL>',
        recipientMobile: '**********',
        ccEmail: '<EMAIL>',
        isSenderNotified: true,
        entityUuid,
        customerUuid: customerId,
      };
      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
          amount: {
            value: '100',
            currency: ISO4217.AUD,
          },
          payeeDetails: {
            debitCardAccountUuid: v4(),
          },
        },
      });
      mockDCAQuery.prototype.getAccountById.mockResolvedValueOnce({
        accountDetails: {
          name: 'Bens Farming corporation',
          account: '154 165 178',
          bsb: '123-123-234',
        },
      } as any);
      mockGetEntity.mockResolvedValue({ name: 'Zeller' });
      jest.spyOn(transferRemittanceService, 'sendToSqsQueue');

      await repo.saveDebitCardTransaction(mockTransaction);
      await transferRemittanceService.handleFailureQueue(input);
      expect(transferRemittanceService.sendToSqsQueue).toHaveBeenCalledTimes(1);
      expect(transferRemittanceService.sendToSqsQueue).toHaveBeenCalledWith({
        dcaTransactionUuid: transactionId,
        queueUrl: 'successQueue',
        type: ReceiptNotificationType.EMAIL,
        mobileNumber: input.recipientMobile,
        email: input.recipientEmail,
        senderMobileNumber: '**********',
        ccEmail: customer.email,
        entityUuid: input.entityUuid,
        isSenderNotified: input.isSenderNotified,
        messageDetails: {
          businessName: 'Zeller',
          amount: '$1.00',
          accountEnding: '*178',
        },
      });
    });

    it('should throw if input is failed to validate', async () => {
      mockCustomerService.prototype.getCustomer.mockResolvedValueOnce(null);
      const transactionId = v4();
      const customerId = v4();
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.EMAIL,
        recipientEmail: '<EMAIL>',
        recipientMobile: '**********',
        ccEmail: '<EMAIL>',
        isSenderNotified: true,
        entityUuid,
        customerUuid: customerId,
      };

      jest.spyOn(transferRemittanceService, 'sendToSqsQueue');
      expect(transferRemittanceService.sendToSqsQueue).not.toHaveBeenCalled();
      await expect(async () => {
        await transferRemittanceService.handleFailureQueue(input);
      }).rejects.toThrow();
    });
  });

  describe('sendSmsNotification', () => {
    let dynamodbService: DynamodbService;
    let transferRemittanceService: TransferRemittanceService;

    beforeAll(async () => {
      process.env = {
        ...env,
        SOURCE_EMAIL_ADDRESS: '<EMAIL>',
        TRANSFER_REMITTANCE_SUCCESS_QUEUE_URL: 'successQueue',
        TRANSFER_REMITTANCE_FAILURE_QUEUE_URL: 'failureQueue',
      };
      dynamodbService = new DynamodbService(envService, dynamodbClient);
      transferRemittanceService = new TransferRemittanceService(envService, dynamodbService, mockEntityQueryService);
    });

    it('should be able to send sms to recipient and sender', async () => {
      const input = {
        dcaTransactionUuid: v4(),
        type: ReceiptNotificationType.SMS,
        mobileNumber: '**********',
        senderMobileNumber: '*********',
        messageDetails: {
          businessName: 'business',
          amount: '$100',
          accountEnding: '*234',
        },
      };

      await transferRemittanceService.sendSmsNotification(input);
      expect(mockSendSms).toHaveBeenCalledTimes(2);
    });

    it('should only send one sms if sender mobile number is not in input', async () => {
      mockSendSms.mockClear();
      const input = {
        dcaTransactionUuid: v4(),
        type: ReceiptNotificationType.SMS,
        mobileNumber: '**********',
        messageDetails: {
          businessName: 'business',
          amount: '$100',
          accountEnding: '*234',
        },
      };

      await transferRemittanceService.sendSmsNotification(input);
      expect(mockSendSms).toHaveBeenCalledTimes(1);
      expect(mockSendSms).toHaveBeenCalledWith(
        '**********',
        `You've been paid $100 to your account ending in *234 from business via Zeller.`,
      );
    });
  });

  describe('sendEmailNotification', () => {
    let dynamodbService: DynamodbService;
    let transferRemittanceService: TransferRemittanceService;
    let repo: DebitCardAccountTransactionRepository;

    beforeAll(async () => {
      process.env = {
        ...env,
        SOURCE_EMAIL_ADDRESS: '<EMAIL>',
        TRANSFER_REMITTANCE_SUCCESS_QUEUE_URL: 'successQueue',
        TRANSFER_REMITTANCE_FAILURE_QUEUE_URL: 'failureQueue',
      };
      dynamodbService = new DynamodbService(envService, dynamodbClient);
      transferRemittanceService = new TransferRemittanceService(envService, dynamodbService, mockEntityQueryService);
      repo = new DebitCardAccountTransactionRepository(dynamodbService, envService, mockEntityQueryService);
    });

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should be able to send email to recipient and cc sender ', async () => {
      const input = {
        dcaTransactionUuid: v4(),
        type: ReceiptNotificationType.EMAIL,
        email: '<EMAIL>',
        ccEmail: '<EMAIL>',
        entityUuid: v4(),
        messageDetails: {
          businessName: 'business',
          amount: '$100',
          accountEnding: '*234',
        },
      };

      const mockTransaction = createTransaction({
        id: input.dcaTransactionUuid,
        transactionType: DebitCardTransactionTypeV2.BPAY_OUT,
        overrides: {
          entityUuid: input.entityUuid,
          referencePayee: 'For Shed repairs',
          updatedTime: *************,
          payeeDetails: {
            bpayDetails: {
              crn: '*********',
              billerCode: '123456',
              billerName: 'Jims Mowing Pty Ltd',
              nickname: 'Jims Mowing',
            },
          },
          payerDetails: {
            debitCardAccountUuid: v4(),
          },
          amount: {
            value: '10050',
            currency: ISO4217.AUD,
          },
        },
      });
      await repo.saveDebitCardTransaction(mockTransaction);
      mockDCAQuery.prototype.getAccountById.mockResolvedValueOnce({
        accountDetails: {
          name: 'Bens Farming corporation',
          account: '*********',
          bsb: '123123',
        },
      } as any);
      mockGetEntity.mockResolvedValue({ name: 'Zeller' });
      await transferRemittanceService.sendEmailNotification(input);
      expect(mockSendEmail).toHaveBeenCalledTimes(1);
    });

    it('should not add cc email if not in input', async () => {
      const input = {
        dcaTransactionUuid: v4(),
        type: ReceiptNotificationType.EMAIL,
        email: '<EMAIL>',
        entityUuid: v4(),
        messageDetails: {
          businessName: 'business',
          amount: '$100',
          accountEnding: '*234',
        },
      };

      const mockTransaction = createTransaction({
        id: input.dcaTransactionUuid,
        transactionType: DebitCardTransactionTypeV2.BPAY_OUT,
        overrides: {
          entityUuid: input.entityUuid,
          referencePayee: 'For Shed repairs',
          updatedTime: *************,
          payeeDetails: {
            bpayDetails: {
              crn: '*********',
              billerCode: '123456',
              billerName: 'Jims Mowing Pty Ltd',
              nickname: 'Jims Mowing',
            },
          },
          payerDetails: {
            debitCardAccountUuid: v4(),
          },
          amount: {
            value: '10050',
            currency: ISO4217.AUD,
          },
        },
      });
      await repo.saveDebitCardTransaction(mockTransaction);
      mockDCAQuery.prototype.getAccountById.mockResolvedValueOnce({
        accountDetails: {
          name: 'Bens Farming corporation',
          account: '*********',
          bsb: '123123',
        },
      } as any);
      mockGetEntity.mockResolvedValue({ name: 'Zeller' });

      await transferRemittanceService.sendEmailNotification(input);
      expect(mockSendEmail).toHaveBeenCalledTimes(1);
    });
  });

  describe('sendToSqsQueue', () => {
    let dynamodbService: DynamodbService;
    let transferRemittanceService: TransferRemittanceService;

    beforeAll(async () => {
      mockSqsSend.mockClear();
      dynamodbService = new DynamodbService(envService, dynamodbClient);
      transferRemittanceService = new TransferRemittanceService(envService, dynamodbService, mockEntityQueryService);
    });

    it('should be able to send sqs message', async () => {
      const input = {
        dcaTransactionUuid: v4(),
        type: ReceiptNotificationType.SMS,
        isSenderNotified: true,
        queueUrl: 'successQueue',
        mobileNumber: '**********',
        senderMobileNumber: '*********',
        email: '<EMAIL>',
        ccEmail: '<EMAIL>',
        entityUuid: v4(),
        customerUuid: v4(),
        messageDetails: {
          businessName: 'business',
          amount: '$100',
          accountEnding: '*234',
        },
      };

      await transferRemittanceService.sendToSqsQueue(input);
      expect(mockSqsSend).toHaveBeenCalledTimes(1);
      expect(mockSqsSend).toHaveBeenCalledWith({
        QueueUrl: 'successQueue',
        MessageDeduplicationId: expect.any(String),
        MessageGroupId: expect.any(String),
        MessageBody: JSON.stringify({
          dcaTransactionUuid: input.dcaTransactionUuid,
          type: input.type,
          mobileNumber: input.mobileNumber,
          senderMobileNumber: input.senderMobileNumber,
          email: input.email,
          ccEmail: input.ccEmail,
          isSenderNotified: input.isSenderNotified,
          entityUuid: input.entityUuid,
          customerUuid: input.customerUuid,
          messageDetails: input.messageDetails,
        }),
      });
    });

    it('should be able to send sqs message without the details from the input', async () => {
      mockSqsSend.mockClear();
      const input = {
        dcaTransactionUuid: v4(),
        type: ReceiptNotificationType.SMS,
        isSenderNotified: true,
        queueUrl: 'successQueue',
      };

      await transferRemittanceService.sendToSqsQueue(input);
      expect(mockSqsSend).toHaveBeenCalledTimes(1);
      expect(mockSqsSend).toHaveBeenCalledWith({
        QueueUrl: 'successQueue',
        MessageGroupId: expect.any(String),
        MessageDeduplicationId: expect.any(String),
        MessageBody: JSON.stringify({
          dcaTransactionUuid: input.dcaTransactionUuid,
          type: input.type,
          isSenderNotified: input.isSenderNotified,
        }),
      });
    });
  });

  describe('transfer remittance validations', () => {
    let repo: DebitCardAccountTransactionRepository;
    let transferRemittanceRepo: TransferRemittanceRepository;
    let dynamodbService: DynamodbService;
    let transferRemittanceService: TransferRemittanceService;
    const customerId = v4();

    beforeAll(async () => {
      process.env = {
        ...env,
        SOURCE_EMAIL_ADDRESS: '<EMAIL>',
        TRANSFER_REMITTANCE_SUCCESS_QUEUE_URL: 'successQueue',
        TRANSFER_REMITTANCE_FAILURE_QUEUE_URL: 'failureQueue',
      };
      dynamodbService = new DynamodbService(envService, dynamodbClient);
      transferRemittanceService = new TransferRemittanceService(envService, dynamodbService, mockEntityQueryService);
      repo = new DebitCardAccountTransactionRepository(dynamodbService, envService, mockEntityQueryService);
      transferRemittanceRepo = new TransferRemittanceRepository(envService, dynamodbService);
    });

    beforeEach(() => {
      mockSqsSend.mockClear();
      jest.clearAllMocks();
    });

    it('should return MOBILE_NOT_FOUND if type is SMS and number is not provided', async () => {
      const input = {
        dcaTransactionUuid: v4(),
        type: ReceiptNotificationType.SMS,
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
      };

      const response = await transferRemittanceService.sendTransferRemittanceNotification(input);
      expect(response).toEqual({
        isTransferRemittanceQueued: false,
        error: TransferRemittanceErrorEnum.RECIPIENT_MOBILE_NOT_FOUND,
      });
    });

    it('should return EMAIL_NOT_FOUND if type is EMAIL and email is not provided', async () => {
      const input = {
        dcaTransactionUuid: v4(),
        type: ReceiptNotificationType.EMAIL,
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
      };

      const response = await transferRemittanceService.sendTransferRemittanceNotification(input);
      expect(response).toEqual({
        isTransferRemittanceQueued: false,
        error: TransferRemittanceErrorEnum.RECIPIENT_EMAIL_NOT_FOUND,
      });
    });

    it('should return TOO_MANY_REQUESTS if more than 5 attempts are made', async () => {
      const transactionId = v4();
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.EMAIL,
        recipientEmail: '<EMAIL>',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
      };
      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
        },
      });

      const mockTransferRemittanceRecord = {
        transactionUuid: transactionId,
        ttlExpiresAt: Date.now(),
        transferRemittanceNotificationType: input.type,
        remittanceAttempts: 5,
      };

      await repo.saveDebitCardTransaction(mockTransaction);
      await transferRemittanceRepo.saveTransferRemittanceRecord(mockTransferRemittanceRecord);
      const response = await transferRemittanceService.sendTransferRemittanceNotification(input);
      expect(response).toEqual({
        isTransferRemittanceQueued: false,
        error: TransferRemittanceErrorEnum.TOO_MANY_REQUESTS,
      });
    });

    it('should increment the remittance attempts count only for that type and create new if doesnt exist', async () => {
      const transactionId = v4();
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.EMAIL,
        recipientEmail: '<EMAIL>',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
      };
      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
          amount: {
            value: '100',
            currency: ISO4217.AUD,
          },
          payeeDetails: {
            debitCardAccountUuid: v4(),
          },
        },
      });
      mockDCAQuery.prototype.getAccountById.mockResolvedValue({
        accountDetails: {
          name: 'Bens Farming corporation',
          account: '154 165 178',
          bsb: '123-123-234',
        },
      } as any);
      mockGetEntity.mockResolvedValue({ name: 'Zeller' });

      const mockTransferRemittanceRecord = {
        transactionUuid: transactionId,
        ttlExpiresAt: Date.now(),
        transferRemittanceNotificationType: input.type,
        remittanceAttempts: 1,
      };

      await repo.saveDebitCardTransaction(mockTransaction);
      await transferRemittanceRepo.saveTransferRemittanceRecord(mockTransferRemittanceRecord);
      await transferRemittanceService.sendTransferRemittanceNotification(input);
      await transferRemittanceService.sendTransferRemittanceNotification({
        ...input,
        type: ReceiptNotificationType.SMS,
        recipientMobile: '0000000',
      });
      const smsRecord = await transferRemittanceRepo.getTransferRemittanceRecord(
        input.dcaTransactionUuid,
        ReceiptNotificationType.SMS,
      );
      // Should have created a new record for SMS
      expect(smsRecord).toEqual({
        ...mockTransferRemittanceRecord,
        transferRemittanceNotificationType: 'SMS',
        ttlExpiresAt: expect.any(Number),
        type: expect.any(String),
        id: mockTransferRemittanceRecord.transactionUuid,
        remittanceAttempts: 1,
      });
      // Should have incremented the email record
      const emailRecord = await transferRemittanceRepo.getTransferRemittanceRecord(
        input.dcaTransactionUuid,
        input.type,
      );
      expect(emailRecord).toEqual({
        ...mockTransferRemittanceRecord,
        type: expect.any(String),
        id: mockTransferRemittanceRecord.transactionUuid,
        remittanceAttempts: 2,
      });
    });

    it('should return TRANSACTION_NOTIFICATION_PERIOD_EXPIRED if more transaction time is more than 2 hours', async () => {
      const transactionId = v4();
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.EMAIL,
        recipientEmail: '<EMAIL>',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
      };
      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
          updatedTime: Number(new Date(Date.now() - 2 * 60 * 60 * 1000)),
        },
      });

      await repo.saveDebitCardTransaction(mockTransaction);

      const response = await transferRemittanceService.sendTransferRemittanceNotification(input);
      expect(response).toEqual({
        isTransferRemittanceQueued: false,
        error: TransferRemittanceErrorEnum.TRANSACTION_NOTIFICATION_PERIOD_EXPIRED,
      });
    });

    it('should return TRANSACTION_NOT_FOUND if no transaction found', async () => {
      const transactionId = v4();
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.EMAIL,
        recipientEmail: '<EMAIL>',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
      };

      const response = await transferRemittanceService.sendTransferRemittanceNotification(input, true);
      expect(response).toEqual({
        isTransferRemittanceQueued: true,
        error: TransferRemittanceErrorEnum.TRANSACTION_NOT_FOUND,
      });
    });

    it('should save transfer remittance record if all validations pass', async () => {
      const transactionId = v4();
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.EMAIL,
        recipientEmail: '<EMAIL>',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
      };

      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
          updatedTime: Number(new Date(Date.now())),
          amount: {
            value: '100',
            currency: ISO4217.AUD,
          },
          payeeDetails: {
            debitCardAccountUuid: v4(),
          },
        },
      });

      mockDCAQuery.prototype.getAccountById.mockResolvedValueOnce({
        accountDetails: {
          name: 'Bens Farming corporation',
          account: '154 165 178',
          bsb: '123-123-234',
        },
      } as any);
      mockGetEntity.mockResolvedValue({ name: 'Zeller' });

      await repo.saveDebitCardTransaction(mockTransaction);
      await transferRemittanceService.sendTransferRemittanceNotification(input, true);
      const record = await transferRemittanceRepo.getTransferRemittanceRecord(input.dcaTransactionUuid, input.type);
      expect(record).toEqual({
        id: expect.any(String),
        transactionUuid: transactionId,
        transferRemittanceNotificationType: input.type,
        remittanceAttempts: 1,
        type: `transferRemittance.${input.type.toLowerCase()}.${transactionId}`,
        ttlExpiresAt: expect.any(Number),
      });
    });

    it('should return SENDER_DETAILS_NOT_FOUND without queueing if isSenderNotified flag is true and input not found', async () => {
      mockCustomerService.prototype.getCustomer.mockReset().mockResolvedValueOnce(null);
      const transactionId = v4();
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.EMAIL,
        recipientEmail: '<EMAIL>',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
      };

      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
          updatedTime: Number(new Date(Date.now())),
          payeeDetails: {
            debitCardAccountUuid: v4(),
          },
        },
      });

      await repo.saveDebitCardTransaction(mockTransaction);
      mockDCAQuery.prototype.getAccountById.mockResolvedValueOnce({
        accountDetails: {
          name: 'Bens Farming corporation',
          account: '154 165 178',
          bsb: '123-123-234',
        },
      } as any);
      mockGetEntity.mockResolvedValue({ name: 'Zeller', businessName: 'Zeller Business' });

      const response = await transferRemittanceService.sendTransferRemittanceNotification(input);
      expect(response).toEqual({
        isTransferRemittanceQueued: false,
        error: TransferRemittanceErrorEnum.SENDER_DETAILS_NOT_FOUND,
      });
    });

    it('should throw if the entity is not found', async () => {
      mockCustomerService.prototype.getCustomer.mockReset().mockResolvedValueOnce(null);
      const transactionId = v4();
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.EMAIL,
        recipientEmail: '<EMAIL>',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
      };

      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
          updatedTime: Number(new Date(Date.now())),
          payeeDetails: {
            debitCardAccountUuid: v4(),
          },
        },
      });

      await repo.saveDebitCardTransaction(mockTransaction);
      mockDCAQuery.prototype.getAccountById.mockResolvedValueOnce({
        accountDetails: {
          name: 'Bens Farming corporation',
          account: '154 165 178',
          bsb: '123-123-234',
        },
      } as any);
      mockGetEntity.mockResolvedValue(null);

      await expect(transferRemittanceService.sendTransferRemittanceNotification(input)).rejects.toThrow(
        'Entity not found',
      );
    });

    it('should throw if the debit card account not found', async () => {
      mockCustomerService.prototype.getCustomer.mockReset().mockResolvedValueOnce(null);
      const transactionId = v4();
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.EMAIL,
        recipientEmail: '<EMAIL>',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
      };

      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
          updatedTime: Number(new Date(Date.now())),
          payeeDetails: {
            debitCardAccountUuid: v4(),
          },
        },
      });

      await repo.saveDebitCardTransaction(mockTransaction);
      mockDCAQuery.prototype.getAccountById.mockResolvedValueOnce(null);

      await expect(transferRemittanceService.sendTransferRemittanceNotification(input)).rejects.toThrow(
        'Debit Card Account not found',
      );
    });

    it('should throw if the debit card account ID not found in the transaction details', async () => {
      mockCustomerService.prototype.getCustomer.mockReset().mockResolvedValueOnce(null);
      const transactionId = v4();
      const input = {
        dcaTransactionUuid: transactionId,
        type: ReceiptNotificationType.EMAIL,
        recipientEmail: '<EMAIL>',
        entityUuid: v4(),
        customerUuid: customerId,
        isSenderNotified: true,
      };

      const mockTransaction = createTransaction({
        id: transactionId,
        transactionType: DebitCardTransactionTypeV2.PURCHASE,
        overrides: {
          entityUuid: input.entityUuid,
          customerUuid: customerId,
          updatedTime: Number(new Date(Date.now())),
        },
      });

      await repo.saveDebitCardTransaction(mockTransaction);
      mockDCAQuery.prototype.getAccountById.mockResolvedValueOnce(null);

      await expect(transferRemittanceService.sendTransferRemittanceNotification(input)).rejects.toThrow(
        'Debit Card Account ID not found',
      );
    });
  });
});
