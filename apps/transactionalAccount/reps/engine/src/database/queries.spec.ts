import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { EntityCategories, ISO4217 } from '@npco/component-dto-core';
import { DebitCardTransactionTypeV2, DebitCardTransactionStatusV2 } from '@npco/component-dto-issuing-transaction';
import { PaymentInstrumentType } from '@npco/component-dto-payment-instrument';

import { v4 as uuid } from 'uuid';

import { EnvironmentService } from '../config/environmentService.js';
import { getAllPaymentInstrumentRecordsById } from '../services/paymentInstrument/db/paymentInstrumentDb.js';

import { PaymentInstrument, DebitCardAccountTransaction } from './entities/index.js';

import { find, insertOrIgnoreOnConflict, update, upsertConditionallyUpdatedTime } from './index.js';

jest.mock('../config/environmentService');

describe('Queries', () => {
  const domicile = Domicile.AU;
  let env: EnvironmentService;

  beforeEach(() => {
    env = new EnvironmentService();
  });
  describe('upsertConditionallyUpdatedTime fn', () => {
    describe('DebitCardAccountTransaction', () => {
      const values: DebitCardAccountTransaction = {
        id: uuid(),
        entityUuid: uuid(),
        accountUuid: uuid(),
        debitCardId: uuid(),
        type: DebitCardTransactionTypeV2.ATM_IN,
        status: DebitCardTransactionStatusV2.APPROVED,
        category: EntityCategories.ADVERTISING,
        timestamp: new Date(),
        amount: 123,
        senderUuid: uuid(),
        updatedTime: 1,
        currency: ISO4217.AUD,
        domicile,
      };

      describe('Add record', () => {
        it('should be able save data', async () => {
          await upsertConditionallyUpdatedTime(
            DebitCardAccountTransaction,
            'id',
            values,
            DebitCardAccountTransaction.name,
            env,
            domicile,
          );

          const findResult = await find(DebitCardAccountTransaction, env, {
            where: {
              id: values.id,
              domicile,
            },
          });

          expect(findResult.length).toBe(1);
          expect(findResult[0].id).toBe(values.id);
        });
      });

      describe('Update record', () => {
        it('should not update database record if updated time value is old', async () => {
          const mockUpdatedValues: DebitCardAccountTransaction = {
            ...values,
            type: DebitCardTransactionTypeV2.ATM_OUT,
            updatedTime: values.updatedTime - 1,
          };
          await upsertConditionallyUpdatedTime(
            DebitCardAccountTransaction,
            'id',
            mockUpdatedValues,
            DebitCardAccountTransaction.name,
            env,
            domicile,
          );

          const findResult = await find(DebitCardAccountTransaction, env, {
            where: {
              id: values.id,
              domicile,
            },
          });

          expect(findResult[0].type).toBe(values.type);
          expect(Number(findResult[0].updatedTime)).toBe(values.updatedTime);
        });

        it('should update database record if updated time value is new', async () => {
          const mockUpdatedValues: DebitCardAccountTransaction = {
            ...values,
            type: DebitCardTransactionTypeV2.PURCHASE,
            category: EntityCategories.COMMISSION,
            updatedTime: values.updatedTime + 1,
            domicile,
          };
          await upsertConditionallyUpdatedTime(
            DebitCardAccountTransaction,
            'id',
            mockUpdatedValues,
            DebitCardAccountTransaction.name,
            env,
            domicile,
          );

          const findResult = await find(DebitCardAccountTransaction, env, {
            where: {
              id: values.id,
              domicile,
            },
          });

          expect(findResult[0].type).toBe(mockUpdatedValues.type);
          expect(Number(findResult[0].updatedTime)).toBe(mockUpdatedValues.updatedTime);
          expect(findResult[0].category).toBe(mockUpdatedValues.category);
          expect(findResult[0].domicile).toBe(mockUpdatedValues.domicile);
        });
      });
    });
  });

  describe('insertAndIgnoreOnConflict', () => {
    const values: PaymentInstrument = {
      paymentInstrumentUuid: uuid(),
      type: PaymentInstrumentType.BSB,
      entityUuid: uuid(),
      contactUuid: uuid(),
      domicile,
    };

    it('should be able to add record', async () => {
      await insertOrIgnoreOnConflict(PaymentInstrument, values, env);

      const record = await getAllPaymentInstrumentRecordsById(values.paymentInstrumentUuid, env, domicile);

      expect(record[0]).toEqual(values);
    });

    it('should not add existing record', async () => {
      await insertOrIgnoreOnConflict(PaymentInstrument, values, env);

      expect((await getAllPaymentInstrumentRecordsById(values.paymentInstrumentUuid, env, domicile)).length).toBe(1);
    });
  });

  describe('update', () => {
    const paymentInstrumentUuid = uuid();
    const values: PaymentInstrument = {
      paymentInstrumentUuid,
      type: PaymentInstrumentType.BSB,
      entityUuid: uuid(),
      contactUuid: uuid(),
      domicile,
    };

    beforeAll(async () => {
      await insertOrIgnoreOnConflict(PaymentInstrument, values, env);

      const record = await getAllPaymentInstrumentRecordsById(paymentInstrumentUuid, env, domicile);

      expect(record[0]).toEqual(values);
    });

    it.each([{ paymentInstrumentUuid, domicile }])(
      'should be able to update record with criteria as %s',
      async (criteria) => {
        const updatedContactUuid = {
          ...values,
          contactUuid: uuid(),
        };

        await update(PaymentInstrument, updatedContactUuid, criteria, env);

        const record = await getAllPaymentInstrumentRecordsById(paymentInstrumentUuid, env, domicile);

        expect(record[0]).toEqual(updatedContactUuid);
      },
    );
  });
});
