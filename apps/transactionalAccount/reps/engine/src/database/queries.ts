import 'reflect-metadata';
import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { error } from '@npco/component-bff-core/dist/utils/logger';

import type { EntityTarget, FindOneOptions, FindOptionsWhere, Repository } from 'typeorm';

import type {
  ArgumentTypes,
  DeleteCriteriaWithRequiredField,
  FindManyOptionsWithRequiredField,
} from '../common/types/index.js';
import type { EnvironmentService } from '../config/environmentService.js';

import { getAppDataSource } from './appDatasource.js';

export const findOne = async <Entity extends Record<string, any>>(
  entity: EntityTarget<Entity>,
  env: EnvironmentService,
  options: FindOneOptions<Entity>,
) => {
  const dataSource = await getAppDataSource(env);
  const repository = dataSource.getRepository(entity);
  const result = await repository.findOne(options);

  dataSource.destroy();

  return result;
};

export const insert = async <Entity extends Record<string, any>>(
  entity: EntityTarget<Entity>,
  values: Entity | Entity[],
  env: EnvironmentService,
) => {
  const dataSource = await getAppDataSource(env);
  const repository = dataSource.getRepository(entity);
  const result = await repository.insert(values);
  await dataSource.destroy();
  return result;
};

export const update = async <Entity extends Record<string, any>>(
  entity: EntityTarget<Entity>,
  values: Partial<Entity>,
  criteria: ArgumentTypes<Repository<Entity>['update']>[0],
  env: EnvironmentService,
) => {
  const dataSource = await getAppDataSource(env);
  const repository = dataSource.getRepository(entity);

  await repository.update(criteria, values);

  await dataSource.destroy();
};

export const upsertConditionallyUpdatedTime = async <Entity extends Record<string, any>>(
  entity: EntityTarget<Entity>,
  id: string,
  values: Entity & { updatedTime: number },
  entityName: string,
  env: EnvironmentService,
  domicile?: Domicile,
) => {
  const dataSource = await getAppDataSource(env);
  const where: FindOptionsWhere<Entity & { domicile?: Domicile }> = {
    [id]: values[id],
    ...(domicile ? { domicile: domicile as any } : {}),
  };

  try {
    const existingRecord = await findOne(entity, env, {
      where,
    });

    const updatedValues = {
      ...values,
      ...(domicile ? { domicile } : {}),
    };

    if (existingRecord) {
      if (existingRecord.updatedTime <= values.updatedTime) {
        await update(entity, updatedValues, where, env);
      }
    } else {
      await insert(entity, updatedValues, env);
    }

    return values;
  } catch (err: any) {
    error(`upsertConditionallyUpdatedTime failed for this ${entityName}: ${err} ${err.stack}`);
    throw err;
  } finally {
    await dataSource.destroy();
  }
};

export const insertOrIgnoreOnConflict = async <Entity extends Record<string, any>>(
  entity: EntityTarget<Entity>,
  values: Entity,
  env: EnvironmentService,
) => {
  const dataSource = await getAppDataSource(env);
  const repository = dataSource.getRepository(entity);

  await repository.createQueryBuilder().insert().into(entity).values(values).orIgnore().execute();

  await dataSource.destroy();
};

export const find = async <Entity extends Record<string, any>>(
  entity: EntityTarget<Entity>,
  env: EnvironmentService,
  options: FindManyOptionsWithRequiredField<Entity, 'domicile'>,
) => {
  const dataSource = await getAppDataSource(env);
  const repository = dataSource.getRepository(entity);

  const result = await repository.find(options);

  await dataSource.destroy();

  return result;
};

export const deleteRecordWithDomicile = async <Entity extends { domicile: Domicile }>(
  entity: EntityTarget<Entity>,
  criteria: DeleteCriteriaWithRequiredField<Entity, 'domicile'>,
  env: EnvironmentService,
) => {
  const dataSource = await getAppDataSource(env);
  const repository = dataSource.getRepository(entity);

  const result = await repository.delete(criteria);
  await dataSource.destroy();

  return result;
};
