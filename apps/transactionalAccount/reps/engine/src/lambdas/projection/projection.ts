import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { debug } from '@npco/component-bff-core/dist/utils/logger';

import type { Handler, SQSRecord } from 'aws-lambda';

import { EnvironmentService } from '../../config/environmentService.js';
import { projectEvent } from '../../services/projection/index.js';
import { setDomicleForProjectionMiddleware } from '../middleware/index.js';

const envService = new EnvironmentService();

export const projectionHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.REPS, eventType: LambdaEventSource.SQS },
  async (event: { Records: Array<SQSRecord & { domicile: Domicile }> }) => {
    debug(`REPS Projection Service - projectionHandler: ${JSON.stringify(event)}`);
    await projectEvent(event.Records);
  },
  [setDomicleForProjectionMiddleware(envService)],
);
