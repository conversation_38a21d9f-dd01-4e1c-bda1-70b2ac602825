import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { ContactSubcategoryLinkEventDto, type ContactCreatedEventDto } from '@npco/component-dto-addressbook';

import { v4 as uuid } from 'uuid';

import type { FunctionTypes } from '../../../common/types/index.js';
import { EnvironmentService } from '../../../config/environmentService.js';
import { ContactLink, ContactMerchantLink, Sender } from '../../../database/index.js';
import { transformObjectToOrmEntity } from '../../../utils/object.js';
import {
  getContactCreatedEventDto,
  getContactLinkEventDto,
  getContactMerchantLinkEventDto,
  getContactTransactionSenderLinkedEventDto,
} from '../mocks/contactDto.js';

import type * as ContactDbModule from './contactDb.js';
import {
  deleteContactLinkRecord,
  deleteContactMerchantLinkedRecord,
  deleteContactRecord,
  getAllContactLinkRecords,
  getAllContactMerchantLinkRecords,
  getAllContactRecordsById,
  getSenderRecord,
  saveContactLinked,
  saveContactMerchantLink,
  updateContact,
  upsertSenderRecord,
} from './contactDb.js';

type ContactDbModuleFunctionTypes = FunctionTypes<typeof ContactDbModule>;

jest.mock('../../../config/environmentService');

const expectedContactDbRecord = (data: ContactCreatedEventDto, domicile: Domicile) => {
  const { contactUuid, contactType: type, entityUuid, category, subcategoryUuid: subcategory } = data;
  return {
    contactUuid,
    type,
    entityUuid,
    category,
    subcategory,
    domicile,
  };
};

describe('contactDb', () => {
  const domicile = Domicile.AU;
  let env: EnvironmentService;
  let saveContact: ContactDbModuleFunctionTypes['saveContact'];

  beforeAll(async () => {
    env = new EnvironmentService();
    const dbService = await import('./contactDb.js');
    saveContact = dbService.saveContact;
  });

  describe('saveContactToDb', () => {
    const dto = getContactCreatedEventDto();
    const { contactUuid } = dto;

    it('should be able to save created contact', async () => {
      await saveContact(dto, env, domicile);

      const result = await getAllContactRecordsById(contactUuid, env, domicile);

      expect(result.length).toBe(1);
      expect(result[0]).toEqual(expectedContactDbRecord(dto, domicile));
    });
  });

  describe('deleteContactRecord', () => {
    const dto = getContactCreatedEventDto();
    const { contactUuid } = dto;

    beforeAll(async () => {
      await saveContact(dto, env, domicile);

      expect((await getAllContactRecordsById(contactUuid, env, domicile)).length).toBe(1);
    });

    it('should be able to delete contact', async () => {
      await deleteContactRecord(dto, env, domicile);

      const result = await getAllContactRecordsById(contactUuid, env, domicile);

      expect(result.length).toBe(0);
    });
  });

  describe('updateContact', () => {
    const dto = getContactCreatedEventDto();
    const { contactUuid, entityUuid } = dto;
    const subcategoryLinkEventDto = new ContactSubcategoryLinkEventDto({
      contactUuid,
      entityUuid,
      category: 'CATEGORY_UPDATE',
      subcategoryUuid: uuid(),
    });

    beforeAll(async () => {
      await saveContact(dto, env, domicile);

      expect((await getAllContactRecordsById(contactUuid, env, domicile)).length).toBe(1);
    });

    it('should be able to update contact', async () => {
      await updateContact(
        { contactUuid, entityUuid, domicile },
        {
          category: subcategoryLinkEventDto.category,
          subcategory: subcategoryLinkEventDto.subcategoryUuid,
        },
        env,
      );

      const result = await getAllContactRecordsById(contactUuid, env, domicile);

      expect(result.length).toBe(1);

      const [item] = result;
      expect(item.category).toEqual(subcategoryLinkEventDto.category);
      expect(item.subcategory).toEqual(subcategoryLinkEventDto.subcategoryUuid);
    });

    it('should be able to update contact category and subcategory to null if both are existing ', async () => {
      await updateContact(
        { contactUuid, entityUuid, domicile },
        {
          category: null,
          subcategory: null,
        },
        env,
      );

      const result = await getAllContactRecordsById(contactUuid, env, domicile);

      expect(result.length).toBe(1);

      const [item] = result;
      expect(item.category).toBeNull();
      expect(item.subcategory).toBeNull();
    });
  });

  describe('saveContactLinked', () => {
    const contact1Uuid = uuid();
    const contact2Uuid = uuid();
    const dto = getContactLinkEventDto(contact1Uuid, contact2Uuid);
    const fields = transformObjectToOrmEntity(dto, new ContactLink(), domicile);

    it('should be able to save contact linked', async () => {
      await saveContactLinked(dto, env, domicile);

      const result = await getAllContactLinkRecords(
        {
          contact1Uuid,
          contact2Uuid,
          domicile,
        },
        env,
      );

      expect(result.length).toBe(1);
      expect(result[0].contact1Uuid).toEqual(contact1Uuid);
      expect(result[0].contact2Uuid).toEqual(contact2Uuid);
      expect(result[0]).toEqual(fields);
    });
  });

  describe('deleteContactLinkRecord', () => {
    const contact1Uuid = uuid();
    const contact2Uuid = uuid();
    const dto = getContactLinkEventDto(contact1Uuid, contact2Uuid);

    beforeAll(async () => {
      await saveContactLinked(dto, env, domicile);

      const result = await getAllContactLinkRecords(
        {
          contact1Uuid,
          contact2Uuid,
          domicile,
        },
        env,
      );

      expect(result.length).toBe(1);
    });

    it('should be able to delete contact linked record', async () => {
      await deleteContactLinkRecord(dto, env, domicile);

      const result = await getAllContactLinkRecords(
        {
          contact1Uuid,
          contact2Uuid,
          domicile,
        },
        env,
      );

      expect(result.length).toBe(0);
    });
  });

  describe('saveContactMerchantLink', () => {
    const dto = getContactMerchantLinkEventDto();
    const fields = transformObjectToOrmEntity(dto, new ContactMerchantLink(), domicile);

    it('should be able to save contact merchant linked', async () => {
      await saveContactMerchantLink(dto, env, domicile);

      const result = await getAllContactMerchantLinkRecords(
        {
          contactUuid: dto.contactUuid,
          entityUuid: dto.entityUuid,
          merchantUuid: dto.merchantUuid,
          domicile,
        },
        env,
      );

      expect(result.length).toBe(1);
      expect(result[0]).toEqual(fields);
    });
  });

  describe('deleteContactMerchantLinkedRecord', () => {
    const dto = getContactMerchantLinkEventDto();

    beforeAll(async () => {
      await saveContactMerchantLink(dto, env, domicile);

      const result = await getAllContactMerchantLinkRecords(
        {
          contactUuid: dto.contactUuid,
          entityUuid: dto.entityUuid,
          merchantUuid: dto.merchantUuid,
          domicile,
        },
        env,
      );

      expect(result.length).toBe(1);
    });

    it('should be able to delete contact merchant linked record', async () => {
      await deleteContactMerchantLinkedRecord(dto, env, domicile);

      const result = await getAllContactMerchantLinkRecords(
        {
          contactUuid: dto.contactUuid,
          entityUuid: dto.entityUuid,
          merchantUuid: dto.merchantUuid,
          domicile,
        },
        env,
      );

      expect(result.length).toBe(0);
    });
  });

  describe('upsertSenderRecord', () => {
    const senderUuid = uuid();
    const dto = getContactTransactionSenderLinkedEventDto(senderUuid);

    it('it should be able to upsert sender record', async () => {
      await upsertSenderRecord(dto, env, domicile);

      const result = await getSenderRecord(senderUuid, env, domicile);

      expect(result?.contactUuid).toBe(dto.contactUuid);
      expect(result).toEqual(transformObjectToOrmEntity(dto, new Sender(), domicile));
    });

    it('it should be able to set contactUuid to null if the same with the existing record', async () => {
      await upsertSenderRecord(dto, env, domicile);

      const result = await getSenderRecord(senderUuid, env, domicile);

      expect(result?.contactUuid).toBe(null);
      expect(result).toEqual(
        transformObjectToOrmEntity(
          {
            ...dto,
            contactUuid: null,
          },
          new Sender(),
          domicile,
        ),
      );
    });

    it('it should be able to update sender record', async () => {
      await upsertSenderRecord(dto, env, domicile);

      const result = await getSenderRecord(senderUuid, env, domicile);

      expect(result?.contactUuid).toBe(dto.contactUuid);
      expect(result).toEqual(transformObjectToOrmEntity(dto, new Sender(), domicile));
    });
  });
});
