import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import type {
  ContactCreatedEventDto,
  ContactDcaTransactionSenderLinkEventDto,
  ContactDcaTransactionSenderUnlinkEventDto,
  ContactDeletedEventDto,
  ContactLinkEventDto,
  ContactMerchantLinkEventDto,
  ContactMerchantUnlinkEventDto,
} from '@npco/component-dto-addressbook';

import type { MakeEntityDomicileNonNullable } from '../../../common/types/query.js';
import type { EnvironmentService } from '../../../config/environmentService.js';
import {
  Contact,
  ContactLink,
  ContactMerchantLink,
  Sender,
  deleteRecordWithDomicile,
  find,
  findOne,
  insert,
  insertOrIgnoreOnConflict,
  update,
} from '../../../database/index.js';
import { transformObjectToOrmEntity } from '../../../utils/object.js';

export const mapContactToDbFields = (data: ContactCreatedEventDto, domicile: Domicile): Contact => {
  const { contactUuid, contactType: type, entityUuid, category, subcategoryUuid: subcategory } = data;
  return {
    contactUuid,
    type,
    entityUuid,
    category,
    subcategory,
    domicile,
  };
};

export const saveContact = async (data: ContactCreatedEventDto, env: EnvironmentService, domicile: Domicile) => {
  await insertOrIgnoreOnConflict(Contact, mapContactToDbFields(data, domicile), env);
};

export const updateContact = async (
  keys: {
    contactUuid: string;
    entityUuid: string;
    domicile: Domicile;
    category?: string;
    subcategory?: string;
  },
  values: Omit<Partial<Contact>, 'contactUuid' | 'entityUuid'>,
  env: EnvironmentService,
) => {
  await update(Contact, values, keys, env);
};

export const deleteContactRecord = async (
  data: ContactDeletedEventDto,
  env: EnvironmentService,
  domicile: Domicile,
) => {
  const { contactUuid, entityUuid } = data;
  await deleteRecordWithDomicile(Contact, { contactUuid, entityUuid, domicile }, env);
};

export const getAllContactRecordsById = async (contactUuid: string, env: EnvironmentService, domicile: Domicile) =>
  find(Contact, env, {
    where: {
      contactUuid,
      domicile,
    },
  });

export const saveContactLinked = async (data: ContactLinkEventDto, env: EnvironmentService, domicile: Domicile) => {
  const fields = transformObjectToOrmEntity(data, new ContactLink(), domicile);

  await insertOrIgnoreOnConflict(ContactLink, fields, env);
};

export const getAllContactLinkRecords = async (
  { contact1Uuid, contact2Uuid, domicile }: MakeEntityDomicileNonNullable<ContactLink>,
  env: EnvironmentService,
) =>
  find(ContactLink, env, {
    where: { contact1Uuid, contact2Uuid, domicile },
  });

export const deleteContactLinkRecord = async (
  data: ContactLinkEventDto,
  env: EnvironmentService,
  domicile: Domicile,
) => {
  const { contact1Uuid, contact2Uuid } = transformObjectToOrmEntity(data, new ContactLink(), domicile);
  await deleteRecordWithDomicile(
    ContactLink,
    {
      contact1Uuid,
      contact2Uuid,
      domicile,
    },
    env,
  );
};

export const saveContactMerchantLink = async (
  data: ContactMerchantLinkEventDto,
  env: EnvironmentService,
  domicile: Domicile,
) => {
  const fields = transformObjectToOrmEntity(data, new ContactMerchantLink(), domicile);

  await insertOrIgnoreOnConflict(ContactMerchantLink, fields, env);
};

export const getAllContactMerchantLinkRecords = async (
  whereFields: MakeEntityDomicileNonNullable<ContactMerchantLink>,
  env: EnvironmentService,
) =>
  find(ContactMerchantLink, env, {
    where: whereFields,
  });

export const deleteContactMerchantLinkedRecord = async (
  data: ContactMerchantUnlinkEventDto,
  env: EnvironmentService,
  domicile: Domicile,
) => {
  const fields = transformObjectToOrmEntity(data, new ContactMerchantLink(), domicile);
  await deleteRecordWithDomicile(
    ContactMerchantLink,
    {
      ...fields,
      domicile,
    },
    env,
  );
};

export const getSenderRecord = async (senderUuid: string, env: EnvironmentService, domicile: Domicile) =>
  findOne(Sender, env, {
    where: {
      senderUuid,
      domicile,
    },
  });

export const upsertSenderRecord = async (
  data: ContactDcaTransactionSenderLinkEventDto | ContactDcaTransactionSenderUnlinkEventDto,
  env: EnvironmentService,
  domicile: Domicile,
) => {
  const fields = transformObjectToOrmEntity(data, new Sender(), domicile);
  const existingRecord = await getSenderRecord(fields.senderUuid, env, domicile);
  const { senderUuid, contactUuid } = fields;

  if (existingRecord) {
    if (existingRecord.contactUuid === contactUuid) {
      return update(
        Sender,
        {
          ...fields,
          contactUuid: null,
        },
        { senderUuid, domicile },
        env,
      );
    }

    return update(Sender, fields, { senderUuid, domicile }, env);
  }

  return insert(Sender, fields, env);
};
