import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { DebitCardTransactionTypeV2, type DebitCardTransactionV2 } from '@npco/component-dto-issuing-transaction';

import type { FindManyOptionsWithRequiredField } from '../../../common/types/query.js';
import type { EnvironmentService } from '../../../config/environmentService.js';
import {
  upsertConditionallyUpdatedTime,
  DebitCardAccountTransaction,
  find,
  SavingsAccountTransaction,
} from '../../../database/index.js';
import { convertAmountWithSign } from '../../../utils/covertAmountWithSign.js';

export const paymentInstrumentTransactionTypes = [DebitCardTransactionTypeV2.BPAY_OUT];

const mapDebitCardTransactionDtoToDbFields = (
  data: DebitCardTransactionV2,
  domicile: Domicile,
): DebitCardAccountTransaction => {
  let paymentInstrumentUuid = data.payeeDetails?.recipientUuid;

  if (paymentInstrumentTransactionTypes.includes(data.type) && !paymentInstrumentUuid) {
    paymentInstrumentUuid = data?.payeeDetails?.paymentInstrumentUuid;
  }

  return {
    id: data.id,
    entityUuid: data.entityUuid,
    accountUuid: data.debitCardAccountUuid,
    debitCardId: data.debitCardId,
    type: data.type,
    status: data.status,
    timestamp: new Date(Number(data.timestamp)),
    amount: Number(data.amount.value),
    merchantId: data.merchant?.id,
    paymentInstrumentUuid,
    senderUuid: data.payerDetails?.senderUuid,
    category: data.category,
    subcategory: data.subcategory,
    accountCategory: data.accountingCategory,
    attachments: data.attachments?.length ?? 0,
    note: !!(data.note && data.note?.trim().length > 0),
    tags: data.tags,
    updatedTime: data.updatedTime,
    currency: data.amount.currency,
    domicile,
  };
};

export const saveDebitCardTransaction = async (
  data: DebitCardTransactionV2,
  env: EnvironmentService,
  domicile: Domicile,
) => {
  const dbValues = mapDebitCardTransactionDtoToDbFields(data, domicile);
  const { amount: rawAmount, ...values } = dbValues;

  const amount = convertAmountWithSign(rawAmount, values.type);
  await upsertConditionallyUpdatedTime(
    DebitCardAccountTransaction,
    'id',
    {
      ...values,
      amount,
    },
    DebitCardAccountTransaction.name,
    env,
    domicile,
  );
};

export const getAllIssuingTransactionRecordsById = async (id: string, env: EnvironmentService, domicile: Domicile) => {
  return find(DebitCardAccountTransaction, env, {
    where: {
      id,
      domicile,
    },
  });
};

export const getAllDebitCardAccountTransactionRecords = async (
  env: EnvironmentService,
  options: FindManyOptionsWithRequiredField<DebitCardAccountTransaction, 'domicile'>,
) => {
  return find(DebitCardAccountTransaction, env, options);
};

export const getAllSavingsAccountTransactionRecords = async (
  env: EnvironmentService,
  options: FindManyOptionsWithRequiredField<SavingsAccountTransaction, 'domicile'>,
) => {
  return find(SavingsAccountTransaction, env, options);
};
