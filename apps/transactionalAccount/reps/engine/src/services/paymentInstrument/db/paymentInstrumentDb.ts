import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import type { PaymentInstrumentCreatedDto } from '@npco/component-dto-payment-instrument';

import type { EnvironmentService } from '../../../config/environmentService.js';
import { find, insertOrIgnoreOnConflict, PaymentInstrument, update } from '../../../database/index.js';

const mapPaymentInstrumentToDBFields = (data: PaymentInstrumentCreatedDto, domicile: Domicile): PaymentInstrument => {
  const { paymentInstrumentUuid, type, entityUuid, contactUuid } = data;

  return {
    paymentInstrumentUuid,
    type,
    entityUuid,
    contactUuid,
    domicile,
  };
};

export const savePaymentInstrumentToDb = async (
  data: PaymentInstrumentCreatedDto,
  env: EnvironmentService,
  domicile: Domicile,
) => {
  await insertOrIgnoreOnConflict(PaymentInstrument, mapPaymentInstrumentToDBFields(data, domicile), env);
};

export const updatePaymentInstrumentContactRecord = async (
  contactUuid: string,
  paymentInstrumentUuid: string,
  env: EnvironmentService,
  domicile: Domicile,
) => {
  await update(PaymentInstrument, { contactUuid }, { paymentInstrumentUuid, domicile }, env);
};

export const updatePaymentInstrumentContactRecordToNull = async (
  paymentInstrumentUuid: string,
  env: EnvironmentService,
  domicile: Domicile,
) => {
  await update(PaymentInstrument, { contactUuid: null }, { paymentInstrumentUuid, domicile }, env);
};

export const getAllPaymentInstrumentRecordsById = async (
  paymentInstrumentUuid: string,
  env: EnvironmentService,
  domicile: Domicile,
) => {
  return find(PaymentInstrument, env, {
    where: {
      paymentInstrumentUuid,
      domicile,
    },
  });
};
