import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { DebitCardAccountTransactionCreatedEventDto } from '@npco/component-dto-issuing-transaction';

const mockDeleteMessage = jest.fn();
jest.mock('@aws-sdk/client-sqs', () => {
  return {
    ...jest.requireActual('@aws-sdk/client-sqs'),
    SQSClient: jest.fn(() => ({
      send: mockDeleteMessage,
    })),
  };
});

const debug = jest.fn();

jest.mock('@npco/component-bff-core/dist/utils/logger', () => ({
  __esModule: true,
  ...jest.requireActual('@npco/component-bff-core/dist/utils/logger'),
  debug,
}));

describe('reps projection service tests', () => {
  const domicile = Domicile.AU;
  let projectEvent: (
    event: {
      body: string;
      receiptHandle: string;
      domicile: Domicile;
    }[],
  ) => Promise<void>;

  beforeAll(async () => {
    const projectionService = await import('../projectionService.js');
    projectEvent = projectionService.projectEvent;
  });

  beforeEach(async () => {
    mockDeleteMessage.mockReset();
    mockDeleteMessage.mockReturnValue({ promise: jest.fn() });
  });

  describe('Projection event tests for debit card transactions', () => {
    const materialiseDebitCardAccountTransaction = jest.fn();

    jest.mock('../../debitCardAccountTransaction', () => ({
      __esModule: true,
      ...jest.requireActual('../../debitCardAccountTransaction'),
      materialiseDebitCardAccountTransaction,
    }));

    it('should execute projectEvent', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.ProjectionDebitCardAccountTransaction.create',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.ProjectionDebitCardAccountTransaction.create',
          }),
          receiptHandle: '',
          domicile,
        },
        {
          body: JSON.stringify({
            'detail-type': 'reps.ProjectionDebitCardAccountTransaction.update',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.ProjectionDebitCardAccountTransaction.update',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialiseDebitCardAccountTransaction).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const mockCalls = materialiseDebitCardAccountTransaction.mock.calls;

      expect(mockCalls[0][2]).toBe(domicile);
      expect(mockCalls[0][0].payload).toBeInstanceOf(DebitCardAccountTransactionCreatedEventDto);
    });

    it('should throw no handler found error when no dto found', async () => {
      const event = [
        {
          body: JSON.stringify({
            'detail-type': 'non-existing-uri',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'non-existing-uri',
          }),
          receiptHandle: '',
          domicile: Domicile.AU,
        },
      ];

      await expect(projectEvent(event)).rejects.toThrowError();
    });
  });
});
