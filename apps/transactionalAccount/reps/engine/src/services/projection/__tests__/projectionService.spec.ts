import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import {
  ContactCreatedEventDto,
  ContactDeletedEventDto,
  ContactSubcategoryLinkEventDto,
  ContactLinkEventDto,
  ContactMerchantLinkEventDto,
  ContactMerchantUnlinkEventDto,
  ContactDcaTransactionSenderLinkEventDto,
  ContactDcaTransactionSenderUnlinkEventDto,
} from '@npco/component-dto-addressbook';
import {
  PaymentInstrumentCreatedDto,
  PaymentInstrumentLinkedDto,
  PaymentInstrumentUnlinkedDto,
} from '@npco/component-dto-payment-instrument';

const mockDeleteMessage = jest.fn();
jest.mock('@aws-sdk/client-sqs', () => {
  return {
    ...jest.requireActual('@aws-sdk/client-sqs'),
    SQSClient: jest.fn(() => ({
      send: mockDeleteMessage,
    })),
  };
});

const debug = jest.fn();

jest.mock('@npco/component-bff-core/dist/utils/logger', () => ({
  __esModule: true,
  ...jest.requireActual('@npco/component-bff-core/dist/utils/logger'),
  debug,
}));

describe('reps projection service tests', () => {
  const domicile = Domicile.AU;
  let projectEvent: (
    event: {
      body: string;
      receiptHandle: string;
      domicile: Domicile;
    }[],
  ) => Promise<void>;

  beforeAll(async () => {
    const projectionService = await import('../projectionService.js');
    projectEvent = projectionService.projectEvent;
  });

  beforeEach(async () => {
    mockDeleteMessage.mockReset();
    mockDeleteMessage.mockReturnValue({ promise: jest.fn() });
  });

  describe('Projection event tests for merchant', () => {
    const materialiseMerchant = jest.fn();

    jest.mock('../../richData', () => ({
      __esModule: true,
      ...jest.requireActual('../../richData'),
      materialiseMerchant,
    }));

    it('should execute projectEvent', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.ProjectionMerchant.create',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.ProjectionMerchant.create',
          }),
          receiptHandle: '',
          domicile,
        },
        {
          body: JSON.stringify({
            'detail-type': 'reps.ProjectionMerchant.update',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.ProjectionMerchant.update',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialiseMerchant).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);
    });
  });

  describe('Projection event tests for payment instruments', () => {
    const materialisePaymentInstrument = jest.fn();
    const materialisePaymentInstrumentLinked = jest.fn();
    const materialisePaymentInstrumentUnlinked = jest.fn();

    jest.mock('../../paymentInstrument', () => ({
      __esModule: true,
      ...jest.requireActual('../../paymentInstrument'),
      materialisePaymentInstrument,
      materialisePaymentInstrumentLinked,
      materialisePaymentInstrumentUnlinked,
    }));
    it('should execute projectEvent for payment instrument', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.PaymentInstrument.Created',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.PaymentInstrument.Created',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialisePaymentInstrument).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const mockCalls = materialisePaymentInstrument.mock.calls;

      expect(mockCalls[0][0].payload).toBeInstanceOf(PaymentInstrumentCreatedDto);
      expect(mockCalls[0][2]).toBe(domicile);
    });

    it('should execute projectEvent for payment instrument linked event', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.PaymentInstrument.Linked',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.PaymentInstrument.Linked',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialisePaymentInstrumentLinked).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const mockCalls = materialisePaymentInstrumentLinked.mock.calls;
      expect(mockCalls[0][0].payload).toBeInstanceOf(PaymentInstrumentLinkedDto);
      expect(mockCalls[0][2]).toBe(domicile);
    });

    it('should execute projectEvent for payment instrument unlinked event', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.PaymentInstrument.Unlinked',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.PaymentInstrument.Unlinked',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialisePaymentInstrumentUnlinked).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const mockCalls = materialisePaymentInstrumentUnlinked.mock.calls;
      expect(mockCalls[0][0].payload).toBeInstanceOf(PaymentInstrumentUnlinkedDto);
      expect(mockCalls[0][2]).toBe(domicile);
    });
  });

  describe('Projection event tests for contact', () => {
    const materialiseContactCreated = jest.fn();
    const materialiseContactDeleted = jest.fn();
    const materialiseContactSubcategoryLinked = jest.fn();
    const materialiseContactSubcategoryUnLinked = jest.fn();
    const materialiseContactLinked = jest.fn();
    const materialiseContactUnLinked = jest.fn();
    const materialiseContactMerchantLinked = jest.fn();
    const materialiseContactMerchantUnlinked = jest.fn();
    const materialiseContactDcaTransactionSender = jest.fn();

    jest.mock('../../contact', () => ({
      __esModule: true,
      ...jest.requireActual('../../contact'),
      materialiseContactCreated,
      materialiseContactDeleted,
      materialiseContactSubcategoryLinked,
      materialiseContactSubcategoryUnLinked,
      materialiseContactLinked,
      materialiseContactUnLinked,
      materialiseContactMerchantLinked,
      materialiseContactMerchantUnlinked,
      materialiseContactDcaTransactionSender,
    }));

    beforeEach(() => {
      materialiseContactDcaTransactionSender.mockReset();
    });

    it('should execute projectEvent for contact created', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.Contact.Created',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.Contact.Created',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialiseContactCreated).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const mockCalls = materialiseContactCreated.mock.calls;
      expect(mockCalls[0][0].payload).toBeInstanceOf(ContactCreatedEventDto);
      expect(mockCalls[0][2]).toBe(domicile);
    });

    it('should execute projectEvent for contact deleted', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.Contact.Deleted',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.Contact.Deleted',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialiseContactDeleted).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const mockCalls = materialiseContactDeleted.mock.calls;
      expect(mockCalls[0][0].payload).toBeInstanceOf(ContactDeletedEventDto);
      expect(mockCalls[0][2]).toBe(domicile);
    });

    it('should execute projectEvent for contact subcategory linked', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.Contact.SubcategoryLinked',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.Contact.SubcategoryLinked',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialiseContactSubcategoryLinked).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const mockCalls = materialiseContactSubcategoryLinked.mock.calls;
      expect(mockCalls[0][0].payload).toBeInstanceOf(ContactSubcategoryLinkEventDto);
      expect(mockCalls[0][2]).toBe(domicile);
    });

    it('should execute projectEvent for contact subcategory unlinked', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.Contact.SubcategoryUnlinked',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.Contact.SubcategoryUnlinked',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialiseContactSubcategoryUnLinked).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const mockCalls = materialiseContactSubcategoryUnLinked.mock.calls;
      expect(mockCalls[0][0].payload).toBeInstanceOf(ContactSubcategoryLinkEventDto);
      expect(mockCalls[0][2]).toBe(domicile);
    });

    it('should execute projectEvent for contact linked', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.Contact.Linked',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.Contact.Linked',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialiseContactLinked).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const mockCalls = materialiseContactLinked.mock.calls;
      expect(mockCalls[0][0].payload).toBeInstanceOf(ContactLinkEventDto);
      expect(mockCalls[0][2]).toBe(domicile);
    });

    it('should execute projectEvent for contact unlinked', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.Contact.Unlinked',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.Contact.Unlinked',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialiseContactUnLinked).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const mockCalls = materialiseContactUnLinked.mock.calls;
      expect(mockCalls[0][0].payload).toBeInstanceOf(ContactLinkEventDto);
      expect(mockCalls[0][2]).toBe(domicile);
    });

    it('should execute projectEvent for contact merchant linked', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.Contact.MerchantLinked',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.Contact.MerchantLinked',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialiseContactMerchantLinked).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const mockCalls = materialiseContactMerchantLinked.mock.calls;
      expect(mockCalls[0][0].payload).toBeInstanceOf(ContactMerchantLinkEventDto);
      expect(mockCalls[0][2]).toBe(domicile);
    });

    it('should execute projectEvent for contact merchant unlinked', async () => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': 'reps.Contact.MerchantUnlinked',
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: 'reps.Contact.MerchantUnlinked',
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialiseContactMerchantUnlinked).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const args = materialiseContactMerchantUnlinked.mock.calls[0];
      expect(args[0].payload).toBeInstanceOf(ContactMerchantUnlinkEventDto);
    });

    it.each([
      {
        detailType: 'reps.Contact.DebitCardAccountTransactionSenderLinked',
        dtoInstance: ContactDcaTransactionSenderLinkEventDto,
      },
      {
        detailType: 'reps.Contact.DebitCardAccountTransactionSenderUnlinked',
        dtoInstance: ContactDcaTransactionSenderUnlinkEventDto,
      },
    ])('should execute projectEvent for contact dca transaction linked', async ({ detailType, dtoInstance }) => {
      const events = [
        {
          body: JSON.stringify({
            'detail-type': detailType,
            detail: {
              aggregateId: '01',
              payload: {},
            },
            uri: detailType,
          }),
          receiptHandle: '',
          domicile,
        },
      ];

      const data = events.map((e) => JSON.parse(e.body));
      const eventData = data[0];

      await projectEvent(events);
      expect(debug).toHaveBeenCalled();
      expect(debug).toHaveBeenCalledWith(`projecting event - ${JSON.stringify(eventData)}`);

      expect(materialiseContactDcaTransactionSender).toHaveBeenCalledTimes(events.length);
      expect(mockDeleteMessage).toHaveBeenCalledTimes(events.length);

      const mockCalls = materialiseContactDcaTransactionSender.mock.calls;
      expect(mockCalls[0][0].payload).toBeInstanceOf(dtoInstance);
      expect(mockCalls[0][2]).toBe(domicile);
    });
  });
});
