import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { debug, error, info, warn } from '@npco/component-bff-core/dist/utils/logger';

import { DeleteMessageCommand, SQSClient } from '@aws-sdk/client-sqs';

import { EnvironmentService } from '../../config/environmentService.js';
import {
  contactModel,
  materialiseContactCreated,
  materialiseContactDcaTransactionSender,
  materialiseContactDeleted,
  materialiseContactLinked,
  materialiseContactMerchantLinked,
  materialiseContactMerchantUnlinked,
  materialiseContactSubcategoryLinked,
  materialiseContactSubcategoryUnLinked,
  materialiseContactUnLinked,
} from '../contact/index.js';
import {
  debitCardAccountTransactionModel,
  materialiseDebitCardAccountTransaction,
} from '../debitCardAccountTransaction/index.js';
import {
  materialisePaymentInstrument,
  materialisePaymentInstrumentLinked,
  materialisePaymentInstrumentUnlinked,
  paymentInstrumentModel,
} from '../paymentInstrument/index.js';
import { materialiseMerchant, merchantModel } from '../richData/index.js';
import {
  materialiseSavingsAccountTransaction,
  savingsAccountTransactionModel,
} from '../savingsAccountTransaction/index.js';

import type { EventBridgeEventData, ProjectionDto } from './index.js';

export const checkErrors = (/* istanbul ignore next: default value */ errorEvents: any = []) => {
  info(`errorlength: ${errorEvents.length}`);
  if (errorEvents.length > 0) {
    throw new Error(`process event ${JSON.stringify(errorEvents)} failed.`);
  }
};

export const deleteMessage = async (event: any, environmentService: EnvironmentService) => {
  const command = new DeleteMessageCommand({
    QueueUrl: environmentService.projectionSqsUrl,
    ReceiptHandle: event.receiptHandle,
  });
  const sqs = new SQSClient({ region: environmentService.region });
  const output = await sqs.send(command);
  info(`delete message from sqs ${JSON.stringify(event)} ${JSON.stringify(output)}`);
};

const materialisationMapping = async <T extends Record<string, any>>(
  deconstructedUri: string,
  data: ProjectionDto<T>,
  environmentService: EnvironmentService,
  domicile: Domicile,
) => {
  debug(`REPS engine -> materialisationMapping domicile ${domicile}`, data.aggregateId);
  const table = new Map<string, any>([
    ['ProjectionDebitCardAccountTransaction.create', materialiseDebitCardAccountTransaction],
    ['ProjectionDebitCardAccountTransaction.update', materialiseDebitCardAccountTransaction],
    ['ProjectionSavingsAccountTransaction.create', materialiseSavingsAccountTransaction],
    ['ProjectionSavingsAccountTransaction.update', materialiseSavingsAccountTransaction],
    ['PaymentInstrument.Created', materialisePaymentInstrument],
    ['PaymentInstrument.Linked', materialisePaymentInstrumentLinked],
    ['PaymentInstrument.Unlinked', materialisePaymentInstrumentUnlinked],
    ['Contact.Created', materialiseContactCreated],
    ['Contact.Deleted', materialiseContactDeleted],
    ['Contact.SubcategoryLinked', materialiseContactSubcategoryLinked],
    ['Contact.SubcategoryUnlinked', materialiseContactSubcategoryUnLinked],
    ['Contact.Linked', materialiseContactLinked],
    ['Contact.Unlinked', materialiseContactUnLinked],
    ['Contact.MerchantLinked', materialiseContactMerchantLinked],
    ['Contact.MerchantUnlinked', materialiseContactMerchantUnlinked],
    ['Contact.DebitCardAccountTransactionSenderLinked', materialiseContactDcaTransactionSender],
    ['Contact.DebitCardAccountTransactionSenderUnlinked', materialiseContactDcaTransactionSender],
    ['ProjectionMerchant.create', materialiseMerchant],
    ['ProjectionMerchant.update', materialiseMerchant],
  ]);

  await table.get(deconstructedUri)?.call(this, data, environmentService, domicile);
};

export const projectEvent = async (
  event: {
    body: string;
    receiptHandle: string;
    domicile: Domicile;
  }[],
) => {
  const environmentService = new EnvironmentService();
  const projectionLookups = {
    ...debitCardAccountTransactionModel,
    ...savingsAccountTransactionModel,
    ...contactModel,
    ...merchantModel,
    ...paymentInstrumentModel,
  };

  const data: EventBridgeEventData[] = event.map((e) => JSON.parse(e.body));
  const errorEvents: any = [];

  for (let i = 0; i < data.length; i += 1) {
    const currentEvent = event[i];
    const eventData = data[i];
    try {
      debug(`projecting event - ${JSON.stringify(eventData)}`);
      const { detail } = eventData;
      const detailType = eventData['detail-type'];
      const { aggregateId, payload, version, createdTimestamp } = detail;
      const [, aggregate, aggregateEvent] = detailType.split('.');
      const deconstructedUri = `${aggregate}.${aggregateEvent}`;
      const dto = projectionLookups[deconstructedUri];
      if (!dto) {
        error(`Handler not found ${detailType} ${JSON.stringify(payload, null, 2)}`);
        throw new Error('Handler not found');
      }
      const dtoEvent: ProjectionDto<typeof payload> = dto(payload, aggregateId, detailType, createdTimestamp, version);
      info(`repsService:projectEvent -> get dtoEvent ${JSON.stringify(dtoEvent)}`, dtoEvent.aggregateId);
      await materialisationMapping(deconstructedUri, dtoEvent, environmentService, currentEvent.domicile);
      await deleteMessage(currentEvent, environmentService);
    } catch (err: any) {
      warn(`get error: ${err}`);
      warn('Failed to project events, need to retry.');
      errorEvents.push(eventData);
      error(err.message, eventData.aggregateId);
    }
    checkErrors(errorEvents);
  }
};
