import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';
import type {
  SavingsAccountTransactionCreatedEventDto,
  SavingsAccountTransactionUpdatedEventDto,
} from '@npco/component-dto-issuing-transaction';

import type { EnvironmentService } from '../../../config/environmentService.js';
import { SavingsAccountTransaction, upsertConditionallyUpdatedTime, find } from '../../../database/index.js';
import { convertAmountWithSign } from '../../../utils/covertAmountWithSign.js';

export const paymentInstrumentTransactionTypes = [DebitCardTransactionTypeV2.BPAY_OUT];

const mapSavingsAccountTransactionToDbFields = (
  data: SavingsAccountTransactionCreatedEventDto | SavingsAccountTransactionUpdatedEventDto,
  domicile: Domicile,
): SavingsAccountTransaction => {
  return {
    id: data.id,
    entityUuid: data.entityUuid,
    accountUuid: data.accountUuid,
    type: data.type,
    status: data.status,
    timestamp: new Date(data.timestamp),
    amount: Number(data.amount.value),
    category: data.category,
    subcategory: data.subcategory,
    accountCategory: data.accountingCategory,
    attachments: data.attachments?.length ?? 0,
    note: !!(data.note && data.note?.trim().length > 0),
    tags: data.tags,
    updatedTime: data.updatedTime,
    currency: data.amount.currency,
    domicile,
  };
};

export const saveSavingsAccountTransaction = async (
  data: SavingsAccountTransactionCreatedEventDto | SavingsAccountTransactionUpdatedEventDto,
  env: EnvironmentService,
  domicile: Domicile,
) => {
  const dbValues = mapSavingsAccountTransactionToDbFields(data, domicile);
  const { amount: rawAmount, ...values } = dbValues;

  const amount = convertAmountWithSign(rawAmount, values.type);
  await upsertConditionallyUpdatedTime(
    SavingsAccountTransaction,
    'id',
    {
      ...values,
      amount,
    },
    SavingsAccountTransaction.name,
    env,
  );
};

export const getAllSavingsAccountTransactionRecordsById = async (
  id: string,
  env: EnvironmentService,
  domicile: Domicile,
) => {
  return find(SavingsAccountTransaction, env, {
    where: {
      id,
      domicile,
    },
  });
};
