import { describeIf } from '@npco/bff-systemtest-utils';
import { isRegionAP } from '@npco/bff-systemtest-utils/dist/helper/baseTestHelper';
import type { DebitCardTransactionV2 } from '@npco/component-dto-issuing-transaction';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';

import axios from 'axios';
import { Client } from 'pg';
import { v4 as uuid } from 'uuid';

import { getApiEndpoint } from '../utils/api';
import { getContactCreatedEventDto } from '../utils/contact/contactDto';
import { createContact } from '../utils/contact/contactSqs';
import { getDbClientOptions } from '../utils/database/database';
import { getDcaTxnDto } from '../utils/debitCardAccountTransaction/debitCardAccountTransaction';
import { createDebitCardAccountTransaction } from '../utils/debitCardAccountTransaction/debitCardAccountTransactionSqs';
import { getDomicileByRegion } from '../utils/domicile';
import { getPaymentInstrumentCreatedDto } from '../utils/paymentInstrument/paymentInstrument';
import { createPaymentInstrument } from '../utils/paymentInstrument/paymentInstrumentSqs';

const currentDate = new Date().toISOString().split('T')[0];

describe('Category net amount test suite', () => {
  const domicile = getDomicileByRegion();
  let client: Client;

  beforeAll(async () => {
    const options = await getDbClientOptions();
    client = new Client(options);
    await client.connect();
    await client.query(`SET search_path TO ${options.search_path}`);
  });

  afterAll(async () => {
    await client.end();
  });

  const runTests = (
    reportType: string,
    version: 'v1' | 'v2',
    endpointBase: (entityUuid: string) => Promise<string>,
  ) => {
    describe(`${version} API for ${reportType}`, () => {
      const entityUuid = uuid();
      const contactDto = getContactCreatedEventDto({ entityUuid });
      const paymentInstrumentDto = getPaymentInstrumentCreatedDto({ entityUuid });

      let dcaTxnDto: DebitCardTransactionV2;
      let endpoint: string;

      beforeAll(async () => {
        const contact = (await createContact(contactDto, client, domicile)).rows[0];
        const paymentInstrument = (
          await createPaymentInstrument(
            {
              ...paymentInstrumentDto,
              contactUuid: contact.contactUuid,
            },
            client,
            domicile,
          )
        ).rows[0];

        dcaTxnDto = getDcaTxnDto({
          entityUuid,
          type: DebitCardTransactionTypeV2.DE_OUT,
          payerDetails: {
            recipientUuid: uuid(),
            senderUuid: uuid(),
          },
          payeeDetails: {
            recipientUuid: paymentInstrument.paymentInstrumentUuid,
          },
        });

        await createDebitCardAccountTransaction(dcaTxnDto, client, domicile);
        endpoint = await endpointBase(entityUuid);
      });

      const assertResponse = (data: any, accountUuidCheck = false) => {
        expect(data).toBeDefined();
        expect(data.reportType).toBe(reportType);
        expect(data.range).toEqual(expect.objectContaining({ start: expect.any(String), end: expect.any(String) }));
        expect(data.timeZone).toEqual(expect.any(String));
        if (accountUuidCheck) {
          expect(data.accountUuid).toBe(dcaTxnDto.debitCardAccountUuid);
        }
        expect(data.categoryNetAmounts.length).toBe(1);
        data.categoryNetAmounts.forEach((item: any) => {
          expect(item.category).toBe(dcaTxnDto.category);
          expect(item.entityUuid).toBe(dcaTxnDto.entityUuid);
        });
      };

      it('should get all records', async () => {
        const res = await axios.post(endpoint, JSON.stringify({ date: currentDate, reportType }), {
          headers: { 'Content-Type': 'application/json' },
        });
        expect(res.status).toBe(200);
        assertResponse(res.data);
      });

      it('should get records for a single account', async () => {
        const res = await axios.post(
          endpoint,
          JSON.stringify({
            date: currentDate,
            reportType,
            accountUuid: dcaTxnDto.debitCardAccountUuid,
          }),
          { headers: { 'Content-Type': 'application/json' } },
        );
        expect(res.status).toBe(200);
        assertResponse(res.data, true);
      });
    });
  };

  ['MONTHLY', 'TTM'].forEach((reportType) => {
    describeIf(isRegionAP, 'v1 API', () => {
      runTests(reportType, 'v1', async (entityUuid) => `${await getApiEndpoint()}/v1/entity/${entityUuid}/category`);
    });

    describe('v2 API', () => {
      runTests(
        reportType,
        'v2',
        async (entityUuid) => `${await getApiEndpoint()}/v2/${domicile}/entity/${entityUuid}/category`,
      );
    });
  });
});
