import { describeIf } from '@npco/bff-systemtest-utils';
import { isRegionAP } from '@npco/bff-systemtest-utils/dist/helper/baseTestHelper';
import { EntityCategories } from '@npco/component-dto-core';
import type { DebitCardTransactionV2 } from '@npco/component-dto-issuing-transaction';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';
import type { PaymentInstrumentCreatedDto } from '@npco/component-dto-payment-instrument';

import axios from 'axios';
import { Client } from 'pg';
import { v4 as uuid } from 'uuid';

import { getApiEndpoint } from '../utils/api';
import { getContactCreatedEventDto } from '../utils/contact/contactDto';
import { createContact } from '../utils/contact/contactSqs';
import { getDbClientOptions } from '../utils/database/database';
import { getDcaTxnDto } from '../utils/debitCardAccountTransaction/debitCardAccountTransaction';
import { createDebitCardAccountTransaction } from '../utils/debitCardAccountTransaction/debitCardAccountTransactionSqs';
import { getDomicileByRegion } from '../utils/domicile';
import { getPaymentInstrumentCreatedDto } from '../utils/paymentInstrument/paymentInstrument';
import { createPaymentInstrument } from '../utils/paymentInstrument/paymentInstrumentSqs';

const currentDate = new Date().toISOString().split('T')[0];

describe('Subcategories net amount test suite', () => {
  const domicile = getDomicileByRegion();
  let client: Client;
  const category = EntityCategories.BANK_FEES;
  const subcategories = ['subcategory1', 'subcategory2'];

  beforeAll(async () => {
    const options = await getDbClientOptions();
    client = new Client(options);
    await client.connect();
    await client.query(`SET search_path TO ${options.search_path}`);
  });

  afterAll(async () => {
    await client.end();
  });

  const runTests = (
    reportType: string,
    version: 'v1' | 'v2',
    endpointBase: (entityUuid: string) => Promise<string>,
  ) => {
    describe(`${version} API for ${reportType}`, () => {
      let endpoint: string;
      const entityUuid = uuid();
      const contactUuid = uuid();

      const dtoContact = getContactCreatedEventDto({
        entityUuid,
      });
      const paymentInstrumentDto = getPaymentInstrumentCreatedDto({
        entityUuid,
      });
      let paymentInstrumentDetails: PaymentInstrumentCreatedDto;
      let dtos: DebitCardTransactionV2[];

      beforeAll(async () => {
        await createContact(
          {
            ...dtoContact,
            contactUuid,
          },
          client,
          domicile,
        );

        paymentInstrumentDetails = (
          await createPaymentInstrument(
            {
              ...paymentInstrumentDto,
              contactUuid,
            },
            client,
            domicile,
          )
        ).rows[0];

        dtos = subcategories.map((subcategory) =>
          getDcaTxnDto({
            entityUuid,
            category,
            subcategory,
            type: DebitCardTransactionTypeV2.DE_OUT,
            payerDetails: {
              recipientUuid: uuid(),
              senderUuid: uuid(),
            },
            payeeDetails: {
              recipientUuid: paymentInstrumentDetails.paymentInstrumentUuid,
            },
          }),
        );

        await Promise.all(dtos.map((dto) => createDebitCardAccountTransaction(dto, client, domicile)));

        endpoint = await endpointBase(entityUuid);
      });

      const assertResponse = (data: any) => {
        expect(data).not.toBeUndefined();
        expect(data.subcategoryNetAmounts.length).toBe(dtos.length);
        data.subcategoryNetAmounts.forEach((item: any) => {
          expect(item.category).toBe(category);
          expect(item.subcategory).toEqual(expect.any(String));
          expect(item.entityUuid).toBe(entityUuid);
        });
      };

      it('should get all records', async () => {
        const res = await axios.post(endpoint, JSON.stringify({ date: currentDate, reportType }), {
          headers: { 'Content-Type': 'application/json' },
        });
        expect(res.status).toBe(200);
        assertResponse(res.data);
      });
    });
  };

  ['MONTHLY', 'TTM'].forEach((reportType) => {
    describeIf(isRegionAP, 'v1 API', () => {
      runTests(
        reportType,
        'v1',
        async (entityUuid) => `${await getApiEndpoint()}/v1/entity/${entityUuid}/category/${category}/subcategory`,
      );
    });

    describe('v2 API', () => {
      runTests(
        reportType,
        'v2',
        async (entityUuid) =>
          `${await getApiEndpoint()}/v2/${domicile}/entity/${entityUuid}/category/${category}/subcategory`,
      );
    });
  });
});
